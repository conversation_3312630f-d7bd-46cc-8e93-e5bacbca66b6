// Landing page functionality
console.log('🚀 Landing script loading...');
alert('JavaScript is loading!'); // Debug test

// Demo user accounts (for demonstration purposes)
const demoAccounts = {
    '<EMAIL>': {
        password: 'demo123',
        name: 'Demo User',
        company: 'Demo Company Ltd',
        plan: 'Pro'
    },
    '<EMAIL>': {
        password: 'admin123',
        name: 'Admin User',
        company: 'DocuGen Pro',
        plan: 'Enterprise'
    },
    '<EMAIL>': {
        password: 'test123',
        name: 'Test Business',
        company: 'Test Business Inc',
        plan: 'Starter'
    }
};

// Authentication functions
function showLogin() {
    alert('showLogin function called!'); // Debug test
    document.getElementById('authModal').style.display = 'block';
    document.getElementById('signinForm').style.display = 'block';
    document.getElementById('signupForm').style.display = 'none';
}

function showSignup() {
    document.getElementById('authModal').style.display = 'block';
    document.getElementById('signinForm').style.display = 'none';
    document.getElementById('signupForm').style.display = 'block';
}

function showContact() {
    document.getElementById('helpModal').style.display = 'block';
}

function closeAuth() {
    document.getElementById('authModal').style.display = 'none';
}

function handleSignIn(event) {
    event.preventDefault();
    
    const email = document.getElementById('signinEmail').value;
    const password = document.getElementById('signinPassword').value;
    
    // Check demo accounts
    if (demoAccounts[email] && demoAccounts[email].password === password) {
        // Store user session
        localStorage.setItem('currentUser', JSON.stringify({
            email: email,
            name: demoAccounts[email].name,
            company: demoAccounts[email].company,
            plan: demoAccounts[email].plan,
            loginTime: new Date().toISOString()
        }));
        
        alert(`Welcome back, ${demoAccounts[email].name}! Redirecting to dashboard...`);
        closeAuth();
        
        // Redirect to main app
        setTimeout(() => {
            window.location.href = 'dashboard2.html';
        }, 1000);
    } else {
        alert('Invalid credentials. Try <EMAIL> / demo123');
    }
}

function handleSignUp(event) {
    event.preventDefault();
    
    const name = document.getElementById('signupName').value;
    const email = document.getElementById('signupEmail').value;
    const company = document.getElementById('signupCompany').value;
    const password = document.getElementById('signupPassword').value;
    
    // Store new user (in real app, this would go to a server)
    const newUser = {
        email: email,
        name: name,
        company: company,
        plan: 'Free Trial',
        loginTime: new Date().toISOString()
    };
    
    localStorage.setItem('currentUser', JSON.stringify(newUser));
    
    alert(`Account created successfully! Welcome, ${name}!`);
    closeAuth();
    
    // Redirect to main app
    setTimeout(() => {
        window.location.href = 'dashboard2.html';
    }, 1000);
}

// Document preview functionality
function previewDocument(type) {
    const previewModal = document.getElementById('previewModal');
    const previewContainer = document.getElementById('previewContainer');
    
    // Generate preview content based on document type
    const previewContent = generateDocumentPreview(type);
    previewContainer.innerHTML = previewContent;
    
    previewModal.style.display = 'block';
}

function closePreview() {
    document.getElementById('previewModal').style.display = 'none';
}

function generateDocumentPreview(type) {
    // Get sample company data for preview
    const sampleCompany = {
        name: 'Benjamin Music Initiatives',
        address: '123 Harmony Street\nMelody Park, Johannesburg\n2000, South Africa',
        phone: '+27 12 345 6789',
        email: '<EMAIL>',
        regNumber: '2023/123456/07',
        vatNumber: '**********',
        banking: {
            bankName: 'First National Bank',
            accountNumber: '**********',
            branchCode: '250655'
        }
    };

    // Generate sample logo (BMI logo)
    const sampleLogo = generateSampleLogo();

    // Generate realistic preview based on document type
    switch(type) {
        case 'invoice':
            return generateLandingInvoicePreview(sampleCompany, sampleLogo);
        case 'receipt':
            return generateLandingReceiptPreview(sampleCompany, sampleLogo);
        case 'quotation':
            return generateLandingQuotationPreview(sampleCompany, sampleLogo);
        case 'contract':
            return generateLandingContractPreview(sampleCompany, sampleLogo);
        case 'rider':
            return generateLandingTechnicalRiderPreview(sampleCompany, sampleLogo);
        case 'annexure':
            return generateLandingAnnexurePreview(sampleCompany, sampleLogo);
        default:
            return generateLandingGenericPreview(type, sampleCompany, sampleLogo);
    }
}

// Generate sample logo for landing page previews
function generateSampleLogo() {
    const canvas = document.createElement('canvas');
    canvas.width = 100;
    canvas.height = 100;
    const ctx = canvas.getContext('2d');

    // Create gradient background (BMI colors)
    const gradient = ctx.createLinearGradient(0, 0, 100, 100);
    gradient.addColorStop(0, '#28a745');
    gradient.addColorStop(1, '#1e7e34');

    // Draw background
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 100, 100);

    // Add border
    ctx.strokeStyle = '#155724';
    ctx.lineWidth = 2;
    ctx.strokeRect(1, 1, 98, 98);

    // Add icon
    ctx.font = '30px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillStyle = '#ffffff';
    ctx.fillText('🎼', 50, 35);

    // Add text
    ctx.font = 'bold 16px Arial';
    ctx.fillText('BMI', 50, 70);

    return canvas.toDataURL();
}

// Landing page realistic preview functions
function generateLandingInvoicePreview(company, logo) {
    return `
        <div class="realistic-preview invoice-preview" style="max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); font-family: 'Open Sans', sans-serif; line-height: 1.6; color: #333;">
            ${logo ? `<div style="text-align: center; margin-bottom: 20px;"><img src="${logo}" style="width: 80px; height: 80px; object-fit: contain;"></div>` : ''}

            <h1 style="text-align: center; color: #2c3e50; margin: 0 0 20px 0; font-size: 32px; font-weight: 700; font-family: 'Montserrat', sans-serif; letter-spacing: 1px;">INVOICE</h1>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 25px;">
                <div>
                    <p style="margin: 5px 0;"><strong>Invoice #:</strong> 000001</p>
                    <p style="margin: 5px 0;"><strong>Date:</strong> 2025-05-09</p>
                    <p style="margin: 5px 0;"><strong>Due Date:</strong> 2025-06-08</p>
                </div>
                <div style="text-align: right;">
                    <h2 style="color: #2c3e50; margin: 0 0 10px 0; font-size: 18px;">FROM</h2>
                    <p style="margin: 2px 0;">Harmony Sound Studios<br>
                    Reg: 2022/123456/07<br>
                    VAT: 4567890123<br>
                    Email: <EMAIL><br>
                    123 Melody Lane<br>
                    Waterfront<br>
                    Cape Town, 8001<br>
                    Western Cape<br>
                    <strong>Contact:</strong> James Anderson<br>
                    <strong>Phone:</strong> +27215557890</p>
                </div>
            </div>

            <div style="margin-bottom: 25px;">
                <h2 style="color: #2c3e50; margin: 0 0 10px 0; font-size: 18px; font-family: 'Montserrat', sans-serif; font-weight: 600;">BILL TO</h2>
                <p style="margin: 2px 0; font-size: 14px;">Sandton Music Academy (Pty) Ltd<br>
                Reg: 2023/235678/07<br>
                VAT: 4789523610<br>
                145 West Street<br>
                Sandown<br>
                Sandton, 2196<br>
                Gauteng</p>
                <p style="margin: 10px 0 0 0; font-weight: 600;"><strong>Attention:</strong> Dr. Sarah van der Merwe</p>
            </div>

            <div style="margin-bottom: 25px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <h2 style="color: #2c3e50; margin: 0 0 10px 0; font-size: 18px; font-family: 'Montserrat', sans-serif; font-weight: 600;">EVENT DETAILS</h2>
                <p style="margin: 2px 0; font-size: 14px;"><strong>Event:</strong> Hondo<br>
                <strong>Date:</strong> 2025-02-22<br>
                <strong>Time:</strong> 20:00<br>
                <strong>Venue:</strong> Stodart Hall, Harare</p>
            </div>

            <h2 style="color: #2c3e50; margin: 20px 0 10px 0; font-size: 18px; font-family: 'Montserrat', sans-serif; font-weight: 600;">DESCRIPTION</h2>
            <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
                <thead>
                    <tr style="background: #f8f9fa;">
                        <th style="padding: 12px; text-align: left; border: 1px solid #dee2e6; font-weight: bold;">Description</th>
                        <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6; font-weight: bold;">Qty</th>
                        <th style="padding: 12px; text-align: right; border: 1px solid #dee2e6; font-weight: bold;">Price</th>
                        <th style="padding: 12px; text-align: right; border: 1px solid #dee2e6; font-weight: bold;">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dee2e6;">Professional Audio Services</td>
                        <td style="padding: 10px; text-align: center; border: 1px solid #dee2e6;">1</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R2500.00</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R2500.00</td>
                    </tr>
                </tbody>
            </table>

            <div style="text-align: right; margin: 20px 0;">
                <p style="margin: 5px 0;"><strong>Subtotal:</strong> R2500.00</p>
                <p style="margin: 5px 0;"><strong>VAT (15%):</strong> R375.00</p>
                <p style="margin: 10px 0; font-size: 20px; color: #2c3e50;"><strong>TOTAL: R2875.00</strong></p>
            </div>

            <div style="margin: 25px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <h2 style="color: #2c3e50; margin: 0 0 10px 0; font-size: 18px; font-family: 'Montserrat', sans-serif; font-weight: 600;">BANK DETAILS</h2>
                <p style="margin: 2px 0; font-size: 14px;">Bank: First National Bank<br>
                Account Name: Harmony Sound Studios (Pty) Ltd<br>
                Account Number: ***********<br>
                Branch Code: 250655</p>
            </div>

            <div style="margin: 25px 0;">
                <h2 style="color: #2c3e50; margin: 0 0 10px 0; font-size: 18px; font-family: 'Montserrat', sans-serif; font-weight: 600;">TERMS AND CONDITIONS</h2>
                <ol style="margin: 0; padding-left: 20px; font-size: 14px; line-height: 1.6;">
                    <li style="margin-bottom: 8px;">Payment is due within the specified payment terms.</li>
                    <li style="margin-bottom: 8px;">Late payments may incur interest charges of 2% per month.</li>
                    <li style="margin-bottom: 8px;">All prices are quoted in South African Rand (ZAR).</li>
                    <li style="margin-bottom: 8px;">Bank charges for payments are for the client's account.</li>
                </ol>
            </div>

            <div style="margin: 30px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #007bff;">
                <h3 style="color: #2c3e50; margin-top: 0; font-family: 'Montserrat', sans-serif; font-weight: 600; font-size: 16px;">✍️ Digital Signature</h3>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="flex: 1;">
                        <p style="margin: 0; color: #666; font-size: 14px;">Ready for digital signature and secure document delivery</p>
                        <small style="color: #999; font-size: 12px;">Supports multiple signature types: drawn, typed, and uploaded</small>
                    </div>
                    <div style="text-align: center; padding: 10px; border: 2px dashed #007bff; border-radius: 5px; min-width: 150px;">
                        <div style="color: #007bff; font-size: 24px;">✍️</div>
                        <small style="color: #007bff; font-size: 12px; font-weight: 500;">Signature Area</small>
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 20px; font-size: 12px; color: #666; font-family: 'Open Sans', sans-serif;">
                <p style="margin: 5px 0; font-weight: 500;">Created by Benjamin Music Initiatives</p>
                <p style="margin: 5px 0;">James Anderson</p>
                <p style="margin: 5px 0; color: #999;">Page 1 of 2</p>
            </div>

            <div class="preview-actions" style="margin-top: 30px; text-align: center; display: flex; gap: 10px; justify-content: center;">
                <button class="btn-primary" onclick="startWithTemplate('invoice')" style="flex: 1; max-width: 200px; padding: 12px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;">Use This Template</button>
                <button class="btn-outline" onclick="showSigningDemo('invoice')" style="flex: 1; max-width: 200px; padding: 12px 20px; background: transparent; color: #007bff; border: 2px solid #007bff; border-radius: 5px; cursor: pointer; font-weight: bold;">📝 Sign Document</button>
            </div>
        </div>
    `;
}

function generateLandingReceiptPreview(company, logo) {
    const currentDate = new Date().toLocaleDateString();
    const receiptNumber = `RCP-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;

    return `
        <div class="realistic-preview receipt-preview">
            <div class="document-header">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 50px; height: 50px; object-fit: contain; float: left; margin-right: 15px;">` : ''}
                <div class="header-content">
                    <h1 style="color: #28a745; margin: 0; font-size: 24px;">PAYMENT RECEIPT</h1>
                    <div class="company-info" style="margin-top: 8px; font-size: 14px;">
                        <strong>${company.name}</strong><br>
                        ${company.address.split('\n')[0]}<br>
                        Tel: ${company.phone}
                    </div>
                </div>
                <div style="clear: both;"></div>
            </div>

            <div class="receipt-details" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                    <span><strong>Receipt #:</strong> ${receiptNumber}</span>
                    <span><strong>Date:</strong> ${currentDate}</span>
                </div>
                <div style="margin-bottom: 10px;">
                    <strong>Received From:</strong> Rhythm Records Ltd
                </div>
                <div>
                    <strong>Payment Method:</strong> Electronic Transfer
                </div>
            </div>

            <div class="payment-details" style="margin: 20px 0;">
                <h3 style="color: #2c3e50; margin-bottom: 15px;">Payment Details</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr style="background: #f8f9fa;">
                        <td style="padding: 10px; border: 1px solid #dee2e6;"><strong>Description</strong></td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;"><strong>Amount</strong></td>
                    </tr>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dee2e6;">Payment for Invoice INV-2025-001</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R 21,275.00</td>
                    </tr>
                </table>
            </div>

            <div class="receipt-total" style="margin: 20px 0; text-align: right; padding: 15px; background: #e8f5e8; border-radius: 5px;">
                <div style="font-size: 20px; color: #28a745;"><strong>Amount Received: R 21,275.00</strong></div>
                <div style="margin-top: 5px; font-size: 14px; color: #666;">Thank you for your payment!</div>
            </div>

            <div class="receipt-footer" style="margin-top: 30px; text-align: center; font-size: 12px; color: #666;">
                This receipt serves as proof of payment.<br>
                For queries, contact: ${company.email}
            </div>

            <div class="signature-section" style="margin: 30px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #28a745;">
                <h3 style="color: #2c3e50; margin-top: 0;">✍️ Digital Signature</h3>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="flex: 1;">
                        <p style="margin: 0; color: #666;">Acknowledge receipt with digital signature</p>
                        <small style="color: #999;">Secure and legally binding electronic signature</small>
                    </div>
                    <div style="text-align: center; padding: 10px; border: 2px dashed #28a745; border-radius: 5px; min-width: 150px;">
                        <div style="color: #28a745; font-size: 24px;">✍️</div>
                        <small style="color: #28a745;">Signature Area</small>
                    </div>
                </div>
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center; display: flex; gap: 10px; justify-content: center;">
                <button class="btn-primary" onclick="startWithTemplate('receipt')" style="flex: 1; max-width: 200px;">Use This Template</button>
                <button class="btn-outline" onclick="showSigningDemo('receipt')" style="flex: 1; max-width: 200px;">📝 Sign Document</button>
            </div>
        </div>
    `;
}

function generateLandingQuotationPreview(company, logo) {
    const currentDate = new Date().toLocaleDateString();
    const validUntil = new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString();
    const quoteNumber = `QUO-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;

    return `
        <div class="realistic-preview quotation-preview">
            <div class="document-header">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 60px; height: 60px; object-fit: contain; float: left; margin-right: 15px;">` : ''}
                <div class="header-content">
                    <h1 style="color: #f39c12; margin: 0; font-size: 28px;">QUOTATION</h1>
                    <div class="company-info" style="margin-top: 10px;">
                        <strong>${company.name}</strong><br>
                        ${company.address.replace(/\n/g, '<br>')}<br>
                        Tel: ${company.phone} | Email: ${company.email}
                    </div>
                </div>
                <div style="clear: both;"></div>
            </div>

            <div class="quote-details" style="margin: 20px 0; display: flex; justify-content: space-between;">
                <div class="quote-info">
                    <strong>Quote #:</strong> ${quoteNumber}<br>
                    <strong>Date:</strong> ${currentDate}<br>
                    <strong>Valid Until:</strong> ${validUntil}
                </div>
                <div class="client-info">
                    <strong>Quote For:</strong><br>
                    Rhythm Records Ltd<br>
                    456 Beat Avenue<br>
                    Sound City, Cape Town, 8001
                </div>
            </div>

            <div class="quote-scope" style="margin: 20px 0; padding: 15px; background: #fff3cd; border-radius: 5px; border-left: 4px solid #f39c12;">
                <h3 style="margin-top: 0; color: #856404;">Project Scope</h3>
                <p style="margin-bottom: 0;">Complete event sound system setup and professional audio engineering services for live performance.</p>
            </div>

            <table class="quote-table" style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                <thead style="background: #f8f9fa;">
                    <tr>
                        <th style="padding: 12px; text-align: left; border: 1px solid #dee2e6;">Service Description</th>
                        <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">Qty</th>
                        <th style="padding: 12px; text-align: right; border: 1px solid #dee2e6;">Rate</th>
                        <th style="padding: 12px; text-align: right; border: 1px solid #dee2e6;">Total</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dee2e6;">Event Sound System Setup</td>
                        <td style="padding: 10px; text-align: center; border: 1px solid #dee2e6;">1</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R 15,000.00</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R 15,000.00</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dee2e6;">Sound Engineer (8 hours)</td>
                        <td style="padding: 10px; text-align: center; border: 1px solid #dee2e6;">8</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R 750.00</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R 6,000.00</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dee2e6;">Equipment Transport</td>
                        <td style="padding: 10px; text-align: center; border: 1px solid #dee2e6;">1</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R 2,000.00</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R 2,000.00</td>
                    </tr>
                </tbody>
            </table>

            <div class="quote-totals" style="margin-top: 20px; text-align: right;">
                <div style="margin: 5px 0;"><strong>Subtotal:</strong> R 23,000.00</div>
                <div style="margin: 5px 0;"><strong>VAT (15%):</strong> R 3,450.00</div>
                <div style="margin: 10px 0; font-size: 18px; color: #f39c12;"><strong>Total Quote:</strong> R 26,450.00</div>
            </div>

            <div class="quote-terms" style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <strong>Terms & Conditions:</strong><br>
                • Quote valid for 30 days from date of issue<br>
                • 50% deposit required to commence work<br>
                • Final payment due upon project completion
            </div>

            <div class="signature-section" style="margin: 30px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #f39c12;">
                <h3 style="color: #2c3e50; margin-top: 0;">✍️ Digital Signature</h3>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="flex: 1;">
                        <p style="margin: 0; color: #666;">Accept quotation with digital signature</p>
                        <small style="color: #999;">Convert quote to binding agreement with signature</small>
                    </div>
                    <div style="text-align: center; padding: 10px; border: 2px dashed #f39c12; border-radius: 5px; min-width: 150px;">
                        <div style="color: #f39c12; font-size: 24px;">✍️</div>
                        <small style="color: #f39c12;">Signature Area</small>
                    </div>
                </div>
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center; display: flex; gap: 10px; justify-content: center;">
                <button class="btn-primary" onclick="startWithTemplate('quotation')" style="flex: 1; max-width: 200px;">Use This Template</button>
                <button class="btn-outline" onclick="showSigningDemo('quotation')" style="flex: 1; max-width: 200px;">📝 Sign Document</button>
            </div>
        </div>
    `;
}

function generateLandingContractPreview(company, logo) {
    return `
        <div class="realistic-preview contract-preview">
            <div class="document-header">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 60px; height: 60px; object-fit: contain; float: left; margin-right: 15px;">` : ''}
                <div class="header-content">
                    <h1 style="color: #2c3e50; margin: 0; font-size: 28px;">SERVICE AGREEMENT</h1>
                    <div class="company-info" style="margin-top: 10px;">
                        <strong>${company.name}</strong><br>
                        Professional Audio Production Services Contract
                    </div>
                </div>
                <div style="clear: both;"></div>
            </div>

            <div class="contract-parties" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <h3 style="color: #2c3e50; margin-top: 0;">Contracting Parties</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <strong>Service Provider:</strong><br>
                        ${company.name}<br>
                        ${company.address.split('\n')[0]}<br>
                        Registration: ${company.regNumber}
                    </div>
                    <div>
                        <strong>Client:</strong><br>
                        Rhythm Records Ltd<br>
                        456 Beat Avenue<br>
                        Registration: 2023/456789/07
                    </div>
                </div>
            </div>

            <div class="contract-terms" style="margin: 20px 0;">
                <h3 style="color: #2c3e50;">Key Terms & Conditions</h3>
                <div style="padding: 15px; background: #fff; border: 1px solid #dee2e6; border-radius: 5px;">
                    <p><strong>1. Scope of Services:</strong> Professional audio production services including recording, mixing, and mastering for album project.</p>
                    <p><strong>2. Contract Duration:</strong> 6 weeks from commencement date with option to extend.</p>
                    <p><strong>3. Payment Terms:</strong> Total fee R85,000.00 - 50% deposit, 50% on completion.</p>
                    <p><strong>4. Intellectual Property:</strong> Client retains rights to final recordings, producer retains production credits.</p>
                    <p><strong>5. Deliverables:</strong> 12 fully produced tracks in WAV and MP3 formats plus album artwork consultation.</p>
                </div>
            </div>

            <div class="signature-section" style="margin: 30px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #2c3e50;">
                <h3 style="color: #2c3e50; margin-top: 0;">✍️ Digital Signatures</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
                    <div style="text-align: center; padding: 15px; border: 2px dashed #2c3e50; border-radius: 5px;">
                        <div style="color: #2c3e50; font-size: 24px; margin-bottom: 5px;">✍️</div>
                        <strong>Service Provider</strong><br>
                        <small style="color: #666;">Benjamin Music Initiatives</small>
                    </div>
                    <div style="text-align: center; padding: 15px; border: 2px dashed #2c3e50; border-radius: 5px;">
                        <div style="color: #2c3e50; font-size: 24px; margin-bottom: 5px;">✍️</div>
                        <strong>Client</strong><br>
                        <small style="color: #666;">Rhythm Records Ltd</small>
                    </div>
                </div>
                <p style="text-align: center; margin: 15px 0 0 0; color: #666; font-size: 14px;">
                    Both parties must sign to execute this agreement
                </p>
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center; display: flex; gap: 10px; justify-content: center;">
                <button class="btn-primary" onclick="startWithTemplate('contract')" style="flex: 1; max-width: 200px;">Use This Template</button>
                <button class="btn-outline" onclick="showSigningDemo('contract')" style="flex: 1; max-width: 200px;">📝 Sign Document</button>
            </div>
        </div>
    `;
}

function generateLandingTechnicalRiderPreview(company, logo) {
    return `
        <div class="realistic-preview technical-rider-preview">
            <div class="document-header">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 60px; height: 60px; object-fit: contain; float: left; margin-right: 15px;">` : ''}
                <div class="header-content">
                    <h1 style="color: #e74c3c; margin: 0; font-size: 28px;">TECHNICAL RIDER</h1>
                    <div class="company-info" style="margin-top: 10px;">
                        <strong>${company.name}</strong><br>
                        Live Performance Technical Requirements
                    </div>
                </div>
                <div style="clear: both;"></div>
            </div>

            <div class="rider-overview" style="margin: 20px 0; padding: 15px; background: #ffeaa7; border-radius: 5px; border-left: 4px solid #fdcb6e;">
                <h3 style="margin-top: 0; color: #2d3436;">Performance Overview</h3>
                <p><strong>Artist:</strong> BMI Live Band | <strong>Performance Duration:</strong> 90 minutes + 20 min encore</p>
                <p><strong>Stage Requirements:</strong> Minimum 10m x 8m x 1.2m high platform</p>
            </div>

            <div class="technical-sections" style="margin: 20px 0;">
                <div class="tech-section" style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                    <h3 style="color: #2c3e50; margin-top: 0;">🎵 Audio Requirements</h3>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>PA System: Professional line array, minimum 15kW total power</li>
                        <li>Mixing Console: 32+ channel digital console (Yamaha CL5 preferred)</li>
                        <li>Microphones: 8x SM58, 4x SM57, 1x AKG D112 for kick drum</li>
                        <li>Wireless Systems: 6x professional wireless microphone systems</li>
                        <li>Monitor System: 8x active wedge monitors + IEM capability</li>
                    </ul>
                </div>

                <div class="tech-section" style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                    <h3 style="color: #2c3e50; margin-top: 0;">🎸 Backline Requirements</h3>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>Drum Kit: Professional 5-piece kit with all hardware</li>
                        <li>Guitar Amps: 2x Marshall JCM800 100W heads + 4x12 cabinets</li>
                        <li>Bass Amp: Ampeg SVT-CL head + 8x10 cabinet</li>
                        <li>Keyboards: Yamaha CP88 stage piano + Nord Lead A1 synthesizer</li>
                    </ul>
                </div>
            </div>

            <div class="schedule-requirements" style="margin: 20px 0; padding: 15px; background: #dff0d8; border-radius: 5px;">
                <h3 style="color: #3c763d; margin-top: 0;">⏰ Schedule Requirements</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div>
                        <strong>Load-in:</strong> 8 hours before doors<br>
                        <strong>Sound Check:</strong> 2 hours minimum<br>
                        <strong>Line Check:</strong> 30 minutes before doors
                    </div>
                    <div>
                        <strong>Show Duration:</strong> 90 minutes + encore<br>
                        <strong>Load-out:</strong> Immediate after show<br>
                        <strong>Curfew:</strong> All equipment removed by venue curfew
                    </div>
                </div>
            </div>

            <div class="signature-section" style="margin: 30px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #e74c3c;">
                <h3 style="color: #2c3e50; margin-top: 0;">✍️ Technical Approval</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
                    <div style="text-align: center; padding: 15px; border: 2px dashed #e74c3c; border-radius: 5px;">
                        <div style="color: #e74c3c; font-size: 24px; margin-bottom: 5px;">✍️</div>
                        <strong>Artist/Band</strong><br>
                        <small style="color: #666;">Technical Requirements Confirmed</small>
                    </div>
                    <div style="text-align: center; padding: 15px; border: 2px dashed #e74c3c; border-radius: 5px;">
                        <div style="color: #e74c3c; font-size: 24px; margin-bottom: 5px;">✍️</div>
                        <strong>Venue/Promoter</strong><br>
                        <small style="color: #666;">Requirements Accepted</small>
                    </div>
                </div>
                <p style="text-align: center; margin: 15px 0 0 0; color: #666; font-size: 14px;">
                    Digital signatures confirm technical requirements agreement
                </p>
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center; display: flex; gap: 10px; justify-content: center;">
                <button class="btn-primary" onclick="startWithTemplate('rider')" style="flex: 1; max-width: 200px;">Use This Template</button>
                <button class="btn-outline" onclick="showSigningDemo('rider')" style="flex: 1; max-width: 200px;">📝 Sign Document</button>
            </div>
        </div>
    `;
}

function generateLandingAnnexurePreview(company, logo) {
    return `
        <div class="realistic-preview annexure-preview">
            <div class="document-header" style="text-align: center; margin-bottom: 30px;">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 60px; height: 60px; object-fit: contain; margin: 0 auto 15px; display: block;">` : ''}
                <h1 style="color: #6c757d; margin: 0; font-size: 32px;">ANNEXURE A</h1>
                <div class="company-info" style="margin-top: 15px;">
                    <strong style="font-size: 16px;">${company.name}</strong><br>
                    Equipment List & Technical Specifications<br>
                    Tel: ${company.phone} | Email: ${company.email}
                </div>
            </div>

            <div class="annexure-content" style="margin: 30px 0;">
                <div class="section" style="margin-bottom: 25px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                    <h3 style="color: #2c3e50; margin-top: 0;">📋 Audio Equipment Inventory</h3>
                    <p>Complete list of professional audio equipment provided for live performances:</p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>Yamaha CL5 Digital Mixing Console with 72 input channels</li>
                        <li>4x Shure SM58 Dynamic Microphones with wireless capability</li>
                        <li>2x Shure Beta 87A Condenser Microphones for vocals</li>
                        <li>8x Professional XLR Cables (10m each) with Neutrik connectors</li>
                        <li>Complete monitor mixing system with dedicated engineer</li>
                    </ul>
                </div>

                <div class="section" style="margin-bottom: 25px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                    <h3 style="color: #2c3e50; margin-top: 0;">🔊 PA System Components</h3>
                    <p>Professional sound reinforcement system specifications:</p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>4x Yamaha DXR12 Active Speakers (2000W total power)</li>
                        <li>2x Yamaha DXS15 Active Subwoofers for low-end reinforcement</li>
                        <li>Professional speaker stands and rigging hardware</li>
                        <li>Complete power distribution and cable management system</li>
                    </ul>
                </div>

                <div class="section" style="margin-bottom: 25px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                    <h3 style="color: #2c3e50; margin-top: 0;">⚖️ Insurance & Liability</h3>
                    <p>Equipment protection and liability coverage:</p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>Comprehensive equipment insurance policy #BMI-2025-EQUIP</li>
                        <li>Public liability coverage up to R5,000,000</li>
                        <li>Professional indemnity insurance for technical services</li>
                        <li>Equipment replacement guarantee within 24 hours</li>
                    </ul>
                </div>
            </div>

            <div class="annexure-footer" style="margin-top: 30px; padding: 15px; background: #e9ecef; border-radius: 5px; text-align: center;">
                <p style="margin: 0; font-style: italic; color: #6c757d;">
                    This annexure forms an integral part of the main service agreement and should be read in conjunction with all other contract documents.
                </p>
            </div>

            <div class="signature-section" style="margin: 30px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #6c757d;">
                <h3 style="color: #2c3e50; margin-top: 0;">✍️ Document Acknowledgment</h3>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="flex: 1;">
                        <p style="margin: 0; color: #666;">Acknowledge receipt and acceptance of annexure terms</p>
                        <small style="color: #999;">Digital signature confirms document review and acceptance</small>
                    </div>
                    <div style="text-align: center; padding: 10px; border: 2px dashed #6c757d; border-radius: 5px; min-width: 150px;">
                        <div style="color: #6c757d; font-size: 24px;">✍️</div>
                        <small style="color: #6c757d;">Signature Area</small>
                    </div>
                </div>
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center; display: flex; gap: 10px; justify-content: center;">
                <button class="btn-primary" onclick="startWithTemplate('annexure')" style="flex: 1; max-width: 200px;">Use This Template</button>
                <button class="btn-outline" onclick="showSigningDemo('annexure')" style="flex: 1; max-width: 200px;">📝 Sign Document</button>
            </div>
        </div>
    `;
}

function generateLandingGenericPreview(type, company, logo) {
    const typeConfig = {
        title: type.charAt(0).toUpperCase() + type.slice(1),
        description: `Professional ${type} document with customizable content and company branding.`
    };

    return `
        <div class="realistic-preview generic-preview">
            <div class="document-header">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 60px; height: 60px; object-fit: contain; float: left; margin-right: 15px;">` : ''}
                <div class="header-content">
                    <h1 style="color: #6c757d; margin: 0; font-size: 28px;">${typeConfig.title.toUpperCase()}</h1>
                    <div class="company-info" style="margin-top: 10px;">
                        <strong>${company.name}</strong><br>
                        Professional ${typeConfig.title} Document
                    </div>
                </div>
                <div style="clear: both;"></div>
            </div>

            <div class="document-description" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <p style="margin: 0; font-size: 16px;">${typeConfig.description}</p>
            </div>

            <div class="features-list" style="margin: 20px 0;">
                <h3 style="color: #2c3e50;">Template Features:</h3>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>✓ Professional layout and formatting</li>
                    <li>✓ Company branding and logo integration</li>
                    <li>✓ Customizable content sections</li>
                    <li>✓ PDF export functionality</li>
                    <li>✓ Mobile-responsive design</li>
                </ul>
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center;">
                <button class="btn-primary" onclick="startWithTemplate('${type}')">Use This Template</button>
            </div>
        </div>
    `;
}

function getMockData(type) {
    const baseData = {
        from: {
            company: "Benjamin Music Initiatives",
            address: "123 Harmony Street, Melody Park, Johannesburg, 2000",
            contact: "Tel: +27 12 345 6789 | Email: <EMAIL>"
        },
        to: {
            company: "Rhythm Records Ltd",
            address: "456 Beat Avenue, Sound City, Cape Town, 8001",
            contact: "Tel: +27 21 987 6543 | Email: <EMAIL>"
        }
    };

    switch(type) {
        case 'invoice':
            return {
                ...baseData,
                title: "INVOICE",
                number: "Invoice #: INV-2025-001",
                date: "Date: January 27, 2025",
                toLabel: "Bill To",
                items: [
                    { description: "Audio Production Services", quantity: "8", price: "R1,250.00", amount: "R10,000.00" },
                    { description: "Studio Rental (2 days)", quantity: "2", price: "R2,500.00", amount: "R5,000.00" },
                    { description: "Mixing & Mastering", quantity: "1", price: "R3,500.00", amount: "R3,500.00" }
                ],
                totals: {
                    subtotal: "R18,500.00",
                    vat: "R2,775.00",
                    total: "R21,275.00"
                }
            };
            
        case 'receipt':
            return {
                ...baseData,
                title: "RECEIPT",
                number: "Receipt #: RCP-2025-001",
                date: "Date: January 27, 2025",
                toLabel: "Received From",
                items: [
                    { description: "Payment for Invoice INV-2025-001", quantity: "1", price: "R21,275.00", amount: "R21,275.00" }
                ],
                totals: {
                    subtotal: "R21,275.00",
                    vat: "R0.00",
                    total: "R21,275.00"
                },
                specialSection: {
                    title: "Payment Method",
                    content: "Electronic Transfer - Reference: BMI2025001"
                }
            };
            
        case 'quotation':
            return {
                ...baseData,
                title: "QUOTATION",
                number: "Quote #: QUO-2025-001",
                date: "Date: January 27, 2025",
                toLabel: "Quote For",
                items: [
                    { description: "Event Sound System Setup", quantity: "1", price: "R15,000.00", amount: "R15,000.00" },
                    { description: "Sound Engineer (8 hours)", quantity: "8", price: "R750.00", amount: "R6,000.00" },
                    { description: "Equipment Transport", quantity: "1", price: "R2,000.00", amount: "R2,000.00" }
                ],
                totals: {
                    subtotal: "R23,000.00",
                    vat: "R3,450.00",
                    total: "R26,450.00"
                },
                specialSection: {
                    title: "Validity",
                    content: "This quotation is valid for 30 days from the date above."
                }
            };
            
        case 'contract':
            return {
                ...baseData,
                title: "SERVICE AGREEMENT",
                number: "Contract #: CON-2025-001",
                date: "Date: January 27, 2025",
                toLabel: "Client",
                content: `
                    <p><strong>1. SCOPE OF SERVICES</strong></p>
                    <p>Benjamin Music Initiatives agrees to provide professional audio production services including recording, mixing, and mastering for the Client's upcoming album project.</p>
                    
                    <p><strong>2. DURATION</strong></p>
                    <p>This agreement shall commence on February 1, 2025, and shall continue until completion of the project, estimated at 6 weeks.</p>
                    
                    <p><strong>3. COMPENSATION</strong></p>
                    <p>Total project fee: R85,000.00 (including VAT)<br>
                    Payment schedule: 50% deposit, 50% on completion</p>
                    
                    <p><strong>4. DELIVERABLES</strong></p>
                    <p>• 12 fully produced tracks<br>
                    • High-quality WAV and MP3 files<br>
                    • Album artwork consultation</p>
                `
            };
            
        case 'rider':
            return {
                ...baseData,
                title: "TECHNICAL RIDER",
                number: "Rider #: RID-2025-001",
                date: "Date: January 27, 2025",
                toLabel: "Venue",
                content: `
                    <p><strong>AUDIO REQUIREMENTS</strong></p>
                    <p>• 32-channel digital mixing console<br>
                    • Full PA system (minimum 10kW)<br>
                    • 8x monitor wedges<br>
                    • Wireless microphone system (4 channels)</p>
                    
                    <p><strong>STAGE REQUIREMENTS</strong></p>
                    <p>• Minimum stage size: 8m x 6m<br>
                    • Power: 32A 3-phase supply<br>
                    • Loading access for equipment truck</p>
                    
                    <p><strong>PERSONNEL</strong></p>
                    <p>• 1x Sound Engineer (provided by venue)<br>
                    • 2x Stage hands for setup/breakdown</p>
                `,
                specialSection: {
                    title: "Setup Time",
                    content: "Minimum 4 hours setup time required before performance"
                }
            };
            
        case 'annexure':
            return {
                ...baseData,
                title: "ANNEXURE A - EQUIPMENT LIST",
                number: "Document #: ANX-2025-001",
                date: "Date: January 27, 2025",
                toLabel: "Related To",
                content: `
                    <p><strong>AUDIO EQUIPMENT</strong></p>
                    <p>• Yamaha CL5 Digital Console<br>
                    • 4x Shure SM58 Microphones<br>
                    • 2x Shure Beta 87A Microphones<br>
                    • 8x XLR Cables (10m each)</p>
                    
                    <p><strong>MONITORING EQUIPMENT</strong></p>
                    <p>• 4x Yamaha DXR12 Speakers<br>
                    • 2x Yamaha DXS15 Subwoofers<br>
                    • Monitor mixing console</p>
                    
                    <p><strong>ADDITIONAL ITEMS</strong></p>
                    <p>• Power distribution<br>
                    • Cable management<br>
                    • Backup equipment</p>
                `,
                specialSection: {
                    title: "Insurance",
                    content: "All equipment covered under comprehensive insurance policy #BMI-2025-EQUIP"
                }
            };
            
        default:
            return baseData;
    }
}

function startWithTemplate(type) {
    // Store selected template type
    localStorage.setItem('selectedDocumentType', type);
    closePreview();
    
    // Check if user is logged in
    const currentUser = localStorage.getItem('currentUser');
    if (currentUser) {
        window.location.href = 'dashboard2.html';
    } else {
        alert('Please sign in to use templates');
        showLogin();
    }
}

function showDemo() {
    alert('Demo mode: You can preview all document types below or sign <NAME_EMAIL> / demo123');
}

// Show signing demonstration
function showSigningDemo(documentType) {
    const signingModal = document.createElement('div');
    signingModal.id = 'signingDemoModal';
    signingModal.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background: rgba(0,0,0,0.8); z-index: 10000; display: flex;
        align-items: center; justify-content: center; padding: 20px;
    `;

    signingModal.innerHTML = `
        <div style="
            background: white; border-radius: 12px; padding: 30px; max-width: 600px; width: 100%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3); position: relative;
        ">
            <button onclick="closeSigning()" style="
                position: absolute; top: 15px; right: 20px; background: none; border: none;
                font-size: 24px; cursor: pointer; color: #666;
            ">×</button>

            <div style="text-align: center; margin-bottom: 30px;">
                <div style="font-size: 48px; margin-bottom: 15px;">📝</div>
                <h2 style="color: #2c3e50; margin: 0;">Digital Document Signing</h2>
                <p style="color: #666; margin: 10px 0 0 0;">Experience our advanced signature capabilities</p>
            </div>

            <div style="margin-bottom: 25px;">
                <h3 style="color: #2c3e50; margin-bottom: 15px;">✍️ Signature Options Available:</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                    <div style="padding: 15px; background: #f8f9fa; border-radius: 8px; text-align: center;">
                        <div style="font-size: 24px; margin-bottom: 8px;">🖊️</div>
                        <strong>Draw Signature</strong><br>
                        <small style="color: #666;">Use mouse or touch to draw</small>
                    </div>
                    <div style="padding: 15px; background: #f8f9fa; border-radius: 8px; text-align: center;">
                        <div style="font-size: 24px; margin-bottom: 8px;">⌨️</div>
                        <strong>Type Signature</strong><br>
                        <small style="color: #666;">Choose from elegant fonts</small>
                    </div>
                    <div style="padding: 15px; background: #f8f9fa; border-radius: 8px; text-align: center;">
                        <div style="font-size: 24px; margin-bottom: 8px;">📤</div>
                        <strong>Upload Image</strong><br>
                        <small style="color: #666;">Use existing signature</small>
                    </div>
                </div>
            </div>

            <div style="margin-bottom: 25px; padding: 20px; background: #e8f4fd; border-radius: 8px; border-left: 4px solid #007bff;">
                <h4 style="color: #2c3e50; margin-top: 0;">🔒 Security Features:</h4>
                <ul style="margin: 0; padding-left: 20px; color: #666;">
                    <li>Legally binding electronic signatures</li>
                    <li>Timestamp and IP address tracking</li>
                    <li>Document integrity verification</li>
                    <li>Audit trail for all signing activities</li>
                    <li>Secure cloud storage and backup</li>
                </ul>
            </div>

            <div style="margin-bottom: 25px;">
                <h4 style="color: #2c3e50; margin-bottom: 10px;">📋 ${documentType.charAt(0).toUpperCase() + documentType.slice(1)} Signing Process:</h4>
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                    <div style="text-align: center; flex: 1;">
                        <div style="width: 40px; height: 40px; background: #007bff; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 8px; font-weight: bold;">1</div>
                        <small>Review Document</small>
                    </div>
                    <div style="color: #007bff; font-size: 20px;">→</div>
                    <div style="text-align: center; flex: 1;">
                        <div style="width: 40px; height: 40px; background: #007bff; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 8px; font-weight: bold;">2</div>
                        <small>Add Signature</small>
                    </div>
                    <div style="color: #007bff; font-size: 20px;">→</div>
                    <div style="text-align: center; flex: 1;">
                        <div style="width: 40px; height: 40px; background: #007bff; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 8px; font-weight: bold;">3</div>
                        <small>Secure & Send</small>
                    </div>
                </div>
            </div>

            <div style="text-align: center;">
                <button onclick="startSigning('${documentType}')" style="
                    background: #007bff; color: white; border: none; padding: 12px 30px;
                    border-radius: 6px; font-size: 16px; font-weight: 600; cursor: pointer;
                    margin-right: 10px; transition: all 0.3s ease;
                " onmouseover="this.style.background='#0056b3'" onmouseout="this.style.background='#007bff'">
                    📝 Try Signing Demo
                </button>
                <button onclick="closeSigning()" style="
                    background: #6c757d; color: white; border: none; padding: 12px 30px;
                    border-radius: 6px; font-size: 16px; cursor: pointer; transition: all 0.3s ease;
                " onmouseover="this.style.background='#545b62'" onmouseout="this.style.background='#6c757d'">
                    Close
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(signingModal);
}

function closeSigning() {
    const modal = document.getElementById('signingDemoModal');
    if (modal) {
        modal.remove();
    }
}

function startSigning(documentType) {
    closeSigning();

    // Check if user is logged in
    const currentUser = localStorage.getItem('currentUser');
    if (currentUser) {
        // Store the document type and redirect to dashboard with signing intent
        localStorage.setItem('selectedDocumentType', documentType);
        localStorage.setItem('signingIntent', 'true');
        window.location.href = 'dashboard2.html';
    } else {
        alert('Please sign in to access the full signing <NAME_EMAIL> / demo123');
        showLogin();
    }
}

// Close modals when clicking outside
window.onclick = function(event) {
    const authModal = document.getElementById('authModal');
    const previewModal = document.getElementById('previewModal');
    
    if (event.target === authModal) {
        closeAuth();
    }
    if (event.target === previewModal) {
        closePreview();
    }
}

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Free Signing Tool Variables
let uploadedFile = null;
let signatureData = null;
let signatureCanvas = null;
let documentCanvas = null;
// placedSignatures is declared in simplified-pdf-signing.js
let isDraggingSignature = false;
let draggedSignature = null;
let documentZoom = 1;
let documentPanX = 0;
let documentPanY = 0;
let isPanning = false;
let lastPanX = 0;
let lastPanY = 0;

// Advanced signature session (matching your existing implementation)
let signatureSession = {
    isDrawing: false,
    lastX: 0,
    lastY: 0,
    lastVelocity: 0,
    lastTime: 0,
    currentStroke: [],
    allStrokes: [],
    sessionId: Date.now()
};

// Simple Free Signing Tool Functions
let currentPDF = null;
let signatureDataURL = null;
// placedSignatures already declared above
let pdfDoc = null;

function openSimpleSigningTool() {
    console.log('🚀 Opening simple signing tool...');
    const modal = document.getElementById('simpleSigningModal');
    if (modal) {
        modal.style.display = 'block';
        console.log('✅ Modal found and displayed');
        initializeSimpleSigningTool();
    } else {
        console.error('❌ Modal not found!');
        alert('Error: Modal not found! Please check the HTML structure.');
    }
}

function closeSimpleSigningTool() {
    console.log('❌ Closing simple signing tool...');
    document.getElementById('simpleSigningModal').style.display = 'none';
    resetSimpleSigningTool();
}

function initializeSimpleSigningTool() {
    console.log('🔧 Initializing simple signing tool...');

    // Reset state
    currentPDF = null;
    signatureDataURL = null;
    placedSignatures = [];
    pdfDoc = null;

    // Show upload step
    showStep('upload');

    // Initialize file upload
    const fileInput = document.getElementById('pdfUpload');
    const uploadZone = document.getElementById('uploadZone');

    console.log('📁 File input found:', !!fileInput);
    console.log('📤 Upload zone found:', !!uploadZone);

    if (fileInput) {
        fileInput.addEventListener('change', handlePDFUpload);
        console.log('✅ File input event listener added');
    } else {
        console.error('❌ File input not found!');
    }

    if (uploadZone) {
        uploadZone.addEventListener('click', () => fileInput.click());
        uploadZone.addEventListener('dragover', handleDragOver);
        uploadZone.addEventListener('drop', handlePDFDrop);
        console.log('✅ Upload zone event listeners added');
    } else {
        console.error('❌ Upload zone not found!');
    }

    // Initialize signature canvas
    initializeSignatureCanvas();

    console.log('✅ Simple signing tool initialized');
}

function resetSimpleSigningTool() {
    console.log('🔄 Resetting simple signing tool...');

    // Reset all variables
    currentPDF = null;
    signatureDataURL = null;
    placedSignatures = [];
    pdfDoc = null;

    // Reset UI
    showStep('upload');

    // Clear file input
    const fileInput = document.getElementById('pdfUpload');
    if (fileInput) fileInput.value = '';

    // Hide file info
    const fileInfo = document.getElementById('uploadedFileInfo');
    if (fileInfo) fileInfo.style.display = 'none';

    // Clear signature canvas
    clearSignature();

    // Update signature count
    updateSignatureCount();

    console.log('✅ Simple signing tool reset');
}

// Step management
function showStep(stepName) {
    console.log(`📋 Showing step: ${stepName}`);

    // Hide all steps
    const steps = ['uploadStep', 'signatureStep', 'placementStep'];
    steps.forEach(step => {
        const element = document.getElementById(step);
        if (element) {
            element.style.display = 'none';
            console.log(`✅ Hidden step: ${step}`);
        } else {
            console.error(`❌ Step not found: ${step}`);
        }
    });

    // Show requested step
    const targetStep = document.getElementById(stepName + 'Step');
    if (targetStep) {
        targetStep.style.display = 'block';
        console.log(`✅ Showing step: ${stepName}Step`);
    } else {
        console.error(`❌ Target step not found: ${stepName}Step`);
    }
}

// File upload handling
function handlePDFUpload(event) {
    const file = event.target.files[0];
    if (file && file.type === 'application/pdf') {
        processPDFFile(file);
    } else {
        alert('❌ Please select a valid PDF file.');
    }
}

function handleDragOver(event) {
    event.preventDefault();
    event.stopPropagation();
    event.currentTarget.style.borderColor = '#007bff';
    event.currentTarget.style.background = '#e3f2fd';
}

function handlePDFDrop(event) {
    event.preventDefault();
    event.stopPropagation();

    const uploadZone = event.currentTarget;
    uploadZone.style.borderColor = '#007bff';
    uploadZone.style.background = '#f8f9fa';

    const files = event.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (file.type === 'application/pdf') {
            processPDFFile(file);
        } else {
            alert('❌ Please drop a valid PDF file.');
        }
    }
}

function processPDFFile(file) {
    console.log('📄 Processing PDF file:', file.name);

    currentPDF = file;

    // Show file info
    const fileInfo = document.getElementById('uploadedFileInfo');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');

    if (fileName) fileName.textContent = file.name;
    if (fileSize) fileSize.textContent = `Size: ${(file.size / 1024 / 1024).toFixed(2)} MB`;
    if (fileInfo) fileInfo.style.display = 'block';

    console.log('✅ PDF file processed successfully');
}

function proceedToSigning() {
    if (!currentPDF) {
        alert('❌ Please upload a PDF file first.');
        return;
    }

    console.log('➡️ Proceeding to signature creation...');
    showStep('signature');
}

// Signature canvas handling
function initializeSignatureCanvas() {
    console.log('🎨 Initializing signature canvas...');

    const canvas = document.getElementById('signatureCanvas');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    let isDrawing = false;

    // Set canvas style
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // Mouse events
    canvas.addEventListener('mousedown', startDrawing);
    canvas.addEventListener('mousemove', draw);
    canvas.addEventListener('mouseup', stopDrawing);
    canvas.addEventListener('mouseout', stopDrawing);

    // Touch events for mobile
    canvas.addEventListener('touchstart', handleTouch);
    canvas.addEventListener('touchmove', handleTouch);
    canvas.addEventListener('touchend', stopDrawing);

    function startDrawing(e) {
        isDrawing = true;
        const rect = canvas.getBoundingClientRect();
        ctx.beginPath();
        ctx.moveTo(e.clientX - rect.left, e.clientY - rect.top);
    }

    function draw(e) {
        if (!isDrawing) return;
        const rect = canvas.getBoundingClientRect();
        ctx.lineTo(e.clientX - rect.left, e.clientY - rect.top);
        ctx.stroke();
    }

    function stopDrawing() {
        isDrawing = false;
    }

    function handleTouch(e) {
        e.preventDefault();
        const touch = e.touches[0];
        const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' :
                                        e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
            clientX: touch.clientX,
            clientY: touch.clientY
        });
        canvas.dispatchEvent(mouseEvent);
    }

    console.log('✅ Signature canvas initialized');
}

function clearSignature() {
    console.log('🧹 Clearing signature...');

    const canvas = document.getElementById('signatureCanvas');
    if (canvas) {
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
    }

    signatureDataURL = null;
}

function proceedToPlacement() {
    console.log('➡️ Proceeding to signature placement...');

    const canvas = document.getElementById('signatureCanvas');
    if (!canvas) {
        alert('❌ Signature canvas not found.');
        return;
    }

    // Check if signature is drawn
    const ctx = canvas.getContext('2d');
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const hasSignature = imageData.data.some(pixel => pixel !== 0);

    if (!hasSignature) {
        alert('❌ Please draw your signature first.');
        return;
    }

    // Save signature as data URL
    signatureDataURL = canvas.toDataURL('image/png');

    // Load and display PDF
    loadPDFForSigning();

    // Show placement step
    showStep('placement');
}

// PDF loading and display
async function loadPDFForSigning() {
    console.log('📖 Loading PDF for signing...');

    if (!currentPDF) {
        alert('❌ No PDF file selected.');
        return;
    }

    try {
        // Load PDF.js if not already loaded
        if (typeof pdfjsLib === 'undefined') {
            console.log('📚 Loading PDF.js library...');
            await loadPDFJS();
        }

        // Read file as array buffer
        const arrayBuffer = await currentPDF.arrayBuffer();

        // Load PDF document
        pdfDoc = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
        console.log(`📄 PDF loaded: ${pdfDoc.numPages} pages`);

        // Render first page (for simplicity, we'll only handle single page)
        await renderPDFPage(1);

        // Setup click handler for signature placement
        setupSignaturePlacement();

    } catch (error) {
        console.error('❌ Error loading PDF:', error);
        alert('❌ Error loading PDF. Please try again.');
    }
}

async function loadPDFJS() {
    return new Promise((resolve, reject) => {
        if (typeof pdfjsLib !== 'undefined') {
            resolve();
            return;
        }

        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js';
        script.onload = () => {
            pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
            resolve();
        };
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

async function renderPDFPage(pageNumber) {
    console.log(`🎨 Rendering PDF page ${pageNumber}...`);

    const page = await pdfDoc.getPage(pageNumber);
    const canvas = document.getElementById('documentCanvas');
    const ctx = canvas.getContext('2d');

    // Calculate scale to fit container
    const container = document.getElementById('documentViewer');
    const containerWidth = container.clientWidth - 40; // Account for padding
    const viewport = page.getViewport({ scale: 1 });
    const scale = Math.min(containerWidth / viewport.width, 1.5);

    const scaledViewport = page.getViewport({ scale });

    // Set canvas dimensions
    canvas.width = scaledViewport.width;
    canvas.height = scaledViewport.height;

    // Render page
    const renderContext = {
        canvasContext: ctx,
        viewport: scaledViewport
    };

    await page.render(renderContext).promise;
    console.log('✅ PDF page rendered successfully');
}

function setupSignaturePlacement() {
    console.log('🎯 Setting up signature placement...');

    const canvas = document.getElementById('documentCanvas');
    if (!canvas) return;

    canvas.addEventListener('click', placeSignatureAtPosition);

    // Update signature count
    updateSignatureCount();
}

function placeSignatureAtPosition(event) {
    console.log('📍 Placing signature at clicked position...');

    if (!signatureDataURL) {
        alert('❌ No signature available. Please create a signature first.');
        return;
    }

    const canvas = document.getElementById('documentCanvas');
    const rect = canvas.getBoundingClientRect();

    // Calculate click position relative to canvas
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Create signature image
    const img = new Image();
    img.onload = function() {
        const ctx = canvas.getContext('2d');

        // Signature dimensions (adjust as needed)
        const sigWidth = 120;
        const sigHeight = 40;

        // Draw signature at clicked position
        ctx.drawImage(img, x - sigWidth/2, y - sigHeight/2, sigWidth, sigHeight);

        // Store signature info
        placedSignatures.push({
            x: x - sigWidth/2,
            y: y - sigHeight/2,
            width: sigWidth,
            height: sigHeight,
            dataURL: signatureDataURL
        });

        updateSignatureCount();
        console.log(`✅ Signature placed at (${x}, ${y})`);
    };

    img.src = signatureDataURL;
}

function updateSignatureCount() {
    const countElement = document.getElementById('signatureCount');
    if (countElement) {
        countElement.textContent = placedSignatures.length;
    }
}

function backToSignature() {
    console.log('⬅️ Going back to signature creation...');
    showStep('signature');
}

function startOver() {
    console.log('🔄 Starting over...');
    resetSimpleSigningTool();
}

// Download signed PDF
async function downloadSignedPDF() {
    console.log('💾 Downloading signed PDF...');

    if (placedSignatures.length === 0) {
        alert('❌ Please place at least one signature on the document before downloading.');
        return;
    }

    try {
        // Load jsPDF if not already loaded
        if (typeof jsPDF === 'undefined') {
            console.log('📚 Loading jsPDF library...');
            await loadJsPDF();
        }

        // Get the canvas with signatures
        const canvas = document.getElementById('documentCanvas');
        const canvasDataURL = canvas.toDataURL('image/jpeg', 0.95);

        // Create new PDF
        const pdf = new jsPDF({
            orientation: canvas.width > canvas.height ? 'landscape' : 'portrait',
            unit: 'px',
            format: [canvas.width, canvas.height]
        });

        // Add the signed document as image
        pdf.addImage(canvasDataURL, 'JPEG', 0, 0, canvas.width, canvas.height);

        // Generate filename
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const originalName = currentPDF.name.replace('.pdf', '');
        const filename = `${originalName}_signed_${timestamp}.pdf`;

        // Download the PDF
        pdf.save(filename);

        // Show success message
        setTimeout(() => {
            alert(`✅ Signed PDF downloaded successfully!\n\nFilename: ${filename}\n\nThank you for using our free signing tool! 🎉`);
        }, 500);

        console.log(`✅ Signed PDF downloaded: ${filename}`);

    } catch (error) {
        console.error('❌ Error creating signed PDF:', error);
        alert('❌ Error creating signed PDF. Please try again.');
    }
}

async function loadJsPDF() {
    return new Promise((resolve, reject) => {
        if (typeof jsPDF !== 'undefined') {
            resolve();
            return;
        }

        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
        script.onload = () => {
            // jsPDF is available as window.jspdf.jsPDF
            window.jsPDF = window.jspdf.jsPDF;
            resolve();
        };
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

// Legacy function compatibility (in case old buttons still call this)
function openFreeSigningTool() {
    console.log('🔄 Redirecting to new simple signing tool...');
    openSimpleSigningTool();
}

function closeFreeSigningTool() {
    console.log('🔄 Redirecting to close simple signing tool...');
    closeSimpleSigningTool();
}

// All old complex signing tool code has been removed to prevent conflicts with the new simple tool
// Large chunk of old complex signing tool code removed
// Massive chunk of old complex signing tool code removed (1200+ lines)
// Another large chunk of old complex signing tool code removed



function proceedToSigning() {
    if (!uploadedFile) {
        alert('Please upload a document first.');
        return;
    }

    showStep(2);

    // Initialize signature canvas and document viewer
    setTimeout(() => {
        initializeSignatureCanvas();
        initializeDocumentViewer();
    }, 100);
}

// Document zoom and pan functionality
function initializeDocumentViewer() {
    const documentDisplay = document.getElementById('documentDisplay');
    const documentContainer = document.getElementById('documentContainer');

    if (!documentDisplay || !documentContainer) return;

    // Initialize zoom and pan
    documentZoom = 1;
    documentPanX = 0;
    documentPanY = 0;
    updateDocumentTransform();

    // Mouse wheel zoom
    documentDisplay.addEventListener('wheel', (e) => {
        e.preventDefault();

        const rect = documentDisplay.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
        const newZoom = Math.max(0.25, Math.min(3, documentZoom * zoomFactor));

        if (newZoom !== documentZoom) {
            // Zoom towards mouse position
            const zoomRatio = newZoom / documentZoom;
            documentPanX = mouseX - (mouseX - documentPanX) * zoomRatio;
            documentPanY = mouseY - (mouseY - documentPanY) * zoomRatio;
            documentZoom = newZoom;

            updateDocumentTransform();
            updateZoomDisplay();
        }
    });

    // Pan functionality
    let isPanning = false;
    let lastPanX = 0;
    let lastPanY = 0;

    documentDisplay.addEventListener('mousedown', (e) => {
        if (e.target === documentDisplay || e.target === documentContainer) {
            isPanning = true;
            lastPanX = e.clientX;
            lastPanY = e.clientY;
            documentDisplay.style.cursor = 'grabbing';
        }
    });

    documentDisplay.addEventListener('mousemove', (e) => {
        if (isPanning) {
            const deltaX = e.clientX - lastPanX;
            const deltaY = e.clientY - lastPanY;

            documentPanX += deltaX;
            documentPanY += deltaY;

            updateDocumentTransform();

            lastPanX = e.clientX;
            lastPanY = e.clientY;
        }
    });

    documentDisplay.addEventListener('mouseup', () => {
        isPanning = false;
        documentDisplay.style.cursor = 'grab';
    });

    documentDisplay.addEventListener('mouseleave', () => {
        isPanning = false;
        documentDisplay.style.cursor = 'grab';
    });

    // Touch support for mobile
    let lastTouchDistance = 0;
    let lastTouchX = 0;
    let lastTouchY = 0;

    documentDisplay.addEventListener('touchstart', (e) => {
        if (e.touches.length === 1) {
            // Single touch - pan
            isPanning = true;
            lastPanX = e.touches[0].clientX;
            lastPanY = e.touches[0].clientY;
        } else if (e.touches.length === 2) {
            // Two touches - zoom
            isPanning = false;
            const touch1 = e.touches[0];
            const touch2 = e.touches[1];
            lastTouchDistance = Math.sqrt(
                Math.pow(touch2.clientX - touch1.clientX, 2) +
                Math.pow(touch2.clientY - touch1.clientY, 2)
            );
            lastTouchX = (touch1.clientX + touch2.clientX) / 2;
            lastTouchY = (touch1.clientY + touch2.clientY) / 2;
        }
        e.preventDefault();
    });

    documentDisplay.addEventListener('touchmove', (e) => {
        if (e.touches.length === 1 && isPanning) {
            // Pan
            const deltaX = e.touches[0].clientX - lastPanX;
            const deltaY = e.touches[0].clientY - lastPanY;

            documentPanX += deltaX;
            documentPanY += deltaY;

            updateDocumentTransform();

            lastPanX = e.touches[0].clientX;
            lastPanY = e.touches[0].clientY;
        } else if (e.touches.length === 2) {
            // Zoom
            const touch1 = e.touches[0];
            const touch2 = e.touches[1];
            const currentDistance = Math.sqrt(
                Math.pow(touch2.clientX - touch1.clientX, 2) +
                Math.pow(touch2.clientY - touch1.clientY, 2)
            );

            if (lastTouchDistance > 0) {
                const zoomFactor = currentDistance / lastTouchDistance;
                const newZoom = Math.max(0.25, Math.min(3, documentZoom * zoomFactor));

                if (newZoom !== documentZoom) {
                    const rect = documentDisplay.getBoundingClientRect();
                    const centerX = lastTouchX - rect.left;
                    const centerY = lastTouchY - rect.top;

                    const zoomRatio = newZoom / documentZoom;
                    documentPanX = centerX - (centerX - documentPanX) * zoomRatio;
                    documentPanY = centerY - (centerY - documentPanY) * zoomRatio;
                    documentZoom = newZoom;

                    updateDocumentTransform();
                    updateZoomDisplay();
                }
            }

            lastTouchDistance = currentDistance;
        }
        e.preventDefault();
    });

    documentDisplay.addEventListener('touchend', () => {
        isPanning = false;
        lastTouchDistance = 0;
    });
}

function updateDocumentTransform() {
    const documentContainer = document.getElementById('documentContainer');
    if (documentContainer) {
        documentContainer.style.transform = `translate(${documentPanX}px, ${documentPanY}px) scale(${documentZoom})`;
    }
}

function updateZoomDisplay() {
    const zoomLevel = document.getElementById('zoomLevel');
    if (zoomLevel) {
        zoomLevel.textContent = Math.round(documentZoom * 100) + '%';
    }
}

function zoomIn() {
    const newZoom = Math.min(3, documentZoom * 1.2);
    if (newZoom !== documentZoom) {
        documentZoom = newZoom;
        updateDocumentTransform();
        updateZoomDisplay();
    }
}

function zoomOut() {
    const newZoom = Math.max(0.25, documentZoom * 0.8);
    if (newZoom !== documentZoom) {
        documentZoom = newZoom;
        updateDocumentTransform();
        updateZoomDisplay();
    }
}

function resetZoom() {
    documentZoom = 1;
    documentPanX = 0;
    documentPanY = 0;
    updateDocumentTransform();
    updateZoomDisplay();
}

function backToUpload() {
    showStep(1);
}

// Old duplicate initializeSignatureCanvas function removed

// Duplicate drawing functions removed - using the ones inside initializeSignatureCanvas

// Old duplicate clearSignature function removed

// Drag and drop functionality for signatures
let isDragMode = false;
let draggedSignatureData = null;

function enableSignatureDrag() {
    if (!signatureCanvas || !signatureSession || signatureSession.allStrokes.length === 0) {
        alert('Please draw your signature first.');
        return;
    }

    isDragMode = !isDragMode;
    const canvas = signatureCanvas;
    const dragHint = document.getElementById('dragHint');
    const dragBtn = document.getElementById('dragModeBtn');

    if (isDragMode) {
        canvas.classList.add('drag-mode');
        canvas.style.cursor = 'grab';
        dragHint.style.display = 'block';
        dragBtn.textContent = '✏️ Draw Mode';
        dragBtn.classList.add('active');

        // Disable drawing events
        canvas.style.pointerEvents = 'none';

        // Enable drag events
        setTimeout(() => {
            canvas.style.pointerEvents = 'all';
            initializeSignatureDrag();
        }, 100);

        console.log('🖱️ Drag mode enabled');
    } else {
        canvas.classList.remove('drag-mode');
        canvas.style.cursor = 'crosshair';
        dragHint.style.display = 'none';
        dragBtn.textContent = '🖱️ Drag Mode';
        dragBtn.classList.remove('active');

        // Re-enable drawing
        canvas.style.pointerEvents = 'all';

        console.log('✏️ Draw mode enabled');
    }
}

function initializeSignatureDrag() {
    const canvas = signatureCanvas;

    canvas.addEventListener('mousedown', startSignatureDrag);
    canvas.addEventListener('touchstart', startSignatureDrag);
}

function startSignatureDrag(e) {
    if (!isDragMode) return;

    e.preventDefault();

    // Capture signature data
    draggedSignatureData = canvas.toDataURL('image/png');

    canvas.classList.add('dragging');

    // Create drag preview
    const dragPreview = document.createElement('div');
    dragPreview.id = 'dragPreview';
    dragPreview.style.cssText = `
        position: fixed;
        pointer-events: none;
        z-index: 1000;
        width: 150px;
        height: 75px;
        background: white;
        border: 2px solid #007bff;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        opacity: 0.8;
    `;

    const img = document.createElement('img');
    img.src = draggedSignatureData;
    img.style.cssText = 'width: 100%; height: 100%; object-fit: contain;';
    dragPreview.appendChild(img);

    document.body.appendChild(dragPreview);

    // Position drag preview
    const clientX = e.clientX || (e.touches && e.touches[0].clientX);
    const clientY = e.clientY || (e.touches && e.touches[0].clientY);

    dragPreview.style.left = (clientX - 75) + 'px';
    dragPreview.style.top = (clientY - 37.5) + 'px';

    // Mouse/touch move events
    function dragMove(e) {
        const clientX = e.clientX || (e.touches && e.touches[0].clientX);
        const clientY = e.clientY || (e.touches && e.touches[0].clientY);

        dragPreview.style.left = (clientX - 75) + 'px';
        dragPreview.style.top = (clientY - 37.5) + 'px';

        // Check if over document
        const documentDisplay = document.getElementById('documentDisplay');
        const rect = documentDisplay.getBoundingClientRect();

        if (clientX >= rect.left && clientX <= rect.right &&
            clientY >= rect.top && clientY <= rect.bottom) {
            dragPreview.style.borderColor = '#28a745';
            documentDisplay.style.background = '#e8f5e8';
        } else {
            dragPreview.style.borderColor = '#007bff';
            documentDisplay.style.background = '#f8f9fa';
        }
    }

    function dragEnd(e) {
        const clientX = e.clientX || (e.changedTouches && e.changedTouches[0].clientX);
        const clientY = e.clientY || (e.changedTouches && e.changedTouches[0].clientY);

        // Check if dropped on document
        const documentDisplay = document.getElementById('documentDisplay');
        const documentContainer = document.getElementById('documentContainer');
        const rect = documentDisplay.getBoundingClientRect();

        if (clientX >= rect.left && clientX <= rect.right &&
            clientY >= rect.top && clientY <= rect.bottom) {

            // Calculate position relative to document
            const containerRect = documentContainer.getBoundingClientRect();
            const relativeX = (clientX - containerRect.left) / documentZoom;
            const relativeY = (clientY - containerRect.top) / documentZoom;

            // Add signature to document at drop position
            const signature = {
                id: Date.now(),
                data: draggedSignatureData,
                type: 'canvas',
                font: null,
                x: Math.max(0, relativeX - 75),
                y: Math.max(0, relativeY - 37.5),
                width: 150,
                height: 75
            };

            placedSignatures.push(signature);
            renderSignatureOnDocument(signature);

            console.log('📍 Signature dropped at:', signature.x, signature.y);

            // Clear signature canvas and exit drag mode
            clearSignature();
            enableSignatureDrag(); // Toggle off drag mode
        }

        // Cleanup
        canvas.classList.remove('dragging');
        documentDisplay.style.background = '#f8f9fa';

        if (dragPreview) {
            document.body.removeChild(dragPreview);
        }

        document.removeEventListener('mousemove', dragMove);
        document.removeEventListener('mouseup', dragEnd);
        document.removeEventListener('touchmove', dragMove);
        document.removeEventListener('touchend', dragEnd);

        draggedSignatureData = null;
    }

    document.addEventListener('mousemove', dragMove);
    document.addEventListener('mouseup', dragEnd);
    document.addEventListener('touchmove', dragMove);
    document.addEventListener('touchend', dragEnd);
}

// Add signature to document functions
function addSignatureToDocument() {
    if (!signatureCanvas || !signatureSession || signatureSession.allStrokes.length === 0) {
        alert('Please draw your signature first.');
        return;
    }

    console.log('📝 Adding signature with', signatureSession.allStrokes.length, 'strokes to document');

    const signatureData = signatureCanvas.toDataURL('image/png');
    addSignatureToDocumentCanvas(signatureData, 'canvas');
    clearSignature();
}

function addTypedSignatureToDocument() {
    const text = document.getElementById('typedSignature').value.trim();
    if (!text) {
        alert('Please type your signature first.');
        return;
    }

    const font = document.getElementById('signatureFont').value;
    addSignatureToDocumentCanvas(text, 'text', font);
    document.getElementById('typedSignature').value = '';
    updateTypedPreview();
}

function addUploadedSignatureToDocument() {
    const uploadInput = document.getElementById('signatureUpload');
    if (!uploadInput.files[0]) {
        alert('Please upload a signature image first.');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        addSignatureToDocumentCanvas(e.target.result, 'image');
        uploadInput.value = '';
        document.getElementById('uploadedSignaturePreview').innerHTML = '';
        document.getElementById('addUploadedBtn').style.display = 'none';
    };
    reader.readAsDataURL(uploadInput.files[0]);
}

function addSignatureToDocumentCanvas(signatureData, type, font = null) {
    const signature = {
        id: Date.now(),
        data: signatureData,
        type: type,
        font: font,
        x: 100,
        y: 400,
        width: type === 'text' ? 200 : 150,
        height: type === 'text' ? 50 : 75
    };

    placedSignatures.push(signature);
    renderSignatureOnDocument(signature);
}

function renderSignatureOnDocument(signature) {
    const dropZones = document.getElementById('signatureDropZones');
    const signatureElement = document.createElement('div');
    signatureElement.className = 'signature-on-document';
    signatureElement.id = `signature-${signature.id}`;
    signatureElement.style.left = signature.x + 'px';
    signatureElement.style.top = signature.y + 'px';
    signatureElement.style.width = signature.width + 'px';
    signatureElement.style.height = signature.height + 'px';

    if (signature.type === 'text') {
        signatureElement.innerHTML = `<div class="signature-text" style="font-family: ${signature.font};">${signature.data}</div>`;
    } else {
        signatureElement.innerHTML = `<img src="${signature.data}" alt="Signature">`;
    }

    // Add drag functionality
    signatureElement.addEventListener('mousedown', startDragSignature);
    signatureElement.addEventListener('dblclick', () => removeSignature(signature.id));

    // Add tooltip
    signatureElement.title = 'Drag to move • Double-click to remove';

    dropZones.appendChild(signatureElement);

    console.log('✅ Signature rendered on document at:', signature.x, signature.y);
}

function startDragSignature(e) {
    e.preventDefault();
    e.stopPropagation();

    isDraggingSignature = true;
    draggedSignature = e.currentTarget;
    draggedSignature.classList.add('dragging');

    const rect = draggedSignature.getBoundingClientRect();
    const containerRect = document.getElementById('documentContainer').getBoundingClientRect();

    const offsetX = (e.clientX - rect.left) / documentZoom;
    const offsetY = (e.clientY - rect.top) / documentZoom;

    function dragSignature(e) {
        if (!isDraggingSignature) return;

        // Calculate position relative to document container with zoom
        const newX = (e.clientX - containerRect.left) / documentZoom - offsetX;
        const newY = (e.clientY - containerRect.top) / documentZoom - offsetY;

        // Get document canvas bounds
        const canvas = document.getElementById('documentCanvas');
        const maxX = canvas.width - draggedSignature.offsetWidth / documentZoom;
        const maxY = canvas.height - draggedSignature.offsetHeight / documentZoom;

        const clampedX = Math.max(0, Math.min(newX, maxX));
        const clampedY = Math.max(0, Math.min(newY, maxY));

        draggedSignature.style.left = clampedX + 'px';
        draggedSignature.style.top = clampedY + 'px';

        // Update signature data
        const signatureId = parseInt(draggedSignature.id.replace('signature-', ''));
        const signature = placedSignatures.find(s => s.id === signatureId);
        if (signature) {
            signature.x = clampedX;
            signature.y = clampedY;
        }
    }

    function stopDragSignature() {
        isDraggingSignature = false;
        if (draggedSignature) {
            draggedSignature.classList.remove('dragging');
            console.log('📍 Signature moved to:', draggedSignature.style.left, draggedSignature.style.top);
            draggedSignature = null;
        }
        document.removeEventListener('mousemove', dragSignature);
        document.removeEventListener('mouseup', stopDragSignature);
        document.removeEventListener('touchmove', dragSignature);
        document.removeEventListener('touchend', stopDragSignature);
    }

    document.addEventListener('mousemove', dragSignature);
    document.addEventListener('mouseup', stopDragSignature);

    // Touch support
    document.addEventListener('touchmove', (e) => {
        if (e.touches[0]) {
            const touchEvent = {
                clientX: e.touches[0].clientX,
                clientY: e.touches[0].clientY
            };
            dragSignature(touchEvent);
        }
    });
    document.addEventListener('touchend', stopDragSignature);
}

function removeSignature(signatureId) {
    if (confirm('Remove this signature?')) {
        placedSignatures = placedSignatures.filter(s => s.id !== signatureId);
        const element = document.getElementById(`signature-${signatureId}`);
        if (element) {
            element.remove();
        }
    }
}

function switchSignatureTab(tabName) {
    // Hide all tabs
    document.querySelectorAll('.signature-tab').forEach(tab => {
        tab.style.display = 'none';
    });

    // Remove active class from all buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab
    document.getElementById(tabName + 'Tab').style.display = 'block';

    // Add active class to selected button
    event.target.classList.add('active');

    // Initialize specific tab functionality
    if (tabName === 'type') {
        initializeTypedSignature();
    } else if (tabName === 'upload') {
        initializeUploadSignature();
    }
}

function initializeTypedSignature() {
    const input = document.getElementById('typedSignature');
    const preview = document.getElementById('typedPreview');
    const fontSelector = document.getElementById('signatureFont');

    function updatePreview() {
        const text = input.value;
        const font = fontSelector.value;
        preview.style.fontFamily = font;
        preview.style.fontSize = '32px';
        preview.style.color = text ? '#2c3e50' : '#999';
        preview.textContent = text || 'Your signature will appear here';
    }

    input.addEventListener('input', updatePreview);
    fontSelector.addEventListener('change', updatePreview);

    updatePreview();
}

function updateTypedPreview() {
    const input = document.getElementById('typedSignature');
    const preview = document.getElementById('typedPreview');
    const fontSelector = document.getElementById('signatureFont');

    const text = input.value;
    const font = fontSelector.value;
    preview.style.fontFamily = font;
    preview.style.fontSize = '32px';
    preview.style.color = text ? '#2c3e50' : '#999';
    preview.textContent = text || 'Your signature will appear here';
}

function initializeUploadSignature() {
    const uploadInput = document.getElementById('signatureUpload');
    const preview = document.getElementById('uploadedSignaturePreview');
    const addBtn = document.getElementById('addUploadedBtn');

    uploadInput.addEventListener('change', function(event) {
        const file = event.target.files[0];
        if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.innerHTML = `<img src="${e.target.result}" style="max-width: 300px; max-height: 100px; border: 2px solid #e9ecef; border-radius: 8px;">`;
                addBtn.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else if (file) {
            alert('Please upload an image file (JPG, PNG).');
            uploadInput.value = '';
        }
    });
}

function proceedToExport() {
    if (placedSignatures.length === 0) {
        alert('Please add at least one signature to the document before proceeding.');
        return;
    }

    showStep(3);
}

function downloadSignedDocument() {
    if (!uploadedFile || placedSignatures.length === 0) {
        alert('Please add at least one signature to the document.');
        return;
    }

    try {
        // Load jsPDF library if not already loaded
        if (typeof window.jspdf === 'undefined') {
            // Load jsPDF dynamically
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
            script.onload = () => {
                generateSignedPDF();
            };
            script.onerror = () => {
                alert('Error loading PDF library. Please try again.');
            };
            document.head.appendChild(script);
        } else {
            generateSignedPDF();
        }

    } catch (error) {
        console.error('Error creating signed document:', error);
        alert('Error creating signed document. Please try again.');
    }
}

function generateSignedPDF() {
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Add document header
    doc.setFontSize(20);
    doc.setFont('helvetica', 'bold');
    doc.text('Signed Document', 20, 30);

    // Add document info
    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.text(`Original file: ${uploadedFile.name}`, 20, 50);
    doc.text(`Signed on: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`, 20, 65);
    doc.text(`Number of signatures: ${placedSignatures.length}`, 20, 80);

    // Add original document if it's an image
    if (documentCanvas && uploadedFile.type.startsWith('image/')) {
        try {
            const documentImage = documentCanvas.toDataURL('image/jpeg', 0.8);
            const canvasAspectRatio = documentCanvas.width / documentCanvas.height;

            // Calculate dimensions to fit on page
            const maxWidth = 170; // Leave margins
            const maxHeight = 200;

            let imgWidth = maxWidth;
            let imgHeight = maxWidth / canvasAspectRatio;

            if (imgHeight > maxHeight) {
                imgHeight = maxHeight;
                imgWidth = maxHeight * canvasAspectRatio;
            }

            doc.addImage(documentImage, 'JPEG', 20, 100, imgWidth, imgHeight);

            // Add signatures on top of the document image
            placedSignatures.forEach((signature, index) => {
                const scaleX = imgWidth / documentCanvas.width;
                const scaleY = imgHeight / documentCanvas.height;

                const signatureX = 20 + (signature.x * scaleX);
                const signatureY = 100 + (signature.y * scaleY);
                const signatureWidth = signature.width * scaleX;
                const signatureHeight = signature.height * scaleY;

                if (signature.type === 'text') {
                    doc.setFont(signature.font || 'helvetica');
                    doc.setFontSize(Math.max(8, signatureHeight / 3));
                    doc.text(signature.data, signatureX, signatureY + signatureHeight / 2);
                } else {
                    // For canvas and image signatures
                    doc.addImage(signature.data, 'PNG', signatureX, signatureY, signatureWidth, signatureHeight);
                }
            });

        } catch (error) {
            console.error('Error adding document image:', error);
        }
    } else {
        // For non-image documents, create a representation
        doc.setFontSize(14);
        doc.setFont('helvetica', 'bold');
        doc.text('Document Content:', 20, 110);

        doc.setFontSize(12);
        doc.setFont('helvetica', 'normal');
        doc.text('This document has been digitally signed.', 20, 130);
        doc.text('Original document type: ' + (uploadedFile.type || 'Unknown'), 20, 145);

        // Add signatures
        let yPosition = 170;
        placedSignatures.forEach((signature, index) => {
            doc.setFontSize(12);
            doc.setFont('helvetica', 'bold');
            doc.text(`Signature ${index + 1}:`, 20, yPosition);

            if (signature.type === 'text') {
                doc.setFont(signature.font || 'helvetica');
                doc.setFontSize(16);
                doc.text(signature.data, 20, yPosition + 15);
                yPosition += 35;
            } else {
                try {
                    doc.addImage(signature.data, 'PNG', 20, yPosition + 5, 80, 25);
                    yPosition += 40;
                } catch (error) {
                    doc.text('[Signature Image]', 20, yPosition + 15);
                    yPosition += 25;
                }
            }

            // Add new page if needed
            if (yPosition > 250 && index < placedSignatures.length - 1) {
                doc.addPage();
                yPosition = 30;
            }
        });
    }

    // Add footer with disclaimer
    const pageCount = doc.internal.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(8);
        doc.setFont('helvetica', 'italic');
        doc.text('This document was signed using DocuGen Pro Free Signing Tool.', 20, 280);
        doc.text('For legal documents, please use a certified e-signature service.', 20, 290);
        doc.text(`Page ${i} of ${pageCount}`, 180, 290);
    }

    // Generate filename
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const filename = `signed_${uploadedFile.name.replace(/\.[^/.]+$/, '')}_${timestamp}.pdf`;

    // Download the PDF
    doc.save(filename);

    // Show success message
    setTimeout(() => {
        alert('✅ Signed document downloaded successfully!\n\nThank you for trying our free signing tool.');
    }, 500);
}

function startOver() {
    resetSigningTool();
}

// Donate Modal Functions
function openDonateModal() {
    document.getElementById('donateModal').style.display = 'block';
}

function closeDonateModal() {
    document.getElementById('donateModal').style.display = 'none';
}

function donateAmount(amount) {
    // In a real implementation, this would integrate with PayPal
    alert(`Thank you for wanting to donate $${amount}! PayPal integration will be added with your PayPal link.`);

    // For now, just show a thank you message
    showDonateThankYou(amount);
}

function donateCustomAmount() {
    const amount = document.getElementById('customAmount').value;
    if (!amount || amount < 1) {
        alert('Please enter a valid amount.');
        return;
    }

    donateAmount(amount);
}

function showDonateThankYou(amount) {
    const modal = document.getElementById('donateModal');
    const content = modal.querySelector('.donate-section');

    content.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <div style="font-size: 64px; margin-bottom: 20px;">🙏</div>
            <h2 style="color: #28a745; margin-bottom: 15px;">Thank You!</h2>
            <p style="font-size: 1.2rem; margin-bottom: 20px;">Your support means the world to us!</p>
            <p style="color: #666;">Your donation of $${amount} will help us keep DocuGen Pro free and improve it for everyone.</p>
            <button onclick="closeDonateModal()" class="btn-primary" style="margin-top: 20px;">Close</button>
        </div>
    `;
}

// Free Signing Tool Signature Integration
function initializeFreeSigningSignatures() {
    console.log('🔧 Initializing free signing signature integration...');
    loadSavedSignaturesForFreeTool();
}

function loadSavedSignaturesForFreeTool() {
    const savedSignaturesSection = document.getElementById('savedSignaturesSection');
    const savedSignaturesGrid = document.getElementById('savedSignaturesGrid');

    if (!savedSignaturesSection || !savedSignaturesGrid) {
        console.log('📝 Free signing signature elements not found');
        return;
    }

    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
    console.log('📝 Loading', signatures.length, 'signatures for free tool');

    if (signatures.length === 0) {
        savedSignaturesSection.style.display = 'none';
        return;
    }

    // Show the section
    savedSignaturesSection.style.display = 'block';

    // Clear existing signatures
    savedSignaturesGrid.innerHTML = '';

    // Add signature items
    signatures.forEach((signature, index) => {
        const signatureItem = document.createElement('div');
        signatureItem.className = 'saved-signature-item';
        signatureItem.draggable = true;
        signatureItem.dataset.signatureIndex = index;
        signatureItem.title = `Click to select or drag to document: ${signature.name}`;

        signatureItem.innerHTML = `
            <img src="${signature.data}" alt="${signature.name}">
            <div class="signature-name">${signature.name}</div>
            <div class="drag-indicator">🖱️</div>
        `;

        // Add click handler
        signatureItem.addEventListener('click', () => {
            selectSavedSignatureForFree(index);
        });

        // Add drag handlers
        signatureItem.addEventListener('dragstart', (e) => {
            e.dataTransfer.setData('text/plain', JSON.stringify({
                type: 'saved-signature',
                index: index,
                data: signature.data,
                name: signature.name
            }));
            signatureItem.style.opacity = '0.5';
        });

        signatureItem.addEventListener('dragend', () => {
            signatureItem.style.opacity = '1';
        });

        savedSignaturesGrid.appendChild(signatureItem);
    });

    console.log('✅ Loaded', signatures.length, 'signatures for free tool');
}

function selectSavedSignatureForFree(index) {
    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
    const signature = signatures[index];

    if (!signature) return;

    console.log('✅ Selected saved signature for free tool:', signature.name);

    // Store selected signature
    window.selectedFreeSignature = signature;

    // Update UI to show selection
    const signatureItems = document.querySelectorAll('.saved-signature-item');
    signatureItems.forEach((item, i) => {
        if (i === index) {
            item.classList.add('selected');
        } else {
            item.classList.remove('selected');
        }
    });

    // Show notification
    showNotification(`✅ Signature "${signature.name}" selected!\n\nClick on the document to place it.`, 'success');
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : '#007bff'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        max-width: 300px;
        font-size: 14px;
        line-height: 1.4;
        white-space: pre-line;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            document.body.removeChild(notification);
        }
    }, 3000);
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('Landing page loaded');

    // Initialize Google Auth if available
    if (window.googleAuth) {
        console.log('🔐 Initializing Google Auth in landing page...');
        googleAuth.initializeGoogleSignIn();
    }

    // Initialize freemium system if available
    if (window.freemiumSystem) {
        console.log('🪙 Initializing freemium system in landing page...');
        freemiumSystem.updateTokenDisplay();
    }

    // Check if user is already logged in
    const currentUser = localStorage.getItem('currentUser');
    if (currentUser) {
        const user = JSON.parse(currentUser);
        console.log('User already logged in:', user.name);

        // Update freemium system with current user
        if (window.freemiumSystem) {
            freemiumSystem.currentUser = user;
            freemiumSystem.loadUserTokens();
            freemiumSystem.updateTokenDisplay();
        }
    }

    // Initialize free signing signature integration
    // initializeFreeSigningSignatures(); // Temporarily commented out for debugging
});

// Modal Functions
function openHelpModal() {
    document.getElementById('helpModal').style.display = 'block';
}

function closeHelpModal() {
    document.getElementById('helpModal').style.display = 'none';
}

function openAboutModal() {
    document.getElementById('aboutModal').style.display = 'block';
}

function closeAboutModal() {
    document.getElementById('aboutModal').style.display = 'none';
}

function openPrivacyModal() {
    document.getElementById('privacyModal').style.display = 'block';
}

function closePrivacyModal() {
    document.getElementById('privacyModal').style.display = 'none';
}

function openTermsModal() {
    document.getElementById('termsModal').style.display = 'block';
}

function closeTermsModal() {
    document.getElementById('termsModal').style.display = 'none';
}

// Simple signing tool functions
function closeSimpleSigningTool() {
    document.getElementById('simpleSigningModal').style.display = 'none';
}

function proceedToSigning() {
    console.log('🔄 Proceeding to signing step...');
    showStep('signature');
}

function clearSignature() {
    console.log('🧹 Clearing signature...');
    const canvas = document.getElementById('signatureCanvas');
    if (canvas) {
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
    }
}

function proceedToPlacement() {
    console.log('🔄 Proceeding to placement step...');
    showStep('placement');
}

function startOver() {
    console.log('🔄 Starting over...');
    showStep('upload');
    // Reset all data
    currentPDF = null;
    signatureDataURL = null;
    placedSignatures = [];
}

console.log('✅ Landing script loaded successfully');
