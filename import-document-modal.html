<!--
  Import Document Modal - Standalone Component
  This file contains the HTML structure and all related JavaScript for the Import Document modal and its functionalities.
  Copy-pasted and adapted from dashboard2.html.
-->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Import Document Modal - DocuGen Pro</title>
  <link rel="stylesheet" href="dashboard-styles.css">
  <style>
    /* Inline styles for overlays and modal */
    #documentPreviewContainer {
      position: relative !important;
      min-height: 400px;
      overflow: visible;
    }
    .draggable-overlay {
      z-index: 10;
      pointer-events: auto;
      box-shadow: 0 2px 8px rgba(0,0,0,0.08);
      user-select: none;
    }
    .draggable-overlay.signature-overlay {
      background: transparent;
    }
    .draggable-overlay.text-overlay {
      background: rgba(255,255,255,0.7);
      border: 1px solid #ddd;
    }
  </style>
</head>
<body>
  <!-- Document Import Modal -->
  <div id="documentImportModal" class="modal" style="display: block;">
    <div class="modal-content document-import-content">
      <div class="modal-header">
        <h2>📥 Import Document</h2>
        <span class="close" onclick="closeDocumentImport()">&times;</span>
      </div>
      <div class="document-import-body">
        <div class="import-layout">
          <!-- Left Panel: Upload and Tools -->
          <div class="import-left-panel">
            <div class="import-section">
              <h3>📄 Upload Document</h3>
              <div class="upload-area" id="documentUploadArea">
                <label for="documentUpload" style="display: none;">Upload Document for Import</label>
                <input type="file" id="documentUpload" name="documentUpload" autocomplete="off" accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif,.bmp,image/*,application/pdf,text/plain" style="display: none;">
                <div class="upload-placeholder" onclick="document.getElementById('documentUpload').click()">
                  <div class="upload-icon">📁</div>
                  <h4>Click to Upload Document</h4>
                  <p>Supported formats: PDF, DOC, DOCX, TXT, JPG, PNG, GIF</p>
                  <p class="file-size-limit">Max file size: 10MB</p>
                </div>
              </div>
              <div id="uploadedFileInfo" class="uploaded-file-info" style="display: none;">
                <div class="file-details">
                  <span class="file-icon">📄</span>
                  <div class="file-info">
                    <div class="file-name"></div>
                    <div class="file-size"></div>
                  </div>
                  <button type="button" class="remove-file-btn" onclick="removeUploadedFile()">🗑️</button>
                </div>
              </div>
            </div>

            <div class="import-section">
              <h3>🔧 Available Tools</h3>
              <div class="tool-grid">
                <div class="tool-card" onclick="enableSignatureMode()">
                  <div class="tool-icon">✍️</div>
                  <h4>Add Signature</h4>
                  <p>Pick from your saved signatures</p>
                </div>
              </div>
            </div>

            <!-- Signature Library -->
            <div class="import-section" id="signatureLibrarySection" style="display: none;">
              <h3>💾 Saved Signatures</h3>
              <div id="signatureLibrary" class="signature-library">
                <p class="no-signatures">No saved signatures. Create some in Signature Tools first.</p>
              </div>
            </div>
          </div>

          <!-- Right Panel: Document Preview -->
          <div class="import-right-panel">
            <div class="document-preview-section">
              <h3>👁️ Document Preview</h3>
              <div id="documentPreviewContainer" class="document-preview-container">
                <div class="preview-placeholder">
                  <div class="preview-icon">📄</div>
                  <h4>Upload a document to see preview</h4>
                  <p>The document will appear here for editing</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="form-actions">
          <button type="button" class="btn-secondary" onclick="closeDocumentImport()">Cancel</button>
          <button type="button" class="btn-primary" onclick="processDocument()" id="processDocumentBtn" disabled>
            Process Document
          </button>
          <button type="button" class="btn-success" onclick="shareDocument()" id="shareDocumentBtn" disabled style="background: #28a745;">
            📤 Share Document
          </button>
        </div>
      </div>
    </div>
  </div>

  <script>
    <!-- Required libraries and dashboard logic for full modal functionality -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script src="dashboard-script.js"></script>
</body>
</html>