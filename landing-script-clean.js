// Landing page functionality - Clean version
console.log('🚀 Landing script loading...');

// Demo user accounts (for demonstration purposes)
const demoAccounts = {
    '<EMAIL>': {
        password: 'demo123',
        name: 'Demo User',
        company: 'Demo Company Ltd',
        plan: 'Pro'
    },
    '<EMAIL>': {
        password: 'admin123',
        name: 'Admin User',
        company: 'DocuGen Pro',
        plan: 'Enterprise'
    },
    '<EMAIL>': {
        password: 'test123',
        name: 'Test Business',
        company: 'Test Business Inc',
        plan: 'Starter'
    }
};

// Authentication functions
function showLogin() {
    document.getElementById('authModal').style.display = 'block';
    document.getElementById('signinForm').style.display = 'block';
    document.getElementById('signupForm').style.display = 'none';
}

function showSignup() {
    document.getElementById('authModal').style.display = 'block';
    document.getElementById('signinForm').style.display = 'none';
    document.getElementById('signupForm').style.display = 'block';
}

function closeAuth() {
    document.getElementById('authModal').style.display = 'none';
}

function showDemo() {
    alert('Demo mode: You can preview all document types below or sign <NAME_EMAIL> / demo123');
}

// Document preview functionality
function previewDocument(type) {
    const previewModal = document.getElementById('previewModal');
    const previewContainer = document.getElementById('previewContainer');
    
    if (!previewModal || !previewContainer) {
        alert('Preview functionality not available');
        return;
    }
    
    // Show modal
    previewModal.style.display = 'block';
    
    // Generate preview content based on type
    let previewContent = `<h3>Document Preview: ${type}</h3><p>This is a preview of the ${type} document.</p>`;
    previewContainer.innerHTML = previewContent;
}

function closePreview() {
    const previewModal = document.getElementById('previewModal');
    if (previewModal) {
        previewModal.style.display = 'none';
    }
}

// Simple Free Signing Tool Functions
function openSimpleSigningTool() {
    console.log('🚀 Opening simple signing tool...');
    const modal = document.getElementById('simpleSigningModal');
    if (modal) {
        modal.style.display = 'block';
        console.log('✅ Modal found and displayed');
    } else {
        console.error('❌ Modal not found!');
        alert('Error: Modal not found! Please check the HTML structure.');
    }
}

function closeSimpleSigningTool() {
    console.log('❌ Closing simple signing tool...');
    const modal = document.getElementById('simpleSigningModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Basic login functionality
function login() {
    const email = document.getElementById('loginEmail').value;
    const password = document.getElementById('loginPassword').value;
    
    if (demoAccounts[email] && demoAccounts[email].password === password) {
        localStorage.setItem('currentUser', JSON.stringify(demoAccounts[email]));
        alert('Login successful!');
        closeAuth();
        window.location.href = 'dashboard2.html';
    } else {
        alert('Invalid credentials. Try <EMAIL> / demo123');
    }
}

// Basic signup functionality
function signup() {
    const name = document.getElementById('signupName').value;
    const email = document.getElementById('signupEmail').value;
    const password = document.getElementById('signupPassword').value;
    
    if (name && email && password) {
        const newUser = { name, email, company: 'New Company', plan: 'Free' };
        localStorage.setItem('currentUser', JSON.stringify(newUser));
        alert('Account created successfully!');
        closeAuth();
        window.location.href = 'dashboard2.html';
    } else {
        alert('Please fill in all fields');
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('Landing page loaded');
    
    // Check if user is already logged in
    const currentUser = localStorage.getItem('currentUser');
    if (currentUser) {
        const user = JSON.parse(currentUser);
        console.log('User already logged in:', user.name);
    }
});

console.log('✅ Landing script loaded successfully');
