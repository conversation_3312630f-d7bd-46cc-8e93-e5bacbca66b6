<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Generator</title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Open+Sans:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <!-- jsPDF CDN -->
    <script src="https://unpkg.com/jspdf@latest/dist/jspdf.umd.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.1/build/qrcode.min.js"></script>
    <!-- Add html2canvas for better PDF rendering -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
</head>
<body>
    <div class="container">
        <div class="header-section">
            <div class="app-header">
                <h1 id="appTitle">DocuGen Pro - Professional Document Generator</h1>
                <div class="header-actions">
                    <button type="button" id="backToDashboard" class="btn-secondary" style="display: none;">
                        <span>← Back to Dashboard</span>
                    </button>
                    <button type="button" id="userProfile" class="btn-outline" style="display: none;">
                        <span id="currentUserName">User</span>
                    </button>
                </div>
            </div>

            <!-- Document Type Tabs Navigation -->
            <div class="document-tabs-container">
                <div class="document-tabs">
                    <button class="tab-btn active" data-document="invoice" onclick="switchDocumentTab('invoice')">
                        <span class="tab-icon">📄</span>
                        <span class="tab-label">Invoice</span>
                    </button>
                    <button class="tab-btn" data-document="receipt" onclick="switchDocumentTab('receipt')">
                        <span class="tab-icon">🧾</span>
                        <span class="tab-label">Receipt</span>
                    </button>
                    <button class="tab-btn" data-document="quotation" onclick="switchDocumentTab('quotation')">
                        <span class="tab-icon">💰</span>
                        <span class="tab-label">Quotation</span>
                    </button>
                    <button class="tab-btn" data-document="contract" onclick="switchDocumentTab('contract')">
                        <span class="tab-icon">📋</span>
                        <span class="tab-label">Contract</span>
                    </button>
                    <button class="tab-btn" data-document="rider" onclick="switchDocumentTab('rider')">
                        <span class="tab-icon">🎸</span>
                        <span class="tab-label">Technical Rider</span>
                    </button>
                    <button class="tab-btn" data-document="annexure" onclick="switchDocumentTab('annexure')">
                        <span class="tab-icon">📑</span>
                        <span class="tab-label">Annexure</span>
                    </button>
                    <button class="tab-btn" data-document="artist-agreement" onclick="switchDocumentTab('artist-agreement')">
                        <span class="tab-icon">🎤</span>
                        <span class="tab-label">Artist Agreement</span>
                    </button>
                </div>

                <!-- Active Document Indicator -->
                <div class="active-document-info">
                    <span class="current-doc-icon">📄</span>
                    <span class="current-doc-name">Invoice Generator</span>
                </div>
            </div>
        </div>

        <!-- Client Profiles Modal -->
        <div id="clientProfileModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Client Profiles</h2>
                    <span class="close-client">&times;</span>
                </div>
                <div class="client-list">
                    <select id="savedClients" class="client-select">
                        <option value="">Select a client...</option>
                    </select>
                </div>
                <form id="clientProfileForm">
                    <div class="form-group">
                        <label for="clientName">Profile Name:</label>
                        <input type="text" id="clientName" required>
                    </div>
                    <div class="form-group">
                        <label for="clientCompany">Company:</label>
                        <input type="text" id="clientCompany" required>
                    </div>
                    <div class="form-group">
                        <label for="clientRegNumber">Registration Number (YYYY/XXXXXX/XX):</label>
                        <input type="text" id="clientRegNumber" required>
                    </div>
                    <div class="form-group">
                        <label for="clientVatNumber">VAT Number (10 digits):</label>
                        <input type="text" id="clientVatNumber" required>
                    </div>
                    <div class="form-group">
                        <label for="clientAddress">Address:</label>
                        <textarea id="clientAddress" rows="3" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="clientAttention">Attention:</label>
                        <input type="text" id="clientAttention">
                    </div>
                    <div class="form-group">
                        <label for="clientPhone">Contact Number:</label>
                        <input type="tel" id="clientPhone" placeholder="e.g., +27 12 345 6789">
                    </div>
                    <div class="buttons-group">
                        <button type="submit" class="save-btn"><span>Save Client</span></button>
                        <button type="button" class="delete-btn" id="deleteClient"><span>Delete Client</span></button>
                        <button type="button" class="use-btn" id="useClient"><span>Use Selected</span></button>
                    </div>
                </form>
            </div>
        </div>

        <form id="invoiceForm">
            <!-- Document Styling -->
            <div class="section styling-section">
                <h2>🎨 Document Styling</h2>
                <div class="form-group">
                    <label for="colorTheme">Color Theme:</label>
                    <select id="colorTheme" class="color-theme">
                        <option value="default">Default (Blue)</option>
                        <option value="green">Green</option>
                        <option value="purple">Purple</option>
                        <option value="orange">Orange</option>
                        <option value="teal">Teal</option>
                    </select>
                </div>
                <div class="form-group template-selection-group">
                    <label for="invoiceTemplate">Invoice Template:</label>
                    <div class="template-controls">
                        <select id="invoiceTemplate" class="invoice-template">
                            <option value="standard">Standard</option>
                            <option value="modern">Modern</option>
                            <option value="classic">Classic</option>
                        </select>
                        <button type="button" id="manageTemplates" class="template-manager-btn"><span>Manage Templates</span></button>
                    </div>
                </div>
                <div class="form-group">
                    <label for="watermarkText">Watermark Text (optional):</label>
                    <input type="text" id="watermarkText" placeholder="e.g., PAID, COPY, DRAFT">
                </div>
                <div class="form-group">
                    <label for="useBackgroundColor">Use Custom Background Color:</label>
                    <input type="checkbox" id="useBackgroundColor">
                    <span class="checkbox-label">Apply a custom background color to the invoice</span>
                </div>
                <div id="colorPickerControls">
                    <div class="form-group color-picker-container">
                        <label for="backgroundColor">Background Color:</label>
                        <input type="color" id="backgroundColor" value="#f5f5f5">
                        <span class="color-preview" id="colorPreview"></span>
                    </div>
                    <div class="form-group">
                        <label for="backgroundOpacity">Background Opacity:</label>
                        <input type="range" id="backgroundOpacity" min="0" max="100" value="15" class="slider">
                        <span id="opacityValue">15%</span>
                    </div>
                    <div class="background-preview">
                        <div class="preview-label">Preview:</div>
                        <div id="backgroundPreview"></div>
                    </div>
                </div>
            </div>

            <!-- Logo Upload -->
            <div class="section logo-section">
                <h2>Company Logo</h2>
                <div class="form-group">
                    <label for="logoUpload">Upload Logo (Recommended size: 200x200px):</label>
                    <input type="file" id="logoUpload" accept="image/*" class="file-input">
                </div>
                <div class="form-group">
                    <label for="logoShape">Logo Frame Shape:</label>
                    <select id="logoShape" class="logo-shape">
                        <option value="square">Square</option>
                        <option value="circle">Circle</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="useLogoWatermark">Include Logo as Watermark:</label>
                    <input type="checkbox" id="useLogoWatermark" checked>
                    <span class="checkbox-label">Add a faint logo watermark in the background of the PDF</span>
                </div>
                <div id="logoPreview" class="logo-preview"></div>
            </div>

            <!-- Invoice Details -->
            <div class="section">
                <h2>Invoice Details</h2>
                <div class="form-group">
                    <label for="invoiceNumber">Invoice Number:</label>
                    <input type="text" id="invoiceNumber" value="000001" required>
                </div>
                <div class="form-group">
                    <label for="invoiceDate">Invoice Date:</label>
                    <input type="date" id="invoiceDate" value="2025-05-09" required>
                </div>
                <div class="form-group">
                    <label for="paymentTerms">Payment Terms:</label>
                    <select id="paymentTerms" required>
                        <option value="immediate">Immediate Payment</option>
                        <option value="7" selected>7 Days</option>
                        <option value="15">15 Days</option>
                        <option value="30">30 Days</option>
                        <option value="60">60 Days</option>
                        <option value="90">90 Days</option>
                        <option value="custom">Custom</option>
                    </select>
                </div>
                <div class="form-group" id="customPaymentTermsGroup" style="display: none;">
                    <label for="customPaymentTerms">Custom Payment Terms (Days):</label>
                    <input type="number" id="customPaymentTerms" min="1" max="365" placeholder="Enter number of days">
                </div>
                <div class="form-group">
                    <label for="dueDate">Due Date:</label>
                    <input type="date" id="dueDate" readonly>
                </div>
            </div>

            <!-- Bill To Section -->
            <div class="section">
                <h2>Bill To</h2>
                <div class="profile-controls">
                    <div class="client-profile-row">
                        <div class="form-group">
                            <label for="clientProfileName">Client Profile Name:</label>
                            <input type="text" id="clientProfileName" placeholder="Enter client name to save">
                        </div>
                        <button type="button" id="saveClientProfile" class="profile-button">Save Client</button>
                    </div>
                    <div class="form-group">
                        <label for="clientProfileDropdown">Load Client Profile:</label>
                        <select id="clientProfileDropdown">
                            <option value="">Select a client...</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="billToCompany">Company:</label>
                    <input type="text" id="billToCompany" required>
                </div>
                <div class="form-group">
                    <label for="billToRegNumber">Registration Number:</label>
                    <input type="text" id="billToRegNumber" required>
                </div>
                <div class="form-group">
                    <label for="billToVatNumber">VAT Number:</label>
                    <input type="text" id="billToVatNumber" required>
                </div>
                <div class="form-group">
                    <label for="billToAddress">Address:</label>
                    <textarea id="billToAddress" rows="3" required></textarea>
                </div>
                <div class="form-group">
                    <label for="billToAttention">Attention:</label>
                    <input type="text" id="billToAttention">
                </div>
                <div class="form-group">
                    <label for="billToPhone">Contact Number:</label>
                    <input type="tel" id="billToPhone" placeholder="e.g., +27 12 345 6789">
                </div>
                <div class="form-group">
                    <label for="billToEmail">Email:</label>
                    <input type="email" id="billToEmail" placeholder="<EMAIL>">
                </div>
            </div>

            <!-- Active Company Information Section -->
            <div class="section">
                <h2>🏢 Company Information</h2>
                <p class="section-description">Company details are automatically loaded from your active company profile.</p>

                <!-- Hidden form fields for company data (populated by active company system) -->
                <div style="display: none;">
                    <input type="text" id="companyName">
                    <input type="email" id="companyEmail">
                    <input type="tel" id="companyPhone">
                    <textarea id="companyAddress"></textarea>
                    <input type="text" id="representativeName">
                    <input type="text" id="companyRegNumber">
                    <input type="text" id="companyVatNumber">
                </div>

                <!-- Display active company info (read-only) -->
                <div class="active-company-display">
                    <div class="company-info-grid">
                        <div class="info-item">
                            <label>Company Name:</label>
                            <span id="displayCompanyName">Loading...</span>
                        </div>
                        <div class="info-item">
                            <label>Email:</label>
                            <span id="displayCompanyEmail">Loading...</span>
                        </div>
                        <div class="info-item">
                            <label>Phone:</label>
                            <span id="displayCompanyPhone">Loading...</span>
                        </div>
                        <div class="info-item">
                            <label>Registration Number:</label>
                            <span id="displayCompanyRegNumber">Loading...</span>
                        </div>
                        <div class="info-item">
                            <label>VAT Number:</label>
                            <span id="displayCompanyVatNumber">Loading...</span>
                        </div>
                        <div class="info-item full-width">
                            <label>Address:</label>
                            <span id="displayCompanyAddress">Loading...</span>
                        </div>
                        <div class="info-item">
                            <label>Representative:</label>
                            <span id="displayRepresentativeName">Loading...</span>
                        </div>
                    </div>

                    <div class="company-note">
                        <small>💡 To change company details, switch to a different company in the <a href="dashboard2.html" target="_blank">dashboard</a> or update your company profile there.</small>
                    </div>
                </div>
            </div>



            <!-- Event Details -->
            <div class="section">
                <h2>Event Details</h2>
                <div class="form-group">
                    <label for="eventName">Event Name:</label>
                    <input type="text" id="eventName">
                </div>
                <div class="form-group">
                    <label for="eventDate">Event Date:</label>
                    <input type="date" id="eventDate">
                </div>
                <div class="form-group">
                    <label for="eventTime">Event Time:</label>
                    <input type="time" id="eventTime">
                </div>
                <div class="form-group">
                    <label for="eventVenue">Venue:</label>
                    <input type="text" id="eventVenue">
                </div>
                <div class="form-group">
                    <label for="eventCity">City:</label>
                    <input type="text" id="eventCity">
                </div>
            </div>

            <!-- Items Section -->
            <div class="section">
                <h2>Items</h2>
                <div id="itemsContainer">
                    <div class="item-row">
                        <div class="form-group">
                            <label for="description0">Description:</label>
                            <input type="text" class="description" id="description0" required>
                        </div>
                        <div class="form-group">
                            <label for="quantity0">Quantity:</label>
                            <input type="number" class="quantity" id="quantity0" min="1" value="1" required>
                        </div>
                        <div class="form-group">
                            <label for="price0">Price:</label>
                            <input type="number" class="price" id="price0" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label for="amount0">Amount:</label>
                            <input type="text" class="amount" id="amount0" readonly>
                        </div>
                    </div>
                </div>
                <button type="button" id="addItem"><span>Add Item</span></button>
            </div>

            <!-- Totals Section -->
            <div class="section">
                <h2>Totals</h2>
                <div class="form-group">
                    <label for="subtotal">Subtotal:</label>
                    <input type="text" id="subtotal" readonly>
                </div>
                <div class="form-group">
                    <label for="vatRate">VAT Rate (%):</label>
                    <input type="number" id="vatRate" value="15" min="0" max="100" required>
                </div>
                <div class="form-group">
                    <label for="vatAmount">VAT Amount:</label>
                    <input type="text" id="vatAmount" readonly>
                </div>
                <div class="form-group">
                    <label for="discount">Discount:</label>
                    <input type="number" id="discount" value="0" min="0" step="0.01">
                </div>
                <div class="form-group">
                    <label for="grandTotal">Grand Total:</label>
                    <input type="text" id="grandTotal" readonly>
                </div>
            </div>

            <!-- Additional Notes -->
            <div class="section">
                <h2>Additional Notes</h2>
                <div class="form-group">
                    <label for="notes">Additional Notes:</label>
                    <textarea id="notes" rows="3" placeholder="Enter any additional notes or special instructions"></textarea>
                </div>
            </div>

            <!-- Bank Details -->
            <div class="section">
                <h2>Bank Details</h2>
                <div class="form-group">
                    <label for="bankName">Bank Name:</label>
                    <input type="text" id="bankName">
                </div>
                <div class="form-group">
                    <label for="accountName">Account Name:</label>
                    <input type="text" id="accountName">
                </div>
                <div class="form-group">
                    <label for="accountNumber">Account Number:</label>
                    <input type="text" id="accountNumber">
                </div>
                <div class="form-group">
                    <label for="branchCode">Branch Code:</label>
                    <input type="text" id="branchCode">
                </div>
                <div class="form-group">
                    <label for="includeQRCode">Include Payment QR Code:</label>
                    <input type="checkbox" id="includeQRCode">
                    <span class="checkbox-label">Generate QR code with payment details</span>
                </div>
            </div>

            <!-- Signature Section -->
            <div class="section">
                <h2>Signature</h2>
                <div class="form-group">
                    <label for="includeSignature">Include Signature Field:</label>
                    <input type="checkbox" id="includeSignature" checked>
                    <span class="checkbox-label">Add signature field to invoice</span>
                </div>
                <div class="form-group">
                    <label for="signatureName">Signature Name:</label>
                    <input type="text" id="signatureName" placeholder="e.g., John Smith, CEO">
                </div>
                <div class="signature-container">
                    <canvas id="signatureCanvas" width="400" height="150"></canvas>
                    <div class="signature-tools">
                        <button type="button" id="clearSignature"><span>Clear</span></button>
                    </div>
                </div>
            </div>

            <!-- Currency Selection -->
            <div class="section">
                <h2>Currency Settings</h2>
                <div class="form-group">
                    <label for="currencySelect">Currency:</label>
                    <select id="currencySelect">
                        <option value="ZAR" selected>South African Rand (R)</option>
                        <option value="USD">US Dollar ($)</option>
                        <option value="EUR">Euro (€)</option>
                        <option value="GBP">British Pound (£)</option>
                    </select>
                </div>
            </div>

<!-- Contract Page 1 Section -->
            <div class="section" id="contractPage1Section" style="display: none;">
                <h2>Contract Agreement - Page 1</h2>

                <!-- Contract Logo Upload Section -->
                <div class="contract-logo-section">
                    <h3>Contract Logo</h3>
                    <div class="form-group">
                        <label for="contractLogoUpload">Upload Contract Logo:</label>
                        <input type="file" id="contractLogoUpload" accept="image/*">
                    </div>
                    <div class="form-group">
                        <label for="contractLogoShape">Logo Frame:</label>
                        <select id="contractLogoShape">
                            <option value="square">Square Frame</option>
                            <option value="round">Round Frame</option>
                            <option value="none">No Frame</option>
                        </select>
                    </div>
                    <div class="contract-logo-preview" id="contractLogoPreview">
                        <div class="logo-placeholder">Click "Upload Contract Logo" to add your logo</div>
                    </div>
                </div>

                <div class="contract-header">
                    <div class="contract-logo-display" id="contractLogoDisplay" style="text-align: center; margin-bottom: 20px;">
                        <!-- Logo will be displayed here -->
                    </div>
                    <div class="form-group">
                        <label for="contractDate">Contract Date:</label>
                        <input type="text" id="contractDate" value="May 2025" style="text-align: center; font-size: 1.2em; font-weight: bold;">
                    </div>
                    <div class="form-group">
                        <label for="contractTitle">Contract Title:</label>
                        <input type="text" id="contractTitle" value="BOOKING CONTRACT" style="text-align: center; font-size: 1.5em; font-weight: bold;">
                    </div>
                </div>

                <div class="form-group contract-agreement-date">
                    <label>Agreement made this</label>
                    <input type="text" id="contractAgreementDay" placeholder="Day (e.g., 1st)" style="width: 100px;">
                    <label>th day of</label>
                    <input type="text" id="contractAgreementMonth" placeholder="Month (e.g., June)" style="width: 150px;">
                    <input type="text" id="contractAgreementYear" placeholder="Year (e.g., 2025)" style="width: 100px;">
                    <label>between:</label>
                </div>

                <div class="contract-party">
                    <p><strong>BROTHER COLLECTIVE PTY (LTD)</strong> (Hereinafter referred to as the “PURCHASER”)</p>
                    <p>DOMICILED AT: 181 Bryanston Drive, Bryanston, Johannesburg, 2191</p>
                    <p>COMPANY REGISTRATION NO: 2024/772455/07</p>
                    <div class="form-group">
                        <label for="contractPurchaserRepresentative">REPRESENTED BY:</label>
                        <input type="text" id="contractPurchaserRepresentative" value="LIZIWE KWANINI">
                    </div>
                </div>

                <p style="text-align: center; margin: 20px 0; font-weight: bold;">And</p>

                <div class="contract-party">
                    <p>(Hereinafter referred to as the “AGENT”)</p>
                    <div class="form-group">
                        <label for="contractAgentDomiciledAt">DOMICILED AT (ADDRESS):</label>
                        <input type="text" id="contractAgentDomiciledAt" placeholder="Enter agent's address">
                    </div>
                    <div class="form-group">
                        <label for="contractAgentRepresenting">REPRESENTING:</label>
                        <input type="text" id="contractAgentRepresenting" value="JAHSEED & ADMIRAL (Hereinafter referred to as the “ARTIST”)">
                    </div>
                </div>

                <div class="contract-signatures" style="margin-top: 40px;">
                    <div class="form-group signature-block">
                        <label for="contractForPurchaser">FOR PURCHASER:</label>
                        <input type="text" id="contractForPurchaser" placeholder="Signature or Name">
                    </div>
                    <div class="form-group signature-block">
                        <label for="contractForArtist">FOR ARTIST:</label>
                        <input type="text" id="contractForArtist" placeholder="Signature or Name">
                    </div>
                </div>
            </div>
            <!-- End Contract Page 1 Section -->

            <!-- Artist Agreement Contract Section -->
            <div class="section" id="artistAgreementSection" style="display: none;">
                <h2>Artist Agreement Contract</h2>

                <!-- Client Selection for Artist Agreement -->
                <div class="profile-controls">
                    <div class="form-group">
                        <label for="artistAgreementClientDropdown">🎯 Select Client for Agreement:</label>
                        <select id="artistAgreementClientDropdown" onchange="loadClientForArtistAgreement(this.value)">
                            <option value="">Select a client...</option>
                        </select>
                    </div>
                </div>

                <!-- Page 1 - Header Information -->
                <div class="contract-page">
                    <h3>Page 1 - Agreement Header</h3>

                    <div class="form-group">
                        <label for="contractCompanyName">Company Name:</label>
                        <input type="text" id="contractCompanyName" value="BROTHER COLLECTIVE PTY (LTD)">
                    </div>

                    <div class="form-group">
                        <label for="contractMonth">Contract Month:</label>
                        <input type="text" id="contractMonth" value="May 2025">
                    </div>

                    <div class="form-group">
                        <label for="contractTitleArtist">Contract Title:</label>
                        <input type="text" id="contractTitleArtist" value="BOOKING CONTRACT">
                    </div>

                    <div class="form-group">
                        <label for="agreementDay">Agreement Day:</label>
                        <input type="text" id="agreementDay" value="15th">
                    </div>

                    <div class="form-group">
                        <label for="agreementMonth">Agreement Month:</label>
                        <input type="text" id="agreementMonth" value="June">
                    </div>

                    <div class="form-group">
                        <label for="agreementYear">Agreement Year:</label>
                        <input type="text" id="agreementYear" value="2025">
                    </div>

                    <div class="form-group">
                        <label for="purchaserAddress">Purchaser Address:</label>
                        <textarea id="purchaserAddress" rows="2">181 Bryanston Drive, Bryanston, Johannesburg, 2191</textarea>
                    </div>

                    <div class="form-group">
                        <label for="purchaserRegNumber">Company Registration No:</label>
                        <input type="text" id="purchaserRegNumber" value="2024/772455/07">
                    </div>

                    <div class="form-group">
                        <label for="purchaserRepresentative">Represented By:</label>
                        <input type="text" id="purchaserRepresentative" value="LIZIWE KWANINI">
                    </div>

                    <div class="form-group">
                        <label for="agentAddress">Agent Address:</label>
                        <textarea id="agentAddress" rows="2">456 Music Avenue, Melville
Johannesburg, 2109
South Africa</textarea>
                    </div>

                    <div class="form-group">
                        <label for="artistName">Artist Name:</label>
                        <input type="text" id="artistName" value="JAHSEED & ADMIRAL">
                    </div>

                    <div class="form-group">
                        <label for="eventName">Event Name:</label>
                        <input type="text" id="eventName" value="FREEDOM DAY FESTIVAL" placeholder="Enter the name of the event">
                    </div>
                </div>

                <!-- Page 2 - Performance Details -->
                <div class="contract-page">
                    <h3>Page 2 - Performance Details</h3>

                    <div class="form-group">
                        <label for="performanceVenue">Place of Engagement:</label>
                        <textarea id="performanceVenue" rows="2">Constitutional Hill, 11 Kotze Street, Johannesburg
GPS: -26.190217, 28.041916</textarea>
                    </div>

                    <div class="form-group">
                        <label for="performanceDuration">Nature of Engagement:</label>
                        <input type="text" id="performanceDuration" value="45 minutes live performance">
                    </div>

                    <div class="form-group">
                        <label for="performanceDate">Date of Engagement:</label>
                        <input type="date" id="performanceDate" value="2025-07-15">
                    </div>

                    <div class="form-group">
                        <label for="performanceTime">Hours of Engagement:</label>
                        <input type="text" id="performanceTime" value="20:30 - 21:15 (subject to change)">
                    </div>

                    <div class="form-group">
                        <label for="performanceFee">Full Price Agreed:</label>
                        <input type="text" id="performanceFee" value="R35 000">
                    </div>

                    <div class="form-group">
                        <label for="invoiceAddress">Invoice Address:</label>
                        <textarea id="invoiceAddress" rows="3">Brother Collective PTY (LTD)
181 Bryanston Drive, Sandton, 2191
2024/772455/07</textarea>
                    </div>
                </div>

                <!-- Payment Terms Section -->
                <div class="contract-page">
                    <h3>Payment Terms</h3>

                    <div class="form-group">
                        <label for="contractPaymentTerms">Payment Terms:</label>
                        <textarea id="contractPaymentTerms" rows="6">1. Payment is due within 7 days from the invoice date unless otherwise specified.
2. 50% Deposit .(All promotional materials will be provided there after).
3. All prices are quoted in South African Rand (ZAR) and include VAT where applicable.
4. Bank charges for payments are for the client's account.
5. Services rendered are subject to our standard terms and conditions.
6. Please reference the invoice number /Name of Event when making payment through EFT.</textarea>
                    </div>
                </div>

                <!-- Additional Terms -->
                <div class="contract-page">
                    <h3>Additional Terms & Conditions</h3>

                    <div class="form-group">
                        <label for="forceClause">Force Majeure Clause:</label>
                        <textarea id="forceClause" rows="4">This agreement by both parties to perform their obligations herein is subject to proven detention by serious illness, accidents, or accidents to means of transportation, labour disputes or walkouts, acts of God, or any act of public authority, material breach of Contract by the PURCHASER, or any other condition beyond either party's control. Neither party shall be liable to fulfil the remainder of the Contract nor perform any "make-up" date unless expressly agreed to by both parties.</textarea>
                    </div>

                    <div class="form-group">
                        <label for="jurisdictionClause">Jurisdiction:</label>
                        <textarea id="jurisdictionClause" rows="2">This contract shall be construed in accordance with the Laws of South Africa. Any disputes shall be settled by arbitration in accordance with the Arbitration Act of South Africa.</textarea>
                    </div>

                    <div class="form-group">
                        <label for="cancellationPolicy">Cancellation Policy:</label>
                        <textarea id="cancellationPolicy" rows="4">The PURCHASER agrees that ARTIST shall have the right to cancel this engagement no later than 14 days prior to the date of performance for radio, television, motion picture, or career advancing opportunities. Should the PURCHASER cancel, notice must be given in writing no later than 14 days prior to engagement. In the event of cancellation due to wilful negligence of the ARTIST, deposits must be returned within 14 days.</textarea>
                    </div>
                </div>

                <!-- Signature Section -->
                <div class="contract-page">
                    <h3>Signatures</h3>

                    <div class="form-group">
                        <label for="signedAtPurchaser">Purchaser Signed At:</label>
                        <input type="text" id="signedAtPurchaser" value="Johannesburg">
                    </div>

                    <div class="form-group">
                        <label for="signedDatePurchaser">Purchaser Signed Date:</label>
                        <input type="date" id="signedDatePurchaser" value="2025-06-15">
                    </div>

                    <div class="form-group">
                        <label for="purchaserSignature">Purchaser Signature:</label>
                        <input type="text" id="purchaserSignature" value="L. Kwanini">
                    </div>

                    <div class="form-group">
                        <label for="signedAtArtist">Artist Signed At:</label>
                        <input type="text" id="signedAtArtist" value="Johannesburg">
                    </div>

                    <div class="form-group">
                        <label for="signedDateArtist">Artist Signed Date:</label>
                        <input type="date" id="signedDateArtist" value="2025-06-15">
                    </div>

                    <div class="form-group">
                        <label for="artistSignature">Artist Signature:</label>
                        <input type="text" id="artistSignature" value="Jahseed & Admiral">
                    </div>
                </div>

                <!-- Page 3 - Definitions and Interpretation (Editable Content) -->
                <div class="contract-page">
                    <h3>Page 3 - Definitions and Interpretation</h3>
                    <p class="page-description">Edit the definitions below. Headings are fixed, but all content between them is editable.</p>

                    <!-- Introduction Text -->
                    <div class="form-group">
                        <label for="definitionsIntro">1.1. Introduction Text:</label>
                        <textarea id="definitionsIntro" rows="3" placeholder="Introduction paragraph for definitions section">In this Agreement, the following words shall, unless otherwise stated or inconsistent with the context in which they appear bear the following meaning and other derived for the same origins as such words (that is, cognate words) shall bear corresponding meaning:</textarea>
                    </div>

                    <!-- Agreement Definition -->
                    <div class="definition-group">
                        <h4 class="definition-term">"Agreement" -</h4>
                        <div class="form-group">
                            <label for="agreementDefinition">Definition:</label>
                            <textarea id="agreementDefinition" rows="2" placeholder="Definition of Agreement">means this Artist Performance Agreement between the Parties</textarea>
                        </div>
                    </div>

                    <!-- Applicable Laws Definition -->
                    <div class="definition-group">
                        <h4 class="definition-term">"Applicable laws" -</h4>
                        <div class="form-group">
                            <label for="applicableLawsDefinition">Definition:</label>
                            <textarea id="applicableLawsDefinition" rows="3" placeholder="Definition of Applicable laws">means all South African laws, ordinances, writs, orders, regulations, judgments and orders of any competent South African court or governmental agency or authority in South Africa;</textarea>
                        </div>
                    </div>

                    <!-- Artist Definition (Editable Names) -->
                    <div class="definition-group artist-definition">
                        <h4 class="definition-term">"Artist"—</h4>
                        <div class="form-group">
                            <label for="artistBandName">Band/Artist Name:</label>
                            <input type="text" id="artistBandName" value="BONGO MAFFIN" placeholder="Enter band/artist name">
                        </div>
                        <div class="form-group">
                            <label for="artistMembers">Band Members (comma-separated):</label>
                            <textarea id="artistMembers" rows="2" placeholder="Enter band member names">Thandiswa Mazwai, Tshepo Seete, Harold MathlakU, Anesu Mupemhi</textarea>
                        </div>
                        <div class="artist-preview">
                            <strong>Preview:</strong> means <span id="artistPreview">BONGO MAFFIN – Comprising of: Thandiswa Mazwai, Tshepo Seete, Harold MathlakU, Anesu Mupemhi.</span>
                        </div>
                    </div>

                    <!-- Business Day Definition -->
                    <div class="definition-group">
                        <h4 class="definition-term">"Business Day" -</h4>
                        <div class="form-group">
                            <label for="businessDayDefinition">Definition:</label>
                            <textarea id="businessDayDefinition" rows="2" placeholder="Definition of Business Day">means any day other than a Saturday, Sunday or official public holiday in South Africa;</textarea>
                        </div>
                    </div>

                    <!-- Client Definition -->
                    <div class="definition-group">
                        <h4 class="definition-term">"Client" -</h4>
                        <div class="form-group">
                            <label for="clientDefinition">Definition:</label>
                            <textarea id="clientDefinition" rows="2" placeholder="Definition of Client">means any Party receiving the Services of the Artist;</textarea>
                        </div>
                    </div>

                    <!-- Confidential Information Definition -->
                    <div class="definition-group">
                        <h4 class="definition-term">"Confidential Information" -</h4>
                        <div class="form-group">
                            <label for="confidentialInfoDefinition">Definition:</label>
                            <textarea id="confidentialInfoDefinition" rows="6" placeholder="Definition of Confidential Information">means all information confidential to a party, including, to the extent that it is not freely and publicly available, commercial, financial, legal, technical, scientific and research information, trade secrets, passwords, or other secret codes, information disclosed with the permission of third parties in which the third parties have confidentiality rights, information legally protected from disclosure, any information the unauthorised disclosure of which could reasonably be expected to cause harm or risk to the disclosing party and any other information designated by the disclosing party as confidential or which is manifestly confidential;</textarea>
                        </div>
                    </div>

                    <!-- Add More Definitions Button -->
                    <div class="form-group">
                        <button type="button" class="btn-secondary" onclick="addCustomDefinition()">+ Add Custom Definition</button>
                    </div>

                    <!-- Custom Definitions Container -->
                    <div id="customDefinitionsContainer"></div>
                </div>

                <!-- Page 4 - Additional Definitions (Editable Content) -->
                <div class="contract-page">
                    <h3>Page 4 - Additional Definitions</h3>
                    <p class="page-description">Edit the additional definitions below. Headings are fixed, but all content between them is editable.</p>

                    <!-- Fees Definition -->
                    <div class="definition-group">
                        <h4 class="definition-term">"Fees" -</h4>
                        <div class="form-group">
                            <label for="feesDefinition">Definition:</label>
                            <textarea id="feesDefinition" rows="3" placeholder="Definition of Fees">means the fees payable to the Artists in consideration for the Services provided in terms of this Agreement as per Annexure "A";</textarea>
                        </div>
                    </div>

                    <!-- Good Industry Practice Definition -->
                    <div class="definition-group">
                        <h4 class="definition-term">"Good Industry Practice" -</h4>
                        <div class="form-group">
                            <label for="goodIndustryPracticeDefinition">Definition:</label>
                            <textarea id="goodIndustryPracticeDefinition" rows="6" placeholder="Definition of Good Industry Practice">means providing the Service in a proper and professional manner, taking into account generally acceptable standards, practice, methods and procedures conforming to Applicable Laws and exercising that degree of skill, that would be reasonable and ordinarily be expected of a skilled and experienced person engaged in a similar type of undertaking under similar circumstance;</textarea>
                        </div>
                    </div>

                    <!-- Intellectual Property Rights Definition -->
                    <div class="definition-group">
                        <h4 class="definition-term">"Intellectual Property" - Rights</h4>
                        <div class="form-group">
                            <label for="intellectualPropertyDefinition">Definition:</label>
                            <textarea id="intellectualPropertyDefinition" rows="5" placeholder="Definition of Intellectual Property Rights">means all patents, copyrights, design rights, trademarks, service marks, trade secrets, know-how, database rights and other rights in the nature of intellectual property rights (whether registered or unregistered) and all applications for the same, in the Territory;</textarea>
                        </div>
                    </div>

                    <!-- Parties and/or Party Definition -->
                    <div class="definition-group">
                        <h4 class="definition-term">"Parties and/or Party" -</h4>
                        <div class="form-group">
                            <label for="partiesDefinition">Definition:</label>
                            <textarea id="partiesDefinition" rows="2" placeholder="Definition of Parties and/or Party">Means individually or collectively, as the context may require the Artist and the Client;</textarea>
                        </div>
                    </div>

                    <!-- Performance Definition -->
                    <div class="definition-group">
                        <h4 class="definition-term">"Performance" -</h4>
                        <div class="form-group">
                            <label for="performanceDefinition">Definition:</label>
                            <textarea id="performanceDefinition" rows="2" placeholder="Definition of Performance">means the musical performance to be undertaken by the Artist as part of the Services in terms of this Agreement;</textarea>
                        </div>
                    </div>

                    <!-- Performance Date Definition -->
                    <div class="definition-group">
                        <h4 class="definition-term">"Performance Date" -</h4>
                        <div class="form-group">
                            <label for="performanceDateDefinition">Definition:</label>
                            <textarea id="performanceDateDefinition" rows="3" placeholder="Definition of Performance Date">means (insert performance date) being the date when the Artist will undertake the Performance as part of the Services for the Client in terms of this Agreement;</textarea>
                        </div>
                    </div>

                    <!-- Rights of Copyright Definition -->
                    <div class="definition-group">
                        <h4 class="definition-term">"Rights of Copyright" -</h4>
                        <div class="form-group">
                            <label for="rightsOfCopyrightDefinition">Definition:</label>
                            <textarea id="rightsOfCopyrightDefinition" rows="8" placeholder="Definition of Rights of Copyright">means the bundle of rights contemplated in the Copyright Act No. 98 of 1978 as amended from time to time, subsisting in the Performance and the music; and any additional or comparable rights of copyright, or sui generis right that may subsist in the future in South Africa in the Performance or that subsists in the Performanceor in the music in any other territory of jurisdiction of the world, including but not limited to do</textarea>
                        </div>
                    </div>

                    <!-- Add More Definitions Button -->
                    <div class="form-group">
                        <button type="button" class="btn-secondary" onclick="addCustomDefinitionPage4()">+ Add Custom Definition</button>
                    </div>

                    <!-- Custom Definitions Container for Page 4 -->
                    <div id="customDefinitionsPage4Container"></div>
                </div>

                <!-- Page 5 - Additional Definitions & Clauses (Editable Content) -->
                <div class="contract-page">
                    <h3>Page 5 - Additional Definitions & Clauses</h3>
                    <p class="page-description">Edit the additional definitions and interpretation clauses below. Headings are fixed, but all content between them is editable.</p>

                    <!-- Signature Date Definition -->
                    <div class="definition-group">
                        <h4 class="definition-term">"Signature Date" -</h4>
                        <div class="form-group">
                            <label for="signatureDateDefinition">Definition:</label>
                            <textarea id="signatureDateDefinition" rows="2" placeholder="Definition of Signature Date">means the date on which this agreement is signed by the party that signs it last in time;</textarea>
                        </div>
                    </div>

                    <!-- Services Definition -->
                    <div class="definition-group">
                        <h4 class="definition-term">"Services" -</h4>
                        <div class="form-group">
                            <label for="servicesDefinition">Definition:</label>
                            <textarea id="servicesDefinition" rows="2" placeholder="Definition of Services">means all the work, duties and obligations related to the Performance by the Artist pursuant to this Agreement;</textarea>
                        </div>
                    </div>

                    <!-- Interpretation Clauses -->
                    <div class="definition-group">
                        <h4 class="definition-term">1.2. Statutory Provision Reference</h4>
                        <div class="form-group">
                            <label for="statutoryProvisionClause">Clause Content:</label>
                            <textarea id="statutoryProvisionClause" rows="3" placeholder="Statutory provision clause">Any reference to a statutory provision shall include any subordinate legislation made from time to time under that provision and shall include that provision as modified or re-enacted from time to time.</textarea>
                        </div>
                    </div>

                    <div class="definition-group">
                        <h4 class="definition-term">1.3. Gender Reference</h4>
                        <div class="form-group">
                            <label for="genderReferenceClause">Clause Content:</label>
                            <textarea id="genderReferenceClause" rows="2" placeholder="Gender reference clause">In this Agreement, words importing the masculine gender shall include the feminine and neuter genders and vice versa.</textarea>
                        </div>
                    </div>

                    <div class="definition-group">
                        <h4 class="definition-term">1.4. Definition Rights and Obligations</h4>
                        <div class="form-group">
                            <label for="definitionRightsClause">Clause Content:</label>
                            <textarea id="definitionRightsClause" rows="3" placeholder="Definition rights clause">If a definition imposes substantive rights and obligations on a Party, such rights and obligations shall be given effect to and shall be enforceable, notwithstanding that they are contained in a definition.</textarea>
                        </div>
                    </div>

                    <div class="definition-group">
                        <h4 class="definition-term">1.5. Days Calculation</h4>
                        <div class="form-group">
                            <label for="daysCalculationClause">Clause Content:</label>
                            <textarea id="daysCalculationClause" rows="4" placeholder="Days calculation clause">Where any number of days is prescribed in this Agreement, those days shall be reckoned exclusively of the first day and inclusively of the last day unless the last day falls on a day which is not a Business Day, in which event the last day shall be the next succeeding Business Day.</textarea>
                        </div>
                    </div>

                    <div class="definition-group">
                        <h4 class="definition-term">1.6. Term Definition Scope</h4>
                        <div class="form-group">
                            <label for="termDefinitionScopeClause">Clause Content:</label>
                            <textarea id="termDefinitionScopeClause" rows="5" placeholder="Term definition scope clause">Where any term is defined within the context of any particular clause in this agreement, the term so defined, unless it is specifically stated in the clause in question that the term so defined has limited application to the relevant clause, shall bear the meaning ascribed to it for all purposes in terms of this agreement, notwithstanding that that term has not been defined in this clause.</textarea>
                        </div>
                    </div>

                    <div class="definition-group">
                        <h4 class="definition-term">1.7. Agreement Termination Effect</h4>
                        <div class="form-group">
                            <label for="agreementTerminationClause">Clause Content:</label>
                            <textarea id="agreementTerminationClause" rows="4" placeholder="Agreement termination clause">The expiry or termination of this Agreement shall not affect such of the provisions of this agreement which are expressly provided to operate after any such expiry or termination, or which of necessity must continue to have effect after such expiry or termination, notwithstanding that the relevant clauses themselves do not provide for this.</textarea>
                        </div>
                    </div>

                    <div class="definition-group">
                        <h4 class="definition-term">1.8. Contract Construction</h4>
                        <div class="form-group">
                            <label for="contractConstructionClause">Clause Content:</label>
                            <textarea id="contractConstructionClause" rows="4" placeholder="Contract construction clause">As the terms of this Agreement have been negotiated by the Parties and drafted for the benefit of the Parties, the rule of construction that the contract shall be interpreted against the Party responsible for its drafting or preparation, shall not apply.</textarea>
                        </div>
                    </div>

                    <div class="definition-group">
                        <h4 class="definition-term">1.9. Ejusdem Generis Rule</h4>
                        <div class="form-group">
                            <label for="ejusdemGenerisClause">Clause Content:</label>
                            <textarea id="ejusdemGenerisClause" rows="3" placeholder="Ejusdem generis clause">In this Agreement, the ejusdem generis rule shall not apply and whenever the term "including" is used followed by specific examples, such examples shall not be construed so as to limit the meaning of that term.</textarea>
                        </div>
                    </div>
                </div>

                <!-- Page 6 - Scope, Duration & Payment Terms (Editable Content) -->
                <div class="contract-page">
                    <h3>Page 6 - Scope, Duration & Payment Terms</h3>
                    <p class="page-description">Edit the scope of services, duration, and payment terms below. Section headings are fixed, but all content is editable.</p>

                    <!-- Scope of Services Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">2. SCOPE OF SERVICES</h4>
                        <div class="form-group">
                            <label for="scopeOfServicesContent">2.1. Performance Intention:</label>
                            <textarea id="scopeOfServicesContent" rows="3" placeholder="Scope of services content">It is the intention of the Parties that the Artist will engage in a Performance as part of the Services for the Client at the Performance Date and in accordance with the terms of this Agreement in Annexure and Annexure B</textarea>
                        </div>
                    </div>

                    <!-- Duration Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">3. DURATION</h4>
                        <div class="form-group">
                            <label for="durationContent">3.1. Agreement Duration:</label>
                            <textarea id="durationContent" rows="2" placeholder="Duration content">This Agreement shall commence on the Signature Date and shall endure for a period agreed up by the Artist and the Client.</textarea>
                        </div>
                    </div>

                    <!-- Payment Terms Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">4. PAYMENT TERMS</h4>
                        <div class="form-group">
                            <label for="paymentTermsIntro">4.1. Fee Consideration:</label>
                            <textarea id="paymentTermsIntro" rows="2" placeholder="Payment terms introduction">In consideration for the Services rendered by the Artist, Client agrees Artist the Fees as provided for in Annexure "A" as follows:</textarea>
                        </div>

                        <div class="form-group">
                            <label for="paymentTermsDeposit">4.2. Initial Payment (50%):</label>
                            <textarea id="paymentTermsDeposit" rows="2" placeholder="Deposit payment terms">Ps .The Client shall pay (50)% of the Fees into the bank account of the Artist on receiving invoice for the booking</textarea>
                        </div>

                        <div class="form-group">
                            <label for="paymentTermsBalance">4.3. Balance Payment (50%):</label>
                            <textarea id="paymentTermsBalance" rows="2" placeholder="Balance payment terms">The Client shall deposit the remaining fifty (50)% of the Fees into the bank account of the Artist not less than a week before the show or day of departure.</textarea>
                        </div>
                    </div>

                    <!-- Add More Sections Button -->
                    <div class="form-group">
                        <button type="button" class="btn-secondary" onclick="addCustomSectionPage6()">+ Add Custom Section</button>
                    </div>

                    <!-- Custom Sections Container for Page 6 -->
                    <div id="customSectionsPage6Container"></div>
                </div>

                <!-- Page 7 - Obligations & Filming Rights (Editable Content) -->
                <div class="contract-page">
                    <h3>Page 7 - Obligations & Filming Rights</h3>
                    <p class="page-description">Edit the client obligations, artist obligations, and filming rights below. Section headings are fixed, but all content is editable.</p>

                    <!-- Client Obligations Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">5. OBLIGATIONS OF THE CLIENT</h4>
                        <div class="form-group">
                            <label for="clientObligationsIntro">5.1. The Client will be obliged to:</label>
                            <textarea id="clientObligationsIntro" rows="1" placeholder="Client obligations introduction">The Client will be obliged to:</textarea>
                        </div>

                        <div class="form-group">
                            <label for="clientObligation511">5.1.1. Remuneration:</label>
                            <textarea id="clientObligation511" rows="1" placeholder="Remuneration obligation">remunerate the Artist for the Services in accordance with clause 4 above.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="clientObligation512">5.1.2. Accommodation Costs:</label>
                            <textarea id="clientObligation512" rows="2" placeholder="Accommodation costs">Pay all the costs of the accommodation, which shall hold no less than a four (4) star rating; breakfast and dinner, where applicable,</textarea>
                        </div>

                        <div class="form-group">
                            <label for="travelCostsNote">Travel Costs Note:</label>
                            <textarea id="travelCostsNote" rows="1" placeholder="Travel costs note">Ps: travel costs are included in the fee quoted by the artist</textarea>
                        </div>

                        <div class="form-group">
                            <label for="clientObligation513">5.1.3. Details in Annexure A:</label>
                            <textarea id="clientObligation513" rows="1" placeholder="Annexure A details">Details in Annexure A</textarea>
                        </div>

                        <div class="form-group">
                            <label for="clientObligation514">5.1.4. Technical Support:</label>
                            <textarea id="clientObligation514" rows="3" placeholder="Technical support obligation">provide all necessary support to the Artist so as to enable it to effectively render the Services including all sound and lighting requirements detailed in technical riders provided; and</textarea>
                        </div>

                        <div class="form-group">
                            <label for="clientObligation515">5.1.5. Access & Information:</label>
                            <textarea id="clientObligation515" rows="3" placeholder="Access and information">use its best endeavours to ensure that the Artist has timely and adequate access to the location, all information and documentation available that will enable the Artist to render the Services.</textarea>
                        </div>
                    </div>

                    <!-- Artist Obligations Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">6. OBLIGATIONS OF ARTIST</h4>
                        <div class="form-group">
                            <label for="artistObligations">6.1. Artist Performance Standards:</label>
                            <textarea id="artistObligations" rows="2" placeholder="Artist obligations">The Artist undertakes to conduct her performance with due skill, care and diligence at all times and on a standard commensurate with Good Industry Practice.</textarea>
                        </div>
                    </div>

                    <!-- Filming Rights Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">7. FILMING RIGHTS</h4>
                        <div class="form-group">
                            <label for="filmingRights71">7.1. Filming Permission:</label>
                            <textarea id="filmingRights71" rows="2" placeholder="Filming permission">The Artist has not granted the Client the right to film the Performance unless its FOR ONLY ARCHIVING PURPOSES</textarea>
                        </div>

                        <div class="form-group">
                            <label for="filmingRights72">7.2. Promotional Use Only:</label>
                            <textarea id="filmingRights72" rows="2" placeholder="Promotional use">The film shall only be used for promotional use and not for any commercial benefit and /or use to the Client without written consent between the two parties;</textarea>
                        </div>

                        <div class="form-group">
                            <label for="filmingRights73">7.3. Professional Filming Authorization:</label>
                            <textarea id="filmingRights73" rows="3" placeholder="Professional filming">The Client must ensure that ANY PROFESSIONAL people filming the Performance have received due authorisation from the Client and as such must at all times carry the media accreditation permit;</textarea>
                        </div>

                        <div class="form-group">
                            <label for="filmingRights74">7.4. Security Services:</label>
                            <textarea id="filmingRights74" rows="3" placeholder="Security services">The Client must deploy security services to stop unauthorised PROFESSIONAL people from filming the Performance. This means media or anyone filming from the media booth infront of the stage.</textarea>
                        </div>
                    </div>

                    <!-- Add More Sections Button -->
                    <div class="form-group">
                        <button type="button" class="btn-secondary" onclick="addCustomSectionPage7()">+ Add Custom Section</button>
                    </div>

                    <!-- Custom Sections Container for Page 7 -->
                    <div id="customSectionsPage7Container"></div>
                </div>

                <!-- Page 8 - Good Faith & Warranties (Editable Content) -->
                <div class="contract-page">
                    <h3>Page 8 - Good Faith Negotiations & Warranties</h3>
                    <p class="page-description">Edit the good faith negotiations and warranties sections below. Section headings are fixed, but all content is editable.</p>

                    <!-- Good Faith Negotiations Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">8. GOOD FAITH NEGOTIATIONS</h4>
                        <div class="form-group">
                            <label for="goodFaith81">8.1. Negotiation Requirement:</label>
                            <textarea id="goodFaith81" rows="1" placeholder="Negotiation requirement">The Parties shall negotiate in good faith the terms and conditions of this Agreement.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="goodFaith82">8.2. Duty of Good Faith:</label>
                            <textarea id="goodFaith82" rows="2" placeholder="Duty of good faith">In addition, the Parties agree to owe to each other a duty of good faith at all times, this means, inter alia, that the Parties shall:</textarea>
                        </div>

                        <div class="form-group">
                            <label for="goodFaith821">8.2.1. Full Disclosure:</label>
                            <textarea id="goodFaith821" rows="2" placeholder="Full disclosure">at all times make full disclosure to each other on any matter which may affect the purpose of this Agreement; and</textarea>
                        </div>

                        <div class="form-group">
                            <label for="goodFaith822">8.2.2. Confidentiality:</label>
                            <textarea id="goodFaith822" rows="3" placeholder="Confidentiality">will at all times endeavour to keep all matters related to the activities, plans, strategies and any other material or documents of each other strictly confidential.</textarea>
                        </div>
                    </div>

                    <!-- Warranties Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">9. WARRANTIES</h4>
                        <div class="form-group">
                            <label for="warranties91">9.1. Artist Warranties:</label>
                            <textarea id="warranties91" rows="1" placeholder="Artist warranties">The Artist warrants that:</textarea>
                        </div>

                        <div class="form-group">
                            <label for="warranties911">9.1.1. Authorization:</label>
                            <textarea id="warranties911" rows="2" placeholder="Authorization warranty">it has taken all necessary actions to authorize its execution of and to fulfil its obligations under this Agreement;</textarea>
                        </div>

                        <div class="form-group">
                            <label for="warranties912">9.1.2. Legal Validity:</label>
                            <textarea id="warranties912" rows="2" placeholder="Legal validity">its obligations under this Agreement are legal, valid, binding and enforceable against it, in accordance with the terms of this Agreement;</textarea>
                        </div>

                        <div class="form-group">
                            <label for="warranties913">9.1.3. Information Accuracy:</label>
                            <textarea id="warranties913" rows="6" placeholder="Information accuracy">all information disclosed by or on behalf of the Artist at any time up to Signature Date and up to the end of the term of this Agreement, and in particular, during the time preceding this Agreement to the Client is true, complete and accurate in all material respects and the Artist is not aware of any material facts or circumstances not disclosed to the Client which would, if disclosed, be likely to have an adverse effect on the Client's decision to enter into this Agreement with the Artist;</textarea>
                        </div>

                        <div class="form-group">
                            <label for="warranties914">9.1.4. No Contraventions:</label>
                            <textarea id="warranties914" rows="4" placeholder="No contraventions">the execution and performance of this Agreement does not and will not contravene any provision of any order or other decision of any responsible authority or arbitrator that is binding on the Artist for the duration of this Agreement;</textarea>
                        </div>

                        <div class="form-group">
                            <label for="warranties915">9.1.5. No Litigation:</label>
                            <textarea id="warranties915" rows="3" placeholder="No litigation">save as may be disclosed from time to time no litigation, arbitration, investigation or administrative proceedings is in progress or, to the Artist's best</textarea>
                        </div>
                    </div>

                    <!-- Add More Sections Button -->
                    <div class="form-group">
                        <button type="button" class="btn-secondary" onclick="addCustomSectionPage8()">+ Add Custom Section</button>
                    </div>

                    <!-- Custom Sections Container for Page 8 -->
                    <div id="customSectionsPage8Container"></div>
                </div>

                <!-- Page 9 - Warranties Continued & Confidential Information (Editable Content) -->
                <div class="contract-page">
                    <h3>Page 9 - Warranties Continued & Confidential Information</h3>
                    <p class="page-description">Edit the remaining warranties and confidential information sections below. Section headings are fixed, but all content is editable.</p>

                    <!-- Warranties Continued Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">9. WARRANTIES (Continued)</h4>
                        <div class="form-group">
                            <label for="warranties915Continued">9.1.5. No Litigation (Continued):</label>
                            <textarea id="warranties915Continued" rows="3" placeholder="No litigation continued">knowledge of having made all reasonable enquiries, threatened against it which is likely to have a material adverse effect on the Artist's ability to provide the Services; and</textarea>
                        </div>

                        <div class="form-group">
                            <label for="warranties916">9.1.6. Reasonable Care:</label>
                            <textarea id="warranties916" rows="2" placeholder="Reasonable care">the Artist will use reasonable care and skill in carrying out its obligations under this Agreement.</textarea>
                        </div>
                    </div>

                    <!-- Confidential Information Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">10. CONFIDENTIAL INFORMATION</h4>
                        <div class="form-group">
                            <label for="confidential101">10.1. Receiving Party Obligations:</label>
                            <textarea id="confidential101" rows="4" placeholder="Receiving party obligations">Each Party ("the Receiving Party") must treat and hold as confidential all information, which they may receive from the other party ("the Disclosing Party") or which becomes known to them concerning the Disclosing Party during the subsistence of this Agreement and any extension thereof.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="confidential102">10.2. Confidential Information Definition:</label>
                            <textarea id="confidential102" rows="1" placeholder="Confidential information definition">The confidential information of the Disclosing Party shall, without limitation, include:-</textarea>
                        </div>

                        <div class="form-group">
                            <label for="confidential1021">10.2.1. Material and Documentation:</label>
                            <textarea id="confidential1021" rows="2" placeholder="Material and documentation">All associated material and documentation, including information contained therein;</textarea>
                        </div>

                        <div class="form-group">
                            <label for="confidential1022">10.2.2. Information Relating to:</label>
                            <textarea id="confidential1022" rows="1" placeholder="Information relating to">All information relating to:-</textarea>
                        </div>

                        <div class="form-group">
                            <label for="confidential10221">10.2.2.1. Business Activities:</label>
                            <textarea id="confidential10221" rows="2" placeholder="Business activities">The Disclosing Party's business activities, products,services, customers and clients, as well as its technical knowledge and trade secrets;</textarea>
                        </div>

                        <div class="form-group">
                            <label for="confidential10222">10.2.2.2. Agreement Terms:</label>
                            <textarea id="confidential10222" rows="1" placeholder="Agreement terms">The terms and conditions of this Agreement.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="confidential103">10.3. Protection Measures:</label>
                            <textarea id="confidential103" rows="2" placeholder="Protection measures">The Receiving Party agrees that in order to protect the proprietary interests of the Disclosing Party in its confidential information:</textarea>
                        </div>

                        <div class="form-group">
                            <label for="confidential1031">10.3.1. Personnel Access:</label>
                            <textarea id="confidential1031" rows="2" placeholder="Personnel access">It will only make the confidential information available to those of its personnel who are actively involved in the delivery of agreed Services;</textarea>
                        </div>

                        <div class="form-group">
                            <label for="confidential1032">10.3.2. Security Procedures:</label>
                            <textarea id="confidential1032" rows="5" placeholder="Security procedures">It will initiate internal security procedures reasonably acceptable to the Disclosing Party to prevent unauthorised disclosure and will take all practical steps to impress upon those personnel who need to be given access to confidential information, the confidential nature thereof;</textarea>
                        </div>

                        <div class="form-group">
                            <label for="confidential1033">10.3.3. Personnel Disclosure Rights:</label>
                            <textarea id="confidential1033" rows="2" placeholder="Personnel disclosure rights">Subject to the right to make the confidential information available to their personnel under clause 10.3.1 above, they will not at any time, whether during</textarea>
                        </div>
                    </div>

                    <!-- Add More Sections Button -->
                    <div class="form-group">
                        <button type="button" class="btn-secondary" onclick="addCustomSectionPage9()">+ Add Custom Section</button>
                    </div>

                    <!-- Custom Sections Container for Page 9 -->
                    <div id="customSectionsPage9Container"></div>
                </div>

                <!-- Page 10 - Confidential Information Continued (Editable Content) -->
                <div class="contract-page">
                    <h3>Page 10 - Confidential Information Continued</h3>
                    <p class="page-description">Edit the remaining confidential information clauses below. Section headings are fixed, but all content is editable.</p>

                    <!-- Confidential Information Continued Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">10. CONFIDENTIAL INFORMATION (Continued)</h4>
                        <div class="form-group">
                            <label for="confidential1033Continued">10.3.3. Personnel Disclosure Rights (Continued):</label>
                            <textarea id="confidential1033Continued" rows="4" placeholder="Personnel disclosure rights continued">this Agreement or thereafter, either use any Confidential Information of the Disclosing Party or directly or indirectly disclose any Confidential Information of the Disclosing Party to third parties; and</textarea>
                        </div>

                        <div class="form-group">
                            <label for="confidential1034">10.3.4. Property Rights:</label>
                            <textarea id="confidential1034" rows="6" placeholder="Property rights">all written instructions, drawings, notes, memoranda and records of whatever nature relating to the confidential information of the Disclosing Party which have or will come into the possession of the Receiving Party and its Personnel, will be, and will at all times remain, the sole and absolute property of such Party and shall be promptly handed over to such Party when no longer required for the purposes of this Agreement.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="confidential104">10.4. Agreement Termination:</label>
                            <textarea id="confidential104" rows="3" placeholder="Agreement termination">Upon termination or expiry of this Agreement, the Receiving Party will deliver to the Disclosing Party, or at the Disclosing Party's option, destroy all originals and copies of the Disclosing Party's confidential information in its possession.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="confidential105">10.5. Information Exceptions:</label>
                            <textarea id="confidential105" rows="1" placeholder="Information exceptions">The foregoing obligations shall not apply to any information which:</textarea>
                        </div>

                        <div class="form-group">
                            <label for="confidential1051">10.5.1. Public Domain:</label>
                            <textarea id="confidential1051" rows="1" placeholder="Public domain">Is lawfully in the public domain at the time of disclosure;</textarea>
                        </div>

                        <div class="form-group">
                            <label for="confidential1052">10.5.2. Subsequent Public Domain:</label>
                            <textarea id="confidential1052" rows="2" placeholder="Subsequent public domain">Subsequently and lawfully becomes part of the public domain by publication or otherwise;</textarea>
                        </div>

                        <div class="form-group">
                            <label for="confidential1053">10.5.3. Alternative Source:</label>
                            <textarea id="confidential1053" rows="3" placeholder="Alternative source">Subsequently becomes available to the Receiving Party from a source other than the Disclosing Party, which source is lawfully entitled without any restriction on disclosure to disclose such Confidential Information; or</textarea>
                        </div>

                        <div class="form-group">
                            <label for="confidential1054">10.5.4. Legal Requirement:</label>
                            <textarea id="confidential1054" rows="2" placeholder="Legal requirement">is disclosed pursuant to a requirement or request by operation of law, regulation or court order; or</textarea>
                        </div>

                        <div class="form-group">
                            <label for="confidential1055">10.5.5. Written Permission:</label>
                            <textarea id="confidential1055" rows="1" placeholder="Written permission">is disclosed with the express written permission of the Parties.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="confidential106">10.6. Intellectual Property:</label>
                            <textarea id="confidential106" rows="2" placeholder="Intellectual property">Copyright of all materials developed shall remain the exclusive intellectual property of the Party that developed the material.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="confidential107">10.7. Professional Advisors:</label>
                            <textarea id="confidential107" rows="3" placeholder="Professional advisors">Nothing in this clause shall preclude the Parties from disclosing the confidential information to their professional advisors or financiers in the bona fide course of seeking finance, business and professional advice.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="confidential108">10.8. Indemnification:</label>
                            <textarea id="confidential108" rows="2" placeholder="Indemnification">the Client hereby indemnifies the Artist against any loss or damage, which the Client may suffer as a result of a breach of this clause by the Client or its personnel.</textarea>
                        </div>
                    </div>

                    <!-- Add More Sections Button -->
                    <div class="form-group">
                        <button type="button" class="btn-secondary" onclick="addCustomSectionPage10()">+ Add Custom Section</button>
                    </div>

                    <!-- Custom Sections Container for Page 10 -->
                    <div id="customSectionsPage10Container"></div>
                </div>

                <!-- Page 11 - Termination, Intellectual Property & Force Majeure (Editable Content) -->
                <div class="contract-page">
                    <h3>Page 11 - Termination, Intellectual Property & Force Majeure</h3>
                    <p class="page-description">Edit the termination, intellectual property, and force majeure sections below. Section headings are fixed, but all content is editable.</p>

                    <!-- Termination Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">11. TERMINATION</h4>
                        <div class="form-group">
                            <label for="termination111">11.1. Termination Notice:</label>
                            <textarea id="termination111" rows="2" placeholder="Termination notice">The Parties shall be entitled to terminate this Agreement upon fourteen (14) calendar day's written notice in the event that an event of default occurs.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="termination112">11.2. Early Termination (14 days):</label>
                            <textarea id="termination112" rows="2" placeholder="Early termination 14 days">Where the Client terminates the Agreement within fourteen (14) days from the date for the Performance Date, the Client's deposited amount shall be forfeited.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="termination113">11.3. Termination (14-30 days):</label>
                            <textarea id="termination113" rows="3" placeholder="Termination 14-30 days">Where the Client terminates the Agreement for a period exceeding fourteen (14) days but less than 30 days from the Performance Date, 25% of the Client's deposited amount shall be forfeited;</textarea>
                        </div>

                        <div class="form-group">
                            <label for="termination114">11.4. Termination (30+ days):</label>
                            <textarea id="termination114" rows="2" placeholder="Termination 30+ days">Where the Client terminates the Agreement more than 30 days from the Performance Date, 10% of the Client's deposited amount shall be forfeited.</textarea>
                        </div>
                    </div>

                    <!-- Intellectual Property Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">12. INTELLECTUAL PROPERTY</h4>
                        <div class="form-group">
                            <label for="intellectualProperty121">12.1. Ownership Rights:</label>
                            <textarea id="intellectualProperty121" rows="4" placeholder="Ownership rights">Each Party's own Intellectual Property Rights, including the Rights of Copyright shall remain the property of that Party and no other Party shall be entitled to use those Intellectual Property Rightsincluding the Rights of Copyright without first having obtained the requisite licence/s from the Party concerned.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="intellectualProperty122">12.2. Joint Development:</label>
                            <textarea id="intellectualProperty122" rows="4" placeholder="Joint development">Any Intellectual Property Rights including the Rights of Copyrightcreated in any joint developments or deliverables pursuant to this Agreement shall be owned by the Artist. To the extent that such Intellectual Property Rightsincluding the Rights of Copyright are registerable, such rights shall be registered in the name of the Artist.</textarea>
                        </div>
                    </div>

                    <!-- Force Majeure Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">13. FORCE MAJEURE</h4>
                        <div class="form-group">
                            <label for="forceMajeure131">13.1. Force Majeure Clause:</label>
                            <textarea id="forceMajeure131" rows="8" placeholder="Force majeure clause">Neither Party shall have any claim against the other Party arising from any failure or delay in the performance of any obligation of either Party under this Agreement caused by an act of force majeure such as acts of God, fire, flood, war, strike, lockout, industrial dispute, government action, laws or regulations, riots, terrorism or civil disturbance, defaults, delays or discontinuance on the part of independent contractors, suppliers, or other circumstances or factors beyond the reasonable control of either Party, and to the extent that the performance of obligations of either Party hereunder is delayed by virtue of the aforegoing, any period stipulated for any such performance shall be reasonably extended.</textarea>
                        </div>
                    </div>

                    <!-- Add More Sections Button -->
                    <div class="form-group">
                        <button type="button" class="btn-secondary" onclick="addCustomSectionPage11()">+ Add Custom Section</button>
                    </div>

                    <!-- Custom Sections Container for Page 11 -->
                    <div id="customSectionsPage11Container"></div>
                </div>

                <!-- Page 12 - Force Majeure Continued, Addresses & Notices, Variation (Editable Content) -->
                <div class="contract-page">
                    <h3>Page 12 - Force Majeure Continued, Addresses & Notices, Variation</h3>
                    <p class="page-description">Edit the remaining force majeure, addresses & notices, and variation sections below. Section headings are fixed, but all content is editable.</p>

                    <!-- Force Majeure Continued Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">13. FORCE MAJEURE (Continued)</h4>
                        <div class="form-group">
                            <label for="forceMajeure132">13.2. Performance Resumption:</label>
                            <textarea id="forceMajeure132" rows="6" placeholder="Performance resumption">Each Party will take all reasonable steps by whatever lawful means that are available, to resume full performance as soon as practical and will seek agreement to modification of the relevant provisions of this Agreement in order to accommodate the new circumstances caused by the act of force majeure. If a Party fails to agree to such modifications proposed by the other Party within 10 days of the act of force majeure first occurring, either Party may thereafter terminate this Agreement with immediate notice.</textarea>
                        </div>
                    </div>

                    <!-- Addresses and Notices Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">14. ADDRESSES AND NOTICES</h4>
                        <div class="form-group">
                            <label for="addresses141">14.1. Domicilium Citandi:</label>
                            <textarea id="addresses141" rows="3" placeholder="Domicilium citandi">For the purposes of this Agreement, including the giving of notices and the serving of legal process (as applicable), the Parties choose domicilium citandi executandi ("domicilium") at:</textarea>
                        </div>

                        <div class="form-group">
                            <label for="artistAddress1411">14.1.1. Artist Details:</label>
                            <textarea id="artistAddress1411" rows="1" placeholder="Artist name">Artist: BONGO MAFFIN    Company: Bongo Music (PTY) Ltd</textarea>
                        </div>

                        <div class="form-group">
                            <label for="artistPhysicalAddress">Physical Address:</label>
                            <textarea id="artistPhysicalAddress" rows="1" placeholder="Artist physical address">16, 21ST Street, Parkhurst.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="artistPhone">Johannesburg Telephone:</label>
                            <input type="text" id="artistPhone" placeholder="Artist phone number" value="0732205971">
                        </div>

                        <div class="form-group">
                            <label for="artistEmail">E-mail Address:</label>
                            <input type="email" id="artistEmail" placeholder="Artist email address" value="<EMAIL>">
                        </div>

                        <div class="form-group">
                            <label for="clientDetails1412">14.1.2. Client Details:</label>
                            <textarea id="clientDetails1412" rows="1" placeholder="Client details">Client: Morgan Ross    Company: UMG LIVE</textarea>
                        </div>

                        <div class="form-group">
                            <label for="clientPhysicalAddress">Client Physical Address:</label>
                            <textarea id="clientPhysicalAddress" rows="4" placeholder="Client physical address">2nd Floor Design District
Keyes Avenue
Rosebank
2196</textarea>
                        </div>

                        <div class="form-group">
                            <label for="clientEmail">Client E-mail Address:</label>
                            <input type="email" id="clientEmail" placeholder="Client email address" value="<EMAIL>">
                        </div>
                    </div>

                    <!-- Variation, Cancellation and Waiver Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">15. VARIATION, CANCELLATION AND WAIVER</h4>
                        <div class="form-group">
                            <label for="variation15">15.1. Contract Variation:</label>
                            <textarea id="variation15" rows="3" placeholder="Contract variation">No contract varying, adding to, deleting from or cancelling this Agreement, and no waiver of any right under this Agreement, shall be effective unless reduced to writing and signed by or on behalf of the Parties.</textarea>
                        </div>
                    </div>

                    <!-- Add More Sections Button -->
                    <div class="form-group">
                        <button type="button" class="btn-secondary" onclick="addCustomSectionPage12()">+ Add Custom Section</button>
                    </div>

                    <!-- Custom Sections Container for Page 12 -->
                    <div id="customSectionsPage12Container"></div>
                </div>

                <!-- Page 13 - Entire Agreement, Severability & Dispute Resolution (Editable Content) -->
                <div class="contract-page">
                    <h3>Page 13 - Entire Agreement, Severability & Dispute Resolution</h3>
                    <p class="page-description">Edit the entire agreement, severability, and dispute resolution sections below. Section headings are fixed, but all content is editable.</p>

                    <!-- Entire Agreement Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">16. ENTIRE AGREEMENT</h4>
                        <div class="form-group">
                            <label for="entireAgreement161">16.1. Complete Agreement:</label>
                            <textarea id="entireAgreement161" rows="4" placeholder="Complete agreement">Except where expressly provided otherwise in this Agreement, this Agreement constitutes the entire Agreement between the Parties in connection with its subject matter and supersedes all prior representations, communications, negotiations and understandings concerning the subject matter of this Agreement.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="entireAgreement162">16.2. Party Acknowledgments:</label>
                            <textarea id="entireAgreement162" rows="1" placeholder="Party acknowledgments">Each of the Parties acknowledges that:</textarea>
                        </div>

                        <div class="form-group">
                            <label for="entireAgreement1621">16.2.1. No Reliance on External Representations:</label>
                            <textarea id="entireAgreement1621" rows="7" placeholder="No reliance on external representations">it does not enter into this Agreement on the basis of and does not rely, and has not relied, upon any statement, representation (whether negligent or innocent) or warranty or other provision (in any case whether oral, written, express or implied) made or agreed to by any person (whether a Party to this Agreement or not) except those expressly contained in or referred to in this Agreement, and the only remedy available in respect of any misrepresentation or untrue statement made to it shall be a remedy available under this Agreement; and</textarea>
                        </div>

                        <div class="form-group">
                            <label for="entireAgreement1622">16.2.2. Fraud Exception:</label>
                            <textarea id="entireAgreement1622" rows="4" placeholder="Fraud exception">this clause shall not apply to any statement, representation or warranty made fraudulently, or to any provision of this Agreement which was induced by fraud, for which the remedies available shall be all those available under any Law governing this Agreement.</textarea>
                        </div>
                    </div>

                    <!-- Severability Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">17. SEVERABILITY</h4>
                        <div class="form-group">
                            <label for="severability17">17.1. Provision Interpretation:</label>
                            <textarea id="severability17" rows="5" placeholder="Provision interpretation">Whenever possible, each provision of this Agreement shall be interpreted in a manner which makes it effective and valid under any Law, but if any provision of this Agreement is held to be illegal, invalid or unenforceable under any Law, that illegality, invalidity or unenforceability shall not affect the other provisions of this Agreement, all of which shall remain in full force.</textarea>
                        </div>
                    </div>

                    <!-- Dispute Resolution Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">18. DISPUTE RESOLUTION</h4>
                        <div class="form-group">
                            <label for="disputeResolution181">18.1. Dispute Notification:</label>
                            <textarea id="disputeResolution181" rows="4" placeholder="Dispute notification">Should a dispute arise pursuant to this Agreement, then the Party declaring the dispute shall notify the other relevant Party in writing and give such other party ten (10) days within which to reply to the allegations set out in such notice. All efforts will be made to resolve the dispute amicably. If the dispute cannot be resolved</textarea>
                        </div>
                    </div>

                    <!-- Add More Sections Button -->
                    <div class="form-group">
                        <button type="button" class="btn-secondary" onclick="addCustomSectionPage13()">+ Add Custom Section</button>
                    </div>

                    <!-- Custom Sections Container for Page 13 -->
                    <div id="customSectionsPage13Container"></div>
                </div>

                <!-- Page 14 - Dispute Resolution Continued & Governing Law (Editable Content) -->
                <div class="contract-page">
                    <h3>Page 14 - Dispute Resolution Continued & Governing Law</h3>
                    <p class="page-description">Edit the remaining dispute resolution and governing law sections below. Section headings are fixed, but all content is editable.</p>

                    <!-- Dispute Resolution Continued Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">18. DISPUTE RESOLUTION (Continued)</h4>
                        <div class="form-group">
                            <label for="disputeResolution181Continued">18.1. Dispute Notification (Continued):</label>
                            <textarea id="disputeResolution181Continued" rows="2" placeholder="Dispute notification continued">within twenty (20) days of notification of the existence of the dispute, then any Party to such dispute may require that such dispute shall be referred to arbitration.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="disputeResolution182">18.2. Arbitrator Appointment:</label>
                            <textarea id="disputeResolution182" rows="3" placeholder="Arbitrator appointment">The arbitrator shall be appointed by the Parties, and failing agreement, shall be nominated by the President of the Law Society of the Northern Province at the time. The arbitration shall be held in Gauteng.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="disputeResolution183">18.3. Arbitration Procedures:</label>
                            <textarea id="disputeResolution183" rows="5" placeholder="Arbitration procedures">The arbitration shall be held in accordance with the formalities and procedures settled by the arbitrator, which shall be in an informal and summary manner, that is, it shall not be necessary to observe or carry out either the usual formalities or procedure or the strict rules of evidence, and the arbitration shall be otherwise subject to the provisions of the Arbitration Act 1965, as amended.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="disputeResolution184">18.4. Arbitrator Investigation Powers:</label>
                            <textarea id="disputeResolution184" rows="6" placeholder="Arbitrator investigation powers">The arbitrator shall be entitled to investigate or cause to be investigated any matter, fact or thing which he/she considers necessary or desirable in connection with any matter referred to him/her for decision, to decide the matters submitted to him/her according to what he/she considers just and equitable in all the circumstances, having regard to the purpose of this Agreement and make such award, as he/she in his discretion may deem fit and appropriate.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="disputeResolution185">18.5. Arbitration Timeline:</label>
                            <textarea id="disputeResolution185" rows="3" placeholder="Arbitration timeline">The arbitration shall be held as expeditiously as possible after such arbitration is demanded, with a view to such arbitration being completed within 20 (twenty) business days after such arbitration has been so demanded.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="disputeResolution186">18.6. Clause Severability:</label>
                            <textarea id="disputeResolution186" rows="2" placeholder="Clause severability">This clause is severable from the rest of this Agreement and shall therefore remain in effect even if this Agreement is terminated.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="disputeResolution187">18.7. Interim Relief:</label>
                            <textarea id="disputeResolution187" rows="3" placeholder="Interim relief">This clause shall not preclude any Party from obtaining interim relief on an urgent basis from a court of competent jurisdiction pending the decision of the arbitrator or enforcing any award made by the arbitrator under this clause.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="disputeResolution188">18.8. Award Binding:</label>
                            <textarea id="disputeResolution188" rows="2" placeholder="Award binding">The Parties hereby confirm that they shall abide by the terms of any arbitral award, the terms of which shall be final and binding on the Parties hereto.</textarea>
                        </div>
                    </div>

                    <!-- Governing Law Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">19. GOVERNING LAW</h4>
                        <div class="form-group">
                            <label for="governingLaw191">19.1. Applicable Law:</label>
                            <textarea id="governingLaw191" rows="3" placeholder="Applicable law">This Agreement is exclusively governed by and construed in accordance with the laws of the Republic of South Africa and is subject to the jurisdiction of the courts of the Republic of South Africa.</textarea>
                        </div>

                        <div class="form-group">
                            <label for="governingLaw192">19.2. Change of Law:</label>
                            <textarea id="governingLaw192" rows="8" placeholder="Change of law">Change of Law: In this Agreement, unless the context otherwise requires, references to a statutory provision include references to that statutory provision as from time to time amended, extended or re-enacted and any regulations made under it, provided that in the event that the amendment, extension or re-enactment of any statutory provision or introduction of any new statutory provision has a material impact on the obligations of either Party, the Parties will negotiate in good faith to agree such amendments to this Agreement as may be appropriate in the circumstances. If, within a reasonable period of time, the Parties cannot reach agreement on the nature of the changes required or on modification of fees, deliverables, warranties, or other terms and conditions, either Party may seek to have the matter determined in accordance with clause 18 above.</textarea>
                        </div>
                    </div>

                    <!-- Add More Sections Button -->
                    <div class="form-group">
                        <button type="button" class="btn-secondary" onclick="addCustomSectionPage14()">+ Add Custom Section</button>
                    </div>

                    <!-- Custom Sections Container for Page 14 -->
                    <div id="customSectionsPage14Container"></div>
                </div>

                <!-- Page 15 - Fees & Costs, Indemnity & Co-operation (Editable Content) -->
                <div class="contract-page">
                    <h3>Page 15 - Fees & Costs, Indemnity & Co-operation</h3>
                    <p class="page-description">Edit the fees & costs, indemnity, and co-operation sections below. Section headings are fixed, but all content is editable.</p>

                    <!-- Fees and Costs Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">20. FEES AND COSTS</h4>
                        <div class="form-group">
                            <label for="feesAndCosts20">20.1. Legal Costs:</label>
                            <textarea id="feesAndCosts20" rows="2" placeholder="Legal costs">Each Party shall bear its own legal costs and disbursements of and incidental to the negotiation, preparation, settling, signing and implementation of this Agreement.</textarea>
                        </div>
                    </div>

                    <!-- Indemnity Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">21. INDEMNITY</h4>
                        <div class="form-group">
                            <label for="indemnity21">21.1. Client Indemnification:</label>
                            <textarea id="indemnity21" rows="5" placeholder="Client indemnification">The Client indemnifies, holds harmless and defends the Artist, its officers, employees, agents and representatives, from and against any claim, liability, loss or expense arising from any injury, loss or damage (including without limitation injury, loss or damage incurred the Artist, its officers, employees, agents or representatives) arising directly through the acts or omissions of the Artist in relation to the Services to be provided in terms of this Agreement.</textarea>
                        </div>
                    </div>

                    <!-- Co-operation Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">22. CO-OPERATION</h4>
                        <div class="form-group">
                            <label for="cooperation22">22.1. Mutual Co-operation:</label>
                            <textarea id="cooperation22" rows="4" placeholder="Mutual co-operation">Each of the Parties undertakes at all times to do all such things, perform all such acts and take all such steps, and to procure the doing of all such things, within its power and control, as may be open to it and necessary for and incidental to the putting into effect or maintenance of the terms and conditions of this Agreement.</textarea>
                        </div>
                    </div>

                    <!-- Add More Sections Button -->
                    <div class="form-group">
                        <button type="button" class="btn-secondary" onclick="addCustomSectionPage15()">+ Add Custom Section</button>
                    </div>

                    <!-- Custom Sections Container for Page 15 -->
                    <div id="customSectionsPage15Container"></div>
                </div>

                <!-- Page 16 - Cession & Delegation, Third Person Stipulation & Counterparts (Editable Content) -->
                <div class="contract-page">
                    <h3>Page 16 - Cession & Delegation, Third Person Stipulation & Counterparts</h3>
                    <p class="page-description">Edit the final legal provisions below. Section headings are fixed, but all content is editable.</p>

                    <!-- Cession and Delegation Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">23. CESSION AND DELEGATION</h4>
                        <div class="form-group">
                            <label for="cessionDelegation23">23.1. Rights and Obligations Transfer:</label>
                            <textarea id="cessionDelegation23" rows="3" placeholder="Cession and delegation">A Party may not cede any or all of that Party's rights or delegate any or all of that Party's obligations under this Agreement without the prior written consent of the other Party.</textarea>
                        </div>
                    </div>

                    <!-- No Stipulation for Third Person Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">24. NO STIPULATION FOR THE BENEFIT OF A THIRD PERSON</h4>
                        <div class="form-group">
                            <label for="noStipulation24">24.1. Third Party Benefits:</label>
                            <textarea id="noStipulation24" rows="3" placeholder="No stipulation for third person">Save as is expressly provided for in this Agreement, no provision of this Agreement constitutes a stipulation for the benefit of a third person (i.e. a stipulation alteri) which, if accepted by the person, would bind any Party in favour of that person.</textarea>
                        </div>
                    </div>

                    <!-- Counterparts Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">25. COUNTERPARTS</h4>
                        <div class="form-group">
                            <label for="counterparts25">25.1. Execution in Counterparts:</label>
                            <textarea id="counterparts25" rows="4" placeholder="Counterparts">This Agreement may be executed in any number of identical counterparts, all of which when taken together shall constitute one Agreement. Any single counterpart or a set of counterparts taken together, which, in either case, are executed by the Parties, shall constitute a full original of this Agreement for all purposes.</textarea>
                        </div>
                    </div>

                    <!-- Add More Sections Button -->
                    <div class="form-group">
                        <button type="button" class="btn-secondary" onclick="addCustomSectionPage16()">+ Add Custom Section</button>
                    </div>

                    <!-- Custom Sections Container for Page 16 -->
                    <div id="customSectionsPage16Container"></div>
                </div>

                <!-- Page 17 - Signatures (Editable Content) -->
                <div class="contract-page">
                    <h3>Page 17 - Signatures</h3>
                    <p class="page-description">Edit the signature section below. Section headings are fixed, but all content is editable.</p>

                    <!-- Signatures Section -->
                    <div class="definition-group">
                        <h4 class="definition-term">SIGNATURES</h4>

                        <!-- Execution Statement -->
                        <div class="form-group">
                            <label for="executionStatement">Execution Statement:</label>
                            <textarea id="executionStatement" rows="2" placeholder="Execution statement">IN WITNESS WHEREOF the Parties have executed this Agreement on the date first written above.</textarea>
                        </div>

                        <!-- Artist Signature Block -->
                        <div class="form-group">
                            <label for="artistSignatureBlock">Artist Signature Block:</label>
                            <textarea id="artistSignatureBlock" rows="8" placeholder="Artist signature block">SIGNED at _________________________ on this _____ day of _____________, 20___

ARTIST/BAND:

_________________________________
Signature

_________________________________
Name (Print)

_________________________________
Capacity

_________________________________
Date</textarea>
                        </div>

                        <!-- Client Signature Block -->
                        <div class="form-group">
                            <label for="clientSignatureBlock">Client Signature Block:</label>
                            <textarea id="clientSignatureBlock" rows="8" placeholder="Client signature block">SIGNED at _________________________ on this _____ day of _____________, 20___

CLIENT/AGENT:

_________________________________
Signature

_________________________________
Name (Print)

_________________________________
Capacity

_________________________________
Date</textarea>
                        </div>

                        <!-- Witness Section -->
                        <div class="form-group">
                            <label for="witnessSection">Witness Section:</label>
                            <textarea id="witnessSection" rows="6" placeholder="Witness section">WITNESSES:

1. _________________________________
   Signature

   _________________________________
   Name (Print)

2. _________________________________
   Signature

   _________________________________
   Name (Print)</textarea>
                        </div>

                        <!-- Digital Signature Integration -->
                        <div class="form-group signature-integration">
                            <h4 style="color: var(--primary-color); margin-bottom: 15px;">🖊️ Digital Signature Integration</h4>
                            <p style="color: #666; margin-bottom: 15px;">Use your saved signatures from Signature Tools in this document:</p>

                            <!-- Artist Signature Selection -->
                            <div class="signature-selection-group">
                                <label for="artistSignatureSelect">Artist/Band Digital Signature:</label>
                                <div class="signature-selector">
                                    <select id="artistSignatureSelect" onchange="previewSelectedSignature('artist')">
                                        <option value="">Select a saved signature...</option>
                                    </select>
                                    <button type="button" class="btn-secondary" onclick="openSignatureTools()">+ Create New Signature</button>
                                </div>
                                <div id="artistSignaturePreview" class="signature-preview-container" style="display: none;">
                                    <label>Preview:</label>
                                    <div class="signature-preview-box">
                                        <img id="artistSignatureImg" src="" alt="Artist Signature Preview">
                                    </div>
                                </div>
                            </div>

                            <!-- Client Signature Selection -->
                            <div class="signature-selection-group">
                                <label for="clientSignatureSelect">Client/Agent Digital Signature:</label>
                                <div class="signature-selector">
                                    <select id="clientSignatureSelect" onchange="previewSelectedSignature('client')">
                                        <option value="">Select a saved signature...</option>
                                    </select>
                                    <button type="button" class="btn-secondary" onclick="openSignatureTools()">+ Create New Signature</button>
                                </div>
                                <div id="clientSignaturePreview" class="signature-preview-container" style="display: none;">
                                    <label>Preview:</label>
                                    <div class="signature-preview-box">
                                        <img id="clientSignatureImg" src="" alt="Client Signature Preview">
                                    </div>
                                </div>
                            </div>

                            <!-- Signature Options -->
                            <div class="signature-options">
                                <label>
                                    <input type="checkbox" id="includeDigitalSignatures" checked onchange="toggleSignatureInclusion()">
                                    Include digital signatures in PDF export
                                </label>
                                <small style="color: #666; display: block; margin-top: 5px;">
                                    When enabled, selected digital signatures will be automatically placed in the signature blocks
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Add More Sections Button -->
                    <div class="form-group">
                        <button type="button" class="btn-secondary" onclick="addCustomSectionPage17()">+ Add Custom Section</button>
                    </div>

                    <!-- Custom Sections Container for Page 17 -->
                    <div id="customSectionsPage17Container"></div>
                </div>
            </div>
            <!-- End Artist Agreement Section -->

            <!-- Preview and Generate Buttons -->
            <div class="button-group" style="display: flex; gap: 10px; margin-bottom: 20px;">
                <button type="button" id="previewInvoicePDF" class="btn-secondary" onclick="previewInvoice(event); return false;" style="flex: 1;">
                    <span>👁️ Preview Invoice</span>
                </button>
                <button type="button" id="previewAgreementPDF" class="btn-secondary" onclick="previewArtistAgreement(event); return false;" style="flex: 1;">
                    <span>👁️ Preview Agreement</span>
                </button>
                <button type="button" class="btn-outline" onclick="showDocumentImport()" style="font-size: 12px; padding: 8px 12px; min-width: 120px;" title="Import and edit existing documents">
                    📥 Import Document
                </button>
                <button type="button" class="btn-outline" onclick="refreshCompanyLogo()" style="font-size: 12px; padding: 8px 12px; min-width: 120px;" title="Refresh company logo from dashboard">
                    🔄 Refresh Logo
                </button>
                <button type="submit" id="generatePDF" style="flex: 1;"><span>Generate Invoice</span></button>
            </div>

            <!-- Quick Sharing Options -->
            <div id="quickSharingOptions" class="quick-sharing-section" style="display: none;">
                <div class="sharing-divider">
                    <span class="divider-text">📤 Share Document</span>
                </div>

                <div class="quick-sharing-buttons">
                    <button type="button" class="quick-share-email-btn" onclick="showQuickEmailShare()">
                        📧 Email
                    </button>
                    <button type="button" class="quick-share-whatsapp-btn" onclick="showQuickWhatsAppShare()">
                        💬 WhatsApp
                    </button>
                    <button type="button" class="quick-share-more-btn" onclick="showDocumentSharing()">
                        🔗 More Options
                    </button>
                </div>

                <!-- Quick Email Form -->
                <div id="quickEmailForm" class="quick-form" style="display: none;">
                    <div class="quick-form-header">
                        <h4>📧 Quick Email Share</h4>
                        <button type="button" class="close-quick-form" onclick="hideQuickEmailShare()">×</button>
                    </div>
                    <div class="quick-form-content">
                        <div class="form-row">
                            <input type="email" id="quickRecipientEmail" placeholder="Recipient email address">
                        </div>
                        <div class="form-row">
                            <input type="text" id="quickEmailSubject" placeholder="Email subject">
                        </div>
                        <div class="form-row">
                            <textarea id="quickEmailMessage" rows="3" placeholder="Your message..."></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn-send-email" onclick="sendQuickEmail()">
                                📧 Send Email
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Quick WhatsApp Form -->
                <div id="quickWhatsAppForm" class="quick-form" style="display: none;">
                    <div class="quick-form-header">
                        <h4>💬 Quick WhatsApp Share</h4>
                        <button type="button" class="close-quick-form" onclick="hideQuickWhatsAppShare()">×</button>
                    </div>
                    <div class="quick-form-content">
                        <div class="form-row">
                            <input type="tel" id="quickWhatsAppNumber" placeholder="Phone number (with country code)">
                            <small>Optional: Include country code (e.g., +1, +27, +44)</small>
                        </div>
                        <div class="form-row">
                            <textarea id="quickWhatsAppMessage" rows="3" placeholder="Your message..."></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn-send-whatsapp" onclick="sendQuickWhatsApp()">
                                💬 Open WhatsApp
                            </button>
                            <button type="button" class="btn-send-whatsapp-web" onclick="sendQuickWhatsAppWeb()">
                                🌐 WhatsApp Web
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Document Import Modal -->
    <div id="documentImportModal" class="modal">
        <div class="modal-content document-import-content">
            <div class="modal-header">
                <h2>📥 Import Document</h2>
                <span class="close" onclick="closeDocumentImport()">&times;</span>
            </div>
            <div class="document-import-body">
                <div class="import-layout">
                    <!-- Left Panel: Upload and Tools -->
                    <div class="import-left-panel">
                        <div class="import-section">
                            <h3>📄 Upload Document</h3>
                            <div class="upload-area" id="documentUploadArea">
                                <input type="file" id="documentUpload" accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif,.bmp,image/*,application/pdf,text/plain" style="display: none;">
                                <div class="upload-placeholder" onclick="document.getElementById('documentUpload').click()">
                                    <div class="upload-icon">📁</div>
                                    <h4>Click to Upload Document</h4>
                                    <p>Supported formats: PDF, DOC, DOCX, TXT, JPG, PNG, GIF</p>
                                    <p class="file-size-limit">Max file size: 10MB</p>
                                    <button type="button" onclick="createTestDocument()" style="margin-top: 10px; padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                        🧪 Create Test Document
                                    </button>
                                </div>
                            </div>
                            <div id="uploadedFileInfo" class="uploaded-file-info" style="display: none;">
                                <div class="file-details">
                                    <span class="file-icon">📄</span>
                                    <div class="file-info">
                                        <div class="file-name"></div>
                                        <div class="file-size"></div>
                                    </div>
                                    <button type="button" class="remove-file-btn" onclick="removeUploadedFile()">🗑️</button>
                                </div>
                            </div>
                        </div>

                        <div class="import-section">
                            <h3>🔧 Available Tools</h3>
                            <div class="tool-grid">
                                <div class="tool-card" onclick="enableSignatureMode()">
                                    <div class="tool-icon">✍️</div>
                                    <h4>Add Signature</h4>
                                    <p>Drag saved signatures</p>
                                </div>
                                <div class="tool-card" onclick="enableTextMode()">
                                    <div class="tool-icon">📝</div>
                                    <h4>Add Text</h4>
                                    <p>Click to add text</p>
                                </div>
                                <div class="tool-card" onclick="enableStampMode()">
                                    <div class="tool-icon">🔖</div>
                                    <h4>Add Stamp</h4>
                                    <p>Click to add stamps</p>
                                </div>
                                <div class="tool-card" onclick="enableDateMode()">
                                    <div class="tool-icon">📅</div>
                                    <h4>Add Date</h4>
                                    <p>Click to add date</p>
                                </div>
                            </div>
                        </div>

                        <!-- Signature Library -->
                        <div class="import-section" id="signatureLibrarySection" style="display: none;">
                            <h3>💾 Saved Signatures</h3>
                            <div id="signatureLibrary" class="signature-library">
                                <p class="no-signatures">No saved signatures. Create some in Signature Tools first.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Right Panel: Document Preview -->
                    <div class="import-right-panel">
                        <div class="document-preview-section">
                            <h3>👁️ Document Preview</h3>
                            <div id="documentPreviewContainer" class="document-preview-container">
                                <div class="preview-placeholder">
                                    <div class="preview-icon">📄</div>
                                    <h4>Upload a document to see preview</h4>
                                    <p>The document will appear here for editing</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn-secondary" onclick="closeDocumentImport()">Cancel</button>
                    <button type="button" class="btn-primary" onclick="processDocument()" id="processDocumentBtn" disabled>
                        Process Document
                    </button>
                    <button type="button" class="btn-success" onclick="shareDocument()" id="shareDocumentBtn" disabled style="background: #28a745;">
                        📤 Share Document
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- PDF Preview Modal -->
    <div id="pdfPreviewModal" class="modal">
        <div class="modal-content pdf-preview-content">
            <div class="modal-header">
                <h2>PDF Preview</h2>
                <span class="close-pdf-preview">&times;</span>
            </div>
            <div class="pdf-preview-container">
                <iframe id="pdfPreviewFrame" frameborder="0"></iframe>
            </div>
            <div class="pdf-actions">
                <button id="downloadPdf" class="download-btn"><span>📥 Download PDF</span></button>
                <button id="sharePdf" class="share-btn"><span>📤 Share Document</span></button>
            </div>
        </div>
    </div>

    <!-- Document Sharing Modal -->
    <div id="documentSharingModal" class="modal">
        <div class="modal-content sharing-modal-content">
            <div class="modal-header">
                <h2>📤 Share Document</h2>
                <span class="close-sharing" onclick="closeDocumentSharing()">&times;</span>
            </div>

            <div class="sharing-container">
                <!-- Document Preview Section -->
                <div class="sharing-section">
                    <h3>📄 Document Information</h3>
                    <div class="document-info-container">
                        <div id="sharingDocumentPreview" class="document-preview">
                            <div class="document-icon">📄</div>
                            <div class="document-details">
                                <div class="document-name" id="sharingDocumentName">Document.pdf</div>
                                <div class="document-type" id="sharingDocumentType">PDF Document</div>
                                <div class="document-size" id="sharingDocumentSize">Calculating...</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Email Sharing Section -->
                <div class="sharing-section">
                    <h3>📧 Email Sharing</h3>
                    <div class="email-sharing-container">
                        <div class="form-group">
                            <label for="recipientEmail">Recipient Email Address</label>
                            <input type="email" id="recipientEmail" placeholder="<EMAIL>" required>
                        </div>
                        <div class="form-group">
                            <label for="emailSubject">Subject</label>
                            <input type="text" id="emailSubject" placeholder="Document from [Your Company]">
                        </div>
                        <div class="form-group">
                            <label for="emailMessage">Message</label>
                            <textarea id="emailMessage" rows="4" placeholder="Hi,

Please find the attached document.

Best regards,
[Your Name]"></textarea>
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="sendCopy">
                                <span class="checkmark"></span>
                                Send a copy to myself
                            </label>
                        </div>
                        <button type="button" class="btn-email" onclick="shareViaEmail()">
                            📧 Send Email
                        </button>
                    </div>
                </div>

                <!-- WhatsApp Sharing Section -->
                <div class="sharing-section">
                    <h3>💬 WhatsApp Sharing</h3>
                    <div class="whatsapp-sharing-container">
                        <div class="form-group">
                            <label for="whatsappNumber">Phone Number (with country code)</label>
                            <input type="tel" id="whatsappNumber" placeholder="+**********">
                            <small>Include country code (e.g., +1 for US, +27 for SA, +44 for UK)</small>
                        </div>
                        <div class="form-group">
                            <label for="whatsappMessage">Message</label>
                            <textarea id="whatsappMessage" rows="3" placeholder="Hi! Please find the attached document..."></textarea>
                        </div>
                        <div class="whatsapp-options">
                            <button type="button" class="btn-whatsapp" onclick="shareViaWhatsApp()">
                                💬 Share on WhatsApp
                            </button>
                            <button type="button" class="btn-whatsapp-web" onclick="shareViaWhatsAppWeb()">
                                🌐 WhatsApp Web
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Quick Share Options -->
                <div class="sharing-section">
                    <h3>🔗 Quick Share Options</h3>
                    <div class="quick-share-container">
                        <button type="button" class="quick-share-btn" onclick="copyDocumentLink()">
                            🔗 Copy Link
                        </button>
                        <button type="button" class="quick-share-btn" onclick="shareViaSMS()">
                            📱 SMS
                        </button>
                        <button type="button" class="quick-share-btn" onclick="shareViaLinkedIn()">
                            💼 LinkedIn
                        </button>
                        <button type="button" class="quick-share-btn" onclick="shareViaTelegram()">
                            ✈️ Telegram
                        </button>
                    </div>
                </div>

                <!-- Sharing Instructions -->
                <div class="sharing-section">
                    <h3>ℹ️ Sharing Instructions</h3>
                    <div class="sharing-instructions">
                        <div class="instruction-item">
                            <span class="instruction-icon">📧</span>
                            <span class="instruction-text">Email: Opens your default email client with the document details</span>
                        </div>
                        <div class="instruction-item">
                            <span class="instruction-icon">💬</span>
                            <span class="instruction-text">WhatsApp: Opens WhatsApp with pre-filled message (attach PDF manually)</span>
                        </div>
                        <div class="instruction-item">
                            <span class="instruction-icon">🔗</span>
                            <span class="instruction-text">Copy Link: Copies document information to clipboard</span>
                        </div>
                        <div class="instruction-item">
                            <span class="instruction-icon">📱</span>
                            <span class="instruction-text">Other platforms: Opens respective apps with document details</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeDocumentSharing()">Close</button>
            </div>
        </div>
    </div>

    <!-- Template Manager Modal -->
    <div id="templateModal" class="modal">
        <div class="modal-content template-modal-content">
            <div class="modal-header">
                <h2>Template Manager</h2>
                <span class="close-template">&times;</span>
            </div>
            <div class="template-tabs">
                <button class="tab-btn active" data-tab="saved-templates">Saved Templates</button>
                <button class="tab-btn" data-tab="create-template">Create New</button>
                <button class="tab-btn" data-tab="import-export">Import/Export</button>
            </div>

            <!-- Saved Templates Tab -->
            <div class="tab-content" id="saved-templates">
                <div class="template-grid" id="templateGrid">
                    <!-- Template cards will be added here dynamically -->
                </div>
                <div class="no-templates-message" id="noTemplatesMessage">
                    <p>You don't have any saved templates yet. Create your first template to get started!</p>
                </div>
            </div>

            <!-- Create New Template Tab -->
            <div class="tab-content" id="create-template" style="display: none;">
                <form id="templateForm">
                    <div class="form-group">
                        <label for="templateName">Template Name:</label>
                        <input type="text" id="templateName" required placeholder="e.g., My Professional Template">
                    </div>
                    <div class="form-group">
                        <label for="templateDescription">Description (optional):</label>
                        <textarea id="templateDescription" rows="2" placeholder="Brief description of this template"></textarea>
                    </div>

                    <div class="template-settings">
                        <h3>Appearance</h3>
                        <div class="form-group">
                            <label for="templateColorTheme">Color Theme:</label>
                            <select id="templateColorTheme">
                                <option value="default">Default (Blue)</option>
                                <option value="green">Green</option>
                                <option value="purple">Purple</option>
                                <option value="orange">Orange</option>
                                <option value="teal">Teal</option>
                                <option value="custom">Custom Color</option>
                            </select>
                        </div>

                        <div id="customColorControls" style="display: none;">
                            <div class="form-group">
                                <label for="templateCustomColor">Custom Color:</label>
                                <input type="color" id="templateCustomColor" value="#3498db">
                                <span class="color-preview" id="templateColorPreview"></span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="templateLayout">Layout Style:</label>
                            <select id="templateLayout">
                                <option value="standard">Standard</option>
                                <option value="modern">Modern</option>
                                <option value="classic">Classic</option>
                                <option value="compact">Compact</option>
                                <option value="detailed">Detailed</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="templateFontHeading">Heading Font:</label>
                            <select id="templateFontHeading">
                                <option value="Montserrat">Montserrat</option>
                                <option value="Arial">Arial</option>
                                <option value="Helvetica">Helvetica</option>
                                <option value="Times New Roman">Times New Roman</option>
                                <option value="Georgia">Georgia</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="templateFontBody">Body Font:</label>
                            <select id="templateFontBody">
                                <option value="Open Sans">Open Sans</option>
                                <option value="Arial">Arial</option>
                                <option value="Helvetica">Helvetica</option>
                                <option value="Verdana">Verdana</option>
                                <option value="Georgia">Georgia</option>
                            </select>
                        </div>
                    </div>

                    <div class="template-preview">
                        <h3>Preview</h3>
                        <div class="template-preview-container" id="templatePreview">
                            <!-- Preview will be rendered here -->
                        </div>
                    </div>

                    <div class="template-actions">
                        <button type="submit" class="save-template-btn"><span>Save Template</span></button>
                        <button type="button" class="reset-template-btn"><span>Reset</span></button>
                    </div>
                </form>
            </div>

            <!-- Import/Export Tab -->
            <div class="tab-content" id="import-export" style="display: none;">
                <div class="import-section">
                    <h3>Import Template</h3>
                    <p>Paste a template code below or select a file to import:</p>
                    <textarea id="importTemplateCode" rows="5" placeholder="Paste template code here..."></textarea>
                    <div class="import-file-container">
                        <input type="file" id="importTemplateFile" accept=".json">
                        <label for="importTemplateFile" class="file-input-label">Choose File</label>
                    </div>
                    <button id="importTemplateBtn" class="import-btn"><span>Import Template</span></button>
                </div>

                <div class="export-section">
                    <h3>Export Templates</h3>
                    <p>Select templates to export:</p>
                    <div class="export-template-list" id="exportTemplateList">
                        <!-- Template checkboxes will be added here dynamically -->
                    </div>
                    <button id="exportTemplateBtn" class="export-btn"><span>Export Selected</span></button>
                </div>
            </div>
        </div>
    </div>

    <!-- AI Assistant Panel -->
    <div id="aiAssistantPanel" class="ai-panel" style="display: none;">
        <div class="ai-panel-header">
            <h3>🤖 AI Assistant</h3>
            <button onclick="toggleAIPanel()" class="ai-close-btn">×</button>
        </div>
        <div class="ai-panel-content">
            <div class="ai-status" id="aiStatus">
                <p>🔗 <a href="ai-setup.html" target="_blank">Connect AI Assistant</a> to get help with debugging and optimization</p>
            </div>
            <div class="ai-quick-actions" id="aiQuickActions" style="display: none;">
                <button onclick="aiDebugSharing()" class="ai-btn">🔧 Debug Sharing Issue</button>
                <button onclick="aiOptimizeCode()" class="ai-btn">🚀 Optimize Code</button>
                <button onclick="aiGenerateFeature()" class="ai-btn">💡 Generate Feature</button>
            </div>
            <div id="aiOutput" class="ai-output"></div>
        </div>
    </div>

    <!-- Company Profile Modal -->
    <div id="companyProfileModal" class="modal">
        <div class="modal-content company-profile-content">
            <div class="modal-header">
                <h2>⚙️ Company Profile Settings</h2>
                <span class="close-company-profile" onclick="closeCompanyProfileModal()">&times;</span>
            </div>
            <div class="company-profile-form">
                <div class="form-group">
                    <label for="modalCompanyName">Company Name:</label>
                    <input type="text" id="modalCompanyName" placeholder="Your Company Name" required>
                </div>
                <div class="form-group">
                    <label for="modalCompanyEmail">Email:</label>
                    <input type="email" id="modalCompanyEmail" placeholder="<EMAIL>" required>
                </div>
                <div class="form-group">
                    <label for="modalCompanyPhone">Phone:</label>
                    <input type="tel" id="modalCompanyPhone" placeholder="+27 21 555 0123" required>
                </div>
                <div class="form-group">
                    <label for="modalCompanyAddress">Address:</label>
                    <textarea id="modalCompanyAddress" rows="3" placeholder="123 Business Street&#10;Suite 456&#10;Cape Town, 8001" required></textarea>
                </div>
                <div class="form-group">
                    <label for="modalRepresentativeName">Representative Name:</label>
                    <input type="text" id="modalRepresentativeName" placeholder="Your Name">
                </div>
                <div class="form-group">
                    <label for="modalCompanyRegNumber">Registration Number:</label>
                    <input type="text" id="modalCompanyRegNumber" placeholder="2023/123456/07">
                </div>
                <div class="form-group">
                    <label for="modalCompanyVatNumber">VAT Number:</label>
                    <input type="text" id="modalCompanyVatNumber" placeholder="**********">
                </div>
                <div class="form-group">
                    <label for="modalBankName">Bank Name:</label>
                    <input type="text" id="modalBankName" placeholder="First National Bank">
                </div>
                <div class="form-group">
                    <label for="modalAccountName">Account Name:</label>
                    <input type="text" id="modalAccountName" placeholder="Your Company (Pty) Ltd">
                </div>
                <div class="form-group">
                    <label for="modalAccountNumber">Account Number:</label>
                    <input type="text" id="modalAccountNumber" placeholder="**********">
                </div>
                <div class="form-group">
                    <label for="modalBranchCode">Branch Code:</label>
                    <input type="text" id="modalBranchCode" placeholder="250655">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeCompanyProfileModal()">Cancel</button>
                <button type="button" class="btn-primary" onclick="saveCompanyProfile()">💾 Save Profile</button>
            </div>
        </div>
    </div>

    <!-- AI Assistant Toggle Button -->
    <button id="aiToggleBtn" onclick="toggleAIPanel()" class="ai-toggle-btn" title="AI Assistant">
        🤖
    </button>

    <script>
        // Simple test script to check if the page is loading correctly
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded successfully');
            document.title = 'Invoice Generator - Ready';

            // Check if AI assistant is available
            if (window.aiAssistant) {
                document.getElementById('aiStatus').innerHTML = '<p>✅ AI Assistant Connected</p>';
                document.getElementById('aiQuickActions').style.display = 'block';
            }
        });

        // AI Assistant Functions
        function toggleAIPanel() {
            const panel = document.getElementById('aiAssistantPanel');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }

        async function aiDebugSharing() {
            if (!window.aiAssistant) {
                alert('Please connect AI assistant first via the setup page');
                return;
            }

            const htmlStructure = document.getElementById('quickSharingOptions').outerHTML.substring(0, 200) + '...';
            const cssRules = '.quick-sharing-section { display: none; }';
            const jsCode = 'quickSharingSection.style.cssText = "display: block !important;";';

            showAIOutput('🔧 Debugging sharing issue...');
            try {
                const result = await window.aiAssistant.fixSharingIssue(htmlStructure, cssRules, jsCode);
                showAIOutput('Sharing Issue Debug', result);
            } catch (error) {
                showAIOutput('Debug Error', { success: false, error: error.message });
            }
        }

        async function aiOptimizeCode() {
            if (!window.aiAssistant) {
                alert('Please connect AI assistant first via the setup page');
                return;
            }

            showAIOutput('🚀 Optimizing code...');
            try {
                const result = await window.aiAssistant.optimizeInvoiceCode('showQuickSharingOptions function', 'Quick Sharing Visibility');
                showAIOutput('Code Optimization', result);
            } catch (error) {
                showAIOutput('Optimization Error', { success: false, error: error.message });
            }
        }

        async function aiGenerateFeature() {
            if (!window.aiAssistant) {
                alert('Please connect AI assistant first via the setup page');
                return;
            }

            showAIOutput('💡 Generating new feature...');
            try {
                const result = await window.aiAssistant.generateInvoiceFeature('Invoice template selector with preview', ['Multiple designs', 'Real-time preview', 'Custom branding']);
                showAIOutput('Feature Generation', result);
            } catch (error) {
                showAIOutput('Generation Error', { success: false, error: error.message });
            }
        }

        function showAIOutput(title, result) {
            const output = document.getElementById('aiOutput');
            if (!output) return;

            const timestamp = new Date().toLocaleTimeString();

            if (typeof result === 'string' || !result) {
                output.innerHTML += `<div class="ai-message"><strong>[${timestamp}]</strong> ${title}</div>`;
            } else if (result && typeof result === 'object') {
                const content = result.success ? result.response : `Error: ${result.error}`;
                output.innerHTML += `
                    <div class="ai-message">
                        <strong>[${timestamp}] ${title}:</strong><br>
                        <div class="ai-response">${content}</div>
                    </div>
                `;
            } else {
                output.innerHTML += `<div class="ai-message"><strong>[${timestamp}]</strong> ${title}: ${result}</div>`;
            }
            output.scrollTop = output.scrollHeight;
        }
    </script>
    <script src="ai-assistant.js"></script>
    <script src="api-config.js"></script>
    <script src="script.js"></script>
    <script src="template-manager.js"></script>
</body>
</html>
