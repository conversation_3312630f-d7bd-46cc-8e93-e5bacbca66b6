/* Freemium System Styles */

/* Token Counter */
.token-counter {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

.token-counter:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
}

.token-counter.low-tokens {
    background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%);
    animation: pulse 2s infinite;
}

.token-counter.no-tokens {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    animation: shake 0.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

.token-balance {
    font-size: 16px;
    font-weight: bold;
}

/* Token Widget for Dashboard */
.token-widget {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.token-widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.token-widget-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.token-widget-balance {
    font-size: 24px;
    font-weight: bold;
    color: #667eea;
}

.token-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.token-btn {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.token-btn.purchase {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.token-btn.history {
    background: #f8f9fa;
    color: #333;
    border: 2px solid #e9ecef;
}

.token-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Welcome Modal */
/* Welcome Modal - Professional Design */
.welcome-modal {
    max-width: 600px;
    text-align: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 25px;
    box-shadow: 0 25px 80px rgba(102, 126, 234, 0.4);
    overflow: hidden;
    position: relative;
}

.welcome-modal::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 25px 25px 0 0;
}

.welcome-content {
    padding: 40px 35px;
    position: relative;
    z-index: 2;
}

.token-gift {
    margin-bottom: 35px;
    position: relative;
}

.token-icon {
    font-size: 72px;
    margin-bottom: 20px;
    display: inline-block;
    animation: tokenBounce 2s ease-in-out infinite;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

@keyframes tokenBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.token-gift h3 {
    color: #ffffff;
    margin-bottom: 15px;
    font-size: 28px;
    font-weight: 800;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    line-height: 1.2;
}

.token-gift p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    font-weight: 500;
    margin: 0;
}

.feature-costs {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    padding: 30px 25px;
    margin: 30px 0;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.cost-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    margin: 8px 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 15px;
}

.cost-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.cost-item:first-child {
    margin-top: 0;
}

.cost-item:last-child {
    margin-bottom: 0;
}

.welcome-note {
    color: rgba(255, 255, 255, 0.85);
    font-style: italic;
    margin-top: 25px;
    font-size: 15px;
    font-weight: 500;
    line-height: 1.4;
}

.welcome-modal .btn-primary {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 50px;
    font-size: 18px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 25px;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.welcome-modal .btn-primary:hover {
    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(40, 167, 69, 0.6);
}

.welcome-modal .btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

/* Insufficient Tokens Modal */
.insufficient-tokens-modal {
    max-width: 800px;
}

.token-status {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    text-align: center;
}

.token-status p {
    margin: 5px 0;
    font-size: 16px;
}

.upgrade-options h3 {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
}

/* Subscription Modal - Professional Design */
.subscription-modal {
    max-width: 900px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 25px;
    box-shadow: 0 25px 80px rgba(102, 126, 234, 0.4);
    overflow: hidden;
    position: relative;
}

.subscription-modal::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 120px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 25px 25px 0 0;
}

.subscription-modal h2 {
    background: rgba(255, 255, 255, 0.1);
    margin: -30px -30px 30px -30px;
    padding: 25px 30px;
    border-radius: 25px 25px 0 0;
    font-size: 28px;
    font-weight: 800;
    text-align: center;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 2;
}

.subscription-intro {
    text-align: center;
    margin-bottom: 30px;
    position: relative;
    z-index: 2;
}

.subscription-intro p {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 20px;
    line-height: 1.5;
}

.current-balance {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 50px;
    padding: 12px 25px;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.balance-label {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
}

.balance-amount {
    font-weight: 800;
    font-size: 18px;
    color: #ffffff;
}

.subscription-footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 25px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 2;
}

.subscription-footer p {
    margin: 8px 0;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.money-back {
    color: #4CAF50 !important;
    font-weight: 600 !important;
}

.secure-payment {
    color: #FFC107 !important;
    font-weight: 600 !important;
}

/* Enhanced Close Button for Subscription Modal */
.subscription-modal .close {
    position: absolute;
    top: 20px;
    right: 25px;
    font-size: 32px;
    font-weight: bold;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    z-index: 10;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.subscription-modal .close:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: rotate(90deg) scale(1.1);
}

/* Subscription Grid */
.subscription-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.subscription-card {
    background: rgba(255, 255, 255, 0.15);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 30px 25px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.subscription-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 20px 20px 0 0;
}

.subscription-card:hover {
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.2);
}

.subscription-card h4 {
    color: #ffffff;
    margin-bottom: 15px;
    font-size: 20px;
    font-weight: 700;
    position: relative;
    z-index: 2;
}

.token-amount {
    font-size: 42px;
    font-weight: 900;
    color: #ffffff;
    margin: 20px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}

.price {
    font-size: 28px;
    font-weight: 800;
    color: #4CAF50;
    margin-bottom: 8px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 2;
}

.duration {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 20px;
    font-weight: 600;
    font-size: 14px;
    position: relative;
    z-index: 2;
}

.features {
    list-style: none;
    padding: 0;
    margin: 20px 0;
    position: relative;
    z-index: 2;
}

.features li {
    padding: 8px 0;
    color: rgba(255, 255, 255, 0.9);
    font-size: 15px;
    font-weight: 500;
    text-align: left;
}

.features li:before {
    content: "✓ ";
    color: #4CAF50;
    font-weight: bold;
    margin-right: 8px;
}

/* Subscription Card Purchase Button */
.subscription-card .btn-primary {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
    width: 100%;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
}

.subscription-card .btn-primary:hover {
    background: linear-gradient(135deg, #45a049 0%, #388e3c 100%);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(76, 175, 80, 0.5);
}

.subscription-card .btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

/* Payment Modal */
.payment-modal {
    max-width: 600px;
}

.purchase-summary {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    text-align: center;
}

.package-details h3 {
    color: #667eea;
    margin-bottom: 10px;
}

.package-details .price {
    font-size: 28px;
    color: #28a745;
    margin: 10px 0;
}

.payment-methods h3 {
    margin-bottom: 20px;
    color: #333;
}

.payment-option {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-option:hover {
    border-color: #667eea;
    background: #f8f9fa;
}

.payment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.card-icons {
    display: flex;
    gap: 5px;
}

.card-icons img {
    width: 30px;
    height: 20px;
    border-radius: 3px;
}

.payment-note {
    background: #e8f5e8;
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
    text-align: center;
}

.payment-note p {
    margin: 0;
    color: #155724;
    font-size: 14px;
}

/* Processing Modal */
.processing-modal {
    text-align: center;
    max-width: 400px;
}

.processing-animation {
    margin: 30px 0;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success Modal */
.success-modal {
    max-width: 500px;
    text-align: center;
}

.success-content {
    padding: 20px 0;
}

.token-added {
    margin-bottom: 30px;
}

.token-added .token-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.token-added h3 {
    color: #28a745;
    margin-bottom: 10px;
}

.package-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.package-info ul {
    list-style: none;
    padding: 0;
    margin: 15px 0 0 0;
}

.package-info li {
    padding: 5px 0;
    color: #28a745;
}

/* Token History Modal - Professional Design */
.history-modal {
    max-width: 700px;
    max-height: 85vh;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
}

.history-modal h2 {
    background: rgba(255, 255, 255, 0.1);
    margin: -30px -30px 30px -30px;
    padding: 25px 30px;
    border-radius: 20px 20px 0 0;
    font-size: 24px;
    font-weight: 700;
    text-align: center;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.history-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.stat {
    text-align: center;
    padding: 25px 20px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.stat:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.stat h3 {
    font-size: 36px;
    font-weight: 800;
    margin: 0 0 8px 0;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat p {
    margin: 0;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.history-list {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.history-list h3 {
    margin: 0 0 20px 0;
    color: #ffffff;
    font-size: 20px;
    font-weight: 700;
    text-align: center;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.history-items {
    max-height: 320px;
    overflow-y: auto;
    padding-right: 10px;
}

/* Custom scrollbar for history items */
.history-items::-webkit-scrollbar {
    width: 6px;
}

.history-items::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.history-items::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.history-items::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

.history-item {
    display: flex;
    align-items: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    margin-bottom: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.history-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #4CAF50 0%, #45a049 100%);
    border-radius: 0 2px 2px 0;
}

.history-item.spent::before {
    background: linear-gradient(180deg, #f44336 0%, #d32f2f 100%);
}

.history-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.history-details {
    flex: 1;
    margin-left: 15px;
}

.history-action {
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 6px;
    font-size: 16px;
    line-height: 1.4;
}

.history-date {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

.history-amount {
    font-weight: 800;
    font-size: 20px;
    margin-right: 20px;
    padding: 8px 15px;
    border-radius: 25px;
    min-width: 60px;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.history-amount.positive {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    box-shadow: 0 3px 10px rgba(76, 175, 80, 0.4);
}

.history-amount.negative {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    color: white;
    box-shadow: 0 3px 10px rgba(244, 67, 54, 0.4);
}

.history-balance {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    min-width: 90px;
    text-align: right;
    font-weight: 600;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Close button enhancement */
.history-modal .close {
    position: absolute;
    top: 20px;
    right: 25px;
    font-size: 28px;
    font-weight: bold;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    z-index: 10;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.history-modal .close:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: rotate(90deg);
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    z-index: 10000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.notification.error {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
}

.notification.info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
}

/* Auth Styles */
.google-signin-container {
    display: flex;
    justify-content: center;
    margin: 15px 0;
}

.auth-divider {
    display: flex;
    align-items: center;
    margin: 20px 0;
    text-align: center;
}

.auth-divider::before,
.auth-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: #e9ecef;
}

.auth-divider span {
    padding: 0 15px;
    color: #666;
    font-size: 14px;
    background: white;
}

.auth-modal {
    max-width: 450px;
}

.auth-options {
    padding: 20px 0;
}

.email-auth-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.email-auth-form input {
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.email-auth-form input:focus {
    outline: none;
    border-color: #667eea;
}

.auth-switch {
    text-align: center;
    margin-top: 20px;
    color: #666;
}

.auth-switch a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}

.auth-switch a:hover {
    text-decoration: underline;
}

/* Demo Google Sign-In Buttons */
.demo-google-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #dadce0;
    border-radius: 8px;
    background: white;
    color: #3c4043;
    font-family: 'Google Sans', Roboto, sans-serif;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.demo-google-btn:hover {
    background: #f8f9fa;
    border-color: #c1c7cd;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.demo-google-btn.signup {
    background: #1a73e8;
    color: white;
    border-color: #1a73e8;
}

.demo-google-btn.signup:hover {
    background: #1557b0;
    border-color: #1557b0;
}

.demo-google-btn img {
    width: 18px;
    height: 18px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .subscription-grid {
        grid-template-columns: 1fr;
    }
    
    .history-stats {
        grid-template-columns: 1fr;
    }
    
    .history-item {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .payment-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .token-actions {
        flex-direction: column;
    }
}
