import { useState } from 'react'
import { Upload, FileImage, Type, Square, MessageSquare, Highlighter, MoreHorizontal, Grid, Eye, ArrowUpDown } from 'lucide-react'

function App() {
  const [dragActive, setDragActive] = useState(false)

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    // Handle file drop here
  }

  return (
    <div className="min-h-screen bg-[#f4f4fb]">
      {/* Header Navigation */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center space-x-8">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-[#e2342f] rounded flex items-center justify-center">
                  <FileImage className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold text-gray-900">iLovePDF</span>
              </div>
            </div>

            {/* Navigation Menu */}
            <nav className="hidden md:flex items-center space-x-6">
              <div className="relative group">
                <button className="text-gray-700 hover:text-[#e2342f] px-3 py-2">
                  All PDF tools
                </button>
              </div>
              <div className="relative group">
                <button className="text-gray-700 hover:text-[#e2342f] px-3 py-2">
                  Convert PDF
                </button>
              </div>
            </nav>

            {/* Auth Buttons */}
            <div className="flex items-center space-x-3">
              <button className="text-gray-700 hover:text-[#e2342f] px-4 py-2">
                Login
              </button>
              <button className="bg-[#e2342f] text-white px-4 py-2 rounded hover:bg-[#c52d27]">
                Sign up
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 py-8">
        {/* Editing Tools Row */}
        <div className="flex items-center justify-center space-x-6 mb-8">
          <div className="flex items-center space-x-2 p-3 bg-white rounded-lg shadow-sm">
            <FileImage className="w-6 h-6 text-[#e2342f]" />
          </div>
          <div className="flex items-center space-x-2 p-3 bg-white rounded-lg shadow-sm">
            <Type className="w-6 h-6 text-[#e2342f]" />
            <span className="text-sm font-medium text-gray-700">Annotate</span>
          </div>
          <div className="flex items-center space-x-2 p-3 bg-white rounded-lg shadow-sm">
            <Square className="w-6 h-6 text-[#e2342f]" />
            <span className="text-sm font-medium text-gray-700">Edit</span>
          </div>
          <div className="flex items-center space-x-2 p-3 bg-white rounded-lg shadow-sm">
            <MessageSquare className="w-6 h-6 text-[#e2342f]" />
          </div>
          <div className="flex items-center space-x-2 p-3 bg-white rounded-lg shadow-sm">
            <Highlighter className="w-6 h-6 text-[#e2342f]" />
          </div>
          <div className="flex items-center space-x-2 p-3 bg-white rounded-lg shadow-sm">
            <MoreHorizontal className="w-6 h-6 text-[#e2342f]" />
          </div>
        </div>

        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            PDF Editor
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Edit PDF by adding text, shapes, comments and highlights. Your secure and simple tool to edit PDF.
          </p>
        </div>

        {/* Upload Area and Instructions */}
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Upload Area */}
          <div className="lg:col-span-2">
            <div
              className={`relative border-2 border-dashed rounded-xl p-12 text-center transition-colors ${
                dragActive
                  ? 'border-[#e2342f] bg-red-50'
                  : 'border-gray-300 bg-white hover:border-[#e2342f]'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <div className="space-y-6">
                {/* Upload Button */}
                <button className="bg-[#e2342f] text-white px-8 py-4 rounded-lg font-semibold hover:bg-[#c52d27] transition-colors inline-flex items-center space-x-2">
                  <Upload className="w-5 h-5" />
                  <span>Select PDF file</span>
                </button>

                {/* Upload Options */}
                <div className="flex items-center justify-center space-x-4">
                  <button className="p-2 border border-gray-300 rounded hover:border-[#e2342f]">
                    <Upload className="w-5 h-5 text-gray-600" />
                  </button>
                  <button className="p-2 border border-gray-300 rounded hover:border-[#e2342f]">
                    <FileImage className="w-5 h-5 text-gray-600" />
                  </button>
                </div>

                <p className="text-gray-500">or drop PDF here</p>

                {/* File Management Icons */}
                <div className="flex items-center justify-center space-x-4 pt-4">
                  <button className="p-2 hover:bg-gray-100 rounded">
                    <ArrowUpDown className="w-5 h-5 text-gray-400" />
                  </button>
                  <button className="p-2 hover:bg-gray-100 rounded">
                    <Grid className="w-5 h-5 text-gray-400" />
                  </button>
                  <button className="p-2 hover:bg-gray-100 rounded">
                    <Eye className="w-5 h-5 text-gray-400" />
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Instructions Panel */}
          <div className="bg-white rounded-xl p-6 h-fit">
            <h3 className="font-semibold text-gray-900 mb-4">Edit PDF</h3>
            <div className="space-y-4 text-sm text-gray-600">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-[#e2342f] rounded-full mt-2" />
                <div>
                  <strong>Elements</strong>
                  <p>Use the toolbar to modify or add text, upload images, and annotate with ease.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-[#e2342f] rounded-full mt-2" />
                <div>
                  <strong>Reorder items</strong>
                  <p>to move them to the back or front.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-[#e2342f] rounded-full mt-2" />
                <div>
                  <strong>Remove all</strong>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-[#e2342f] rounded-full mt-2" />
                <div>
                  <p>Select text to edit, move, or delete the existing content.</p>
                </div>
              </div>
            </div>

            {/* Upload Status */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="text-sm text-gray-500 space-y-2">
                <p>Getting files from Drive</p>
                <p>Getting files from Dropbox</p>
                <p>Uploading file 0 of 0</p>
                <p>Time left - seconds - Upload speed - MB/S</p>
              </div>

              <div className="mt-4">
                <div className="text-sm font-medium text-gray-700 mb-1">Uploaded</div>
                <div className="text-sm text-gray-500">Editing PDF...</div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="mt-16 text-center text-gray-500 text-sm">
        <p>© iLovePDF 2025 - Your PDF Editor</p>
      </footer>
    </div>
  )
}

export default App
