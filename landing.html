<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DocuGen Pro - Professional Document Generator</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Open+Sans:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="landing-styles.css">
    <link rel="stylesheet" href="freemium-styles.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2>DocuGen Pro</h2>
            </div>
            <div class="nav-menu">
                <a href="#features" class="nav-link">Features</a>
                <a href="#pricing" class="nav-link">Pricing</a>
                <a href="#contact" class="nav-link">Contact</a>
                <button class="btn-secondary" onclick="showLogin()">Sign In</button>
                <button class="btn-primary" onclick="showSignup()">Get Started</button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1>Professional Document Generation Made Simple</h1>
                <p>Create invoices, receipts, quotations, contracts, technical riders, and annexures with our powerful document generator. Professional PDFs in minutes, not hours.</p>
                <div class="hero-buttons">
                    <button class="btn-primary large" onclick="showSignup()">Start Free Trial</button>
                    <button class="btn-secondary large" onclick="showDemo()">View Demo</button>
                </div>
            </div>
            <div class="hero-image">
                <div class="document-preview">
                    <div class="preview-header">INVOICE</div>
                    <div class="preview-content">
                        <div class="preview-line"></div>
                        <div class="preview-line short"></div>
                        <div class="preview-line"></div>
                        <div class="preview-table">
                            <div class="table-row"></div>
                            <div class="table-row"></div>
                            <div class="table-row"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Free Upload & Sign Section -->
    <section class="free-upload-sign">
        <div class="container">
            <div class="free-sign-content">
                <!-- Big FREE Header -->
                <div class="free-header">
                    <h1 class="free-title">🎉 100% FREE 🎉</h1>
                    <h2 class="free-subtitle">Document Signing Tool</h2>
                    <p class="free-description">No registration • No limits • No hidden fees • Completely FREE!</p>
                </div>

                <!-- Features Grid -->
                <div class="free-features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">📄</div>
                        <h3>Upload PDF</h3>
                        <p>Any PDF document instantly</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">✍️</div>
                        <h3>Smooth Signatures</h3>
                        <p>Professional signature creation</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🎯</div>
                        <h3>Drag & Drop</h3>
                        <p>Place signatures anywhere</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📏</div>
                        <h3>Dynamic Resize</h3>
                        <p>Perfect signature sizing</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">💾</div>
                        <h3>Export PDF</h3>
                        <p>Download signed documents</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🚀</div>
                        <h3>Multi-Page</h3>
                        <p>4 pages free, unlimited with $5+ donation</p>
                    </div>
                </div>

                <!-- Big CTA Button -->
                <div class="free-cta">
                    <button class="btn-free-sign" onclick="window.location.href='upload-sign.html'">
                        🆓 START SIGNING FOR FREE NOW! 🆓
                    </button>
                    <p class="cta-subtext">✨ No account needed • Start in 5 seconds ✨</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Document Types Section -->
    <section class="document-types" id="features">
        <div class="container">
            <h2>Six Powerful Document Types</h2>
            <p class="section-subtitle">Everything you need for professional business documentation</p>
            <div class="types-grid">
                <div class="type-card" data-type="invoice">
                    <div class="type-icon">📄</div>
                    <h3>Invoices</h3>
                    <p>Professional invoices with automatic calculations, VAT handling, and payment tracking</p>
                    <button class="btn-outline" onclick="previewDocument('invoice')">Preview</button>
                </div>
                <div class="type-card" data-type="receipt">
                    <div class="type-icon">🧾</div>
                    <h3>Receipts</h3>
                    <p>Payment receipts with transaction details and professional formatting</p>
                    <button class="btn-outline" onclick="previewDocument('receipt')">Preview</button>
                </div>
                <div class="type-card" data-type="quotation">
                    <div class="type-icon">💰</div>
                    <h3>Quotations</h3>
                    <p>Detailed price quotes with validity periods and terms & conditions</p>
                    <button class="btn-outline" onclick="previewDocument('quotation')">Preview</button>
                </div>
                <div class="type-card" data-type="contract">
                    <div class="type-icon">📋</div>
                    <h3>Contracts</h3>
                    <p>Legal agreements and contracts with customizable terms and signatures</p>
                    <button class="btn-outline" onclick="previewDocument('contract')">Preview</button>
                </div>
                <div class="type-card" data-type="rider">
                    <div class="type-icon">🎵</div>
                    <h3>Technical Riders</h3>
                    <p>Event and performance technical requirements and specifications</p>
                    <button class="btn-outline" onclick="previewDocument('rider')">Preview</button>
                </div>
                <div class="type-card" data-type="annexure">
                    <div class="type-icon">📎</div>
                    <h3>Annexures</h3>
                    <p>Additional documents and appendices for contracts and agreements</p>
                    <button class="btn-outline" onclick="previewDocument('annexure')">Preview</button>
                </div>
            </div>
        </div>
    </section>




    <!-- Features Section -->


    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <h2>Why Choose DocuGen Pro?</h2>
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon">⚡</div>
                    <h3>Lightning Fast</h3>
                    <p>Generate professional documents in seconds with our optimized engine</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🎨</div>
                    <h3>Customizable Templates</h3>
                    <p>Multiple themes and layouts to match your brand identity</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">💾</div>
                    <h3>Auto-Save Profiles</h3>
                    <p>Save client and company information for quick document generation</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📱</div>
                    <h3>Mobile Responsive</h3>
                    <p>Works perfectly on desktop, tablet, and mobile devices</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔒</div>
                    <h3>Secure & Private</h3>
                    <p>Your data stays private with client-side processing</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📊</div>
                    <h3>Smart Calculations</h3>
                    <p>Automatic VAT, discounts, and total calculations</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Authentication Modals -->
    <div id="authModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeAuth()">&times;</span>
            
            <!-- Sign In Form -->
            <div id="signinForm" class="auth-form">
                <h2>Welcome Back</h2>
                <p>Sign in to your DocuGen Pro account</p>

                <!-- Google Sign-In Button -->
                <div id="google-signin-landing" class="google-signin-container" style="margin-bottom: 20px;"></div>

                <div class="auth-divider">
                    <span>or</span>
                </div>

                <form onsubmit="handleSignIn(event)">
                    <div class="form-group">
                        <label for="signinEmail">Email Address</label>
                        <input type="email" id="signinEmail" required>
                    </div>
                    <div class="form-group">
                        <label for="signinPassword">Password</label>
                        <input type="password" id="signinPassword" required>
                    </div>
                    <button type="submit" class="btn-primary full-width">Sign In</button>
                </form>
                <p class="auth-switch">Don't have an account? <a href="#" onclick="showSignup()">Sign up here</a></p>
            </div>

            <!-- Sign Up Form -->
            <div id="signupForm" class="auth-form" style="display: none;">
                <h2>Get Started Free</h2>
                <p>Create your DocuGen Pro account and get 25 FREE tokens!</p>

                <!-- Google Sign-Up Button -->
                <div id="google-signin-signup" class="google-signin-container" style="margin-bottom: 20px;"></div>

                <div class="auth-divider">
                    <span>or</span>
                </div>

                <form onsubmit="handleSignUp(event)">
                    <div class="form-group">
                        <label for="signupName">Full Name</label>
                        <input type="text" id="signupName" required>
                    </div>
                    <div class="form-group">
                        <label for="signupEmail">Email Address</label>
                        <input type="email" id="signupEmail" required>
                    </div>
                    <div class="form-group">
                        <label for="signupCompany">Company Name</label>
                        <input type="text" id="signupCompany" required>
                    </div>
                    <div class="form-group">
                        <label for="signupPassword">Password</label>
                        <input type="password" id="signupPassword" required>
                    </div>
                    <button type="submit" class="btn-primary full-width">Create Account</button>
                </form>
                <p class="auth-switch">Already have an account? <a href="#" onclick="showLogin()">Sign in here</a></p>
            </div>
        </div>
    </div>

    <!-- Document Preview Modal -->
    <div id="previewModal" class="modal">
        <div class="modal-content preview-content">
            <span class="close" onclick="closePreview()">&times;</span>
            <div id="previewContainer">
                <!-- Preview content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Simple Free Signing Tool Modal -->
    <div id="simpleSigningModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 900px; max-height: 90vh; overflow-y: auto;">
            <span class="close" onclick="closeSimpleSigningTool()">&times;</span>

            <div class="simple-signing-tool" style="padding: 30px;">
                <div class="tool-header" style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #e9ecef; padding-bottom: 20px;">
                    <h2 style="color: #2c3e50; margin: 0 0 10px 0; font-size: 2rem;">📝 Free PDF Signing Tool</h2>
                    <p style="color: #666; margin: 0; font-size: 1.1rem;">Upload PDF → Draw signature → Click to place → Download signed PDF</p>
                </div>

                <!-- Step 1: Upload PDF -->
                <div id="uploadStep" class="signing-step">
                    <h3 style="color: #007bff; margin-bottom: 20px; display: flex; align-items: center;">
                        <span style="background: #007bff; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; margin-right: 10px; font-size: 14px;">1</span>
                        Upload PDF Document
                    </h3>

                    <div class="upload-zone" id="uploadZone" style="
                        border: 3px dashed #007bff;
                        border-radius: 12px;
                        padding: 40px;
                        text-align: center;
                        background: #f8f9fa;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    ">
                        <div style="font-size: 48px; margin-bottom: 15px;">📄</div>
                        <h4 style="color: #007bff; margin-bottom: 10px;">Drop PDF here or click to upload</h4>
                        <p style="color: #666; margin-bottom: 20px;">Only PDF files are supported</p>
                        <input type="file" id="pdfUpload" accept=".pdf" style="display: none;">
                        <button onclick="document.getElementById('pdfUpload').click()" style="
                            background: #007bff;
                            color: white;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 6px;
                            font-size: 16px;
                            cursor: pointer;
                            transition: background 0.3s ease;
                        ">Choose PDF File</button>
                    </div>

                    <div id="uploadedFileInfo" style="display: none; margin-top: 20px; padding: 15px; background: #e8f5e8; border-radius: 8px; border-left: 4px solid #28a745;">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div>
                                <strong id="fileName" style="color: #28a745;"></strong>
                                <p id="fileSize" style="margin: 5px 0 0 0; color: #666; font-size: 14px;"></p>
                            </div>
                            <button onclick="proceedToSigning()" style="
                                background: #28a745;
                                color: white;
                                border: none;
                                padding: 10px 20px;
                                border-radius: 6px;
                                cursor: pointer;
                            ">Continue →</button>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Create Signature -->
                <div id="signatureStep" class="signing-step" style="display: none;">
                    <h3 style="color: #007bff; margin-bottom: 20px; display: flex; align-items: center;">
                        <span style="background: #007bff; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; margin-right: 10px; font-size: 14px;">2</span>
                        Create Your Signature
                    </h3>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                        <h4 style="margin-bottom: 15px; color: #333;">✍️ Draw your signature below:</h4>
                        <div style="border: 2px solid #ddd; border-radius: 8px; background: white; display: inline-block;">
                            <canvas id="signatureCanvas" width="400" height="120" style="display: block; cursor: crosshair;"></canvas>
                        </div>
                        <div style="margin-top: 15px;">
                            <button onclick="clearSignature()" style="
                                background: #6c757d;
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 4px;
                                cursor: pointer;
                                margin-right: 10px;
                            ">Clear</button>
                            <button onclick="proceedToPlacement()" style="
                                background: #28a745;
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 4px;
                                cursor: pointer;
                            ">Use This Signature →</button>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Place Signature -->
                <div id="placementStep" class="signing-step" style="display: none;">
                    <h3 style="color: #007bff; margin-bottom: 20px; display: flex; align-items: center;">
                        <span style="background: #007bff; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; margin-right: 10px; font-size: 14px;">3</span>
                        Place Signature on Document
                    </h3>

                    <div style="display: flex; gap: 20px;">
                        <!-- Document Display -->
                        <div style="flex: 1;">
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                                <h4 style="margin: 0 0 10px 0; color: #333;">📄 Click anywhere on the document to place your signature</h4>
                                <p style="margin: 0; color: #666; font-size: 14px;">You can place multiple signatures. Click "Download Signed PDF" when finished.</p>
                            </div>

                            <div id="documentViewer" style="
                                border: 2px solid #ddd;
                                border-radius: 8px;
                                background: white;
                                max-height: 500px;
                                overflow: auto;
                                position: relative;
                                cursor: crosshair;
                            ">
                                <canvas id="documentCanvas" style="display: block; max-width: 100%;"></canvas>
                            </div>
                        </div>

                        <!-- Actions Panel -->
                        <div style="width: 200px;">
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                                <h4 style="margin: 0 0 15px 0; color: #333;">Actions</h4>

                                <button onclick="downloadSignedPDF()" style="
                                    background: #007bff;
                                    color: white;
                                    border: none;
                                    padding: 12px;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    width: 100%;
                                    margin-bottom: 10px;
                                    font-weight: bold;
                                ">📥 Download Signed PDF</button>

                                <button onclick="backToSignature()" style="
                                    background: #6c757d;
                                    color: white;
                                    border: none;
                                    padding: 10px;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    width: 100%;
                                    margin-bottom: 10px;
                                ">← Change Signature</button>

                                <button onclick="startOver()" style="
                                    background: #dc3545;
                                    color: white;
                                    border: none;
                                    padding: 10px;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    width: 100%;
                                ">🔄 Start Over</button>

                                <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #ddd;">
                                    <p style="font-size: 12px; color: #666; margin: 0;">
                                        <strong>Signatures placed:</strong> <span id="signatureCount">0</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Donate Modal -->
    <div id="donateModal" class="modal">
        <div class="modal-content donate-content">
            <span class="close" onclick="closeDonateModal()">&times;</span>
            <div class="donate-section">
                <div class="donate-header">
                    <div class="heart-icon">❤️</div>
                    <h2>Support DocuGen Pro</h2>
                    <p>Help us keep this tool free and improve it for everyone!</p>
                </div>

                <div class="donate-info">
                    <div class="donate-feature">
                        <div class="feature-icon">🎁</div>
                        <h4>Free Tools</h4>
                        <p>Keep document signing free for everyone</p>
                    </div>
                    <div class="donate-feature">
                        <div class="feature-icon">🚀</div>
                        <h4>New Features</h4>
                        <p>Fund development of new templates and tools</p>
                    </div>
                    <div class="donate-feature">
                        <div class="feature-icon">🛠️</div>
                        <h4>Maintenance</h4>
                        <p>Cover hosting and maintenance costs</p>
                    </div>
                </div>

                <div class="donate-amounts">
                    <h4>Choose an amount:</h4>
                    <div class="amount-buttons">
                        <button class="amount-btn" onclick="donateAmount(5)">$5</button>
                        <button class="amount-btn" onclick="donateAmount(10)">$10</button>
                        <button class="amount-btn" onclick="donateAmount(25)">$25</button>
                        <button class="amount-btn" onclick="donateAmount(50)">$50</button>
                    </div>
                    <div class="custom-amount">
                        <input type="number" id="customAmount" placeholder="Custom amount" min="1">
                        <button onclick="donateCustomAmount()" class="btn-primary">Donate</button>
                    </div>
                </div>

                <div class="paypal-section">
                    <p>Donations are processed securely through PayPal</p>
                    <div id="paypal-button-container">
                        <!-- PayPal button will be inserted here -->
                        <p><em>PayPal integration will be added with your PayPal link</em></p>
                    </div>
                </div>

                <div class="donate-note">
                    <p><strong>Thank you for your support!</strong> Every donation helps us maintain and improve DocuGen Pro.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>DocuGen Pro</h3>
                    <p>Professional document generation made simple</p>
                </div>
                <div class="footer-section">
                    <h4>Product</h4>
                    <a href="#features">Features</a>
                    <a href="#pricing">Pricing</a>
                    <a href="#">Templates</a>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <a href="#" onclick="openHelpModal();return false;">Help Center</a>
                    <a href="#contact">Contact</a>
                    <a href="#">Documentation</a>
                </div>
                <div class="footer-section" id="contact">
                    <h4>Contact</h4>
                    <p><strong>Benjamin Music Initiatives</strong></p>
                    <p>34 Derby Rd<br>Kensington<br>Johannesburg<br>SA 2094</p>
                    <p>+27 72 442 5958</p>
                    <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
                <div class="footer-section">
                    <h4>Company</h4>
                    <a href="#" onclick="openAboutModal();return false;">About</a>
                    <a href="#" onclick="openPrivacyModal();return false;">Privacy</a>
                    <a href="#" onclick="openTermsModal();return false;">Terms</a>
                </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 DocuGen Pro. All rights reserved.</p>
                <p class="attribution">Created by Anesu Adrian Mupemhi For Benjamin Music Initiatives (BMI)</p>
            </div>
        </div>
    </footer>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="simplified-pdf-signing.js"></script>
    <script src="freemium-system.js"></script>
    <script src="google-auth.js"></script>
    <script src="landing-script.js"></script>

<!-- Help Center Modal -->
<div id="helpModal" class="modal" style="display:none;">
  <div class="modal-content">
    <span class="close" onclick="closeHelpModal()">&times;</span>
    <h2>Contact Support</h2>
    <form id="helpForm" onsubmit="sendHelpEmail(event)">
      <div class="form-group">
        <label for="helpName">Your Name</label>
        <input type="text" id="helpName" name="helpName" required>
      </div>
      <div class="form-group">
        <label for="helpEmail">Your Email</label>
        <input type="email" id="helpEmail" name="helpEmail" required>
      </div>
      <div class="form-group">
        <label for="helpMessage">Message</label>
        <textarea id="helpMessage" name="helpMessage" rows="4" required></textarea>
      </div>
      <div style="display: flex; justify-content: center;">
        <button type="submit" class="btn-primary" style="min-width: 180px;">Send Message</button>
      </div>
    </form>
  </div>
</div>
<script>
function openHelpModal() {
  document.getElementById('helpModal').style.display = 'flex';
}
function closeHelpModal() {
  document.getElementById('helpModal').style.display = 'none';
}
function sendHelpEmail(event) {
  event.preventDefault();
  var name = document.getElementById('helpName').value;
  var email = document.getElementById('helpEmail').value;
  var message = document.getElementById('helpMessage').value;
  var subject = encodeURIComponent('Support Query from DocuGen Pro User');
  var body = encodeURIComponent('Name: ' + name + '\nEmail: ' + email + '\n\n' + message);
  window.location.href = 'mailto:<EMAIL>?subject=' + subject + '&body=' + body;
  closeHelpModal();
}
</script>
</body>
<!-- About Modal and Script moved outside footer for proper display -->
<div id="aboutModal" class="modal" style="display:none;">
    <div class="modal-content">
        <span class="close" onclick="closeAboutModal()">&times;</span>
        <h2>About</h2>
        <p><strong>Benjamin Music Initiatives (BMI)</strong> is dedicated to empowering musicians, artists, and businesses with innovative digital tools. Our mission is to simplify professional documentation and support creative entrepreneurship in South Africa and beyond. DocuGen Pro was created to make generating invoices, contracts, and other essential documents fast, easy, and accessible for everyone. We believe in local support, user-friendly design, and helping our community thrive.</p>
    </div>
</div>
<!-- Privacy Modal -->
<div id="privacyModal" class="modal" style="display:none;">
    <div class="modal-content">
        <span class="close" onclick="closePrivacyModal()">&times;</span>
        <h2>Privacy Policy</h2>
        <p><strong>Your Privacy Matters</strong></p>
        <p>DocuGen Pro values your privacy. All documents and data are processed locally in your browser and are never uploaded to our servers. We do not collect, store, or share your personal information, document contents, or signatures. Any information you provide for support or feedback is used solely to assist you and improve our services.</p>
        <ul>
            <li>No document data is stored or transmitted to us.</li>
            <li>We use cookies only for essential site functionality.</li>
            <li>Your email and contact details are only used for support queries.</li>
            <li>We do not sell or share your data with third parties.</li>
        </ul>
        <p>If you have questions about privacy, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
    </div>
</div>
<!-- Terms Modal -->
<div id="termsModal" class="modal" style="display:none;">
    <div class="modal-content">
        <span class="close" onclick="closeTermsModal()">&times;</span>
        <h2>Terms & Conditions</h2>
        <p><strong>Usage Terms</strong></p>
        <ul>
            <li>DocuGen Pro is provided as-is, without warranties of any kind.</li>
            <li>All generated documents are the responsibility of the user.</li>
            <li>Do not use DocuGen Pro for unlawful or fraudulent activities.</li>
            <li>We reserve the right to update these terms at any time.</li>
            <li>By using this tool, you agree to these terms and our privacy policy.</li>
        </ul>
        <p>For questions about these terms, contact <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
    </div>
</div>
<script>
function openPrivacyModal() {
    document.getElementById('privacyModal').style.display = 'block';
}
function closePrivacyModal() {
    document.getElementById('privacyModal').style.display = 'none';
}
function openTermsModal() {
    document.getElementById('termsModal').style.display = 'block';
}
function closeTermsModal() {
    document.getElementById('termsModal').style.display = 'none';
}
</script>
<script>
function openAboutModal() {
    document.getElementById('aboutModal').style.display = 'block';
}
function closeAboutModal() {
    document.getElementById('aboutModal').style.display = 'none';
}


</script>



</body>
</html>
