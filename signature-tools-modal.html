<!--
  Signature Tools Modal - Standalone Component
  This file contains the HTML structure and all related JavaScript for the Signature Tools modal and its functionalities.
  Copy-pasted and adapted from dashboard2.html.
-->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Signature Tools Modal - DocuGen Pro</title>
  <style>
    /* --- Inlined from dashboard-styles.css for standalone Signature Tools Modal --- */
    :root {
        --primary-color: #4a9eff;
        --secondary-color: #667eea;
        --accent-color: #764ba2;
        --success-color: #28a745;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
        --dark-color: #343a40;
        --light-color: #f8f9fa;
        --border-color: #dee2e6;
        --text-color: #333;
        --text-muted: #6c757d;
        --shadow: 0 2px 10px rgba(0,0,0,0.1);
        --border-radius: 8px;
    }
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { font-family: 'Open Sans', sans-serif; background: #f5f7fa; color: var(--text-color); line-height: 1.6; overflow-x: hidden; }
    .modal {
        display: block;
        position: fixed;
        z-index: 2000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        backdrop-filter: blur(5px);
    }
    .modal-content {
        background-color: white;
        margin: 5% auto;
        padding: 0;
        border-radius: var(--border-radius);
        width: 90%;
        max-width: 800px;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    }
    .modal-header {
        background: var(--primary-color);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .modal-header h2 {
        font-family: 'Montserrat', sans-serif;
        font-size: 1.3rem;
        font-weight: 600;
    }
    .close {
        color: white;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        line-height: 1;
    }
    .close:hover { opacity: 0.7; }
    .signature-tools-content { max-width: 900px; }
    .signature-tools-body { padding: 2rem; }
    .signature-canvas {
      border: 2px solid var(--primary-color);
      border-radius: 8px;
      background: #fff;
      width: 100%;
      height: 180px;
      cursor: crosshair;
      margin-bottom: 12px;
      display: block;
    }
    .signature-tools-actions {
      display: flex;
      gap: 10px;
      margin-top: 10px;
    }
    .btn-primary {
        background: var(--primary-color);
        color: white;
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: var(--border-radius);
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    .btn-primary:hover {
        background: var(--secondary-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(74, 158, 255, 0.3);
    }
    .btn-secondary {
        background: var(--light-color);
        color: var(--text-color);
        border: 1px solid var(--border-color);
        padding: 0.75rem 1.5rem;
        border-radius: var(--border-radius);
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    .btn-secondary:hover { background: var(--border-color); }
    .saved-signatures-list {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 15px;
    }
    .saved-signature-item {
      border: 1px solid #ddd;
      border-radius: 6px;
      background: #fafbfc;
      padding: 6px 10px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .saved-signature-img {
      height: 36px;
      width: auto;
      border-radius: 4px;
      background: #fff;
      border: 1px solid #eee;
    }
    h3 { font-family: 'Montserrat', sans-serif; font-size: 1.1rem; font-weight: 600; color: var(--dark-color); margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid var(--primary-color); }
    @media (max-width: 768px) {
      .modal-content { width: 98%; max-width: 99vw; }
      .signature-tools-body { padding: 1rem; }
    }
  </style>
</head>
<body>
  <!-- Signature Tools Modal -->
  <div id="signatureToolsModal" class="modal" style="display: block;">
    <div class="modal-content signature-tools-content">
      <div class="modal-header">
        <h2>✍️ Signature Tools</h2>
        <span class="close" onclick="closeSignatureTools()">&times;</span>
      </div>
      <div class="signature-tools-body">
        <h3>Create a New Signature</h3>
        <div style="margin-bottom: 10px; display: flex; align-items: center; gap: 16px;">
          <label for="penThickness" style="font-weight:600;">Pen Thickness:</label>
          <input type="range" id="penThickness" min="1" max="12" value="2" style="flex:1; max-width:180px;">
          <span id="penThicknessValue" style="min-width:32px; text-align:center; font-weight:600;">2px</span>
        </div>
        <canvas id="signatureCanvas" class="signature-canvas"></canvas>
        <div class="signature-tools-actions">
          <button class="btn-secondary" onclick="clearSignatureCanvas()">Clear</button>
          <button class="btn-primary" onclick="saveSignature()">Save Signature</button>
        </div>
        <h3 style="margin-top: 24px;">Saved Signatures</h3>
        <div id="savedSignaturesList" class="saved-signatures-list">
          <!-- Saved signatures will be rendered here -->
        </div>
      </div>
    </div>
  </div>

  <!-- Required libraries and dashboard logic for full modal functionality -->
  <script src="dashboard-script.js"></script>
  <script>
    // Standalone fallback for modal close if dashboard-script.js is not present
    if (typeof closeSignatureTools !== 'function') {
      window.closeSignatureTools = function() {
        document.getElementById('signatureToolsModal').style.display = 'none';
      };
    }

    // Signature canvas logic (with smoothing and pen thickness)
    if (!window.signatureCanvasInitialized) {
      const canvas = document.getElementById('signatureCanvas');
      const ctx = canvas.getContext('2d');
      const thicknessInput = document.getElementById('penThickness');
      const thicknessValue = document.getElementById('penThicknessValue');
      let drawing = false;
      let points = [];
      let penThickness = parseInt(thicknessInput.value, 10);
      let strokes = [];

      function resizeCanvas() {
        // Redraw all strokes after resize
        const prevStrokes = strokes.slice();
        canvas.width = canvas.offsetWidth;
        canvas.height = canvas.offsetHeight;
        strokes = prevStrokes;
        redrawAll();
      }
      resizeCanvas();
      window.addEventListener('resize', resizeCanvas);

      thicknessInput.addEventListener('input', function() {
        penThickness = parseInt(this.value, 10);
        thicknessValue.textContent = penThickness + 'px';
      });

      function drawSmoothLine(pts, thickness) {
        if (pts.length < 2) return;
        ctx.lineJoin = ctx.lineCap = 'round';
        ctx.strokeStyle = '#222';
        ctx.lineWidth = thickness;
        ctx.beginPath();
        ctx.moveTo(pts[0].x, pts[0].y);
        for (let i = 1; i < pts.length - 2; i++) {
          const xc = (pts[i].x + pts[i + 1].x) / 2;
          const yc = (pts[i].y + pts[i + 1].y) / 2;
          ctx.quadraticCurveTo(pts[i].x, pts[i].y, xc, yc);
        }
        ctx.quadraticCurveTo(
          pts[pts.length - 2].x,
          pts[pts.length - 2].y,
          pts[pts.length - 1].x,
          pts[pts.length - 1].y
        );
        ctx.stroke();
      }

      function redrawAll() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        for (const stroke of strokes) {
          drawSmoothLine(stroke.points, stroke.thickness);
        }
        if (drawing && points.length > 1) {
          drawSmoothLine(points, penThickness);
        }
      }

      canvas.addEventListener('pointerdown', function(e) {
        drawing = true;
        points = [{ x: e.offsetX, y: e.offsetY }];
      });
      canvas.addEventListener('pointermove', function(e) {
        if (!drawing) return;
        points.push({ x: e.offsetX, y: e.offsetY });
        redrawAll();
      });
      canvas.addEventListener('pointerup', function(e) {
        if (!drawing) return;
        drawing = false;
        points.push({ x: e.offsetX, y: e.offsetY });
        if (points.length > 1) {
          strokes.push({ points: points.slice(), thickness: penThickness });
        }
        points = [];
        redrawAll();
      });
      canvas.addEventListener('pointerleave', function() {
        if (!drawing) return;
        drawing = false;
        if (points.length > 1) {
          strokes.push({ points: points.slice(), thickness: penThickness });
        }
        points = [];
        redrawAll();
      });
      window.signatureCanvasInitialized = true;
    }
    // Clear signature canvas
    if (typeof clearSignatureCanvas !== 'function') {
      window.clearSignatureCanvas = function() {
        const canvas = document.getElementById('signatureCanvas');
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        if (window.strokes !== undefined) window.strokes = [];
        // If strokes is local, clear it as well
        try { strokes = []; } catch (e) {}
      };
    }
    // Save signature to localStorage and update list (robust)
    if (typeof saveSignature !== 'function') {
      window.saveSignature = function() {
        const canvas = document.getElementById('signatureCanvas');
        const ctx = canvas.getContext('2d');
        // Only save if not blank
        const blank = document.createElement('canvas');
        blank.width = canvas.width;
        blank.height = canvas.height;
        if (canvas.toDataURL() === blank.toDataURL()) {
          alert('Please draw a signature before saving.');
          return;
        }
        const dataUrl = canvas.toDataURL('image/png');
        let saved = [];
        try {
          saved = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
        } catch (e) { saved = []; }
        saved.push(dataUrl);
        localStorage.setItem('savedSignatures', JSON.stringify(saved));
        renderSavedSignatures();
        clearSignatureCanvas();
      };
    }
    // Render saved signatures
    function renderSavedSignatures() {
      const list = document.getElementById('savedSignaturesList');
      list.innerHTML = '';
      let saved = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
      if (!saved.length) {
        list.innerHTML = '<div style="color:#888;">No saved signatures yet.</div>';
        return;
      }
      saved.forEach((dataUrl, idx) => {
        const item = document.createElement('div');
        item.className = 'saved-signature-item';
        const img = document.createElement('img');
        img.src = dataUrl;
        img.className = 'saved-signature-img';
        const delBtn = document.createElement('button');
        delBtn.textContent = '🗑️';
        delBtn.className = 'btn-secondary';
        delBtn.style.fontSize = '14px';
        delBtn.onclick = function() {
          let saved = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
          saved.splice(idx, 1);
          localStorage.setItem('savedSignatures', JSON.stringify(saved));
          renderSavedSignatures();
        };
        item.appendChild(img);
        item.appendChild(delBtn);
        list.appendChild(item);
      });
    }
    renderSavedSignatures();
  </script>
</body>
</html>
