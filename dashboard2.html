<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - DocuGen Pro</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Open+Sans:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="dashboard-styles.css">
    <link rel="stylesheet" href="freemium-styles.css">

    <!-- PDF Generation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
</head>
<body>
    <!-- Cosmic Web Animated Background -->
    <div id="cosmic-backdrop">
        <canvas id="canvas-bottom"></canvas>
        <canvas id="canvas-top"></canvas>
    </div>

    <!-- Navigation -->
    <nav class="dashboard-nav">
        <div class="nav-container">
            <div class="nav-logo">
                <h2>DocuGen Pro</h2>
            </div>
            <div class="nav-user">
                <div class="token-counter" onclick="freemiumSystem.showTokenHistory()">
                    🪙 <span class="token-balance">0</span>
                </div>
                <span id="userWelcome">Welcome, User</span>
                <button class="btn-secondary" onclick="logout()">Logout</button>
            </div>
        </div>
    </nav>

    <!-- Main Dashboard -->
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-section">
                <h3>Document Types</h3>
                <ul class="document-menu">
                    <li><a href="#" onclick="selectDocumentType('invoice')" class="doc-link active" data-type="invoice">
                        <span class="doc-icon">📄</span> Invoices
                    </a></li>
                    <li><a href="#" onclick="selectDocumentType('receipt')" class="doc-link" data-type="receipt">
                        <span class="doc-icon">🧾</span> Receipts
                    </a></li>
                    <li><a href="#" onclick="selectDocumentType('quotation')" class="doc-link" data-type="quotation">
                        <span class="doc-icon">💰</span> Quotations
                    </a></li>
                    <li><a href="#" onclick="selectDocumentType('contract')" class="doc-link" data-type="contract">
                        <span class="doc-icon">📋</span> Contracts
                    </a></li>
                    <li><a href="#" onclick="selectDocumentType('rider')" class="doc-link" data-type="rider">
                        <span class="doc-icon">🎵</span> Technical Riders
                    </a></li>
                    <li><a href="#" onclick="selectDocumentType('annexure')" class="doc-link" data-type="annexure">
                        <span class="doc-icon">📎</span> Annexures
                    </a></li>
                    <li><a href="#" onclick="selectDocumentType('artist-agreement')" class="doc-link" data-type="artist-agreement">
                        <span class="doc-icon">🎤</span> Artist Agreement
                    </a></li>
                </ul>
            </div>
            
            <div class="sidebar-section">
                <h3>Company Setup</h3>

                <!-- Company Selector -->
                <div class="company-selector" style="margin-bottom: 15px;">
                    <div class="form-group">
                        <label for="companySetupSelect" style="font-size: 12px; color: #666; margin-bottom: 5px; display: block;">Select Company for Setup:</label>
                        <select id="companySetupSelect" onchange="selectCompanyForSetup()" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px;">
                            <option value="">-- Choose a Company --</option>
                            <option value="docugen-pro">DocuGen Pro Solutions</option>
                            <option value="bongomaffin">Bongomaffin</option>
                            <option value="benjamin-music">Benjamin Music Initiatives</option>
                        </select>
                    </div>
                    <div class="company-preview" id="companyPreview" style="margin-top: 8px; padding: 8px; background: #f8f9fa; border-radius: 4px; font-size: 11px; color: #666; text-align: center; display: none;">
                        <div class="company-logo-mini" id="companyLogoMini" style="width: 30px; height: 30px; margin: 0 auto 5px; border-radius: 4px; background: #ddd; display: flex; align-items: center; justify-content: center; font-size: 12px;">📄</div>
                        <div class="company-name-mini" id="companyNameMini">Select a company</div>
                        <button onclick="useSelectedCompany()" style="margin-top: 8px; padding: 6px 12px; background: #007bff; color: white; border: none; border-radius: 4px; font-size: 11px; cursor: pointer;">Use This Company</button>
                    </div>
                </div>

                <ul class="action-menu">
                    <li><a href="#" onclick="showAddCompanyForm()">
                        <span class="action-icon">➕</span> Add Company
                        <span class="company-status new" style="background: #28a745; color: white;">New</span>
                    </a></li>
                    <li><a href="#" onclick="showCompanyInfo()">
                        <span class="action-icon">🏢</span> Company Information
                        <span class="company-status empty" id="companyInfoStatus">Not Set</span>
                    </a></li>
                    <li><a href="#" onclick="showBankDetails()">
                        <span class="action-icon">🏦</span> Banking Details
                        <span class="company-status empty" id="bankDetailsStatus">Not Set</span>
                    </a></li>
                </ul>
                <div class="setup-notice" id="setupNotice" style="margin-top: 15px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; font-size: 12px; color: #856404;">
                    <strong>💡 Quick Setup:</strong> Click "Add Company" to create professional company profiles with banking details, branding, and logos. Configure once and auto-populate all documents!
                </div>

                <!-- Test Active Company Integration -->
                <div class="test-integration" style="margin-top: 10px; padding: 8px; background: #e3f2fd; border: 1px solid #2196f3; border-radius: 4px; font-size: 11px;">
                    <strong>🧪 Test Integration:</strong>
                    <button onclick="testActiveCompanyIntegration()" style="margin-left: 5px; padding: 2px 6px; background: #2196f3; color: white; border: none; border-radius: 3px; font-size: 10px; cursor: pointer;">Test Document Generator</button>
                    <button onclick="testModal()" style="margin-left: 5px; padding: 2px 6px; background: #ff9800; color: white; border: none; border-radius: 3px; font-size: 10px; cursor: pointer;">Test Modal</button>
                </div>
            </div>

            <div class="sidebar-section">
                <h3>Document Toolkit</h3>
                <ul class="action-menu">
                    <li><a href="#" onclick="showSignatureTools()">
                        <span class="action-icon">✍️</span> Signature Tools
                    </a></li>
                    <li><a href="#" onclick="showDocumentEditor()">
                        <span class="action-icon">📝</span> Document Editor
                    </a></li>
                    <li><a href="#" onclick="showPDFTools()">
                        <span class="action-icon">🔧</span> PDF Tools
                    </a></li>


                </ul>
            </div>

            <div class="sidebar-section">
                <h3>Quick Actions</h3>
                <ul class="action-menu">
                    <li><a href="#" onclick="openGenerator()">
                        <span class="action-icon">➕</span> Create New Document
                    </a></li>

                    <li><a href="#" onclick="showTemplates()">
                        <span class="action-icon">🎨</span> Manage Templates
                    </a></li>
                    <li><a href="#" onclick="showClients()">
                        <span class="action-icon">👥</span> Client Profiles
                    </a></li>
                </ul>
            </div>

            <div class="sidebar-section">
                <h3>Analytics</h3>
                <ul class="action-menu">
                    <li><a href="#" onclick="showSharingAnalytics()">
                        <span class="action-icon">📊</span> Sharing Analytics
                    </a></li>
                    <li><a href="#" onclick="showDocumentHistory()">
                        <span class="action-icon">📋</span> Document History
                    </a></li>
                </ul>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Header -->
            <div class="dashboard-header">
                <div class="active-company-banner" id="activeCompanyBanner" style="margin-bottom: 15px; padding: 8px 12px; background: linear-gradient(135deg, #4a9eff 0%, #667eea 100%); color: white; border-radius: 8px; font-size: 14px; font-weight: 600; text-align: center;">
                    <span id="activeCompanyDisplay">📄 DocuGen Pro Solutions Ltd</span>
                    <small style="display: block; font-size: 11px; opacity: 0.9; margin-top: 2px;">Active Company - All documents will use this company's details</small>
                </div>
                <h1 id="documentTypeTitle">Invoice Generator</h1>
                <p id="documentTypeDescription">Create professional invoices with automatic calculations and VAT handling</p>
                <div class="header-actions">
                    <button class="btn-primary" onclick="openGenerator()">
                        <span>Create New</span>
                    </button>
                    <button class="btn-outline" onclick="showPreview()">
                        <span>Preview Template</span>
                    </button>
                </div>
            </div>

            <!-- Token Widget -->
            <div class="token-widget">
                <div class="token-widget-header">
                    <div class="token-widget-title">🪙 Token Balance</div>
                    <div class="token-widget-balance"><span class="token-balance">0</span> tokens</div>
                </div>
                <div class="token-actions">
                    <button class="token-btn purchase" onclick="freemiumSystem.showSubscriptionModal()">
                        💳 Purchase Tokens
                    </button>
                    <button class="token-btn history" onclick="freemiumSystem.showTokenHistory()">
                        📊 Usage History
                    </button>
                </div>
            </div>

            <!-- Document Type Info -->
            <div class="document-info">
                <div class="info-grid">
                    <div class="info-card">
                        <div class="info-icon">📊</div>
                        <div class="info-content">
                            <h3>Features</h3>
                            <ul id="documentFeatures">
                                <li>Automatic calculations</li>
                                <li>VAT handling</li>
                                <li>Professional formatting</li>
                                <li>Client management</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="info-card">
                        <div class="info-icon">🎯</div>
                        <div class="info-content">
                            <h3>Best For</h3>
                            <ul id="documentUseCases">
                                <li>Service billing</li>
                                <li>Product sales</li>
                                <li>Recurring payments</li>
                                <li>Business transactions</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="info-card">
                        <div class="info-icon">⚙️</div>
                        <div class="info-content">
                            <h3>Customization</h3>
                            <ul id="documentCustomization">
                                <li>Multiple themes</li>
                                <li>Logo upload</li>
                                <li>Custom watermarks</li>
                                <li>Template management</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Documents -->
            <div class="recent-section">
                <div class="section-header">
                    <h2>Recent Documents</h2>
                    <button class="btn-outline small" onclick="viewAllDocuments()">View All</button>
                </div>
                <div class="recent-grid" id="recentDocuments">
                    <!-- Recent documents will be populated here -->
                </div>
            </div>

            <!-- Quick Start Guide -->
            <div class="quick-start">
                <h2>Quick Start Guide</h2>
                <div class="steps-grid">
                    <div class="step-card">
                        <div class="step-number">1</div>
                        <h3>Choose Document Type</h3>
                        <p>Select from 7 professional document types in the sidebar</p>
                    </div>
                    <div class="step-card">
                        <div class="step-number">2</div>
                        <h3>Fill in Details</h3>
                        <p>Add your company info, client details, and document content</p>
                    </div>
                    <div class="step-card">
                        <div class="step-number">3</div>
                        <h3>Customize & Generate</h3>
                        <p>Apply themes, add logos, and generate professional PDFs</p>
                    </div>
                </div>
            </div>
        </main>
    </div>


    <!-- Document Generator Modal -->
    <div id="generatorModal" class="modal">
        <div class="modal-content generator-content">
            <div class="modal-header">
                <h2 id="generatorTitle">Create New Invoice</h2>
                <span class="close" onclick="closeGenerator()">&times;</span>
            </div>
            <div class="generator-body">
                <p>Loading document generator...</p>
                <div class="loading-spinner"></div>
            </div>
        </div>
    </div>


    <!-- Preview Modal -->
    <div id="previewModal" class="modal">
        <div class="modal-content preview-content">
            <div class="modal-header">
                <h2 id="previewTitle">Document Preview</h2>
                <span class="close" onclick="closePreview()">&times;</span>
            </div>
            <div id="previewContainer">
                <!-- Preview content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Company Information Modal -->
    <div id="companyInfoModal" class="modal">
        <div class="modal-content company-info-content">
            <div class="modal-header">
                <h2>Company Information</h2>
                <span class="close" onclick="closeCompanyInfo()">&times;</span>
            </div>
            <div class="company-info-body">
                <form id="companyInfoForm">
                    <div class="form-grid">
                        <div class="form-section">
                            <h3>Basic Information</h3>
                            <div class="form-group">
                                <label for="companyName">Company Name *</label>
                                <input type="text" id="companyName" name="companyName" autocomplete="organization" placeholder="e.g., DocuGen Pro Solutions Ltd" required>
                            </div>
                            <div class="form-group">
                                <label for="companyRegNumber">Registration Number</label>
                                <input type="text" id="companyRegNumber" name="companyRegNumber" autocomplete="off" placeholder="e.g., 2024/123456/07">
                            </div>
                            <div class="form-group">
                                <label for="companyVatNumber">VAT Number</label>
                                <input type="text" id="companyVatNumber" name="companyVatNumber" autocomplete="off" placeholder="e.g., 4123456789">
                            </div>
                        </div>

                        <div class="form-section">
                            <h3>Contact Details</h3>
                            <div class="form-group">
                                <label for="companyAddress">Physical Address *</label>
                                <textarea id="companyAddress" rows="3" placeholder="123 Business Street&#10;Business District&#10;Johannesburg, 2000" required></textarea>
                            </div>
                            <div class="form-group">
                                <label for="companyPhone">Phone Number</label>
                                <input type="tel" id="companyPhone" name="companyPhone" autocomplete="tel-national" placeholder="+27 11 123 4567">
                            </div>
                            <div class="form-group">
                                <label for="companyEmail">Email Address *</label>
                                <input type="email" id="companyEmail" name="companyEmail" autocomplete="email" placeholder="<EMAIL>" required>
                            </div>
                            <div class="form-group">
                                <label for="companyWebsite">Website</label>
                                <input type="url" id="companyWebsite" name="companyWebsite" autocomplete="url" placeholder="https://www.docugenpro.com">
                            </div>
                        </div>

                        <div class="form-section">
                            <h3>Representative Information</h3>
                            <div class="form-group">
                                <label for="representativeName">Representative Name</label>
                                <input type="text" id="representativeName" name="representativeName" autocomplete="name" placeholder="John Smith">
                            </div>
                            <div class="form-group">
                                <label for="representativeTitle">Title/Position</label>
                                <input type="text" id="representativeTitle" name="representativeTitle" autocomplete="organization-title" placeholder="Managing Director">
                            </div>
                            <div class="form-group">
                                <label for="representativeEmail">Representative Email</label>
                                <input type="email" id="representativeEmail" name="representativeEmail" autocomplete="email" placeholder="<EMAIL>">
                            </div>
                        </div>

                        <div class="form-section" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border: 2px solid var(--primary-color); border-radius: 12px;">
                            <h3 style="color: var(--primary-color); font-size: 1.3rem; text-align: center; margin-bottom: 25px;">
                                🎨 Company Logo
                            </h3>

                            <!-- Current Company Logo Display -->
                            <div class="form-group" style="text-align: center; margin-bottom: 20px;">
                                <label style="font-size: 16px; font-weight: 600; color: #333; margin-bottom: 15px; display: block;">
                                    🏢 Current Company Logo
                                </label>
                                <div id="currentCompanyLogo" class="current-company-logo" style="width: 100px; height: 100px; margin: 0 auto 10px; border: 2px solid #ddd; border-radius: 8px; display: flex; align-items: center; justify-content: center; background: white;">
                                    <div class="logo-placeholder">
                                        📄<br>
                                        <small>Loading...</small>
                                    </div>
                                </div>
                                <div id="currentCompanyName" style="font-size: 14px; font-weight: 600; color: #333; margin-bottom: 5px;">Company Name</div>
                                <small style="color: #666; font-size: 12px;">This is your company's current logo</small>
                            </div>

                            <div class="form-group" style="text-align: center;">
                                <label for="companyLogoUpload" style="font-size: 16px; font-weight: 600; color: #333;">
                                    📤 Upload New Logo (Optional)
                                </label>
                                <input type="file" id="companyLogoUpload" name="companyLogoUpload" autocomplete="off" accept="image/*" style="margin: 10px 0; width: 100%; max-width: 300px;">
                                <small style="color: #666; display: block; margin-bottom: 15px; font-size: 13px;">
                                    📋 <strong>Recommended:</strong> PNG or JPG format, square aspect ratio (500x500px or higher) for best results
                                </small>
                            </div>

                            <div class="form-group" style="text-align: center;">
                                <label for="companyLogoShape" style="font-size: 16px; font-weight: 600; color: #333;">
                                    🖼️ Logo Frame Style
                                </label>
                                <select id="companyLogoShape" style="margin-top: 8px; padding: 10px; font-size: 14px; border-radius: 6px;">
                                    <option value="square">⬜ Square Frame</option>
                                    <option value="round">⭕ Round Frame</option>
                                </select>
                            </div>

                            <div class="form-group" style="text-align: center;">
                                <label style="font-size: 16px; font-weight: 600; color: #333; margin-bottom: 15px; display: block;">
                                    👁️ New Logo Preview
                                </label>
                                <div id="companyLogoPreview" class="company-logo-preview">
                                    <div class="logo-placeholder">
                                        📷<br>
                                        <strong>Upload New Logo</strong><br>
                                        <small>Click "Choose File" above</small>
                                    </div>
                                </div>
                                <small style="color: #666; margin-top: 10px; display: block;">
                                    New logo will replace the current one and appear on all your documents
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn-secondary" onclick="closeCompanyInfo()">Cancel</button>
                        <button type="submit" class="btn-primary">Save Company Information</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Banking Details Modal -->
    <div id="bankDetailsModal" class="modal">
        <div class="modal-content bank-details-content">
            <div class="modal-header">
                <h2>Banking Details</h2>
                <span class="close" onclick="closeBankDetails()">&times;</span>
            </div>
            <div class="bank-details-body">
                <!-- Current Company Display -->
                <div class="current-company-display" style="text-align: center; margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; border: 1px solid #dee2e6;">
                    <div id="bankingCurrentCompanyLogo" class="current-company-logo" style="width: 60px; height: 60px; margin: 0 auto 10px; border: 2px solid #ddd; border-radius: 6px; display: flex; align-items: center; justify-content: center; background: white;">
                        <div class="logo-placeholder">
                            📄<br>
                            <small>Loading...</small>
                        </div>
                    </div>
                    <div id="bankingCurrentCompanyName" style="font-size: 16px; font-weight: 600; color: #333; margin-bottom: 5px;">Company Name</div>
                    <small style="color: #666; font-size: 12px;">Banking details for this company</small>
                </div>

                <form id="bankDetailsForm">
                    <div class="form-grid">
                        <div class="form-section">
                            <h3>Primary Bank Account</h3>
                            <div class="form-group">
                                <label for="bankName">Bank Name *</label>
                                <input type="text" id="bankName" name="bankName" autocomplete="off" placeholder="e.g., First National Bank" required>
                            </div>
                            <div class="form-group">
                                <label for="accountHolder">Account Holder *</label>
                                <input type="text" id="accountHolder" name="accountHolder" autocomplete="name" placeholder="DocuGen Pro Solutions Ltd" required>
                            </div>
                            <div class="form-group">
                                <label for="accountNumber">Account Number *</label>
                                <input type="text" id="accountNumber" name="accountNumber" autocomplete="off" placeholder="**********" required>
                            </div>
                            <div class="form-group">
                                <label for="branchCode">Branch Code</label>
                                <input type="text" id="branchCode" name="branchCode" autocomplete="off" placeholder="123456">
                            </div>
                            <div class="form-group">
                                <label for="accountType">Account Type</label>
                                <select id="accountType">
                                    <option value="current">Current Account</option>
                                    <option value="savings">Savings Account</option>
                                    <option value="business">Business Account</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3>International Details (Optional)</h3>
                            <div class="form-group">
                                <label for="swiftCode">SWIFT Code</label>
                                <input type="text" id="swiftCode" name="swiftCode" autocomplete="off" placeholder="FIRNZAJJ">
                            </div>
                            <div class="form-group">
                                <label for="iban">IBAN</label>
                                <input type="text" id="iban" name="iban" autocomplete="off" placeholder="ZA89 1234 5678 9012 3456 789">
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn-secondary" onclick="closeBankDetails()">Cancel</button>
                        <button type="submit" class="btn-primary">Save Banking Details</button>
                    </div>
                </form>
            </div>
        </div>
    </div>





    <!-- Simplified PDF Signing Modal -->
    <div id="documentImportModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>✍️ Sign PDF Document</h3>
                <span class="close" onclick="closePdfSigning()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="pdf-upload-section">
                    <h4>📄 Upload PDF Document</h4>
                    <div id="pdfUploadArea" class="upload-area">
                        <input type="file" id="pdfUpload" name="pdfUpload" accept=".pdf,application/pdf" style="display: none;">
                        <div class="upload-placeholder" onclick="document.getElementById('pdfUpload').click()">
                            <div class="upload-icon">📄</div>
                            <h4>Click to Upload PDF</h4>
                            <p>Only PDF files are supported</p>
                            <p class="file-size-limit">Max file size: 10MB</p>
                        </div>
                    </div>
                    <div id="uploadedPdfInfo" class="uploaded-file-info" style="display: none;">
                        <div class="file-details">
                            <span class="file-icon">📄</span>
                            <div class="file-info">
                                <div class="file-name"></div>
                                <div class="file-size"></div>
                            </div>
                            <button class="remove-file-btn" onclick="removeUploadedPdf()" title="Remove PDF">×</button>
                        </div>
                    </div>
                </div>

                <div class="signature-section" id="signatureSection" style="display: none;">
                    <h4>✍️ Add Your Signature</h4>
                    <div class="signature-tools">
                        <button class="signature-btn active" onclick="enableSignatureMode()" id="signatureBtn">
                            ✍️ Place Signature
                        </button>
                        <div class="signature-library-toggle">
                            <button class="tool-btn" onclick="toggleSignatureLibrary()" id="signatureLibraryBtn">
                                📚 Signature Library
                            </button>
                        </div>
                    </div>

                    <!-- Signature Library -->
                    <div id="signatureLibrary" class="signature-library" style="display: none;">
                        <div class="signature-library-header">
                            <h5>📚 Your Signatures</h5>
                            <button class="btn-secondary" onclick="openSignatureCreator()">+ Create New</button>
                        </div>
                        <div id="signatureList" class="signature-list">
                            <!-- Signatures will be loaded here -->
                        </div>
                    </div>
                </div>

                <div class="pdf-preview-section" id="pdfPreviewSection" style="display: none;">
                    <h4>👁️ PDF Preview</h4>
                    <div class="zoom-controls">
                        <button class="zoom-btn" onclick="zoomOut()" title="Zoom Out">🔍-</button>
                        <span class="zoom-level" id="zoomLevel">100%</span>
                        <button class="zoom-btn" onclick="zoomIn()" title="Zoom In">🔍+</button>
                        <button class="zoom-btn" onclick="resetZoom()" title="Reset Zoom">🔄</button>
                    </div>
                    <div class="pdf-preview-container" id="pdfPreviewContainer">
                        <div class="preview-placeholder">
                            <div class="preview-icon">📄</div>
                            <h4>Upload a PDF to see preview</h4>
                            <p>Click anywhere on the PDF to place your signature</p>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn-secondary" onclick="closePdfSigning()">Cancel</button>
                    <button type="button" class="btn-success" onclick="exportSignedPdf()" id="exportPdfBtn" disabled style="background: #28a745;">
                        📥 Download Signed PDF
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Signature Tools Modal -->
    <div id="signatureToolsModal" class="modal">
        <div class="modal-content signature-tools-content">
            <div class="modal-header">
                <h2>✍️ Signature Tools</h2>
                <span class="close" onclick="closeSignatureTools()">&times;</span>
            </div>
            <div class="signature-tools-body">
                <div class="signature-options">
                    <div class="signature-section">
                        <h3>🖊️ Create Digital Signature</h3>
                        <div class="signature-style-controls">
                            <div class="style-group">
                                <label for="signatureColor">Pen Color:</label>
                                <input type="color" id="signatureColor" name="signatureColor" autocomplete="off" value="#000000" onchange="updateSignatureStyle()">
                            </div>
                            <div class="style-group">
                                <label for="signatureThickness">Pen Thickness:</label>
                                <input type="range" id="signatureThickness" name="signatureThickness" autocomplete="off" min="1" max="8" value="2" onchange="updateSignatureStyle()">
                                <span id="thicknessValue">2px</span>
                            </div>
                            <div class="style-group">
                                <label for="signatureSmoothing">Smoothing:</label>
                                <input type="checkbox" id="signatureSmoothing" name="signatureSmoothing" autocomplete="off" checked onchange="updateSignatureStyle()">
                                <span>Enable smooth curves</span>
                            </div>
                        </div>
                        <div class="signature-canvas-container">
                            <canvas id="signatureCanvas" width="400" height="200"></canvas>
                            <div class="signature-controls">
                                <button type="button" class="btn-secondary" onclick="clearSignature()">🗑️ Clear</button>
                                <button type="button" class="btn-secondary" onclick="undoLastStroke()">↶ Undo</button>
                                <button type="button" class="btn-primary" onclick="saveSignature()">💾 Save Signature</button>
                            </div>
                        </div>
                    </div>

                    <div class="signature-section">
                        <h3>📝 Text Signature</h3>
                        <div class="text-signature-container">
                            <label for="textSignature" style="display: none;">Text Signature Input</label>
                            <input type="text" id="textSignature" name="textSignature" autocomplete="name" placeholder="Type your name" maxlength="50">
                            <select id="signatureFont">
                                <option value="cursive">Cursive Style</option>
                                <option value="script">Script Style</option>
                                <option value="elegant">Elegant Style</option>
                            </select>
                            <div class="text-signature-preview" id="textSignaturePreview">
                                Preview will appear here
                            </div>
                            <button type="button" class="btn-primary" onclick="saveTextSignature()">Save Text Signature</button>
                        </div>
                    </div>

                    <div class="signature-section">
                        <h3>📤 Upload Signature</h3>
                        <div class="signature-upload-container">
                            <label for="signatureUpload" style="display: none;">Upload Signature Image</label>
                            <input type="file" id="signatureUpload" name="signatureUpload" autocomplete="off" accept="image/*" style="display: none;">
                            <div class="signature-upload-area" onclick="document.getElementById('signatureUpload').click()">
                                <div class="upload-icon">🖼️</div>
                                <p>Click to upload signature image</p>
                                <small>PNG, JPG formats supported</small>
                            </div>
                            <div id="uploadedSignaturePreview" class="signature-preview" style="display: none;"></div>
                        </div>
                    </div>
                </div>

                <div class="saved-signatures">
                    <h3>💾 Saved Signatures</h3>
                    <div id="savedSignaturesList" class="signatures-list" style="display: flex; flex-wrap: wrap; gap: 10px; padding: 10px;"></div>
                    <script>
                    function populateSavedSignaturesList() {
                        const list = document.getElementById('savedSignaturesList');
                        if (!list) return;

                        list.innerHTML = ''; // Clear existing content
                        let savedSignatures = [];
                        const raw = localStorage.getItem('savedSignatures');

                        if (raw) {
                            try {
                                const parsed = JSON.parse(raw);
                                savedSignatures = Array.isArray(parsed) ? parsed : (typeof parsed === 'object' && parsed !== null ? Object.values(parsed) : []);
                            } catch (e) {
                                console.error('Failed to parse savedSignatures:', e);
                            }
                        }

                        if (!savedSignatures.length) {
                            list.innerHTML = '<p class="no-signatures" style="color: #888; font-style: italic;">No saved signatures yet</p>';
                            return;
                        }

                        savedSignatures.forEach((sig, idx) => {
                            if (!sig?.data) return; // Skip invalid signatures
                            const div = document.createElement('div');
                            div.className = 'signature-thumb';
                            div.style.cssText = `
                                display: inline-flex;
                                flex-direction: column;
                                align-items: center;
                                margin: 0;
                                padding: 10px;
                                background: #fff;
                                border: 1px solid #e0e0e0;
                                border-radius: 8px;
                                min-width: 100px;
                                max-width: 120px;
                                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                                transition: transform 0.2s;
                            `;
                            div.innerHTML = `
                                <img src="${sig.data}" alt="Signature ${idx + 1}" style="max-width: 80px; max-height: 40px; display: block; margin-bottom: 8px;">
                                <span style="font-size: 12px; color: #555; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; width: 100%; text-align: center;">
                                    ${sig.name ? sig.name.replace(/</g, '&lt;') : 'Signature ' + (idx + 1)}
                                </span>
                            `;
                            div.addEventListener('mouseover', () => div.style.transform = 'scale(1.05)');
                            div.addEventListener('mouseout', () => div.style.transform = 'scale(1)');
                            list.appendChild(div);
                        });
                    }

                    // Handle modal open with standard Bootstrap or custom modal events
                    const sigToolsModal = document.getElementById('signatureToolsModal');
                    if (sigToolsModal) {
                        sigToolsModal.addEventListener('show.bs.modal', populateSavedSignaturesList); // Bootstrap modal
                        sigToolsModal.addEventListener('shown', populateSavedSignaturesList); // Custom modal fallback
                    }

                    // Handle new signature save event
                    document.addEventListener('signatureSaved', populateSavedSignaturesList);

                    // Populate on DOMContentLoaded if modal is already open
                    document.addEventListener('DOMContentLoaded', () => {
                        if (sigToolsModal?.style.display === 'block') {
                            populateSavedSignaturesList();
                        }
                    });

                    // Example function to save a new signature (for reference)
                    function saveSignature(signatureData, name) {
                        let savedSignatures = [];
                        const raw = localStorage.getItem('savedSignatures');
                        if (raw) {
                            try {
                                const parsed = JSON.parse(raw);
                                savedSignatures = Array.isArray(parsed) ? parsed : Object.values(parsed || {});
                            } catch (e) {
                                console.error('Failed to parse savedSignatures for save:', e);
                            }
                        }
                        savedSignatures.push({ data: signatureData, name });
                        localStorage.setItem('savedSignatures', JSON.stringify(savedSignatures));
                        document.dispatchEvent(new CustomEvent('signatureSaved')); // Trigger update
                    }
                    </script>
                </div>
            </div>
        </div>
    </div>

    <!-- Document Sharing Modal -->
    <div id="documentSharingModal" class="modal">
        <div class="modal-content document-sharing-content">
            <div class="modal-header">
                <h2>📤 Share Document</h2>
                <span class="close" onclick="closeDocumentSharing()">&times;</span>
            </div>
            <div class="document-sharing-body">
                <div class="sharing-options">
                    <div class="sharing-section">
                        <h3>📧 Email Sharing</h3>
                        <div class="email-sharing-container">
                            <div class="form-group">
                                <label for="recipientEmail">Recipient Email *</label>
                                <input type="email" id="recipientEmail" name="recipientEmail" autocomplete="email" placeholder="<EMAIL>" required>
                            </div>
                            <div class="form-group">
                                <label for="emailSubject">Subject</label>
                                <input type="text" id="emailSubject" name="emailSubject" autocomplete="off" placeholder="Document from [Your Company]">
                            </div>
                            <div class="form-group">
                                <label for="emailMessage">Message</label>
                                <textarea id="emailMessage" rows="4" placeholder="Please find the attached document..."></textarea>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="sendCopy" name="sendCopy" autocomplete="off" checked>
                                    Send a copy to myself
                                </label>
                            </div>
                            <button type="button" class="btn-primary" onclick="shareViaEmail()">
                                📧 Send Email
                            </button>
                        </div>
                    </div>

                    <div class="sharing-section">
                        <h3>💬 WhatsApp Sharing</h3>
                        <div class="whatsapp-sharing-container">
                            <div class="form-group">
                                <label for="whatsappNumber">Phone Number (with country code)</label>
                                <input type="tel" id="whatsappNumber" name="whatsappNumber" autocomplete="tel" placeholder="+**********">
                                <small>Include country code (e.g., +1 for US, +44 for UK)</small>
                            </div>
                            <div class="form-group">
                                <label for="whatsappMessage">Message</label>
                                <textarea id="whatsappMessage" rows="3" placeholder="Hi! Please find the attached document..."></textarea>
                            </div>
                            <div class="whatsapp-options">
                                <button type="button" class="btn-whatsapp" onclick="shareViaWhatsApp()">
                                    💬 Share on WhatsApp
                                </button>
                                <button type="button" class="btn-whatsapp-web" onclick="shareViaWhatsAppWeb()">
                                    🌐 WhatsApp Web
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="sharing-section">
                        <h3>🔗 Quick Share Options</h3>
                        <div class="quick-share-container">
                            <button type="button" class="quick-share-btn" onclick="copyDocumentLink()">
                                🔗 Copy Link
                            </button>
                            <button type="button" class="quick-share-btn" onclick="shareViaSMS()">
                                📱 SMS
                            </button>
                            <button type="button" class="quick-share-btn" onclick="shareViaLinkedIn()">
                                💼 LinkedIn
                            </button>
                            <button type="button" class="quick-share-btn" onclick="shareViaTelegram()">
                                ✈️ Telegram
                            </button>
                        </div>
                    </div>

                    <div class="sharing-section">
                        <h3>📊 Sharing Analytics</h3>
                        <div class="analytics-container">
                            <div class="analytics-card">
                                <div class="analytics-icon">👁️</div>
                                <div class="analytics-info">
                                    <h4>Document Views</h4>
                                    <p>Track when recipients view your document</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="trackViews" name="trackViews" autocomplete="off" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="analytics-card">
                                <div class="analytics-icon">📧</div>
                                <div class="analytics-info">
                                    <h4>Email Notifications</h4>
                                    <p>Get notified when document is opened</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="emailNotifications" name="emailNotifications" autocomplete="off">
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="sharing-preview">
                    <h3>📄 Document Preview</h3>
                    <div id="sharingDocumentPreview" class="sharing-document-preview">
                        <div class="preview-placeholder">
                            <div class="preview-icon">📄</div>
                            <p>Document preview will appear here</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Client Profiles Management Modal -->
    <div id="clientProfilesModal" class="modal">
        <div class="modal-content client-profiles-content">
            <div class="modal-header">
                <h2>👥 Client Profiles Management</h2>
                <span class="close" onclick="closeClientProfilesModal()">&times;</span>
            </div>

            <div class="client-profiles-container">
                <!-- Client List Section -->
                <div class="client-list-section">
                    <div class="section-header">
                        <h3>📋 Existing Clients</h3>
                        <button type="button" class="btn-primary" onclick="showAddClientForm()">
                            <span>➕ Add New Client</span>
                        </button>
                    </div>

                    <div class="client-search">
                        <input type="text" id="clientSearchInput" placeholder="🔍 Search clients..." onkeyup="filterClients()">
                    </div>

                    <div class="clients-grid" id="clientsGrid">
                        <!-- Clients will be populated here -->
                    </div>
                </div>

                <!-- Client Form Section -->
                <div class="client-form-section" id="clientFormSection" style="display: none;">
                    <div class="section-header">
                        <h3 id="clientFormTitle">➕ Add New Client</h3>
                        <button type="button" class="btn-secondary" onclick="hideClientForm()">
                            <span>← Back to List</span>
                        </button>
                    </div>

                    <form id="clientProfileForm" class="client-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="clientProfileName">
                                    <span class="required">*</span> Profile Name:
                                </label>
                                <input type="text" id="clientProfileName" placeholder="Enter client profile name" required>
                            </div>
                            <div class="form-group">
                                <label for="clientCompanyName">
                                    <span class="required">*</span> Company Name:
                                </label>
                                <input type="text" id="clientCompanyName" placeholder="Enter company name" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="clientRegNumber">Registration Number:</label>
                                <input type="text" id="clientRegNumber" placeholder="YYYY/XXXXXX/XX">
                            </div>
                            <div class="form-group">
                                <label for="clientVatNumber">VAT Number:</label>
                                <input type="text" id="clientVatNumber" placeholder="VAT registration number">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="clientAddress">
                                <span class="required">*</span> Address:
                            </label>
                            <textarea id="clientAddress" rows="3" placeholder="Enter full address" required></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="clientAttention">Contact Person:</label>
                                <input type="text" id="clientAttention" placeholder="Contact person name">
                            </div>
                            <div class="form-group">
                                <label for="clientPhone">
                                    <span class="required">*</span> Phone:
                                </label>
                                <input type="tel" id="clientPhone" placeholder="+27 XX XXX XXXX" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="clientEmail">Email:</label>
                                <input type="email" id="clientEmail" placeholder="<EMAIL>">
                            </div>
                            <div class="form-group">
                                <label for="clientWebsite">Website:</label>
                                <input type="url" id="clientWebsite" placeholder="https://www.company.com">
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn-secondary" onclick="resetClientForm()">
                                <span>🔄 Reset Form</span>
                            </button>
                            <button type="submit" class="btn-primary">
                                <span id="saveClientBtnText">💾 Save Client</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Company Management Modal -->
    <div id="companyManagementModal" class="modal">
        <div class="modal-content company-management-content">
            <div class="modal-header">
                <h2>🏢 Company Management</h2>
                <span class="close" onclick="closeCompanyManagementModal()">&times;</span>
            </div>

            <div class="company-management-container">
                <!-- Company Selector -->
                <div class="company-selector-section">
                    <div class="section-header">
                        <h3>🏢 Your Companies</h3>
                        <button type="button" class="btn-primary" onclick="showAddCompanyForm()">
                            <span>➕ Add Company</span>
                        </button>
                    </div>

                    <div class="companies-grid" id="companiesGrid">
                        <!-- Companies will be populated here -->
                    </div>
                </div>

                <!-- Company Form Section -->
                <div class="company-form-section" id="companyFormSection" style="display: none;">
                    <div class="section-header">
                        <h3 id="companyFormTitle">➕ Add New Company</h3>
                        <button type="button" class="btn-secondary" onclick="hideCompanyForm()">
                            <span>← Back to List</span>
                        </button>
                    </div>

                    <form id="companyProfileForm" class="company-form">
                        <div class="form-tabs">
                            <button type="button" class="tab-btn active" onclick="switchCompanyTab('basic')">Basic Info</button>
                            <button type="button" class="tab-btn" onclick="switchCompanyTab('banking')">Banking</button>
                            <button type="button" class="tab-btn" onclick="switchCompanyTab('branding')">Branding</button>
                        </div>

                        <!-- Basic Info Tab -->
                        <div id="basicInfoTab" class="tab-content active">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="companyName">
                                        <span class="required">*</span> Company Name:
                                    </label>
                                    <input type="text" id="companyName" placeholder="Enter company name" required>
                                </div>
                                <div class="form-group">
                                    <label for="companyRegNumber">Registration Number:</label>
                                    <input type="text" id="companyRegNumber" placeholder="YYYY/XXXXXX/XX">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="companyVatNumber">VAT Number:</label>
                                    <input type="text" id="companyVatNumber" placeholder="VAT registration number">
                                </div>
                                <div class="form-group">
                                    <label for="companyPhone">
                                        <span class="required">*</span> Phone:
                                    </label>
                                    <input type="tel" id="companyPhone" placeholder="+27 XX XXX XXXX" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="companyAddress">
                                    <span class="required">*</span> Address:
                                </label>
                                <textarea id="companyAddress" rows="3" placeholder="Enter full address" required></textarea>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="companyEmail">
                                        <span class="required">*</span> Email:
                                    </label>
                                    <input type="email" id="companyEmail" placeholder="<EMAIL>" required>
                                </div>
                                <div class="form-group">
                                    <label for="companyWebsite">Website:</label>
                                    <input type="url" id="companyWebsite" placeholder="https://www.company.com">
                                </div>
                            </div>
                        </div>

                        <!-- Banking Tab -->
                        <div id="bankingTab" class="tab-content">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="bankName">Bank Name:</label>
                                    <input type="text" id="bankName" placeholder="e.g., First National Bank">
                                </div>
                                <div class="form-group">
                                    <label for="accountHolder">Account Holder:</label>
                                    <input type="text" id="accountHolder" placeholder="Account holder name">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="accountNumber">Account Number:</label>
                                    <input type="text" id="accountNumber" placeholder="Account number">
                                </div>
                                <div class="form-group">
                                    <label for="branchCode">Branch Code:</label>
                                    <input type="text" id="branchCode" placeholder="Branch code">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="swiftCode">SWIFT Code:</label>
                                    <input type="text" id="swiftCode" placeholder="SWIFT/BIC code">
                                </div>
                                <div class="form-group">
                                    <label for="accountType">Account Type:</label>
                                    <select id="accountType">
                                        <option value="">Select account type</option>
                                        <option value="current">Current Account</option>
                                        <option value="savings">Savings Account</option>
                                        <option value="business">Business Account</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Branding Tab -->
                        <div id="brandingTab" class="tab-content">
                            <div class="form-group">
                                <label for="companyLogo">Company Logo:</label>
                                <input type="file" id="companyLogo" accept="image/*" onchange="previewCompanyLogo(this)">
                                <div class="logo-preview" id="logoPreview"></div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="brandColor">Brand Color:</label>
                                    <input type="color" id="brandColor" value="#4a9eff">
                                </div>
                                <div class="form-group">
                                    <label for="companySlogan">Company Slogan:</label>
                                    <input type="text" id="companySlogan" placeholder="Your company slogan">
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn-secondary" onclick="resetCompanyForm()">
                                <span>🔄 Reset Form</span>
                            </button>
                            <button type="submit" class="btn-primary">
                                <span id="saveCompanyBtnText">💾 Save Company</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- PDF.js Library for PDF Processing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script>
        // Configure PDF.js worker
        if (typeof pdfjsLib !== 'undefined') {
            pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
        }
    </script>

    <!-- Cosmic Web Animation Script -->
    <script>
        // Vector class for cosmic web animation
        class Vector {
            constructor(x = 0, y = 0) {
                this.x = x;
                this.y = y;
            }

            add(vector) {
                this.x += vector.x;
                this.y += vector.y;
                return this;
            }

            subtract(vector) {
                this.x -= vector.x;
                this.y -= vector.y;
                return this;
            }

            multiply(scalar) {
                this.x *= scalar;
                this.y *= scalar;
                return this;
            }

            divide(scalar) {
                this.x /= scalar;
                this.y /= scalar;
                return this;
            }

            magnitude() {
                return Math.sqrt(this.x * this.x + this.y * this.y);
            }

            normalize() {
                const mag = this.magnitude();
                if (mag > 0) {
                    this.divide(mag);
                }
                return this;
            }

            distance(vector) {
                const dx = this.x - vector.x;
                const dy = this.y - vector.y;
                return Math.sqrt(dx * dx + dy * dy);
            }

            copy() {
                return new Vector(this.x, this.y);
            }

            static random(min = 0, max = 1) {
                return new Vector(
                    Math.random() * (max - min) + min,
                    Math.random() * (max - min) + min
                );
            }
        }

        // Cosmic Web Animation
        class CosmicWeb {
            constructor() {
                this.canvasBottom = document.getElementById('canvas-bottom');
                this.canvasTop = document.getElementById('canvas-top');

                if (!this.canvasBottom || !this.canvasTop) return;

                this.ctxBottom = this.canvasBottom.getContext('2d');
                this.ctxTop = this.canvasTop.getContext('2d');

                this.orbs = [];
                this.orbCount = 20;
                this.maxDistance = 150;

                this.init();
                this.animate();

                window.addEventListener('resize', () => this.resize());
            }

            init() {
                this.resize();
                this.createOrbs();
            }

            resize() {
                const width = window.innerWidth;
                const height = window.innerHeight;

                this.canvasBottom.width = width;
                this.canvasBottom.height = height;
                this.canvasTop.width = width;
                this.canvasTop.height = height;
            }

            createOrbs() {
                this.orbs = [];
                for (let i = 0; i < this.orbCount; i++) {
                    this.orbs.push({
                        position: new Vector(
                            Math.random() * this.canvasBottom.width,
                            Math.random() * this.canvasBottom.height
                        ),
                        velocity: Vector.random(-0.5, 0.5),
                        radius: Math.random() * 3 + 2,
                        opacity: Math.random() * 0.5 + 0.3
                    });
                }
            }

            updateOrbs() {
                this.orbs.forEach(orb => {
                    orb.position.add(orb.velocity);

                    // Bounce off edges
                    if (orb.position.x < 0 || orb.position.x > this.canvasBottom.width) {
                        orb.velocity.x *= -1;
                    }
                    if (orb.position.y < 0 || orb.position.y > this.canvasBottom.height) {
                        orb.velocity.y *= -1;
                    }

                    // Keep within bounds
                    orb.position.x = Math.max(0, Math.min(this.canvasBottom.width, orb.position.x));
                    orb.position.y = Math.max(0, Math.min(this.canvasBottom.height, orb.position.y));
                });
            }

            drawOrbs() {
                this.ctxBottom.clearRect(0, 0, this.canvasBottom.width, this.canvasBottom.height);

                this.orbs.forEach(orb => {
                    this.ctxBottom.save();
                    this.ctxBottom.globalAlpha = orb.opacity;
                    this.ctxBottom.fillStyle = '#4a9eff';
                    this.ctxBottom.beginPath();
                    this.ctxBottom.arc(orb.position.x, orb.position.y, orb.radius, 0, Math.PI * 2);
                    this.ctxBottom.fill();
                    this.ctxBottom.restore();
                });
            }

            drawConnections() {
                this.ctxTop.clearRect(0, 0, this.canvasTop.width, this.canvasTop.height);

                for (let i = 0; i < this.orbs.length; i++) {
                    for (let j = i + 1; j < this.orbs.length; j++) {
                        const distance = this.orbs[i].position.distance(this.orbs[j].position);

                        if (distance < this.maxDistance) {
                            const opacity = (1 - distance / this.maxDistance) * 0.3;

                            this.ctxTop.save();
                            this.ctxTop.globalAlpha = opacity;
                            this.ctxTop.strokeStyle = '#4a9eff';
                            this.ctxTop.lineWidth = 1;
                            this.ctxTop.beginPath();
                            this.ctxTop.moveTo(this.orbs[i].position.x, this.orbs[i].position.y);
                            this.ctxTop.lineTo(this.orbs[j].position.x, this.orbs[j].position.y);
                            this.ctxTop.stroke();
                            this.ctxTop.restore();
                        }
                    }
                }
            }

            animate() {
                this.updateOrbs();
                this.drawOrbs();
                this.drawConnections();

                requestAnimationFrame(() => this.animate());
            }
        }

        // Initialize cosmic web when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new CosmicWeb();
        });
    </script>

    <!-- PDF.js for PDF preview support -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script>
        // Configure PDF.js worker
        if (typeof pdfjsLib !== 'undefined') {
            pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
        }
    </script>
    <script src="freemium-system.js"></script>
    <script src="google-auth.js"></script>
    <script src="dashboard-script.js"></script>
    <script src="simplified-pdf-signing.js"></script>
</body>
</html>
