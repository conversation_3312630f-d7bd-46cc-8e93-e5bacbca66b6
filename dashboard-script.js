// Dashboard Script for DocuGen Pro

// Essential functions that need to be available immediately
function logout() {
    if (confirm('Are you sure you want to logout?')) {
        // Clear session data
        sessionStorage.clear();
        localStorage.removeItem('currentUser');
        localStorage.removeItem('userSession');
        // Redirect to login page or home
        window.location.href = 'landing.html';
        console.log('👋 User logged out');
    }
}

// Global state
let currentUser = null;
let signatureSession = null;

// Demo user accounts (for demonstration purposes)
const demoAccounts = {
    '<EMAIL>': {
        password: 'demo123',
        name: 'Demo User',
        company: 'Demo Company Ltd',
        plan: 'Pro'
    },
    '<EMAIL>': {
        password: 'admin123',
        name: 'Admin User',
        company: 'DocuGen Pro',
        plan: 'Enterprise'
    },
    '<EMAIL>': {
        password: 'test123',
        name: 'Test Business',
        company: 'Test Business Inc',
        plan: 'Starter'
    }
};

// Document type configurations
const documentTypes = {
    invoice: {
        title: 'Invoice Generator',
        description: 'Create professional invoices with automatic calculations and VAT handling',
        features: [
            'Automatic calculations',
            'VAT handling',
            'Professional formatting',
            'Client management'
        ],
        useCases: [
            'Service billing',
            'Product sales',
            'Recurring payments',
            'Business transactions'
        ],
        customization: [
            'Multiple themes',
            'Logo upload',
            'Custom watermarks',
            'Template management'
        ]
    },
    receipt: {
        title: 'Receipt Generator',
        description: 'Generate professional receipts for payments and transactions',
        features: [
            'Payment confirmation',
            'Transaction tracking',
            'Professional layout',
            'Digital signatures'
        ],
        useCases: [
            'Payment confirmations',
            'Cash transactions',
            'Service receipts',
            'Product purchases'
        ],
        customization: [
            'Custom branding',
            'Receipt templates',
            'Logo integration',
            'Color themes'
        ]
    },
    quotation: {
        title: 'Quotation Generator',
        description: 'Create detailed quotations and estimates for potential clients',
        features: [
            'Detailed estimates',
            'Validity periods',
            'Terms & conditions',
            'Professional presentation'
        ],
        useCases: [
            'Project estimates',
            'Service quotes',
            'Product pricing',
            'Proposal submissions'
        ],
        customization: [
            'Quote templates',
            'Validity settings',
            'Terms customization',
            'Brand integration'
        ]
    },
    contract: {
        title: 'Contract Generator',
        description: 'Generate comprehensive contracts and agreements',
        features: [
            'Legal templates',
            'Clause management',
            'Digital signatures',
            'Multi-page support'
        ],
        useCases: [
            'Service agreements',
            'Employment contracts',
            'Partnership deals',
            'Vendor agreements'
        ],
        customization: [
            'Contract templates',
            'Clause library',
            'Signature fields',
            'Legal formatting'
        ]
    },
    rider: {
        title: 'Technical Rider Generator',
        description: 'Create detailed technical riders for events and performances',
        features: [
            'Equipment specifications',
            'Setup requirements',
            'Technical details',
            'Performance needs'
        ],
        useCases: [
            'Concert riders',
            'Event specifications',
            'Performance requirements',
            'Technical setups'
        ],
        customization: [
            'Equipment templates',
            'Specification formats',
            'Technical layouts',
            'Performance branding'
        ]
    },
    annexure: {
        title: 'Annexure Generator',
        description: 'Create professional annexures and supplementary documents',
        features: [
            'Document supplements',
            'Additional clauses',
            'Reference materials',
            'Professional formatting'
        ],
        useCases: [
            'Contract supplements',
            'Additional terms',
            'Reference documents',
            'Supporting materials'
        ],
        customization: [
            'Annexure templates',
            'Reference formatting',
            'Document linking',
            'Professional layout'
        ]
    },
    'artist-agreement': {
        title: 'Artist Agreement Generator',
        description: 'Generate comprehensive artist booking agreements and contracts',
        features: [
            'Booking contracts',
            'Payment terms',
            'Performance clauses',
            'Legal protection'
        ],
        useCases: [
            'Artist bookings',
            'Performance contracts',
            'Event agreements',
            'Talent management'
        ],
        customization: [
            'Agreement templates',
            'Performance terms',
            'Payment structures',
            'Legal clauses'
        ]
    }
};

// Global state
let currentDocumentType = 'invoice';
let activeCompany = 'company1';
let companies = {
    company1: {
        name: 'Company 1',
        type: 'general',
        info: null,
        banking: null
    }
};

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    initializeClientProfiles();
    initializeClientManagementEventListeners();

    // Initialize company setup dropdown
    updateCompanySetupDropdown();

    // Initialize Google Auth if available
    if (window.googleAuth) {
        console.log('🔐 Initializing Google Auth in dashboard...');
        googleAuth.initializeGoogleSignIn();
    }

    // Initialize freemium system if available
    if (window.freemiumSystem) {
        console.log('🪙 Initializing freemium system in dashboard...');
        if (window.googleAuth && googleAuth.isAuthenticated()) {
            const user = googleAuth.getCurrentUser();
            freemiumSystem.currentUser = user;
            freemiumSystem.loadUserTokens();
        }
        freemiumSystem.updateTokenDisplay();
    }
});

function initializeDashboard() {
    // Check if user is logged in
    const userData = localStorage.getItem('currentUser');
    if (!userData) {
        // Redirect to landing page for login
        window.location.href = 'landing.html';
        return;
    }

    currentUser = JSON.parse(userData);

    // Update welcome message
    const welcomeEl = document.getElementById('userWelcome');
    if (welcomeEl) {
        welcomeEl.textContent = `Welcome, ${currentUser.name}`;
    }

    // Check for selected document type from landing page
    const selectedType = localStorage.getItem('selectedDocumentType');
    if (selectedType && documentTypes[selectedType]) {
        selectDocumentType(selectedType);
    } else {
        selectDocumentType('invoice');
    }

    // Initialize test companies and generate logos
    Object.keys(testCompanies).forEach(companyKey => {
        createGenericLogo(companyKey);
    });

    // Initialize company management
    loadCompanyData();
    updateCompanySelector();
    updateCompanyPreview();
    updateCompanyStatus();

    // Load recent documents
    loadRecentDocuments();

    // Initialize signature tools when modal opens
    initializeSignatureToolsLater();

    // Update all document previews with current company logo
    setTimeout(() => {
        updateAllDocumentPreviews();
    }, 500);

    console.log('📋 Available test companies:', Object.keys(testCompanies).map(key => testCompanies[key].name));
}

function selectDocumentType(type) {
    currentDocumentType = type;
    
    // Update active tab
    document.querySelectorAll('.doc-link').forEach(link => {
        link.classList.remove('active');
    });
    document.querySelector(`[data-type="${type}"]`).classList.add('active');
    
    // Update main content
    updateDocumentTypeInfo(type);
}

function updateDocumentTypeInfo(type) {
    const config = documentTypes[type];
    if (!config) return;
    
    // Update header
    document.getElementById('documentTypeTitle').textContent = config.title;
    document.getElementById('documentTypeDescription').textContent = config.description;
    
    // Update features
    const featuresEl = document.getElementById('documentFeatures');
    featuresEl.innerHTML = config.features.map(feature => `<li>${feature}</li>`).join('');
    
    // Update use cases
    const useCasesEl = document.getElementById('documentUseCases');
    useCasesEl.innerHTML = config.useCases.map(useCase => `<li>${useCase}</li>`).join('');
    
    // Update customization
    const customizationEl = document.getElementById('documentCustomization');
    customizationEl.innerHTML = config.customization.map(item => `<li>${item}</li>`).join('');
}

function openGenerator() {
    // Redirect to the document generator with the selected type
    const generatorUrl = `index.html?type=${currentDocumentType}`;
    window.open(generatorUrl, '_blank');
}

function showPreview() {
    try {
        console.log('🔍 showPreview() function called');
        console.log('📄 Current document type:', currentDocumentType);

        const modal = document.getElementById('previewModal');
        console.log('📋 Modal element found:', modal);

        if (!modal) {
            console.error('❌ Preview modal not found!');
            alert('Preview modal not found. Please check the page structure.');
            return;
        }

        // Update modal title
        const previewTitle = document.getElementById('previewTitle');
        if (previewTitle) {
            const config = documentTypes[currentDocumentType];
            previewTitle.textContent = `${config.title.replace(' Generator', '')} Preview`;
        }

        // Show modal
        modal.style.display = 'block';
        modal.style.visibility = 'visible';
        modal.style.opacity = '1';
        modal.style.zIndex = '9999';

        console.log('✅ Modal display set to block');

        // Generate preview content with option for PDF or HTML
        generatePreview();

        console.log('✅ Preview generated successfully');

    } catch (error) {
        console.error('❌ Error in showPreview():', error);
        alert('Error showing preview: ' + error.message);
    }
}

function closePreview() {
    document.getElementById('previewModal').style.display = 'none';
}

function generatePreview() {
    console.log('🔍 generatePreview() function called');

    const container = document.getElementById('previewContainer');
    console.log('📋 Preview container found:', container);

    if (!container) {
        console.error('❌ Preview container not found!');
        alert('Preview container not found. Please check the page structure.');
        return;
    }

    const config = documentTypes[currentDocumentType];
    console.log('📄 Document type config:', config);
    console.log('📄 Current document type:', currentDocumentType);

    // Get active company data
    const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';
    const activeCompany = testCompanies[activeCompanyKey];
    const companyLogo = localStorage.getItem(`companyLogo_${activeCompanyKey}`);

    console.log(`🔍 Generating preview for document type: ${currentDocumentType}`);
    console.log(`🏢 Using company: ${activeCompany ? activeCompany.name : 'No company found'}`);
    console.log(`🖼️ Company logo: ${companyLogo ? 'Found' : 'Not found'}`);

    if (!activeCompany) {
        console.error('❌ No active company found!');
        container.innerHTML = '<div style="padding: 20px; text-align: center;">❌ No company data found. Please set up your company information first.</div>';
        return;
    }

    // Generate realistic preview based on document type
    switch(currentDocumentType) {
        case 'invoice':
            container.innerHTML = generateInvoicePreview(activeCompany, companyLogo);
            break;
        case 'receipt':
            container.innerHTML = generateReceiptPreview(activeCompany, companyLogo);
            break;
        case 'quotation':
            container.innerHTML = generateQuotationPreview(activeCompany, companyLogo);
            break;
        case 'contract':
            container.innerHTML = generateContractPreview(activeCompany, companyLogo);
            break;
        case 'artist-agreement':
            container.innerHTML = generateArtistAgreementPreview(activeCompany, companyLogo);
            break;
        case 'rider':
            container.innerHTML = generateTechnicalRiderPreview(activeCompany, companyLogo);
            break;
        case 'annexure':
            container.innerHTML = generateAnnexurePreview(activeCompany, companyLogo);
            break;
        default:
            console.log('🔧 Using generic preview for:', currentDocumentType);
            container.innerHTML = generateGenericPreview(config, activeCompany, companyLogo);
    }

    console.log('✅ Preview content generated and inserted into container');
    console.log('📋 Container innerHTML length:', container.innerHTML.length);

    // Add PDF preview option
    addPDFPreviewOption(container);

    // Add theme selector
    addThemeSelector(container);
}

// Add PDF preview option to the preview container
function addPDFPreviewOption(container) {
    const pdfPreviewButton = document.createElement('div');
    pdfPreviewButton.style.cssText = `
        margin-top: 20px;
        text-align: center;
        padding: 15px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
    `;

    pdfPreviewButton.innerHTML = `
        <button onclick="generatePDFPreview()" class="btn-primary" style="
            background: white;
            color: #667eea;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
            📄 Generate PDF Preview
        </button>
        <p style="color: white; margin: 10px 0 0 0; font-size: 14px;">
            Click to see how your actual document will look as a PDF
        </p>
    `;

    container.appendChild(pdfPreviewButton);
}

// Color theme definitions
const documentThemes = {
    default: {
        name: 'Professional Default',
        primary: '#2c3e50',
        secondary: '#34495e',
        accent: '#3498db',
        background: '#ffffff',
        text: '#2c3e50',
        border: '#e0e0e0'
    },
    invoice: {
        name: 'Invoice Blue',
        primary: '#007bff',
        secondary: '#0056b3',
        accent: '#66b3ff',
        background: '#f8f9ff',
        text: '#003d7a',
        border: '#b3d9ff',
        glow: 'rgba(0,123,255,0.3)'
    },
    receipt: {
        name: 'Receipt Green',
        primary: '#28a745',
        secondary: '#1e7e34',
        accent: '#5cb85c',
        background: '#f8fff8',
        text: '#155724',
        border: '#b3e5b3',
        glow: 'rgba(40,167,69,0.3)'
    },
    quotation: {
        name: 'Quotation Gold',
        primary: '#ffc107',
        secondary: '#e0a800',
        accent: '#ffdb4d',
        background: '#fffef8',
        text: '#856404',
        border: '#ffe066',
        glow: 'rgba(255,193,7,0.3)'
    },
    contract: {
        name: 'Contract Purple',
        primary: '#6f42c1',
        secondary: '#5a32a3',
        accent: '#9966cc',
        background: '#faf8ff',
        text: '#4a2c7a',
        border: '#c299ff',
        glow: 'rgba(111,66,193,0.3)'
    },
    rider: {
        name: 'Technical Rider Red',
        primary: '#dc3545',
        secondary: '#c82333',
        accent: '#e66b7a',
        background: '#fff8f8',
        text: '#721c24',
        border: '#ffb3b3',
        glow: 'rgba(220,53,69,0.3)'
    },
    annexure: {
        name: 'Annexure Gray',
        primary: '#6c757d',
        secondary: '#545b62',
        accent: '#9ca3af',
        background: '#f8f9fa',
        text: '#495057',
        border: '#d1d3d7',
        glow: 'rgba(108,117,125,0.3)'
    }
};

// Current theme state
let currentTheme = 'default';

// Add theme selector to container
function addThemeSelector(container) {
    const themeSection = document.createElement('div');
    themeSection.style.cssText = 'margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 12px; border: 1px solid #e0e0e0;';

    themeSection.innerHTML = `
        <div style="text-align: center; margin-bottom: 15px;">
            <h4 style="margin: 0 0 10px 0; color: #2c3e50; font-size: 16px;">🎨 Document Theme</h4>
            <p style="margin: 0; color: #666; font-size: 14px;">Choose a color theme for your document</p>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px; margin-bottom: 15px;">
            <button class="theme-btn" data-theme="default" style="padding: 8px 12px; border: 2px solid #e0e0e0; border-radius: 8px; background: white; cursor: pointer; font-size: 12px; font-weight: 600; transition: all 0.3s ease;">
                <div style="width: 20px; height: 20px; background: linear-gradient(45deg, #2c3e50, #34495e); border-radius: 4px; margin: 0 auto 5px;"></div>
                Default
            </button>
            <button class="theme-btn" data-theme="invoice" style="padding: 8px 12px; border: 2px solid #e0e0e0; border-radius: 8px; background: white; cursor: pointer; font-size: 12px; font-weight: 600; transition: all 0.3s ease;">
                <div style="width: 20px; height: 20px; background: linear-gradient(45deg, #007bff, #66b3ff); border-radius: 4px; margin: 0 auto 5px; box-shadow: 0 0 10px rgba(0,123,255,0.3);"></div>
                Blue
            </button>
            <button class="theme-btn" data-theme="receipt" style="padding: 8px 12px; border: 2px solid #e0e0e0; border-radius: 8px; background: white; cursor: pointer; font-size: 12px; font-weight: 600; transition: all 0.3s ease;">
                <div style="width: 20px; height: 20px; background: linear-gradient(45deg, #28a745, #5cb85c); border-radius: 4px; margin: 0 auto 5px; box-shadow: 0 0 10px rgba(40,167,69,0.3);"></div>
                Green
            </button>
            <button class="theme-btn" data-theme="quotation" style="padding: 8px 12px; border: 2px solid #e0e0e0; border-radius: 8px; background: white; cursor: pointer; font-size: 12px; font-weight: 600; transition: all 0.3s ease;">
                <div style="width: 20px; height: 20px; background: linear-gradient(45deg, #ffc107, #ffdb4d); border-radius: 4px; margin: 0 auto 5px; box-shadow: 0 0 10px rgba(255,193,7,0.3);"></div>
                Gold
            </button>
            <button class="theme-btn" data-theme="contract" style="padding: 8px 12px; border: 2px solid #e0e0e0; border-radius: 8px; background: white; cursor: pointer; font-size: 12px; font-weight: 600; transition: all 0.3s ease;">
                <div style="width: 20px; height: 20px; background: linear-gradient(45deg, #6f42c1, #9966cc); border-radius: 4px; margin: 0 auto 5px; box-shadow: 0 0 10px rgba(111,66,193,0.3);"></div>
                Purple
            </button>
            <button class="theme-btn" data-theme="rider" style="padding: 8px 12px; border: 2px solid #e0e0e0; border-radius: 8px; background: white; cursor: pointer; font-size: 12px; font-weight: 600; transition: all 0.3s ease;">
                <div style="width: 20px; height: 20px; background: linear-gradient(45deg, #dc3545, #e66b7a); border-radius: 4px; margin: 0 auto 5px; box-shadow: 0 0 10px rgba(220,53,69,0.3);"></div>
                Red
            </button>
            <button class="theme-btn" data-theme="annexure" style="padding: 8px 12px; border: 2px solid #e0e0e0; border-radius: 8px; background: white; cursor: pointer; font-size: 12px; font-weight: 600; transition: all 0.3s ease;">
                <div style="width: 20px; height: 20px; background: linear-gradient(45deg, #6c757d, #9ca3af); border-radius: 4px; margin: 0 auto 5px; box-shadow: 0 0 10px rgba(108,117,125,0.3);"></div>
                Gray
            </button>
        </div>
    `;

    container.appendChild(themeSection);

    // Add theme selection functionality
    const themeButtons = themeSection.querySelectorAll('.theme-btn');
    themeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const selectedTheme = this.dataset.theme;
            selectTheme(selectedTheme, themeButtons);
        });
    });

    // Set default theme as selected
    selectTheme('default', themeButtons);
}

// Theme selection function
function selectTheme(themeName, themeButtons) {
    currentTheme = themeName;
    const theme = documentThemes[themeName];

    // Update button states
    themeButtons.forEach(btn => {
        if (btn.dataset.theme === themeName) {
            btn.style.borderColor = theme.primary;
            btn.style.background = theme.background;
            btn.style.boxShadow = `0 0 15px ${theme.glow || 'rgba(0,0,0,0.1)'}`;
            btn.style.transform = 'scale(1.05)';
        } else {
            btn.style.borderColor = '#e0e0e0';
            btn.style.background = 'white';
            btn.style.boxShadow = 'none';
            btn.style.transform = 'scale(1)';
        }
    });

    // Update preview with new theme
    updatePreviewWithTheme();

    console.log(`🎨 Theme changed to: ${theme.name}`);
}

// Update preview with current theme
function updatePreviewWithTheme() {
    const container = document.getElementById('previewContainer');
    if (!container) return;

    // Get current company and logo data
    const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';
    const activeCompany = testCompanies[activeCompanyKey];
    const companyLogo = localStorage.getItem(`companyLogo_${activeCompanyKey}`);

    // Regenerate preview with theme
    switch(currentDocumentType) {
        case 'invoice':
            container.innerHTML = generateThemedInvoicePreview(activeCompany, companyLogo, currentTheme);
            break;
        case 'receipt':
            container.innerHTML = generateThemedReceiptPreview(activeCompany, companyLogo, currentTheme);
            break;
        case 'quotation':
            container.innerHTML = generateThemedQuotationPreview(activeCompany, companyLogo, currentTheme);
            break;
        case 'contract':
            container.innerHTML = generateThemedContractPreview(activeCompany, companyLogo, currentTheme);
            break;
        case 'rider':
            container.innerHTML = generateThemedTechnicalRiderPreview(activeCompany, companyLogo, currentTheme);
            break;
        case 'annexure':
            container.innerHTML = generateThemedAnnexurePreview(activeCompany, companyLogo, currentTheme);
            break;
        case 'artist-agreement':
            container.innerHTML = generateThemedArtistAgreementPreview(activeCompany, companyLogo, currentTheme);
            break;
        default:
            container.innerHTML = generateThemedGenericPreview(documentTypes[currentDocumentType], activeCompany, companyLogo, currentTheme);
    }

    // Re-add PDF preview and theme selector
    addPDFPreviewOption(container);
    addThemeSelector(container);
}

// Themed preview generation functions
function generateThemedInvoicePreview(company, logo, themeName) {
    const theme = documentThemes[themeName];
    const currentDate = new Date().toLocaleDateString();
    const invoiceNumber = `INV-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;

    return `
        <div class="realistic-preview invoice-preview" style="
            background: ${theme.background};
            border: 2px solid ${theme.border};
            color: ${theme.text};
            box-shadow: 0 8px 25px ${theme.glow || 'rgba(0,0,0,0.1)'};
        ">
            <div class="document-header" style="border-bottom: 2px solid ${theme.border};">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 60px; height: 60px; object-fit: contain; float: left; margin-right: 15px; border: 2px solid ${theme.primary}; border-radius: 8px;">` : ''}
                <div class="header-content">
                    <h1 style="color: ${theme.primary}; margin: 0; font-size: 28px; text-shadow: 0 2px 4px ${theme.glow || 'rgba(0,0,0,0.1)'};">INVOICE</h1>
                    <div class="company-info" style="margin-top: 10px; color: ${theme.text};">
                        <strong style="color: ${theme.primary};">${company.name}</strong><br>
                        ${company.address.replace(/\n/g, '<br>')}<br>
                        Tel: ${company.phone} | Email: ${company.email}
                    </div>
                </div>
                <div style="clear: both;"></div>
            </div>

            <div class="invoice-details" style="margin: 20px 0; padding: 15px; background: ${theme.background}; border: 1px solid ${theme.border}; border-radius: 8px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                    <div>
                        <strong style="color: ${theme.primary};">Invoice Number:</strong> ${invoiceNumber}<br>
                        <strong style="color: ${theme.primary};">Date:</strong> ${currentDate}<br>
                        <strong style="color: ${theme.primary};">Due Date:</strong> ${new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString()}
                    </div>
                </div>

                <div style="margin-bottom: 20px;">
                    <h3 style="color: ${theme.primary}; border-bottom: 1px solid ${theme.border}; padding-bottom: 5px;">Bill To:</h3>
                    <strong>Rhythm Records Ltd</strong><br>
                    456 Beat Avenue, Sound City<br>
                    Cape Town, 8001<br>
                    Tel: +27 21 987 6543
                </div>
            </div>

            <table class="invoice-table" style="width: 100%; border-collapse: collapse; margin: 20px 0; border: 1px solid ${theme.border};">
                <thead style="background: ${theme.primary}; color: white;">
                    <tr>
                        <th style="padding: 12px; text-align: left; border: 1px solid ${theme.border};">Description</th>
                        <th style="padding: 12px; text-align: center; border: 1px solid ${theme.border};">Qty</th>
                        <th style="padding: 12px; text-align: right; border: 1px solid ${theme.border};">Price</th>
                        <th style="padding: 12px; text-align: right; border: 1px solid ${theme.border};">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr style="background: ${theme.background};">
                        <td style="padding: 10px; border: 1px solid ${theme.border};">Audio Production Services</td>
                        <td style="padding: 10px; text-align: center; border: 1px solid ${theme.border};">8</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid ${theme.border};">R1,250.00</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid ${theme.border};">R10,000.00</td>
                    </tr>
                    <tr style="background: ${theme.accent}20;">
                        <td style="padding: 10px; border: 1px solid ${theme.border};">Studio Rental (2 days)</td>
                        <td style="padding: 10px; text-align: center; border: 1px solid ${theme.border};">2</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid ${theme.border};">R2,500.00</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid ${theme.border};">R5,000.00</td>
                    </tr>
                    <tr style="background: ${theme.background};">
                        <td style="padding: 10px; border: 1px solid ${theme.border};">Mixing & Mastering</td>
                        <td style="padding: 10px; text-align: center; border: 1px solid ${theme.border};">1</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid ${theme.border};">R3,500.00</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid ${theme.border};">R3,500.00</td>
                    </tr>
                </tbody>
            </table>

            <div class="invoice-totals" style="margin-top: 20px; padding: 15px; background: ${theme.accent}20; border: 2px solid ${theme.primary}; border-radius: 8px;">
                <div style="text-align: right;">
                    <div style="margin-bottom: 8px;">
                        <span>Subtotal:</span>
                        <span style="margin-left: 20px; font-weight: bold;">R18,500.00</span>
                    </div>
                    <div style="margin-bottom: 8px;">
                        <span>VAT (15%):</span>
                        <span style="margin-left: 20px; font-weight: bold;">R2,775.00</span>
                    </div>
                    <div style="font-size: 18px; font-weight: bold; color: ${theme.primary}; border-top: 2px solid ${theme.primary}; padding-top: 8px;">
                        <span>Total:</span>
                        <span style="margin-left: 20px;">R21,275.00</span>
                    </div>
                </div>
            </div>

            <div class="payment-info" style="margin-top: 30px; padding: 15px; background: ${theme.accent}30; border-left: 4px solid ${theme.primary}; border-radius: 5px;">
                <strong style="color: ${theme.primary};">Payment Terms:</strong><br>
                Payment due within 30 days of invoice date.<br>
                Late payments may incur additional charges.
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center;">
                <button class="btn-primary" onclick="openGenerator()" style="background: ${theme.primary}; border-color: ${theme.primary};">Create Invoice</button>
            </div>
        </div>
    `;
}

function generateThemedReceiptPreview(company, logo, themeName) {
    const theme = documentThemes[themeName];
    const currentDate = new Date().toLocaleDateString();
    const receiptNumber = `RCP-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;

    return `
        <div class="realistic-preview receipt-preview" style="
            background: ${theme.background};
            border: 2px solid ${theme.border};
            color: ${theme.text};
            box-shadow: 0 8px 25px ${theme.glow || 'rgba(0,0,0,0.1)'};
        ">
            <div class="document-header" style="border-bottom: 2px solid ${theme.border};">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 60px; height: 60px; object-fit: contain; float: left; margin-right: 15px; border: 2px solid ${theme.primary}; border-radius: 8px;">` : ''}
                <div class="header-content">
                    <h1 style="color: ${theme.primary}; margin: 0; font-size: 28px; text-shadow: 0 2px 4px ${theme.glow || 'rgba(0,0,0,0.1)'};">PAYMENT RECEIPT</h1>
                    <div class="company-info" style="margin-top: 10px; color: ${theme.text};">
                        <strong style="color: ${theme.primary};">${company.name}</strong><br>
                        ${company.address.split('\n')[0]}<br>
                        Tel: ${company.phone}
                    </div>
                </div>
                <div style="clear: both;"></div>
            </div>

            <div class="receipt-details" style="margin: 20px 0; padding: 15px; background: ${theme.background}; border: 1px solid ${theme.border}; border-radius: 8px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                    <div>
                        <strong style="color: ${theme.primary};">Receipt Number:</strong> ${receiptNumber}<br>
                        <strong style="color: ${theme.primary};">Date:</strong> ${currentDate}<br>
                        <strong style="color: ${theme.primary};">Payment Method:</strong> Electronic Transfer
                    </div>
                </div>

                <div style="margin-bottom: 20px;">
                    <h3 style="color: ${theme.primary}; border-bottom: 1px solid ${theme.border}; padding-bottom: 5px;">Payment Received From:</h3>
                    <strong>Rhythm Records Ltd</strong><br>
                    456 Beat Avenue, Sound City<br>
                    Cape Town, 8001<br>
                    Tel: +27 21 987 6543
                </div>
            </div>

            <div class="payment-details" style="margin: 20px 0; padding: 20px; background: ${theme.accent}20; border: 2px solid ${theme.primary}; border-radius: 8px;">
                <h3 style="color: ${theme.primary}; margin-top: 0;">Payment Details</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div>
                        <strong>For Invoice:</strong> INV-2025-001<br>
                        <strong>Reference:</strong> BMI2025001<br>
                        <strong>Bank:</strong> Standard Bank
                    </div>
                    <div style="text-align: right;">
                        <div style="font-size: 24px; font-weight: bold; color: ${theme.primary};">
                            R21,275.00
                        </div>
                        <div style="color: ${theme.secondary};">Amount Received</div>
                    </div>
                </div>
            </div>

            <div class="receipt-confirmation" style="margin-top: 30px; padding: 15px; background: ${theme.accent}30; border-left: 4px solid ${theme.primary}; border-radius: 5px; text-align: center;">
                <div style="font-size: 18px; font-weight: bold; color: ${theme.primary}; margin-bottom: 10px;">✅ PAYMENT CONFIRMED</div>
                <p style="margin: 0; color: ${theme.text};">This receipt serves as proof of payment. Thank you for your business!</p>
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center;">
                <button class="btn-primary" onclick="openGenerator()" style="background: ${theme.primary}; border-color: ${theme.primary};">Create Receipt</button>
            </div>
        </div>
    `;
}

function generateThemedQuotationPreview(company, logo, themeName) {
    const theme = documentThemes[themeName];
    const currentDate = new Date().toLocaleDateString();
    const validUntil = new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString();
    const quoteNumber = `QUO-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;

    return `
        <div class="realistic-preview quotation-preview" style="
            background: ${theme.background};
            border: 2px solid ${theme.border};
            color: ${theme.text};
            box-shadow: 0 8px 25px ${theme.glow || 'rgba(0,0,0,0.1)'};
        ">
            <div class="document-header" style="border-bottom: 2px solid ${theme.border};">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 60px; height: 60px; object-fit: contain; float: left; margin-right: 15px; border: 2px solid ${theme.primary}; border-radius: 8px;">` : ''}
                <div class="header-content">
                    <h1 style="color: ${theme.primary}; margin: 0; font-size: 28px; text-shadow: 0 2px 4px ${theme.glow || 'rgba(0,0,0,0.1)'};">QUOTATION</h1>
                    <div class="company-info" style="margin-top: 10px; color: ${theme.text};">
                        <strong style="color: ${theme.primary};">${company.name}</strong><br>
                        ${company.address.replace(/\n/g, '<br>')}<br>
                        Tel: ${company.phone} | Email: ${company.email}
                    </div>
                </div>
                <div style="clear: both;"></div>
            </div>

            <div class="quote-details" style="margin: 20px 0; padding: 15px; background: ${theme.background}; border: 1px solid ${theme.border}; border-radius: 8px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                    <div>
                        <strong style="color: ${theme.primary};">Quote Number:</strong> ${quoteNumber}<br>
                        <strong style="color: ${theme.primary};">Date:</strong> ${currentDate}<br>
                        <strong style="color: ${theme.primary};">Valid Until:</strong> ${validUntil}
                    </div>
                </div>

                <div style="margin-bottom: 20px;">
                    <h3 style="color: ${theme.primary}; border-bottom: 1px solid ${theme.border}; padding-bottom: 5px;">Quote For:</h3>
                    <strong>Rhythm Records Ltd</strong><br>
                    456 Beat Avenue, Sound City<br>
                    Cape Town, 8001<br>
                    Tel: +27 21 987 6543
                </div>
            </div>

            <div style="margin: 20px 0; padding: 15px; background: ${theme.accent}20; border-left: 4px solid ${theme.primary}; border-radius: 5px;">
                <h3 style="color: ${theme.primary}; margin-top: 0;">Project: Event Sound System Setup</h3>
                <p style="margin: 0; color: ${theme.text};">Professional audio equipment and engineering services for live event.</p>
            </div>

            <table class="quote-table" style="width: 100%; border-collapse: collapse; margin: 20px 0; border: 1px solid ${theme.border};">
                <thead style="background: ${theme.primary}; color: white;">
                    <tr>
                        <th style="padding: 12px; text-align: left; border: 1px solid ${theme.border};">Description</th>
                        <th style="padding: 12px; text-align: center; border: 1px solid ${theme.border};">Qty</th>
                        <th style="padding: 12px; text-align: right; border: 1px solid ${theme.border};">Price</th>
                        <th style="padding: 12px; text-align: right; border: 1px solid ${theme.border};">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr style="background: ${theme.background};">
                        <td style="padding: 10px; border: 1px solid ${theme.border};">Event Sound System Setup</td>
                        <td style="padding: 10px; text-align: center; border: 1px solid ${theme.border};">1</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid ${theme.border};">R15,000.00</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid ${theme.border};">R15,000.00</td>
                    </tr>
                    <tr style="background: ${theme.accent}20;">
                        <td style="padding: 10px; border: 1px solid ${theme.border};">Sound Engineer (8 hours)</td>
                        <td style="padding: 10px; text-align: center; border: 1px solid ${theme.border};">8</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid ${theme.border};">R750.00</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid ${theme.border};">R6,000.00</td>
                    </tr>
                    <tr style="background: ${theme.background};">
                        <td style="padding: 10px; border: 1px solid ${theme.border};">Equipment Transport</td>
                        <td style="padding: 10px; text-align: center; border: 1px solid ${theme.border};">1</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid ${theme.border};">R2,000.00</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid ${theme.border};">R2,000.00</td>
                    </tr>
                </tbody>
            </table>

            <div class="quote-totals" style="margin-top: 20px; padding: 15px; background: ${theme.accent}20; border: 2px solid ${theme.primary}; border-radius: 8px;">
                <div style="text-align: right;">
                    <div style="margin-bottom: 8px;">
                        <span>Subtotal:</span>
                        <span style="margin-left: 20px; font-weight: bold;">R23,000.00</span>
                    </div>
                    <div style="margin-bottom: 8px;">
                        <span>VAT (15%):</span>
                        <span style="margin-left: 20px; font-weight: bold;">R3,450.00</span>
                    </div>
                    <div style="font-size: 18px; font-weight: bold; color: ${theme.primary}; border-top: 2px solid ${theme.primary}; padding-top: 8px;">
                        <span>Total:</span>
                        <span style="margin-left: 20px;">R26,450.00</span>
                    </div>
                </div>
            </div>

            <div class="quote-terms" style="margin-top: 30px; padding: 15px; background: ${theme.accent}30; border-left: 4px solid ${theme.primary}; border-radius: 5px;">
                <strong style="color: ${theme.primary};">Terms & Conditions:</strong><br>
                • Quote valid for 30 days from date of issue<br>
                • 50% deposit required to commence work<br>
                • Final payment due upon project completion<br>
                • Equipment insurance included in pricing
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center;">
                <button class="btn-primary" onclick="openGenerator()" style="background: ${theme.primary}; border-color: ${theme.primary};">Create Quotation</button>
            </div>
        </div>
    `;
}

// Themed Contract Preview
function generateThemedContractPreview(company, logo, themeName) {
    const theme = documentThemes[themeName];
    const currentDate = new Date().toLocaleDateString();

    return `
        <div class="realistic-preview contract-preview" style="background: ${theme.background}; border: 2px solid ${theme.border}; color: ${theme.text}; box-shadow: 0 8px 25px ${theme.glow || 'rgba(0,0,0,0.1)'};">
            <div class="document-header" style="border-bottom: 2px solid ${theme.border};">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 60px; height: 60px; object-fit: contain; float: left; margin-right: 15px; border: 2px solid ${theme.primary}; border-radius: 8px;">` : ''}
                <div class="header-content">
                    <h1 style="color: ${theme.primary}; margin: 0; font-size: 28px; text-shadow: 0 2px 4px ${theme.glow || 'rgba(0,0,0,0.1)'};">SERVICE AGREEMENT</h1>
                    <div class="company-info" style="margin-top: 10px; color: ${theme.text};">
                        <strong style="color: ${theme.primary};">${company.name}</strong><br>
                        Professional Services Contract
                    </div>
                </div>
                <div style="clear: both;"></div>
            </div>

            <div style="margin: 20px 0; padding: 15px; background: ${theme.accent}20; border-left: 4px solid ${theme.primary}; border-radius: 5px;">
                <strong style="color: ${theme.primary};">Contract Date:</strong> ${currentDate}<br>
                <strong style="color: ${theme.primary};">Contract Duration:</strong> 6 months<br>
                <strong style="color: ${theme.primary};">Contract Value:</strong> R85,000.00
            </div>

            <div class="contract-content" style="margin: 20px 0; line-height: 1.6;">
                <h3 style="color: ${theme.primary};">1. SCOPE OF SERVICES</h3>
                <p>Benjamin Music Initiatives agrees to provide professional audio production services including recording, mixing, and mastering for the Client's upcoming album project.</p>

                <h3 style="color: ${theme.primary};">2. COMPENSATION</h3>
                <p>Total project fee: <strong style="color: ${theme.primary};">R85,000.00</strong> (including VAT)<br>
                Payment schedule: 50% deposit, 50% on completion</p>

                <h3 style="color: ${theme.primary};">3. DELIVERABLES</h3>
                <ul>
                    <li>12 fully produced tracks</li>
                    <li>High-quality WAV and MP3 files</li>
                    <li>Album artwork consultation</li>
                </ul>
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center;">
                <button class="btn-primary" onclick="openGenerator()" style="background: ${theme.primary}; border-color: ${theme.primary};">Create Contract</button>
            </div>
        </div>
    `;
}

// Themed Technical Rider Preview
function generateThemedTechnicalRiderPreview(company, logo, themeName) {
    const theme = documentThemes[themeName];
    const currentDate = new Date().toLocaleDateString();

    return `
        <div class="realistic-preview rider-preview" style="background: ${theme.background}; border: 2px solid ${theme.border}; color: ${theme.text}; box-shadow: 0 8px 25px ${theme.glow || 'rgba(0,0,0,0.1)'};">
            <div class="document-header" style="border-bottom: 2px solid ${theme.border};">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 60px; height: 60px; object-fit: contain; float: left; margin-right: 15px; border: 2px solid ${theme.primary}; border-radius: 8px;">` : ''}
                <div class="header-content">
                    <h1 style="color: ${theme.primary}; margin: 0; font-size: 28px; text-shadow: 0 2px 4px ${theme.glow || 'rgba(0,0,0,0.1)'};">TECHNICAL RIDER</h1>
                    <div class="company-info" style="margin-top: 10px; color: ${theme.text};">
                        <strong style="color: ${theme.primary};">${company.name}</strong><br>
                        Live Performance Requirements
                    </div>
                </div>
                <div style="clear: both;"></div>
            </div>

            <div style="margin: 20px 0; padding: 15px; background: ${theme.accent}20; border-left: 4px solid ${theme.primary}; border-radius: 5px;">
                <strong style="color: ${theme.primary};">Event Date:</strong> ${new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString()}<br>
                <strong style="color: ${theme.primary};">Setup Time:</strong> 4 hours minimum<br>
                <strong style="color: ${theme.primary};">Performance Duration:</strong> 2 hours
            </div>

            <div class="rider-content" style="margin: 20px 0; line-height: 1.6;">
                <h3 style="color: ${theme.primary};">AUDIO REQUIREMENTS</h3>
                <ul>
                    <li>32-channel digital mixing console</li>
                    <li>Full PA system (minimum 10kW)</li>
                    <li>8x monitor wedges</li>
                    <li>Wireless microphone system (4 channels)</li>
                </ul>

                <h3 style="color: ${theme.primary};">STAGE REQUIREMENTS</h3>
                <ul>
                    <li>Minimum stage size: 8m x 6m</li>
                    <li>Power: 32A 3-phase supply</li>
                    <li>Loading access for equipment truck</li>
                </ul>
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center;">
                <button class="btn-primary" onclick="openGenerator()" style="background: ${theme.primary}; border-color: ${theme.primary};">Create Technical Rider</button>
            </div>
        </div>
    `;
}

// Themed Annexure Preview
function generateThemedAnnexurePreview(company, logo, themeName) {
    const theme = documentThemes[themeName];

    return `
        <div class="realistic-preview annexure-preview" style="background: ${theme.background}; border: 2px solid ${theme.border}; color: ${theme.text}; box-shadow: 0 8px 25px ${theme.glow || 'rgba(0,0,0,0.1)'};">
            <div class="document-header" style="text-align: center; border-bottom: 2px solid ${theme.border}; margin-bottom: 30px;">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 60px; height: 60px; object-fit: contain; margin: 0 auto 15px; display: block; border: 2px solid ${theme.primary}; border-radius: 8px;">` : ''}
                <h1 style="color: ${theme.primary}; margin: 0; font-size: 32px; text-shadow: 0 2px 4px ${theme.glow || 'rgba(0,0,0,0.1)'};">ANNEXURE</h1>
                <div class="company-info" style="margin-top: 15px; color: ${theme.text};">
                    <strong style="font-size: 16px; color: ${theme.primary};">${company.name}</strong><br>
                    ${company.address.split('\n')[0]}<br>
                    Tel: ${company.phone} | Email: ${company.email}
                </div>
            </div>

            <div style="margin: 20px 0; padding: 15px; background: ${theme.accent}20; border-left: 4px solid ${theme.primary}; border-radius: 5px;">
                <strong style="color: ${theme.primary};">Document Reference:</strong> ANX-2025-001<br>
                <strong style="color: ${theme.primary};">Related Contract:</strong> CON-2025-001<br>
                <strong style="color: ${theme.primary};">Effective Date:</strong> ${new Date().toLocaleDateString()}
            </div>

            <div class="annexure-content" style="margin: 20px 0; line-height: 1.6;">
                <h3 style="color: ${theme.primary};">EQUIPMENT LIST</h3>
                <ul>
                    <li>Yamaha CL5 Digital Console</li>
                    <li>4x Shure SM58 Microphones</li>
                    <li>2x Shure Beta 87A Microphones</li>
                    <li>8x XLR Cables (10m each)</li>
                </ul>

                <h3 style="color: ${theme.primary};">ADDITIONAL TERMS</h3>
                <p>All equipment covered under comprehensive insurance policy #BMI-2025-EQUIP. Equipment replacement value: R150,000.00</p>
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center;">
                <button class="btn-primary" onclick="openGenerator()" style="background: ${theme.primary}; border-color: ${theme.primary};">Create Annexure</button>
            </div>
        </div>
    `;
}

// Themed Artist Agreement Preview
function generateThemedArtistAgreementPreview(company, logo, themeName) {
    const theme = documentThemes[themeName];

    return `
        <div class="realistic-preview artist-agreement-preview" style="background: ${theme.background}; border: 2px solid ${theme.border}; color: ${theme.text}; box-shadow: 0 8px 25px ${theme.glow || 'rgba(0,0,0,0.1)'};">
            <div class="document-header" style="text-align: center; border-bottom: 2px solid ${theme.border}; margin-bottom: 30px;">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 80px; height: 80px; object-fit: contain; margin: 0 auto 15px; display: block; border: 2px solid ${theme.primary}; border-radius: 8px;">` : ''}
                <h1 style="color: ${theme.primary}; margin: 0; font-size: 32px; text-shadow: 0 2px 4px ${theme.glow || 'rgba(0,0,0,0.1)'};">ARTIST AGREEMENT</h1>
                <div class="company-info" style="margin-top: 15px; color: ${theme.text};">
                    <strong style="font-size: 16px; color: ${theme.primary};">${company.name}</strong><br>
                    Performance Booking Contract
                </div>
            </div>

            <div style="margin: 20px 0; padding: 15px; background: ${theme.accent}20; border-left: 4px solid ${theme.primary}; border-radius: 5px;">
                <strong style="color: ${theme.primary};">Performance Date:</strong> ${new Date(Date.now() + 60*24*60*60*1000).toLocaleDateString()}<br>
                <strong style="color: ${theme.primary};">Venue:</strong> Freedom Day Festival<br>
                <strong style="color: ${theme.primary};">Performance Fee:</strong> R25,000.00
            </div>

            <div class="agreement-content" style="margin: 20px 0; line-height: 1.6;">
                <h3 style="color: ${theme.primary};">PERFORMANCE DETAILS</h3>
                <p>Artist agrees to perform a 90-minute set including original compositions and selected covers as agreed upon.</p>

                <h3 style="color: ${theme.primary};">PAYMENT TERMS</h3>
                <ul>
                    <li>50% deposit upon contract signing</li>
                    <li>50% balance on performance date</li>
                    <li>Travel and accommodation expenses included</li>
                </ul>
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center;">
                <button class="btn-primary" onclick="openGenerator()" style="background: ${theme.primary}; border-color: ${theme.primary};">Create Artist Agreement</button>
            </div>
        </div>
    `;
}

// Themed Generic Preview
function generateThemedGenericPreview(config, company, logo, themeName) {
    const theme = documentThemes[themeName];

    return `
        <div class="realistic-preview generic-preview" style="background: ${theme.background}; border: 2px solid ${theme.border}; color: ${theme.text}; box-shadow: 0 8px 25px ${theme.glow || 'rgba(0,0,0,0.1)'};">
            <div class="document-header" style="border-bottom: 2px solid ${theme.border};">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 60px; height: 60px; object-fit: contain; float: left; margin-right: 15px; border: 2px solid ${theme.primary}; border-radius: 8px;">` : ''}
                <div class="header-content">
                    <h1 style="color: ${theme.primary}; margin: 0; font-size: 28px; text-shadow: 0 2px 4px ${theme.glow || 'rgba(0,0,0,0.1)'};">${config.title.toUpperCase()}</h1>
                    <div class="company-info" style="margin-top: 10px; color: ${theme.text};">
                        <strong style="color: ${theme.primary};">${company.name}</strong><br>
                        Professional ${config.title} Document
                    </div>
                </div>
                <div style="clear: both;"></div>
            </div>

            <div style="margin: 20px 0; padding: 15px; background: ${theme.accent}20; border-left: 4px solid ${theme.primary}; border-radius: 5px;">
                <p style="margin: 0; color: ${theme.text};">This is a ${config.title.toLowerCase()} document with professional ${theme.name.toLowerCase()} styling.</p>
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center;">
                <button class="btn-primary" onclick="openGenerator()" style="background: ${theme.primary}; border-color: ${theme.primary};">Create ${config.title}</button>
            </div>
        </div>
    `;
}

// Test function to check if modal works at all
function testModal() {
    console.log('🧪 Testing modal functionality...');
    const modal = document.getElementById('previewModal');
    const container = document.getElementById('previewContainer');

    if (modal && container) {
        container.innerHTML = '<div style="padding: 20px; text-align: center;"><h2>🧪 Test Modal Content</h2><p>This is a test to see if the modal works.</p></div>';
        modal.style.display = 'block';
        console.log('✅ Test modal should be visible now');
    } else {
        console.error('❌ Modal or container not found');
    }
}

// Generate PDF Preview Function
function generatePDFPreview() {
    try {
        console.log('🔍 Generating PDF Preview for:', currentDocumentType);

        // Check if jsPDF is available
        if (typeof window.jspdf === 'undefined') {
            console.error('❌ jsPDF library not found');
            alert('PDF library not loaded. Please refresh the page and try again.');
            return;
        }

        // Get active company data with comprehensive validation
        const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';
        const rawActiveCompany = testCompanies[activeCompanyKey];
        const companyLogo = localStorage.getItem(`companyLogo_${activeCompanyKey}`);

        // Validate and sanitize company data to prevent jsPDF errors
        const activeCompany = {
            name: safeText(rawActiveCompany?.name, 'Sample Company Ltd'),
            address: safeText(rawActiveCompany?.address, '123 Business Street\nBusiness District\nJohannesburg, 2000'),
            phone: safeText(rawActiveCompany?.phone, '+27 11 123 4567'),
            email: safeText(rawActiveCompany?.email, '<EMAIL>'),
            regNumber: safeText(rawActiveCompany?.regNumber, '2023/123456/07'),
            vatNumber: safeText(rawActiveCompany?.vatNumber, '**********'),
            representative: {
                name: safeText(rawActiveCompany?.representative?.name, 'John Smith'),
                title: safeText(rawActiveCompany?.representative?.title, 'Director')
            }
        };

        console.log('📋 Using validated company data for PDF:', activeCompany);

        // Show loading indicator
        const container = document.getElementById('previewContainer');
        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'pdfLoadingIndicator';
        loadingDiv.style.cssText = `
            position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8); color: white; padding: 20px; border-radius: 8px;
            z-index: 10000; text-align: center;
        `;
        loadingDiv.innerHTML = `
            <div style="font-size: 24px; margin-bottom: 10px;">📄</div>
            <div>Generating PDF Preview...</div>
            <div style="margin-top: 10px; font-size: 12px;">Please wait</div>
        `;
        document.body.appendChild(loadingDiv);

        // Generate PDF based on document type
        setTimeout(() => {
            generateDocumentPDF(currentDocumentType, activeCompany, companyLogo);
        }, 100);

    } catch (error) {
        console.error('❌ Error generating PDF preview:', error);
        alert('Error generating PDF preview: ' + error.message);
        removePDFLoadingIndicator();
    }
}

function removePDFLoadingIndicator() {
    const loadingDiv = document.getElementById('pdfLoadingIndicator');
    if (loadingDiv) {
        loadingDiv.remove();
    }
}

// Generate PDF for specific document types
function generateDocumentPDF(documentType, company, logo) {
    // Check if user is authenticated
    if (window.googleAuth && !googleAuth.isAuthenticated()) {
        googleAuth.showSignInModal();
        return;
    }

    // Check and consume tokens
    if (window.freemiumSystem && !freemiumSystem.consumeTokens('document_generation', documentType, `Generated ${documentType}`)) {
        return; // Token consumption failed, modal already shown
    }

    try {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        // Make PDF safe to prevent invalid argument errors
        makePDFSafe(doc);

        // Set up PDF parameters
        const margin = 20;
        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;
        const contentWidth = pageWidth - (margin * 2);
        let y = margin;

        // Generate content based on document type
        switch(documentType) {
            case 'invoice':
                y = generateInvoicePDFContent(doc, margin, y, contentWidth, company, logo);
                break;
            case 'receipt':
                y = generateReceiptPDFContent(doc, margin, y, contentWidth, company, logo);
                break;
            case 'quotation':
                y = generateQuotationPDFContent(doc, margin, y, contentWidth, company, logo);
                break;
            case 'contract':
                y = generateContractPDFContent(doc, margin, y, contentWidth, company, logo);
                break;
            case 'rider':
                y = generateTechnicalRiderPDFContent(doc, margin, y, contentWidth, company, logo);
                break;
            case 'annexure':
                y = generateAnnexurePDFContent(doc, margin, y, contentWidth, company, logo);
                break;
            case 'artist-agreement':
                y = generateArtistAgreementPDFContent(doc, margin, y, contentWidth, company, logo);
                break;
            default:
                y = generateGenericPDFContent(doc, margin, y, contentWidth, company, logo, documentType);
        }

        // Create preview blob
        const pdfOutput = doc.output('blob');

        // Show PDF preview in modal
        showPDFPreviewInModal(pdfOutput, documentType);

        // Remove loading indicator
        removePDFLoadingIndicator();

        console.log('✅ PDF Preview generated successfully for:', documentType);

    } catch (error) {
        console.error('❌ Error in generateDocumentPDF:', error);
        alert('Error generating PDF: ' + error.message);
        removePDFLoadingIndicator();
    }
}

// Show PDF preview in modal
function showPDFPreviewInModal(pdfBlob, documentType) {
    console.log('🔍 Showing PDF preview for:', documentType);

    try {
        // Convert blob to base64 for safer handling in local environments
        const reader = new FileReader();
        reader.onload = function(e) {
            const base64Data = e.target.result;

            // Update modal content to show PDF
            const container = document.getElementById('previewContainer');
            container.innerHTML = `
                <div class="pdf-preview-wrapper" style="height: 70vh; border: 1px solid #ddd; border-radius: 8px; overflow: hidden; background: #f8f9fa;">
                    <iframe
                        src="${base64Data}"
                        style="width: 100%; height: 100%; border: none;"
                        frameborder="0"
                        title="PDF Preview"
                        onload="console.log('✅ PDF preview loaded successfully')"
                        onerror="console.error('❌ PDF preview failed to load')">
                    </iframe>
                </div>
                <div class="pdf-preview-actions" style="margin-top: 15px; text-align: center; display: flex; gap: 10px; justify-content: center;">
                    <button onclick="downloadPDFPreview()" class="btn-primary" style="flex: 1; max-width: 200px;">
                        📥 Download PDF
                    </button>
                    <button onclick="openGenerator()" class="btn-outline" style="flex: 1; max-width: 200px;">
                        ✏️ Create Document
                    </button>
                    <button onclick="closePreview()" class="btn-secondary" style="flex: 1; max-width: 200px;">
                        ❌ Close Preview
                    </button>
                </div>
                <div style="text-align: center; margin-top: 10px; color: #666; font-size: 14px;">
                    This is a preview of your ${documentType} document with sample data
                </div>
            `;

            console.log('✅ PDF preview modal updated with base64 data');
        };

        reader.onerror = function(error) {
            console.error('❌ Error reading PDF blob:', error);
            showPDFPreviewFallback(documentType);
        };

        // Read blob as data URL (base64)
        reader.readAsDataURL(pdfBlob);

        // Store PDF blob for download
        window.currentPreviewPDF = pdfBlob;
        window.currentPreviewType = documentType;

    } catch (error) {
        console.error('❌ Error in showPDFPreviewInModal:', error);
        showPDFPreviewFallback(documentType);
    }
}

// Fallback preview when blob URL fails
function showPDFPreviewFallback(documentType) {
    console.log('🔄 Using fallback preview for:', documentType);

    const container = document.getElementById('previewContainer');
    container.innerHTML = `
        <div class="pdf-preview-fallback" style="height: 70vh; border: 1px solid #ddd; border-radius: 8px; display: flex; flex-direction: column; justify-content: center; align-items: center; background: #f8f9fa;">
            <div style="text-align: center; padding: 40px;">
                <div style="font-size: 48px; margin-bottom: 20px;">📄</div>
                <h3 style="color: #333; margin-bottom: 15px;">PDF Preview Ready</h3>
                <p style="color: #666; margin-bottom: 25px;">
                    Your ${documentType} document has been generated successfully.<br>
                    Click the download button below to view the PDF.
                </p>
                <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <strong>Document Type:</strong> ${documentType}<br>
                    <strong>Generated:</strong> ${new Date().toLocaleString()}<br>
                    <strong>Status:</strong> <span style="color: #28a745;">✅ Ready for Download</span>
                </div>
            </div>
        </div>
        <div class="pdf-preview-actions" style="margin-top: 15px; text-align: center; display: flex; gap: 10px; justify-content: center;">
            <button onclick="downloadPDFPreview()" class="btn-primary" style="flex: 1; max-width: 200px;">
                📥 Download PDF
            </button>
            <button onclick="openGenerator()" class="btn-outline" style="flex: 1; max-width: 200px;">
                ✏️ Create Document
            </button>
            <button onclick="closePreview()" class="btn-secondary" style="flex: 1; max-width: 200px;">
                ❌ Close Preview
            </button>
        </div>
        <div style="text-align: center; margin-top: 10px; color: #666; font-size: 14px;">
            PDF preview may not display in local environments due to security restrictions
        </div>
    `;
}

// Download current PDF preview
function downloadPDFPreview() {
    if (window.currentPreviewPDF && window.currentPreviewType) {
        try {
            // Use FileReader to convert blob to data URL for safer download
            const reader = new FileReader();
            reader.onload = function(e) {
                const link = document.createElement('a');
                link.href = e.target.result;
                link.download = `${window.currentPreviewType}_preview.pdf`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                console.log('✅ PDF preview downloaded successfully');
            };

            reader.onerror = function(error) {
                console.error('❌ Error reading PDF for download:', error);
                // Fallback to blob URL method
                try {
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(window.currentPreviewPDF);
                    link.download = `${window.currentPreviewType}_preview.pdf`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    // Clean up blob URL
                    setTimeout(() => URL.revokeObjectURL(link.href), 100);
                    console.log('✅ PDF downloaded using fallback method');
                } catch (fallbackError) {
                    console.error('❌ Fallback download failed:', fallbackError);
                    alert('Error downloading PDF. Please try again.');
                }
            };

            reader.readAsDataURL(window.currentPreviewPDF);

        } catch (error) {
            console.error('❌ Error in downloadPDFPreview:', error);
            alert('Error downloading PDF. Please try again.');
        }
    } else {
        console.error('❌ No PDF available for download');
        alert('No PDF available for download');
    }
}

// ========================================
// SAFE TEXT FUNCTIONS FOR jsPDF
// ========================================

// Safe text function for jsPDF to prevent invalid arguments
function safeText(value, fallback = '') {
    if (value === null || value === undefined || value === '') {
        return fallback;
    }
    return String(value).trim() || fallback;
}

// Professional Font System for Dashboard PDFs
let dashboardFontsLoaded = false;

// Initialize Professional Fonts for Dashboard jsPDF
function initializeDashboardProfessionalFonts(doc) {
    if (dashboardFontsLoaded) return;

    try {
        console.log('🎨 Loading professional fonts for dashboard PDF...');

        // Use built-in fonts with professional styling as fallback
        // This ensures compatibility while maintaining professional appearance
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(10);

        // Mark fonts as loaded
        dashboardFontsLoaded = true;
        console.log('✅ Dashboard professional fonts initialized successfully');

    } catch (error) {
        console.warn('⚠️ Dashboard font loading fallback - using system fonts:', error);
        dashboardFontsLoaded = true; // Prevent retry loops
    }
}

// Enhanced Font Setting Functions for Dashboard
function setDashboardProfessionalHeadingFont(doc, weight = 'bold', size = 12) {
    try {
        initializeDashboardProfessionalFonts(doc);
        doc.setFont('helvetica', weight); // Using helvetica as reliable fallback
        doc.setFontSize(size);
    } catch (error) {
        console.warn('Dashboard font setting fallback:', error);
        doc.setFont('helvetica', weight);
        doc.setFontSize(size);
    }
}

function setDashboardProfessionalBodyFont(doc, weight = 'normal', size = 10) {
    try {
        initializeDashboardProfessionalFonts(doc);
        doc.setFont('helvetica', weight); // Using helvetica as reliable fallback
        doc.setFontSize(size);
    } catch (error) {
        console.warn('Dashboard font setting fallback:', error);
        doc.setFont('helvetica', weight);
        doc.setFontSize(size);
    }
}

// Safe doc.text wrapper to prevent jsPDF errors
function safeDocText(doc, text, x, y, options = {}) {
    try {
        const safeTextValue = safeText(text, '');
        if (safeTextValue && typeof x === 'number' && typeof y === 'number') {
            doc.text(safeTextValue, x, y, options);
        } else {
            console.warn('⚠️ Skipped invalid text in dashboard:', { text, x, y });
        }
    } catch (error) {
        console.error('❌ Error in safeDocText:', error, { text, x, y });
    }
}

// Monkey patch doc.text to make it safe globally
function makePDFSafe(doc) {
    const originalText = doc.text;
    doc.text = function(text, x, y, options = {}) {
        try {
            const safeTextValue = safeText(text, '');
            if (safeTextValue && typeof x === 'number' && typeof y === 'number') {
                return originalText.call(this, safeTextValue, x, y, options);
            } else {
                console.warn('⚠️ Skipped invalid text in dashboard:', { text, x, y });
            }
        } catch (error) {
            console.error('❌ Error in doc.text:', error, { text, x, y });
        }
    };
    return doc;
}

// Generate Invoice PDF Content
function generateInvoicePDFContent(doc, margin, y, contentWidth, company, logo) {
    const normalSize = 10;
    const titleSize = 16;
    const sectionSize = 12;

    // Initialize professional fonts
    initializeDashboardProfessionalFonts(doc);

    // Title
    setDashboardProfessionalHeadingFont(doc, 'bold', titleSize);
    safeDocText(doc, 'INVOICE', margin, y);
    y += 15;

    // Company info with safe text validation
    setDashboardProfessionalBodyFont(doc, 'normal', normalSize);
    safeDocText(doc, `From: ${safeText(company?.name, 'Company Name')}`, margin, y);
    y += 6;
    safeDocText(doc, safeText(company?.address, 'Company Address').replace(/\n/g, ', '), margin, y);
    y += 6;
    safeDocText(doc, `Tel: ${safeText(company?.phone, 'Phone')} | Email: ${safeText(company?.email, 'Email')}`, margin, y);
    y += 15;

    // Invoice details
    const invoiceNumber = `INV-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;
    const currentDate = new Date().toLocaleDateString();

    safeDocText(doc, `Invoice Number: ${invoiceNumber}`, margin, y);
    safeDocText(doc, `Date: ${currentDate}`, margin + 100, y);
    y += 15;

    // Bill to section
    setDashboardProfessionalHeadingFont(doc, 'bold', normalSize);
    safeDocText(doc, 'Bill To:', margin, y);
    y += 8;
    setDashboardProfessionalBodyFont(doc, 'normal', normalSize);
    safeDocText(doc, 'Sample Client Company Ltd', margin, y);
    y += 6;
    safeDocText(doc, '123 Client Street, Business District', margin, y);
    y += 6;
    safeDocText(doc, 'Johannesburg, 2000, South Africa', margin, y);
    y += 15;

    // Items table header
    doc.setFont(undefined, 'bold');
    doc.text('Description', margin, y);
    doc.text('Qty', margin + 100, y);
    doc.text('Price', margin + 130, y);
    doc.text('Amount', margin + 160, y);
    y += 8;

    // Draw line under header
    doc.line(margin, y, margin + contentWidth, y);
    y += 8;

    // Sample items
    doc.setFont(undefined, 'normal');
    const items = [
        { desc: 'Professional Services', qty: '1', price: 'R 2,500.00', amount: 'R 2,500.00' },
        { desc: 'Consultation Fee', qty: '2', price: 'R 750.00', amount: 'R 1,500.00' },
        { desc: 'Project Management', qty: '1', price: 'R 1,200.00', amount: 'R 1,200.00' }
    ];

    items.forEach(item => {
        doc.text(item.desc, margin, y);
        doc.text(item.qty, margin + 100, y);
        doc.text(item.price, margin + 130, y);
        doc.text(item.amount, margin + 160, y);
        y += 8;
    });

    y += 10;

    // Totals
    doc.line(margin + 120, y, margin + contentWidth, y);
    y += 8;

    doc.text('Subtotal:', margin + 120, y);
    doc.text('R 5,200.00', margin + 160, y);
    y += 8;

    doc.text('VAT (15%):', margin + 120, y);
    doc.text('R 780.00', margin + 160, y);
    y += 8;

    doc.setFont(undefined, 'bold');
    doc.text('Total:', margin + 120, y);
    doc.text('R 5,980.00', margin + 160, y);
    y += 15;

    // Payment terms
    doc.setFont(undefined, 'normal');
    doc.text('Payment Terms: 30 days from invoice date', margin, y);
    y += 8;
    doc.text('Thank you for your business!', margin, y);

    return y;
}

// Generate Receipt PDF Content
function generateReceiptPDFContent(doc, margin, y, contentWidth, company, logo) {
    const normalSize = 10;
    const titleSize = 16;

    // Title
    doc.setFontSize(titleSize);
    doc.setFont(undefined, 'bold');
    doc.text('RECEIPT', margin, y);
    y += 15;

    // Company info
    doc.setFontSize(normalSize);
    doc.setFont(undefined, 'normal');
    doc.text(`From: ${company.name}`, margin, y);
    y += 6;
    doc.text(company.address.replace(/\n/g, ', '), margin, y);
    y += 6;
    doc.text(`Tel: ${company.phone} | Email: ${company.email}`, margin, y);
    y += 15;

    // Receipt details
    const receiptNumber = `RCP-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;
    const currentDate = new Date().toLocaleDateString();

    doc.text(`Receipt Number: ${receiptNumber}`, margin, y);
    doc.text(`Date: ${currentDate}`, margin + 100, y);
    y += 15;

    // Payment received from
    doc.setFont(undefined, 'bold');
    doc.text('Payment Received From:', margin, y);
    y += 8;
    doc.setFont(undefined, 'normal');
    doc.text('Sample Client Company Ltd', margin, y);
    y += 6;
    doc.text('123 Client Street, Business District', margin, y);
    y += 15;

    // Payment details
    doc.setFont(undefined, 'bold');
    doc.text('Payment Details:', margin, y);
    y += 8;
    doc.setFont(undefined, 'normal');
    doc.text('Amount Received: R 5,980.00', margin, y);
    y += 8;
    doc.text('Payment Method: Electronic Transfer', margin, y);
    y += 8;
    doc.text('Reference: Invoice INV-2024-001', margin, y);
    y += 15;

    // Thank you message
    doc.setFont(undefined, 'bold');
    doc.text('Thank you for your payment!', margin, y);
    y += 10;
    doc.setFont(undefined, 'normal');
    doc.text('This receipt serves as proof of payment.', margin, y);

    return y;
}

// Generate Quotation PDF Content
function generateQuotationPDFContent(doc, margin, y, contentWidth, company, logo) {
    const normalSize = 10;
    const titleSize = 16;

    // Title
    doc.setFontSize(titleSize);
    doc.setFont(undefined, 'bold');
    doc.text('QUOTATION', margin, y);
    y += 15;

    // Company info
    doc.setFontSize(normalSize);
    doc.setFont(undefined, 'normal');
    doc.text(`From: ${company.name}`, margin, y);
    y += 6;
    doc.text(company.address.replace(/\n/g, ', '), margin, y);
    y += 6;
    doc.text(`Tel: ${company.phone} | Email: ${company.email}`, margin, y);
    y += 15;

    // Quote details
    const quoteNumber = `QUO-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;
    const currentDate = new Date().toLocaleDateString();
    const validUntil = new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString();

    doc.text(`Quote Number: ${quoteNumber}`, margin, y);
    doc.text(`Date: ${currentDate}`, margin + 100, y);
    y += 8;
    doc.text(`Valid Until: ${validUntil}`, margin, y);
    y += 15;

    // Quote for section
    doc.setFont(undefined, 'bold');
    doc.text('Quote For:', margin, y);
    y += 8;
    doc.setFont(undefined, 'normal');
    doc.text('Sample Client Company Ltd', margin, y);
    y += 6;
    doc.text('123 Client Street, Business District', margin, y);
    y += 15;

    // Project description
    doc.setFont(undefined, 'bold');
    doc.text('Project: Professional Services Package', margin, y);
    y += 10;

    // Items table
    doc.setFont(undefined, 'bold');
    doc.text('Description', margin, y);
    doc.text('Qty', margin + 100, y);
    doc.text('Price', margin + 130, y);
    doc.text('Amount', margin + 160, y);
    y += 8;

    doc.line(margin, y, margin + contentWidth, y);
    y += 8;

    // Sample quote items
    doc.setFont(undefined, 'normal');
    const items = [
        { desc: 'Initial Consultation', qty: '1', price: 'R 1,500.00', amount: 'R 1,500.00' },
        { desc: 'Project Development', qty: '1', price: 'R 8,500.00', amount: 'R 8,500.00' },
        { desc: 'Implementation Support', qty: '20 hrs', price: 'R 450.00', amount: 'R 9,000.00' }
    ];

    items.forEach(item => {
        doc.text(item.desc, margin, y);
        doc.text(item.qty, margin + 100, y);
        doc.text(item.price, margin + 130, y);
        doc.text(item.amount, margin + 160, y);
        y += 8;
    });

    y += 10;

    // Totals
    doc.line(margin + 120, y, margin + contentWidth, y);
    y += 8;

    doc.text('Subtotal:', margin + 120, y);
    doc.text('R 19,000.00', margin + 160, y);
    y += 8;

    doc.text('VAT (15%):', margin + 120, y);
    doc.text('R 2,850.00', margin + 160, y);
    y += 8;

    doc.setFont(undefined, 'bold');
    doc.text('Total:', margin + 120, y);
    doc.text('R 21,850.00', margin + 160, y);
    y += 15;

    // Terms
    doc.setFont(undefined, 'normal');
    doc.text('Terms & Conditions:', margin, y);
    y += 8;
    doc.text('• Quote valid for 30 days from date of issue', margin, y);
    y += 6;
    doc.text('• 50% deposit required to commence work', margin, y);
    y += 6;
    doc.text('• Final payment due upon project completion', margin, y);

    return y;
}

// Generate Contract PDF Content
function generateContractPDFContent(doc, margin, y, contentWidth, company, logo) {
    const normalSize = 10;
    const titleSize = 16;

    // Title
    doc.setFontSize(titleSize);
    doc.setFont(undefined, 'bold');
    doc.text('SERVICE AGREEMENT', margin, y);
    y += 15;

    // Contract date
    const currentDate = new Date().toLocaleDateString();
    doc.setFontSize(normalSize);
    doc.setFont(undefined, 'normal');
    doc.text(`Contract Date: ${currentDate}`, margin, y);
    y += 15;

    // Parties
    doc.setFont(undefined, 'bold');
    doc.text('BETWEEN:', margin, y);
    y += 10;

    doc.setFont(undefined, 'normal');
    doc.text(`Service Provider: ${company.name}`, margin, y);
    y += 6;
    doc.text(company.address.replace(/\n/g, ', '), margin, y);
    y += 6;
    doc.text(`Tel: ${company.phone} | Email: ${company.email}`, margin, y);
    y += 15;

    doc.setFont(undefined, 'bold');
    doc.text('AND:', margin, y);
    y += 10;

    doc.setFont(undefined, 'normal');
    doc.text('Client: Sample Client Company Ltd', margin, y);
    y += 6;
    doc.text('123 Client Street, Business District', margin, y);
    y += 6;
    doc.text('Johannesburg, 2000, South Africa', margin, y);
    y += 15;

    // Services
    doc.setFont(undefined, 'bold');
    doc.text('SERVICES TO BE PROVIDED:', margin, y);
    y += 10;

    doc.setFont(undefined, 'normal');
    doc.text('1. Professional consulting services', margin, y);
    y += 6;
    doc.text('2. Project management and coordination', margin, y);
    y += 6;
    doc.text('3. Implementation support and training', margin, y);
    y += 6;
    doc.text('4. Ongoing maintenance and support', margin, y);
    y += 15;

    // Terms
    doc.setFont(undefined, 'bold');
    doc.text('TERMS AND CONDITIONS:', margin, y);
    y += 10;

    doc.setFont(undefined, 'normal');
    doc.text('• Contract period: 12 months from signing date', margin, y);
    y += 6;
    doc.text('• Payment terms: Monthly invoicing, 30 days payment terms', margin, y);
    y += 6;
    doc.text('• Either party may terminate with 30 days written notice', margin, y);
    y += 6;
    doc.text('• All work remains confidential and proprietary', margin, y);
    y += 15;

    // Signatures
    doc.setFont(undefined, 'bold');
    doc.text('SIGNATURES:', margin, y);
    y += 15;

    doc.setFont(undefined, 'normal');
    doc.text('Service Provider: ________________________', margin, y);
    doc.text('Date: ____________', margin + 120, y);
    y += 15;

    doc.text('Client: ________________________', margin, y);
    doc.text('Date: ____________', margin + 120, y);

    return y;
}

// Generate Technical Rider PDF Content
function generateTechnicalRiderPDFContent(doc, margin, y, contentWidth, company, logo) {
    const normalSize = 10;
    const titleSize = 16;
    const sectionSize = 12;

    // Title
    doc.setFontSize(titleSize);
    doc.setFont(undefined, 'bold');
    doc.text('TECHNICAL RIDER', margin, y);
    y += 15;

    // Event info
    doc.setFontSize(normalSize);
    doc.setFont(undefined, 'normal');
    doc.text(`Artist/Band: ${company.name}`, margin, y);
    y += 6;
    doc.text('Event: Sample Music Festival', margin, y);
    y += 6;
    doc.text(`Date: ${new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString()}`, margin, y);
    y += 6;
    doc.text('Venue: Sample Concert Hall', margin, y);
    y += 15;

    // Audio requirements
    doc.setFontSize(sectionSize);
    doc.setFont(undefined, 'bold');
    doc.text('AUDIO REQUIREMENTS:', margin, y);
    y += 10;

    doc.setFontSize(normalSize);
    doc.setFont(undefined, 'normal');
    doc.text('• Professional PA system capable of 110dB at FOH', margin, y);
    y += 6;
    doc.text('• 32-channel mixing console (digital preferred)', margin, y);
    y += 6;
    doc.text('• Monitor system with 6 separate mixes', margin, y);
    y += 6;
    doc.text('• Wireless microphone system (2 channels minimum)', margin, y);
    y += 6;
    doc.text('• DI boxes (8 channels)', margin, y);
    y += 15;

    // Lighting requirements
    doc.setFont(undefined, 'bold');
    doc.text('LIGHTING REQUIREMENTS:', margin, y);
    y += 10;

    doc.setFont(undefined, 'normal');
    doc.text('• Full stage wash (warm and cool)', margin, y);
    y += 6;
    doc.text('• Moving head spots (minimum 8)', margin, y);
    y += 6;
    doc.text('• LED strip lights for stage edge', margin, y);
    y += 6;
    doc.text('• Haze machine for atmosphere', margin, y);
    y += 6;
    doc.text('• Lighting operator required', margin, y);
    y += 15;

    // Stage requirements
    doc.setFont(undefined, 'bold');
    doc.text('STAGE REQUIREMENTS:', margin, y);
    y += 10;

    doc.setFont(undefined, 'normal');
    doc.text('• Stage size: minimum 8m x 6m', margin, y);
    y += 6;
    doc.text('• Power: 32A 3-phase supply', margin, y);
    y += 6;
    doc.text('• Drum riser: 3m x 2m x 0.4m high', margin, y);
    y += 6;
    doc.text('• Piano/keyboard stand', margin, y);
    y += 15;

    // Contact
    doc.setFont(undefined, 'bold');
    doc.text('TECHNICAL CONTACT:', margin, y);
    y += 8;
    doc.setFont(undefined, 'normal');
    doc.text(`${company.representative.name} - ${company.phone}`, margin, y);

    return y;
}

// Generate Annexure PDF Content
function generateAnnexurePDFContent(doc, margin, y, contentWidth, company, logo) {
    const normalSize = 10;
    const titleSize = 16;

    // Title
    doc.setFontSize(titleSize);
    doc.setFont(undefined, 'bold');
    doc.text('ANNEXURE', margin, y);
    y += 15;

    // Company info
    doc.setFontSize(normalSize);
    doc.setFont(undefined, 'normal');
    doc.text(`Company: ${company.name}`, margin, y);
    y += 6;
    doc.text(company.address.replace(/\n/g, ', '), margin, y);
    y += 15;

    // Annexure content
    doc.setFont(undefined, 'bold');
    doc.text('EQUIPMENT LIST:', margin, y);
    y += 10;

    doc.setFont(undefined, 'normal');
    doc.text('Audio Equipment:', margin, y);
    y += 8;
    doc.text('• Microphones: Shure SM58 (x4), Shure Beta 52A (x2)', margin + 10, y);
    y += 6;
    doc.text('• Instruments: Yamaha P-125 Digital Piano', margin + 10, y);
    y += 6;
    doc.text('• Amplifiers: Marshall JCM800 (x2)', margin + 10, y);
    y += 10;

    doc.text('Technical Specifications:', margin, y);
    y += 8;
    doc.text('• Power requirements: 240V AC, 32A supply', margin + 10, y);
    y += 6;
    doc.text('• Setup time required: 2 hours', margin + 10, y);
    y += 6;
    doc.text('• Sound check: 30 minutes before performance', margin + 10, y);

    return y;
}

// Generate Artist Agreement PDF Content
function generateArtistAgreementPDFContent(doc, margin, y, contentWidth, company, logo) {
    const normalSize = 10;
    const titleSize = 16;

    // Title
    doc.setFontSize(titleSize);
    doc.setFont(undefined, 'bold');
    doc.text('ARTIST AGREEMENT', margin, y);
    y += 15;

    // Agreement date
    const currentDate = new Date().toLocaleDateString();
    doc.setFontSize(normalSize);
    doc.setFont(undefined, 'normal');
    doc.text(`Agreement Date: ${currentDate}`, margin, y);
    y += 15;

    // Parties
    doc.setFont(undefined, 'bold');
    doc.text('ARTIST/BAND:', margin, y);
    y += 8;
    doc.setFont(undefined, 'normal');
    doc.text(`${company.name}`, margin, y);
    y += 6;
    doc.text(company.address.replace(/\n/g, ', '), margin, y);
    y += 15;

    doc.setFont(undefined, 'bold');
    doc.text('CLIENT/AGENT:', margin, y);
    y += 8;
    doc.setFont(undefined, 'normal');
    doc.text('Sample Event Company Ltd', margin, y);
    y += 6;
    doc.text('456 Event Street, Entertainment District', margin, y);
    y += 15;

    // Performance details
    doc.setFont(undefined, 'bold');
    doc.text('PERFORMANCE DETAILS:', margin, y);
    y += 10;

    doc.setFont(undefined, 'normal');
    doc.text('Event: Summer Music Festival 2024', margin, y);
    y += 6;
    doc.text('Venue: Grand Concert Hall', margin, y);
    y += 6;
    doc.text(`Date: ${new Date(Date.now() + 45*24*60*60*1000).toLocaleDateString()}`, margin, y);
    y += 6;
    doc.text('Performance Time: 8:00 PM - 10:00 PM', margin, y);
    y += 15;

    // Financial terms
    doc.setFont(undefined, 'bold');
    doc.text('FINANCIAL TERMS:', margin, y);
    y += 10;

    doc.setFont(undefined, 'normal');
    doc.text('Performance Fee: R 25,000.00', margin, y);
    y += 6;
    doc.text('Deposit (50%): R 12,500.00 (due upon signing)', margin, y);
    y += 6;
    doc.text('Balance: R 12,500.00 (due on performance date)', margin, y);
    y += 15;

    // Terms
    doc.setFont(undefined, 'bold');
    doc.text('TERMS & CONDITIONS:', margin, y);
    y += 10;

    doc.setFont(undefined, 'normal');
    doc.text('• Artist will provide 2-hour performance as specified', margin, y);
    y += 6;
    doc.text('• Technical rider requirements must be met', margin, y);
    y += 6;
    doc.text('• Cancellation policy: 30 days notice required', margin, y);
    y += 6;
    doc.text('• Force majeure clause applies', margin, y);

    return y;
}

// Generate Generic PDF Content
function generateGenericPDFContent(doc, margin, y, contentWidth, company, logo, documentType) {
    const normalSize = 10;
    const titleSize = 16;

    // Title
    doc.setFontSize(titleSize);
    doc.setFont(undefined, 'bold');
    doc.text(documentType.toUpperCase().replace('-', ' '), margin, y);
    y += 15;

    // Company info
    doc.setFontSize(normalSize);
    doc.setFont(undefined, 'normal');
    doc.text(`From: ${company.name}`, margin, y);
    y += 6;
    doc.text(company.address.replace(/\n/g, ', '), margin, y);
    y += 6;
    doc.text(`Tel: ${company.phone} | Email: ${company.email}`, margin, y);
    y += 15;

    // Generic content
    doc.text(`This is a sample ${documentType} document.`, margin, y);
    y += 10;
    doc.text('Content will be customized based on your specific requirements.', margin, y);
    y += 10;
    doc.text('Please use the document generator to create your actual document.', margin, y);

    return y;
}

// Function to open document generator is defined earlier in the file (line 283-287)

// Company Management Functions
function switchActiveCompany() {
    const selector = document.getElementById('activeCompanySelect');
    activeCompany = selector.value;
    updateCompanyStatus();
}

function updateCompanySelector() {
    const selector = document.getElementById('activeCompanySelect');
    if (!selector) {
        console.log('ℹ️ Company selector not found - skipping update');
        return;
    }

    selector.innerHTML = '';

    Object.keys(companies).forEach(companyId => {
        const company = companies[companyId];
        const option = document.createElement('option');
        option.value = companyId;
        option.textContent = company.name;
        selector.appendChild(option);
    });

    selector.value = activeCompany;
}

function showAddCompanyModal() {
    // Check subscription limits
    const companyCount = Object.keys(companies).length;
    const maxCompanies = getMaxCompanies();

    if (companyCount >= maxCompanies) {
        showUpgradeModal();
        return;
    }

    // Show company management modal
    showCompanyManager();
}

function getMaxCompanies() {
    // Return max companies based on subscription
    // Free: 3, Pro: 10, Enterprise: unlimited
    return 3; // Default to free tier
}

function showUpgradeModal() {
    alert('Upgrade to Pro or Enterprise for more companies!');
}

function showCompanyInfo() {
    const modal = document.getElementById('companyInfoModal');
    modal.style.display = 'block';

    // Ensure modal title is correct for "Edit Company" mode
    const modalTitle = modal.querySelector('.modal-header h2');
    if (modalTitle) {
        modalTitle.textContent = 'Company Information';
    }

    // Ensure submit button text is correct
    const submitBtn = modal.querySelector('.btn-primary');
    if (submitBtn) {
        submitBtn.textContent = 'Save Company Information';
    }

    // Display current company logo and info
    displayCurrentCompanyLogo();

    // Load company information
    loadCompanyInfo();

    // Initialize logo upload functionality
    initializeCompanyLogoUpload();
}

function closeCompanyInfo() {
    document.getElementById('companyInfoModal').style.display = 'none';
}

function loadCompanyInfo() {
    const companyInfo = getStoredCompanyInfo();

    if (companyInfo) {
        // Populate form with existing data
        document.getElementById('companyName').value = companyInfo.name || '';
        document.getElementById('companyRegNumber').value = companyInfo.regNumber || '';
        document.getElementById('companyVatNumber').value = companyInfo.vatNumber || '';
        document.getElementById('companyAddress').value = companyInfo.address || '';
        document.getElementById('companyPhone').value = companyInfo.phone || '';
        document.getElementById('companyEmail').value = companyInfo.email || '';
        document.getElementById('companyWebsite').value = companyInfo.website || '';

        // Handle representative info
        if (companyInfo.representative) {
            const repName = document.getElementById('representativeName');
            const repTitle = document.getElementById('representativeTitle');
            const repEmail = document.getElementById('representativeEmail');

            if (repName) repName.value = companyInfo.representative.name || '';
            if (repTitle) repTitle.value = companyInfo.representative.title || '';
            if (repEmail) repEmail.value = companyInfo.representative.email || '';
        }
    } else {
        // Load demo data for first time users
        loadDemoCompanyInfo();
    }

    // Initialize logo upload functionality
    initializeCompanyLogoUpload();
}

// Test companies data
const testCompanies = {
    'docugen-pro': {
        name: 'DocuGen Pro Solutions Ltd',
        regNumber: '2024/123456/07',
        vatNumber: '**********',
        address: '123 Business Street\nBusiness District\nJohannesburg, 2000\nSouth Africa',
        phone: '+27 11 123 4567',
        email: '<EMAIL>',
        website: 'https://www.docugenpro.com',
        representative: {
            name: 'John Smith',
            title: 'Managing Director',
            email: '<EMAIL>'
        },
        banking: {
            bankName: 'First National Bank',
            accountHolder: 'DocuGen Pro Solutions Ltd',
            accountNumber: '**********',
            branchCode: '250655',
            accountType: 'current',
            swiftCode: 'FIRNZAJJ',
            iban: 'ZA89 1234 5678 9012 3456 789'
        }
    },
    'bongomaffin': {
        name: 'Bongomaffin',
        regNumber: '2023/987654/07',
        vatNumber: '**********',
        address: '456 Music Avenue\nMelody Park\nCape Town, 8001\nSouth Africa',
        phone: '+27 21 987 6543',
        email: '<EMAIL>',
        website: 'https://www.bongomaffin.co.za',
        representative: {
            name: 'Stoan Seate',
            title: 'Creative Director',
            email: '<EMAIL>'
        },
        banking: {
            bankName: 'Standard Bank',
            accountHolder: 'Bongomaffin',
            accountNumber: '**********',
            branchCode: '051001',
            accountType: 'business',
            swiftCode: 'SBZAZAJJ',
            iban: 'ZA12 9876 5432 1098 7654 321'
        }
    },
    'benjamin-music': {
        name: 'Benjamin Music Initiatives',
        regNumber: '2022/555666/07',
        vatNumber: '**********',
        address: '789 Harmony Street\nRhythm District\nDurban, 4001\nSouth Africa',
        phone: '+27 31 555 7777',
        email: '<EMAIL>',
        website: 'https://www.benjaminmusic.co.za',
        representative: {
            name: 'Benjamin Mthembu',
            title: 'Founder & CEO',
            email: '<EMAIL>'
        },
        banking: {
            bankName: 'ABSA Bank',
            accountHolder: 'Benjamin Music Initiatives',
            accountNumber: '**********',
            branchCode: '632005',
            accountType: 'business',
            swiftCode: 'ABSAZAJJ',
            iban: 'ZA34 5556 6677 7788 8999 000'
        }
    }
};

function loadDemoCompanyInfo() {
    // Get current active company or default to DocuGen Pro
    const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';
    const demoData = testCompanies[activeCompanyKey];

    if (!demoData) {
        console.error('Demo company data not found for:', activeCompanyKey);
        return;
    }

    document.getElementById('companyName').value = demoData.name;
    document.getElementById('companyRegNumber').value = demoData.regNumber;
    document.getElementById('companyVatNumber').value = demoData.vatNumber;
    document.getElementById('companyAddress').value = demoData.address;
    document.getElementById('companyPhone').value = demoData.phone;
    document.getElementById('companyEmail').value = demoData.email;
    document.getElementById('companyWebsite').value = demoData.website;

    const repName = document.getElementById('representativeName');
    const repTitle = document.getElementById('representativeTitle');
    const repEmail = document.getElementById('representativeEmail');

    if (repName) repName.value = demoData.representative.name;
    if (repTitle) repTitle.value = demoData.representative.title;
    if (repEmail) repEmail.value = demoData.representative.email;

    console.log('✅ Demo company info loaded for:', demoData.name);
}

function getStoredCompanyInfo() {
    const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';
    const stored = localStorage.getItem(`companyInfo_${activeCompanyKey}`);
    return stored ? JSON.parse(stored) : null;
}

function getStoredBankDetails() {
    const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';
    const stored = localStorage.getItem(`bankDetails_${activeCompanyKey}`);
    return stored ? JSON.parse(stored) : null;
}

function showBankDetails() {
    document.getElementById('bankDetailsModal').style.display = 'block';

    // Display current company logo and info in banking modal
    displayCurrentCompanyLogoInBanking();

    // Load banking details
    loadBankDetails();
}

function closeBankDetails() {
    document.getElementById('bankDetailsModal').style.display = 'none';
}

function loadBankDetails() {
    const bankDetails = getStoredBankDetails();

    if (bankDetails) {
        // Populate form with existing data
        document.getElementById('bankName').value = bankDetails.bankName || '';
        document.getElementById('accountHolder').value = bankDetails.accountHolder || '';
        document.getElementById('accountNumber').value = bankDetails.accountNumber || '';
        document.getElementById('branchCode').value = bankDetails.branchCode || '';
        document.getElementById('accountType').value = bankDetails.accountType || 'current';
        document.getElementById('swiftCode').value = bankDetails.swiftCode || '';
        document.getElementById('iban').value = bankDetails.iban || '';
    } else {
        // Load demo banking data for first time users
        loadDemoBankDetails();
    }
}

function loadDemoBankDetails() {
    // Get current active company or default to DocuGen Pro
    const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';
    const demoData = testCompanies[activeCompanyKey]?.banking;

    if (!demoData) {
        console.error('Demo banking data not found for:', activeCompanyKey);
        return;
    }

    document.getElementById('bankName').value = demoData.bankName;
    document.getElementById('accountHolder').value = demoData.accountHolder;
    document.getElementById('accountNumber').value = demoData.accountNumber;
    document.getElementById('branchCode').value = demoData.branchCode;
    document.getElementById('accountType').value = demoData.accountType;
    document.getElementById('swiftCode').value = demoData.swiftCode;
    document.getElementById('iban').value = demoData.iban;

    console.log('✅ Demo banking details loaded for:', testCompanies[activeCompanyKey].name);
}

function updateCompanyStatus() {
    const companyInfo = getStoredCompanyInfo();
    const bankDetails = getStoredBankDetails();

    // Update company info status
    const infoStatus = document.getElementById('companyInfoStatus');
    if (infoStatus) {
        infoStatus.textContent = companyInfo ? 'Complete' : 'Not Set';
        infoStatus.className = companyInfo ? 'company-status' : 'company-status empty';
    }

    // Update bank details status
    const bankStatus = document.getElementById('bankDetailsStatus');
    if (bankStatus) {
        bankStatus.textContent = bankDetails ? 'Complete' : 'Not Set';
        bankStatus.className = bankDetails ? 'company-status' : 'company-status empty';
    }

    // Update company preview
    updateCompanyPreview();
}

// Test Company Management
function switchTestCompany() {
    const selector = document.getElementById('testCompanySelect');
    const selectedCompany = selector.value;

    // Store the active company
    localStorage.setItem('activeTestCompany', selectedCompany);

    // Update company preview
    updateCompanyPreview();

    // Update status indicators
    updateCompanyStatus();

    console.log('✅ Switched to test company:', testCompanies[selectedCompany].name);
}

function updateCompanyPreview() {
    const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';
    const company = testCompanies[activeCompanyKey];

    if (!company) return;

    const logoMini = document.getElementById('companyLogoMini');
    const nameMini = document.getElementById('companyNameMini');

    if (logoMini) {
        // Set company-specific logo/icon
        const logoData = getCompanyLogo(activeCompanyKey);
        if (logoData) {
            logoMini.innerHTML = `<img src="${logoData}" style="width: 100%; height: 100%; object-fit: contain; border-radius: 4px;">`;
        } else {
            logoMini.textContent = getCompanyIcon(activeCompanyKey);
        }
    }

    if (nameMini) {
        nameMini.textContent = company.name;
    }

    // Update selector
    const selector = document.getElementById('testCompanySelect');
    if (selector) {
        selector.value = activeCompanyKey;
    }

    // Update main dashboard banner
    const activeCompanyDisplay = document.getElementById('activeCompanyDisplay');
    if (activeCompanyDisplay) {
        const icon = getCompanyIcon(activeCompanyKey);
        activeCompanyDisplay.textContent = `${icon} ${company.name}`;
    }

    console.log(`✅ Company preview updated for: ${company.name}`);
}

function getCompanyIcon(companyKey) {
    const icons = {
        'docugen-pro': '📄',
        'bongomaffin': '🎵',
        'benjamin-music': '🎼'
    };
    return icons[companyKey] || '🏢';
}

function getCompanyLogo(companyKey) {
    // Check if company has a saved logo
    const savedLogo = localStorage.getItem(`companyLogo_${companyKey}`);
    if (savedLogo) {
        return savedLogo;
    }

    // Return generic logo for the company
    return createGenericLogo(companyKey);
}

function createGenericLogo(companyKey) {
    const canvas = document.createElement('canvas');
    canvas.width = 100;
    canvas.height = 100;
    const ctx = canvas.getContext('2d');

    // Company-specific colors and designs
    const logoConfigs = {
        'docugen-pro': {
            bgColor: '#4a9eff',
            textColor: '#ffffff',
            text: 'DGP',
            icon: '📄'
        },
        'bongomaffin': {
            bgColor: '#ff6b35',
            textColor: '#ffffff',
            text: 'BM',
            icon: '🎵'
        },
        'benjamin-music': {
            bgColor: '#28a745',
            textColor: '#ffffff',
            text: 'BMI',
            icon: '🎼'
        }
    };

    const config = logoConfigs[companyKey] || logoConfigs['docugen-pro'];

    // Create gradient background
    const gradient = ctx.createLinearGradient(0, 0, 100, 100);
    gradient.addColorStop(0, config.bgColor);
    gradient.addColorStop(1, adjustBrightness(config.bgColor, -20));

    // Draw background
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 100, 100);

    // Add border
    ctx.strokeStyle = adjustBrightness(config.bgColor, -30);
    ctx.lineWidth = 2;
    ctx.strokeRect(1, 1, 98, 98);

    // Add icon
    ctx.font = '30px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillStyle = config.textColor;
    ctx.fillText(config.icon, 50, 35);

    // Add text
    ctx.font = 'bold 16px Arial';
    ctx.fillText(config.text, 50, 70);

    const logoData = canvas.toDataURL();

    // Save the generated logo
    localStorage.setItem(`companyLogo_${companyKey}`, logoData);

    return logoData;
}

function adjustBrightness(hex, percent) {
    // Remove # if present
    hex = hex.replace('#', '');

    // Parse RGB
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    // Adjust brightness
    const newR = Math.max(0, Math.min(255, r + (r * percent / 100)));
    const newG = Math.max(0, Math.min(255, g + (g * percent / 100)));
    const newB = Math.max(0, Math.min(255, b + (b * percent / 100)));

    // Convert back to hex
    return '#' +
        Math.round(newR).toString(16).padStart(2, '0') +
        Math.round(newG).toString(16).padStart(2, '0') +
        Math.round(newB).toString(16).padStart(2, '0');
}

// Display current company logo in the company info modal
function displayCurrentCompanyLogo() {
    const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';
    const activeCompany = testCompanies[activeCompanyKey];

    if (!activeCompany) return;

    const currentLogoDiv = document.getElementById('currentCompanyLogo');
    const currentNameDiv = document.getElementById('currentCompanyName');

    if (currentLogoDiv) {
        // Get the company's logo
        const logoData = getCompanyLogo(activeCompanyKey);

        if (logoData) {
            // Display the actual logo
            currentLogoDiv.innerHTML = `<img src="${logoData}" style="width: 100%; height: 100%; object-fit: contain; border-radius: 6px;">`;
        } else {
            // Display company icon as fallback
            const icon = getCompanyIcon(activeCompanyKey);
            currentLogoDiv.innerHTML = `<div style="font-size: 40px; color: #666;">${icon}</div>`;
        }
    }

    if (currentNameDiv) {
        currentNameDiv.textContent = activeCompany.name;
    }

    console.log(`✅ Current company logo displayed for: ${activeCompany.name}`);
}

// Initialize company logo upload functionality
function initializeCompanyLogoUpload() {
    const uploadInput = document.getElementById('companyLogoUpload');
    const preview = document.getElementById('companyLogoPreview');
    const shapeSelect = document.getElementById('companyLogoShape');

    if (!uploadInput || !preview) return;

    uploadInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(event) {
                const img = document.createElement('img');
                img.src = event.target.result;
                img.style.cssText = 'width: 100%; height: 100%; object-fit: contain;';

                // Update preview
                preview.innerHTML = '';
                preview.appendChild(img);

                // Apply shape
                if (shapeSelect && shapeSelect.value === 'round') {
                    preview.style.borderRadius = '50%';
                } else {
                    preview.style.borderRadius = '8px';
                }

                // Store the new logo data
                const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';
                localStorage.setItem(`companyLogo_${activeCompanyKey}`, event.target.result);

                // Update current logo display
                displayCurrentCompanyLogo();

                // Update company preview in sidebar
                updateCompanyPreview();

                console.log(`✅ New logo uploaded for: ${activeCompanyKey}`);
            };
            reader.readAsDataURL(file);
        }
    });

    // Handle shape changes
    if (shapeSelect) {
        shapeSelect.addEventListener('change', function() {
            if (preview.querySelector('img')) {
                if (this.value === 'round') {
                    preview.style.borderRadius = '50%';
                } else {
                    preview.style.borderRadius = '8px';
                }
            }
        });
    }

    console.log('✅ Company logo upload functionality initialized');
}

// Realistic Document Preview Generation Functions
function generateInvoicePreview(company, logo) {
    const currentDate = new Date().toLocaleDateString();
    const invoiceNumber = `INV-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;

    return `
        <div class="realistic-preview invoice-preview">
            <div class="document-header">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 60px; height: 60px; object-fit: contain; float: left; margin-right: 15px;">` : ''}
                <div class="header-content">
                    <h1 style="color: #2c3e50; margin: 0; font-size: 28px;">INVOICE</h1>
                    <div class="company-info" style="margin-top: 10px;">
                        <strong>${company.name}</strong><br>
                        ${company.address.replace(/\n/g, '<br>')}<br>
                        Tel: ${company.phone} | Email: ${company.email}
                    </div>
                </div>
                <div style="clear: both;"></div>
            </div>

            <div class="invoice-details" style="margin: 20px 0; display: flex; justify-content: space-between;">
                <div class="invoice-info">
                    <strong>Invoice #:</strong> ${invoiceNumber}<br>
                    <strong>Date:</strong> ${currentDate}<br>
                    <strong>Due Date:</strong> ${new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString()}
                </div>
                <div class="client-info">
                    <strong>Bill To:</strong><br>
                    Sample Client Ltd<br>
                    456 Client Street<br>
                    Business City, 1234
                </div>
            </div>

            <table class="invoice-table" style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                <thead style="background: #f8f9fa;">
                    <tr>
                        <th style="padding: 12px; text-align: left; border: 1px solid #dee2e6;">Description</th>
                        <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">Qty</th>
                        <th style="padding: 12px; text-align: right; border: 1px solid #dee2e6;">Rate</th>
                        <th style="padding: 12px; text-align: right; border: 1px solid #dee2e6;">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dee2e6;">Professional Services</td>
                        <td style="padding: 10px; text-align: center; border: 1px solid #dee2e6;">1</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R 5,000.00</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R 5,000.00</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dee2e6;">Consultation Hours</td>
                        <td style="padding: 10px; text-align: center; border: 1px solid #dee2e6;">10</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R 500.00</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R 5,000.00</td>
                    </tr>
                </tbody>
            </table>

            <div class="invoice-totals" style="margin-top: 20px; text-align: right;">
                <div style="margin: 5px 0;"><strong>Subtotal:</strong> R 10,000.00</div>
                <div style="margin: 5px 0;"><strong>VAT (15%):</strong> R 1,500.00</div>
                <div style="margin: 10px 0; font-size: 18px; color: #2c3e50;"><strong>Total:</strong> R 11,500.00</div>
            </div>

            <div class="payment-info" style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <strong>Payment Details:</strong><br>
                Bank: ${company.banking.bankName}<br>
                Account: ${company.banking.accountNumber}<br>
                Branch Code: ${company.banking.branchCode}
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center;">
                <button class="btn-primary" onclick="openGenerator()">Create Invoice</button>
            </div>
        </div>
    `;
}

function generateReceiptPreview(company, logo) {
    const currentDate = new Date().toLocaleDateString();
    const receiptNumber = `RCP-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;

    return `
        <div class="realistic-preview receipt-preview">
            <div class="document-header">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 50px; height: 50px; object-fit: contain; float: left; margin-right: 15px;">` : ''}
                <div class="header-content">
                    <h1 style="color: #28a745; margin: 0; font-size: 24px;">PAYMENT RECEIPT</h1>
                    <div class="company-info" style="margin-top: 8px; font-size: 14px;">
                        <strong>${company.name}</strong><br>
                        ${company.address.split('\n')[0]}<br>
                        Tel: ${company.phone}
                    </div>
                </div>
                <div style="clear: both;"></div>
            </div>

            <div class="receipt-details" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                    <span><strong>Receipt #:</strong> ${receiptNumber}</span>
                    <span><strong>Date:</strong> ${currentDate}</span>
                </div>
                <div style="margin-bottom: 10px;">
                    <strong>Received From:</strong> Sample Client Ltd
                </div>
                <div>
                    <strong>Payment Method:</strong> Bank Transfer
                </div>
            </div>

            <div class="payment-details" style="margin: 20px 0;">
                <h3 style="color: #2c3e50; margin-bottom: 15px;">Payment Details</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr style="background: #f8f9fa;">
                        <td style="padding: 10px; border: 1px solid #dee2e6;"><strong>Description</strong></td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;"><strong>Amount</strong></td>
                    </tr>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dee2e6;">Payment for Invoice INV-1234</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R 11,500.00</td>
                    </tr>
                </table>
            </div>

            <div class="receipt-total" style="margin: 20px 0; text-align: right; padding: 15px; background: #e8f5e8; border-radius: 5px;">
                <div style="font-size: 20px; color: #28a745;"><strong>Amount Received: R 11,500.00</strong></div>
                <div style="margin-top: 5px; font-size: 14px; color: #666;">Thank you for your payment!</div>
            </div>

            <div class="receipt-footer" style="margin-top: 30px; text-align: center; font-size: 12px; color: #666;">
                This receipt serves as proof of payment.<br>
                For queries, contact: ${company.email}
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center;">
                <button class="btn-primary" onclick="openGenerator()">Create Receipt</button>
            </div>
        </div>
    `;
}

function generateQuotationPreview(company, logo) {
    const currentDate = new Date().toLocaleDateString();
    const validUntil = new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString();
    const quoteNumber = `QUO-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;

    return `
        <div class="realistic-preview quotation-preview">
            <div class="document-header">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 60px; height: 60px; object-fit: contain; float: left; margin-right: 15px;">` : ''}
                <div class="header-content">
                    <h1 style="color: #f39c12; margin: 0; font-size: 28px;">QUOTATION</h1>
                    <div class="company-info" style="margin-top: 10px;">
                        <strong>${company.name}</strong><br>
                        ${company.address.replace(/\n/g, '<br>')}<br>
                        Tel: ${company.phone} | Email: ${company.email}
                    </div>
                </div>
                <div style="clear: both;"></div>
            </div>

            <div class="quote-details" style="margin: 20px 0; display: flex; justify-content: space-between;">
                <div class="quote-info">
                    <strong>Quote #:</strong> ${quoteNumber}<br>
                    <strong>Date:</strong> ${currentDate}<br>
                    <strong>Valid Until:</strong> ${validUntil}
                </div>
                <div class="client-info">
                    <strong>Quote For:</strong><br>
                    Prospective Client Ltd<br>
                    789 Prospect Avenue<br>
                    Future City, 5678
                </div>
            </div>

            <div class="quote-scope" style="margin: 20px 0; padding: 15px; background: #fff3cd; border-radius: 5px; border-left: 4px solid #f39c12;">
                <h3 style="margin-top: 0; color: #856404;">Project Scope</h3>
                <p style="margin-bottom: 0;">Complete digital transformation solution including consultation, implementation, and training services.</p>
            </div>

            <table class="quote-table" style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                <thead style="background: #f8f9fa;">
                    <tr>
                        <th style="padding: 12px; text-align: left; border: 1px solid #dee2e6;">Service Description</th>
                        <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">Qty</th>
                        <th style="padding: 12px; text-align: right; border: 1px solid #dee2e6;">Rate</th>
                        <th style="padding: 12px; text-align: right; border: 1px solid #dee2e6;">Total</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dee2e6;">Initial Consultation & Analysis</td>
                        <td style="padding: 10px; text-align: center; border: 1px solid #dee2e6;">1</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R 8,000.00</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R 8,000.00</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dee2e6;">System Implementation</td>
                        <td style="padding: 10px; text-align: center; border: 1px solid #dee2e6;">1</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R 25,000.00</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R 25,000.00</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dee2e6;">Training & Support (3 months)</td>
                        <td style="padding: 10px; text-align: center; border: 1px solid #dee2e6;">1</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R 12,000.00</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R 12,000.00</td>
                    </tr>
                </tbody>
            </table>

            <div class="quote-totals" style="margin-top: 20px; text-align: right;">
                <div style="margin: 5px 0;"><strong>Subtotal:</strong> R 45,000.00</div>
                <div style="margin: 5px 0;"><strong>VAT (15%):</strong> R 6,750.00</div>
                <div style="margin: 10px 0; font-size: 18px; color: #f39c12;"><strong>Total Quote:</strong> R 51,750.00</div>
            </div>

            <div class="quote-terms" style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <strong>Terms & Conditions:</strong><br>
                • Quote valid for 30 days from date of issue<br>
                • 50% deposit required to commence work<br>
                • Final payment due upon project completion
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center;">
                <button class="btn-primary" onclick="openGenerator()">Create Quotation</button>
            </div>
        </div>
    `;
}

function generateArtistAgreementPreview(company, logo) {
    const currentDate = new Date().toLocaleDateString();
    const eventDate = new Date(Date.now() + 60*24*60*60*1000).toLocaleDateString();

    return `
        <div class="realistic-preview artist-agreement-preview">
            <div class="document-header" style="text-align: center; margin-bottom: 30px;">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 80px; height: 80px; object-fit: contain; margin: 0 auto 15px; display: block;">` : ''}
                <h1 style="color: #8e44ad; margin: 0; font-size: 24px;">ARTIST PERFORMANCE AGREEMENT</h1>
                <div style="margin-top: 15px; font-size: 14px; color: #666;">between</div>
            </div>

            <div class="parties-section" style="margin: 30px 0; display: flex; justify-content: space-between;">
                <div class="client-party" style="flex: 1; margin-right: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                    <h3 style="color: #2c3e50; margin-top: 0;">CLIENT</h3>
                    <strong>${company.name}</strong><br>
                    ${company.address.split('\n')[0]}<br>
                    ${company.address.split('\n')[1]}<br>
                    Tel: ${company.phone}<br>
                    Email: ${company.email}
                </div>
                <div class="artist-party" style="flex: 1; margin-left: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                    <h3 style="color: #2c3e50; margin-top: 0;">ARTIST/BAND</h3>
                    <strong>Sample Artist</strong><br>
                    123 Music Street<br>
                    Creative District, 9876<br>
                    Tel: +27 11 555 0123<br>
                    Email: <EMAIL>
                </div>
            </div>

            <div class="performance-details" style="margin: 20px 0; padding: 15px; background: #e8f4fd; border-radius: 5px; border-left: 4px solid #3498db;">
                <h3 style="margin-top: 0; color: #2c3e50;">Performance Details</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div>
                        <strong>Event:</strong> Summer Music Festival<br>
                        <strong>Date:</strong> ${eventDate}<br>
                        <strong>Time:</strong> 8:00 PM - 10:00 PM
                    </div>
                    <div>
                        <strong>Venue:</strong> Grand Concert Hall<br>
                        <strong>Address:</strong> 456 Entertainment Blvd<br>
                        <strong>Capacity:</strong> 2,000 people
                    </div>
                </div>
            </div>

            <div class="financial-terms" style="margin: 20px 0;">
                <h3 style="color: #2c3e50;">Financial Terms</h3>
                <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                    <tr style="background: #f8f9fa;">
                        <td style="padding: 10px; border: 1px solid #dee2e6;"><strong>Performance Fee</strong></td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R 50,000.00</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dee2e6;">Deposit (50%)</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R 25,000.00</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dee2e6;">Balance on Performance</td>
                        <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">R 25,000.00</td>
                    </tr>
                </table>
            </div>

            <div class="key-terms" style="margin: 20px 0; padding: 15px; background: #fff3cd; border-radius: 5px;">
                <h3 style="margin-top: 0; color: #856404;">Key Terms</h3>
                <ul style="margin: 0; padding-left: 20px;">
                    <li>Sound check: 2 hours before performance</li>
                    <li>Technical rider requirements must be met</li>
                    <li>Cancellation: 30 days notice required</li>
                    <li>Force majeure provisions apply</li>
                </ul>
            </div>

            <div class="signature-section" style="margin: 30px 0; display: flex; justify-content: space-between;">
                <div style="text-align: center; flex: 1;">
                    <div style="border-top: 1px solid #333; margin: 20px 0; padding-top: 5px;">
                        <strong>Client Signature</strong><br>
                        <small>Date: ___________</small>
                    </div>
                </div>
                <div style="text-align: center; flex: 1;">
                    <div style="border-top: 1px solid #333; margin: 20px 0; padding-top: 5px;">
                        <strong>Artist Signature</strong><br>
                        <small>Date: ___________</small>
                    </div>
                </div>
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center;">
                <button class="btn-primary" onclick="openGenerator()">Create Artist Agreement</button>
            </div>
        </div>
    `;
}

function generateTechnicalRiderPreview(company, logo) {
    return `
        <div class="realistic-preview technical-rider-preview">
            <div class="document-header">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 60px; height: 60px; object-fit: contain; float: left; margin-right: 15px;">` : ''}
                <div class="header-content">
                    <h1 style="color: #e74c3c; margin: 0; font-size: 28px;">TECHNICAL RIDER</h1>
                    <div class="company-info" style="margin-top: 10px;">
                        <strong>${company.name}</strong><br>
                        Event Technical Requirements & Specifications
                    </div>
                </div>
                <div style="clear: both;"></div>
            </div>

            <div class="rider-overview" style="margin: 20px 0; padding: 15px; background: #ffeaa7; border-radius: 5px; border-left: 4px solid #fdcb6e;">
                <h3 style="margin-top: 0; color: #2d3436;">Performance Overview</h3>
                <p><strong>Artist:</strong> Sample Artist | <strong>Performance Duration:</strong> 90 minutes + 20 min encore</p>
                <p><strong>Stage Requirements:</strong> Minimum 10m x 8m x 1.2m high platform</p>
            </div>

            <div class="technical-sections" style="margin: 20px 0;">
                <div class="tech-section" style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                    <h3 style="color: #2c3e50; margin-top: 0;">🎵 Audio Requirements</h3>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>PA System: Professional line array, minimum 15kW total power</li>
                        <li>Mixing Console: 32+ channel digital console</li>
                        <li>Microphones: 8x SM58, 4x SM57, 1x AKG D112</li>
                        <li>Wireless Systems: 6x professional wireless microphone systems</li>
                        <li>Monitor System: 8x active wedge monitors + IEM capability</li>
                    </ul>
                </div>

                <div class="tech-section" style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                    <h3 style="color: #2c3e50; margin-top: 0;">🎸 Backline Requirements</h3>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>Drum Kit: Professional 5-piece kit with hardware</li>
                        <li>Guitar Amps: 2x Marshall JCM800 100W heads + 4x12 cabinets</li>
                        <li>Bass Amp: Ampeg SVT-CL head + 8x10 cabinet</li>
                        <li>Keyboards: Yamaha CP88 stage piano + Nord Lead A1</li>
                    </ul>
                </div>

                <div class="tech-section" style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                    <h3 style="color: #2c3e50; margin-top: 0;">💡 Lighting Requirements</h3>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>Lighting Console: MA Lighting GrandMA3 or equivalent</li>
                        <li>LED Fixtures: 24x RGBW LED wash lights (300W each)</li>
                        <li>Moving Lights: 12x LED spot moving heads</li>
                        <li>Special Effects: Haze machine, 4x LED strobes</li>
                    </ul>
                </div>
            </div>

            <div class="schedule-requirements" style="margin: 20px 0; padding: 15px; background: #dff0d8; border-radius: 5px;">
                <h3 style="color: #3c763d; margin-top: 0;">⏰ Schedule Requirements</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div>
                        <strong>Load-in:</strong> 8 hours before doors<br>
                        <strong>Sound Check:</strong> 2 hours minimum<br>
                        <strong>Line Check:</strong> 30 minutes before doors
                    </div>
                    <div>
                        <strong>Show Duration:</strong> 90 minutes + encore<br>
                        <strong>Load-out:</strong> Immediate after show<br>
                        <strong>Curfew:</strong> All equipment removed by venue curfew
                    </div>
                </div>
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center;">
                <button class="btn-primary" onclick="openGenerator()">Create Technical Rider</button>
            </div>
        </div>
    `;
}

function generateAnnexurePreview(company, logo) {
    return `
        <div class="realistic-preview annexure-preview">
            <div class="document-header" style="text-align: center; margin-bottom: 30px;">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 60px; height: 60px; object-fit: contain; margin: 0 auto 15px; display: block;">` : ''}
                <h1 style="color: #6c757d; margin: 0; font-size: 32px;">ANNEXURE</h1>
                <div class="company-info" style="margin-top: 15px;">
                    <strong style="font-size: 16px;">${company.name}</strong><br>
                    ${company.address.split('\n')[0]}<br>
                    Tel: ${company.phone} | Email: ${company.email}
                </div>
            </div>

            <div class="annexure-content" style="margin: 30px 0;">
                <div class="section" style="margin-bottom: 25px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                    <h3 style="color: #2c3e50; margin-top: 0;">📋 Marketing & Promotional Requirements</h3>
                    <p>Details the artist's promotional obligations and materials provided to the venue or promoter:</p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>High-resolution photos, logos, and bios for advertising</li>
                        <li>Approved social media posts and branding guidelines</li>
                        <li>Radio/TV interview schedules or promotional appearances</li>
                        <li>Deadlines for submitting materials to ensure timely marketing</li>
                    </ul>
                </div>

                <div class="section" style="margin-bottom: 25px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                    <h3 style="color: #2c3e50; margin-top: 0;">🎫 Ticketing & Revenue Sharing</h3>
                    <p>Comprehensive breakdown of ticket sales and revenue distribution:</p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>Ticket pricing structure and categories</li>
                        <li>Revenue sharing percentages between parties</li>
                        <li>Box office reporting and settlement procedures</li>
                        <li>Complimentary ticket allocations</li>
                    </ul>
                </div>

                <div class="section" style="margin-bottom: 25px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                    <h3 style="color: #2c3e50; margin-top: 0;">🏨 Hospitality & Accommodation</h3>
                    <p>Detailed requirements for artist and crew hospitality:</p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>Accommodation standards and room requirements</li>
                        <li>Catering and refreshment specifications</li>
                        <li>Transportation arrangements</li>
                        <li>Security and access control measures</li>
                    </ul>
                </div>

                <div class="section" style="margin-bottom: 25px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                    <h3 style="color: #2c3e50; margin-top: 0;">⚖️ Legal & Insurance Requirements</h3>
                    <p>Essential legal protections and insurance coverage:</p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>Public liability insurance requirements</li>
                        <li>Intellectual property and performance rights</li>
                        <li>Force majeure and cancellation policies</li>
                        <li>Dispute resolution procedures</li>
                    </ul>
                </div>
            </div>

            <div class="annexure-footer" style="margin-top: 30px; padding: 15px; background: #e9ecef; border-radius: 5px; text-align: center;">
                <p style="margin: 0; font-style: italic; color: #6c757d;">
                    This annexure forms an integral part of the main agreement and should be read in conjunction with all other contract documents.
                </p>
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center;">
                <button class="btn-primary" onclick="openGenerator()">Create Annexure</button>
            </div>
        </div>
    `;
}

function generateContractPreview(company, logo) {
    return `
        <div class="realistic-preview contract-preview">
            <div class="document-header">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 60px; height: 60px; object-fit: contain; float: left; margin-right: 15px;">` : ''}
                <div class="header-content">
                    <h1 style="color: #2c3e50; margin: 0; font-size: 28px;">SERVICE AGREEMENT</h1>
                    <div class="company-info" style="margin-top: 10px;">
                        <strong>${company.name}</strong><br>
                        Professional Services Contract
                    </div>
                </div>
                <div style="clear: both;"></div>
            </div>

            <div class="contract-parties" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <h3 style="color: #2c3e50; margin-top: 0;">Contracting Parties</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <strong>Service Provider:</strong><br>
                        ${company.name}<br>
                        ${company.address.split('\n')[0]}<br>
                        Registration: ${company.regNumber}
                    </div>
                    <div>
                        <strong>Client:</strong><br>
                        Sample Client Corporation<br>
                        456 Business Avenue<br>
                        Registration: 2023/456789/07
                    </div>
                </div>
            </div>

            <div class="contract-terms" style="margin: 20px 0;">
                <h3 style="color: #2c3e50;">Key Terms & Conditions</h3>
                <div style="padding: 15px; background: #fff; border: 1px solid #dee2e6; border-radius: 5px;">
                    <p><strong>1. Scope of Services:</strong> Professional consulting and implementation services as detailed in Schedule A.</p>
                    <p><strong>2. Contract Duration:</strong> 12 months from commencement date with option to extend.</p>
                    <p><strong>3. Payment Terms:</strong> Monthly invoicing with 30-day payment terms.</p>
                    <p><strong>4. Intellectual Property:</strong> All work product remains property of service provider unless otherwise agreed.</p>
                    <p><strong>5. Confidentiality:</strong> Both parties agree to maintain strict confidentiality of proprietary information.</p>
                </div>
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center;">
                <button class="btn-primary" onclick="openGenerator()">Create Contract</button>
            </div>
        </div>
    `;
}

function generateGenericPreview(config, company, logo) {
    return `
        <div class="realistic-preview generic-preview">
            <div class="document-header">
                ${logo ? `<img src="${logo}" class="preview-logo" style="width: 60px; height: 60px; object-fit: contain; float: left; margin-right: 15px;">` : ''}
                <div class="header-content">
                    <h1 style="color: #6c757d; margin: 0; font-size: 28px;">${config.title.toUpperCase()}</h1>
                    <div class="company-info" style="margin-top: 10px;">
                        <strong>${company.name}</strong><br>
                        Professional ${config.title} Document
                    </div>
                </div>
                <div style="clear: both;"></div>
            </div>

            <div class="document-description" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <p style="margin: 0; font-size: 16px;">${config.description}</p>
            </div>

            <div class="features-list" style="margin: 20px 0;">
                <h3 style="color: #2c3e50;">Features Included:</h3>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    ${config.features.map(feature => `<li>✓ ${feature}</li>`).join('')}
                </ul>
            </div>

            <div class="preview-actions" style="margin-top: 20px; text-align: center;">
                <button class="btn-primary" onclick="openGenerator()">Create ${config.title}</button>
            </div>
        </div>
    `;
}

// Display current company logo in banking modal
function displayCurrentCompanyLogoInBanking() {
    const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';
    const activeCompany = testCompanies[activeCompanyKey];

    if (!activeCompany) return;

    const currentLogoDiv = document.getElementById('bankingCurrentCompanyLogo');
    const currentNameDiv = document.getElementById('bankingCurrentCompanyName');

    if (currentLogoDiv) {
        // Get the company's logo
        const logoData = getCompanyLogo(activeCompanyKey);

        if (logoData) {
            // Display the actual logo
            currentLogoDiv.innerHTML = `<img src="${logoData}" style="width: 100%; height: 100%; object-fit: contain; border-radius: 6px;">`;
        } else {
            // Display company icon as fallback
            const icon = getCompanyIcon(activeCompanyKey);
            currentLogoDiv.innerHTML = `<div style="font-size: 30px; color: #666;">${icon}</div>`;
        }
    }

    if (currentNameDiv) {
        currentNameDiv.textContent = activeCompany.name;
    }

    console.log(`✅ Banking modal company logo displayed for: ${activeCompany.name}`);
}

function loadCompanyData() {
    // Load company data from localStorage
    const savedCompanies = localStorage.getItem('docugen_companies');
    if (savedCompanies) {
        companies = JSON.parse(savedCompanies);
        updateCompanySelector();
        updateCompanyStatus();
    }
}

function saveCompanyData() {
    // Save company data to localStorage
    localStorage.setItem('docugen_companies', JSON.stringify(companies));
}



// Independent Document Management System
class DocumentManager {
    constructor() {
        this.documents = new Map(); // Store multiple documents
        this.activeDocumentId = null;
        this.documentCounter = 0;
    }

    addDocument(file, documentData) {
        const documentId = `doc_${++this.documentCounter}_${Date.now()}`;
        const document = {
            id: documentId,
            file: file,
            name: file.name,
            type: file.type,
            size: file.size,
            uploadedAt: new Date(),
            canvas: null,
            overlay: null,
            viewport: null,
            pdf: null,
            page: null,
            scale: 1,
            elements: [],
            history: [],
            historyIndex: -1,
            ...documentData
        };

        this.documents.set(documentId, document);
        this.setActiveDocument(documentId);

        console.log(`📄 Document added: ${file.name} (ID: ${documentId})`);
        return documentId;
    }

    removeDocument(documentId) {
        if (this.documents.has(documentId)) {
            const document = this.documents.get(documentId);
            console.log(`🗑️ Removing document: ${document.name}`);

            // Clean up canvas and overlay
            if (document.canvas) {
                document.canvas.remove();
            }
            if (document.overlay) {
                document.overlay.remove();
            }

            this.documents.delete(documentId);

            // If this was the active document, switch to another or clear
            if (this.activeDocumentId === documentId) {
                const remainingDocs = Array.from(this.documents.keys());
                if (remainingDocs.length > 0) {
                    this.setActiveDocument(remainingDocs[0]);
                } else {
                    this.activeDocumentId = null;
                }
            }
        }
    }

    setActiveDocument(documentId) {
        if (this.documents.has(documentId)) {
            this.activeDocumentId = documentId;
            console.log(`🎯 Active document set to: ${this.getActiveDocument()?.name}`);
            this.renderActiveDocument();
            return true;
        }
        return false;
    }

    getActiveDocument() {
        return this.activeDocumentId ? this.documents.get(this.activeDocumentId) : null;
    }

    getAllDocuments() {
        return Array.from(this.documents.values());
    }

    getDocumentById(documentId) {
        return this.documents.get(documentId);
    }

    updateDocument(documentId, updates) {
        if (this.documents.has(documentId)) {
            const document = this.documents.get(documentId);
            Object.assign(document, updates);

            // If this is the active document, re-render
            if (documentId === this.activeDocumentId) {
                this.renderActiveDocument();
            }
        }
    }

    renderActiveDocument() {
        const activeDoc = this.getActiveDocument();
        if (!activeDoc) {
            this.clearPreviewContainer();
            return;
        }

        console.log(`🎨 Rendering active document: ${activeDoc.name}`);

        // Update preview container with active document
        const previewContainer = document.getElementById('editorPreviewContainer');
        if (previewContainer && activeDoc.canvas) {
            previewContainer.innerHTML = '';

            const canvasContainer = document.createElement('div');
            canvasContainer.className = 'editor-canvas';
            canvasContainer.appendChild(activeDoc.canvas);
            canvasContainer.appendChild(activeDoc.overlay);

            previewContainer.appendChild(canvasContainer);


        }

        // Update document selector with delay to ensure modal is ready
        setTimeout(() => {
            this.updateDocumentSelector();
        }, 100);
    }

    clearPreviewContainer() {
        const previewContainer = document.getElementById('editorPreviewContainer');
        if (previewContainer) {
            previewContainer.innerHTML = `
                <div class="editor-placeholder">
                    <div class="placeholder-icon">📄</div>
                    <h4>No Document Selected</h4>
                    <p>Upload a PDF to start editing</p>
                </div>
            `;
        }
    }

    updateDocumentSelector() {
        // Create or update document selector UI
        let selector = document.getElementById('documentSelector');
        if (!selector) {
            selector = this.createDocumentSelector();
            if (!selector) {
                console.log('📄 Document selector not created - header not found');
                return;
            }
        }

        // Clear existing options
        const select = selector.querySelector('select');
        if (!select) {
            console.log('📄 Document select element not found');
            return;
        }

        select.innerHTML = '<option value="">Select Document...</option>';

        // Add documents
        this.getAllDocuments().forEach(doc => {
            const option = document.createElement('option');
            option.value = doc.id;
            option.textContent = `${doc.name} (${this.formatFileSize(doc.size)})`;
            option.selected = doc.id === this.activeDocumentId;
            select.appendChild(option);
        });

        // Update document count
        const countSpan = selector.querySelector('.document-count');
        if (countSpan) {
            countSpan.textContent = `${this.documents.size} document${this.documents.size !== 1 ? 's' : ''}`;
        }
    }

    createDocumentSelector() {
        // Try multiple possible header locations
        let editorHeader = document.querySelector('.modal-header');

        if (!editorHeader) {
            console.log('📄 No suitable header found for document selector');
            return null;
        }

        const selector = document.createElement('div');
        selector.id = 'documentSelector';
        selector.className = 'document-selector';
        selector.innerHTML = `
            <div class="selector-group">
                <label>📄 Active Document:</label>
                <select id="documentSelect">
                    <option value="">Select Document...</option>
                </select>
                <span class="document-count">0 documents</span>
                <button type="button" class="btn-secondary btn-sm" onclick="documentManager.showDocumentManager()">
                    📋 Manage
                </button>
            </div>
        `;

        // Add event listener for document switching
        const select = selector.querySelector('select');
        select.addEventListener('change', (e) => {
            if (e.target.value) {
                this.setActiveDocument(e.target.value);
            }
        });

        // Insert after header or at the beginning
        if (editorHeader.classList.contains('modal-header')) {
            editorHeader.insertAdjacentElement('afterend', selector);
        } else {
            editorHeader.appendChild(selector);
        }

        console.log('📄 Document selector created and added to:', editorHeader.className);
        return selector;
    }

    showDocumentManager() {
        // Create document management modal
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.id = 'documentManagerModal';
        modal.innerHTML = `
            <div class="modal-content" style="max-width: 800px;">
                <div class="modal-header">
                    <h3>📋 Document Manager</h3>
                    <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="document-list">
                        ${this.renderDocumentList()}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary" onclick="this.closest('.modal').remove()">Close</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        modal.style.display = 'block';
    }

    renderDocumentList() {
        if (this.documents.size === 0) {
            return '<p class="text-center">No documents uploaded yet.</p>';
        }

        return `
            <div class="document-grid">
                ${this.getAllDocuments().map(doc => `
                    <div class="document-card ${doc.id === this.activeDocumentId ? 'active' : ''}">
                        <div class="document-icon">📄</div>
                        <div class="document-info">
                            <h4>${doc.name}</h4>
                            <p>Size: ${this.formatFileSize(doc.size)}</p>
                            <p>Uploaded: ${doc.uploadedAt.toLocaleDateString()}</p>
                            <p>Elements: ${doc.elements.length}</p>
                        </div>
                        <div class="document-actions">
                            <button class="btn-primary btn-sm" onclick="documentManager.setActiveDocument('${doc.id}')">
                                ${doc.id === this.activeDocumentId ? '✅ Active' : '📄 Select'}
                            </button>
                            <button class="btn-danger btn-sm" onclick="documentManager.removeDocument('${doc.id}')">
                                🗑️ Delete
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}



















// Coordinate Mapping Utilities
function getPDFCoordinates(mouseEvent, canvas, viewport) {

    if (!canvas || typeof canvas.getBoundingClientRect !== 'function') {
        console.warn('❌ Canvas not found or invalid in getPDFCoordinates');
        return { pdfX: 0, pdfY: 0 };
    }
    const rect = canvas.getBoundingClientRect();
    const canvasX = mouseEvent.clientX - rect.left;
    const canvasY = mouseEvent.clientY - rect.top;

    // Convert to PDF coordinates
    const pdfX = canvasX / viewport.scale;
    const pdfY = canvasY / viewport.scale;

    return {
        mouse: { x: mouseEvent.clientX, y: mouseEvent.clientY },
        canvas: { x: canvasX, y: canvasY },
        pdf: { x: pdfX, y: pdfY },
        viewport: viewport
    };
}

function getOverlayCoordinates(pdfCoords, viewport) {
    return {
        x: pdfCoords.x * viewport.scale,
        y: pdfCoords.y * viewport.scale
    };
}

// Throttle utility for performance
function throttle(func, delay) {
    let timeoutId;
    let lastExecTime = 0;
    return function (...args) {
        const currentTime = Date.now();

        if (currentTime - lastExecTime > delay) {
            func.apply(this, args);
            lastExecTime = currentTime;
        } else {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => {
                func.apply(this, args);
                lastExecTime = Date.now();
            }, delay - (currentTime - lastExecTime));
        }
    };
}















// Initialize Editor Tools


















// Document Preview Zoom Functions
let previewZoom = 100;

function zoomInPreview() {
    previewZoom = Math.min(previewZoom + 25, 500); // Allow up to 500%
    updatePreviewZoom();
    console.log('🔍+ Preview zoomed in to:', previewZoom + '%');
}

function zoomOutPreview() {
    previewZoom = Math.max(previewZoom - 25, 25); // Minimum 25%
    updatePreviewZoom();
    console.log('🔍- Preview zoomed out to:', previewZoom + '%');
}

function resetPreviewZoom() {
    previewZoom = 100;
    updatePreviewZoom();
    console.log('⚡ Preview zoom reset to:', previewZoom + '%');
}

function updatePreviewZoom() {
    const container = document.getElementById('documentPreviewContainer');
    const zoomLevel = document.getElementById('previewZoomLevel');

    if (container) {
        const scale = previewZoom / 100;

        // For canvas elements, re-render at higher resolution to prevent blur
        const canvas = container.querySelector('canvas');
        if (canvas && importedDocument) {
            rerenderCanvasAtZoom(canvas, scale);

            // Adjust container to fit the scaled canvas
            const canvasWrapper = canvas.parentElement;
            if (canvasWrapper) {
                canvasWrapper.style.width = (canvas.offsetWidth * scale) + 'px';
                canvasWrapper.style.height = (canvas.offsetHeight * scale) + 'px';
                canvasWrapper.style.overflow = 'visible';
            }
        } else {
            // For non-canvas elements, use CSS transform with image-rendering optimization
            container.style.transform = `scale(${scale})`;
            container.style.transformOrigin = 'top left';
            container.style.imageRendering = 'crisp-edges';
        }

        // Ensure container can scroll if content is larger
        container.style.overflow = 'auto';
        container.style.maxHeight = '600px';

        console.log(`🔍 Preview zoom updated to: ${previewZoom}%`);
    }

    if (zoomLevel) {
        zoomLevel.textContent = `${previewZoom}%`;
    }
}

function rerenderCanvasAtZoom(canvas, scale) {
    if (!importedDocument) return;

    const ctx = canvas.getContext('2d');

    // Store original dimensions if not already stored
    if (!canvas.originalWidth) {
        canvas.originalWidth = canvas.width;
        canvas.originalHeight = canvas.height;
        canvas.originalStyleWidth = canvas.style.width;
        canvas.originalStyleHeight = canvas.style.height;
    }

    // Calculate new dimensions based on original size
    const baseWidth = parseInt(canvas.originalStyleWidth) || canvas.originalWidth;
    const baseHeight = parseInt(canvas.originalStyleHeight) || canvas.originalHeight;
    const newWidth = baseWidth * scale;
    const newHeight = baseHeight * scale;

    // Set canvas display size
    canvas.style.width = newWidth + 'px';
    canvas.style.height = newHeight + 'px';

    // Set actual canvas size for high DPI rendering
    const dpr = window.devicePixelRatio || 1;
    canvas.width = newWidth * dpr;
    canvas.height = newHeight * dpr;

    // Scale context for device pixel ratio
    ctx.scale(dpr, dpr);

    // Configure high-quality rendering
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';
    ctx.textRenderingOptimization = 'optimizeQuality';

    // Re-render the document at the new scale
    if (importedDocument.type.startsWith('image/')) {
        // For images, redraw at higher resolution
        const img = new Image();
        img.onload = function() {
            ctx.drawImage(img, 0, 0, newWidth, newHeight);
        };
        img.src = URL.createObjectURL(importedDocument);
    } else if (importedDocument.type === 'application/pdf') {
        // For PDFs, re-render the page at higher resolution
        rerenderPDFAtZoom(canvas, scale);
    }

    console.log(`🎨 Canvas re-rendered at ${scale}x scale for crisp zoom`);
}

function rerenderPDFAtZoom(canvas, scale) {
    if (typeof pdfjsLib === 'undefined' || !window.currentPDFDoc) {
        console.warn('⚠️ PDF.js not available or no PDF document loaded');
        return;
    }

    const ctx = canvas.getContext('2d');

    // Get the first page for re-rendering
    window.currentPDFDoc.getPage(1).then(function(page) {
        // Use a higher base scale for better text rendering
        const baseScale = Math.max(1.5, scale); // Minimum 1.5x for text quality
        const renderScale = baseScale * scale;

        // Calculate viewport at the render scale
        const viewport = page.getViewport({ scale: renderScale });

        // Get original canvas dimensions
        const baseWidth = parseInt(canvas.originalStyleWidth) || 600;
        const baseHeight = parseInt(canvas.originalStyleHeight) || 800;

        // Set canvas display size
        canvas.style.width = (baseWidth * scale) + 'px';
        canvas.style.height = (baseHeight * scale) + 'px';

        // Set actual canvas size for high-quality rendering
        const dpr = window.devicePixelRatio || 1;
        canvas.width = viewport.width * dpr;
        canvas.height = viewport.height * dpr;

        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Scale context for device pixel ratio
        ctx.scale(dpr, dpr);

        const renderContext = {
            canvasContext: ctx,
            viewport: viewport,
            enableWebGL: false, // Disable WebGL for better text rendering
            renderInteractiveForms: false
        };

        page.render(renderContext).promise.then(function() {
            console.log(`📄 PDF re-rendered at ${scale}x scale (render scale: ${renderScale}x) for crisp zoom`);
        }).catch(function(error) {
            console.error('❌ PDF re-rendering error:', error);
        });
    }).catch(function(error) {
        console.error('❌ Failed to get PDF page for re-rendering:', error);
    });
}

function showPreviewZoomControls() {
    const zoomControls = document.getElementById('previewZoomControls');
    if (zoomControls) {
        zoomControls.style.display = 'flex';
        console.log('🔍 Preview zoom controls shown');
    }
}

function hidePreviewZoomControls() {
    const zoomControls = document.getElementById('previewZoomControls');
    if (zoomControls) {
        zoomControls.style.display = 'none';
        console.log('🔍 Preview zoom controls hidden');
    }
}

// Add Text Element
function addTextElement(x, y) {
    const text = prompt('Enter text:');
    if (!text) return;

    console.log(`📝 Adding text "${text}" at position (${x}, ${y})`);

    const element = createEditorElement('text', x, y, {
        text: text,
        fontSize: document.getElementById('toolSize').value + 'px',
        color: document.getElementById('toolColor').value,
        opacity: document.getElementById('toolOpacity').value / 100,
        fontFamily: document.getElementById('toolFont')?.value || 'Arial'
    });

    addElementToCanvas(element);
    console.log('✅ Text element added successfully');
}

// Add Rectangle Element
function addRectangleElement(x, y) {
    console.log(`🔷 Adding rectangle at position (${x}, ${y})`);

    const element = createEditorElement('rectangle', x, y, {
        width: 120,
        height: 80,
        borderColor: document.getElementById('toolColor').value,
        strokeWidth: document.getElementById('toolStroke')?.value || 2,
        opacity: document.getElementById('toolOpacity').value / 100,
        fillColor: 'transparent'
    });

    addElementToCanvas(element);
    console.log('✅ Rectangle element added successfully');
}

// Add Circle Element
function addCircleElement(x, y) {
    const element = createEditorElement('circle', x, y, {
        radius: 30,
        borderColor: document.getElementById('toolColor').value,
        opacity: document.getElementById('toolOpacity').value / 100
    });

    addElementToCanvas(element);
}

// Add Highlight Element
function addHighlightElement(x, y) {
    console.log(`🖍️ Adding highlight at position (${x}, ${y})`);

    const element = createEditorElement('highlight', x, y, {
        width: 150,
        height: 25,
        backgroundColor: document.getElementById('toolColor').value,
        opacity: 0.4
    });

    addElementToCanvas(element);
    console.log('✅ Highlight element added successfully');
}

// Add Polygon Element
function addPolygonElement(x, y) {
    const sides = prompt('Enter number of sides (3-12):', '6');
    const numSides = parseInt(sides);

    if (isNaN(numSides) || numSides < 3 || numSides > 12) {
        alert('Please enter a valid number of sides (3-12)');
        return;
    }

    const element = createEditorElement('polygon', x, y, {
        sides: numSides,
        radius: 40,
        color: document.getElementById('toolColor').value,
        strokeWidth: document.getElementById('toolStroke')?.value || 2,
        opacity: document.getElementById('toolOpacity').value / 100
    });

    addElementToCanvas(element);
}

// Freehand Drawing Functions
function startFreehandDrawing(x, y) {
    console.log('✏️ Starting freehand drawing at:', x, y);
    // This will be handled by mouse events
}

function startFreehandPath(x, y) {
    window.currentPath = {
        points: [{x, y}],
        color: document.getElementById('toolColor').value,
        strokeWidth: document.getElementById('toolStroke')?.value || 2,
        opacity: document.getElementById('toolOpacity').value / 100
    };
    console.log('✏️ Started freehand path at:', x, y);
}

function continueFreehandPath(x, y) {
    if (window.currentPath) {
        window.currentPath.points.push({x, y});
        // Update preview of current path
        renderCurrentPath();
    }
}

function finishFreehandPath() {
    if (window.currentPath && window.currentPath.points.length > 1) {
        const element = createEditorElement('freehand', 0, 0, {
            points: window.currentPath.points,
            color: window.currentPath.color,
            strokeWidth: window.currentPath.strokeWidth,
            opacity: window.currentPath.opacity
        });

        addElementToCanvas(element);
        window.currentPath = null;
        console.log('✏️ Finished freehand path');
    }
}

function renderCurrentPath() {
    // This would render the current path being drawn
    // For now, we'll just log it
    if (window.currentPath) {
        console.log('✏️ Drawing path with', window.currentPath.points.length, 'points');
    }
}

// Add Underline Element
function addUnderlineElement(x, y) {
    const element = createEditorElement('underline', x, y, {
        width: 100,
        height: 2,
        backgroundColor: document.getElementById('toolColor').value,
        opacity: document.getElementById('toolOpacity').value / 100
    });

    addElementToCanvas(element);
}

// Add Strikethrough Element
function addStrikethroughElement(x, y) {
    const element = createEditorElement('strikethrough', x, y, {
        width: 100,
        height: 2,
        backgroundColor: document.getElementById('toolColor').value,
        opacity: document.getElementById('toolOpacity').value / 100
    });

    addElementToCanvas(element);
}

// Add Comment Element
function addCommentElement(x, y) {
    const comment = prompt('Enter comment:');
    if (!comment) return;

    const element = createEditorElement('comment', x, y, {
        text: comment,
        backgroundColor: '#ffeb3b',
        opacity: 0.9
    });

    addElementToCanvas(element);
}

// Add Arrow Element
function addArrowElement(x, y) {
    const element = createEditorElement('arrow', x, y, {
        width: 100,
        height: 20,
        color: document.getElementById('toolColor').value,
        strokeWidth: document.getElementById('toolStroke')?.value || 2,
        opacity: document.getElementById('toolOpacity').value / 100
    });

    addElementToCanvas(element);
}

// Add Line Element
function addLineElement(x, y) {
    const element = createEditorElement('line', x, y, {
        width: 100,
        height: 2,
        color: document.getElementById('toolColor').value,
        strokeWidth: document.getElementById('toolStroke')?.value || 2,
        opacity: document.getElementById('toolOpacity').value / 100
    });

    addElementToCanvas(element);
}

// Add Sticky Note Element
function addStickyNoteElement(x, y) {
    const note = prompt('Enter sticky note text:');
    if (!note) return;

    const element = createEditorElement('sticky-note', x, y, {
        text: note,
        backgroundColor: '#fff59d',
        width: 120,
        height: 80,
        opacity: 0.9
    });

    addElementToCanvas(element);
}

// Add Callout Element
function addCalloutElement(x, y) {
    const text = prompt('Enter callout text:');
    if (!text) return;

    const element = createEditorElement('callout', x, y, {
        text: text,
        backgroundColor: '#e3f2fd',
        borderColor: document.getElementById('toolColor').value,
        width: 150,
        height: 60,
        opacity: 0.9
    });

    addElementToCanvas(element);
}

// Add Image Element
function addImageElement(x, y) {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(event) {
                const element = createEditorElement('image', x, y, {
                    src: event.target.result,
                    width: 100,
                    height: 100,
                    opacity: document.getElementById('toolOpacity').value / 100
                });
                addElementToCanvas(element);
            };
            reader.readAsDataURL(file);
        }
    };
    input.click();
}

// Add Signature Element
function addSignatureElement(x, y) {
    // Get saved signatures from localStorage
    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');

    if (signatures.length === 0) {
        alert('No saved signatures found. Please create a signature first in the signature tools.');
        return;
    }

    // Use the first signature for now
    const signature = signatures[0];
    const element = createEditorElement('signature', x, y, {
        src: signature.dataUrl,
        width: 120,
        height: 60,
        opacity: document.getElementById('toolOpacity').value / 100
    });

    addElementToCanvas(element);
}

// Add Stamp Element
function addStampElement(x, y) {
    const stampText = prompt('Enter stamp text:', 'APPROVED');
    if (!stampText) return;

    const element = createEditorElement('stamp', x, y, {
        text: stampText,
        color: document.getElementById('toolColor').value,
        fontSize: '16px',
        fontWeight: 'bold',
        border: '3px solid',
        borderRadius: '8px',
        padding: '8px 16px',
        opacity: 0.8
    });

    addElementToCanvas(element);
}

// Add Watermark Element
function addWatermarkElement(x, y) {
    const watermarkText = prompt('Enter watermark text:', 'CONFIDENTIAL');
    if (!watermarkText) return;

    const element = createEditorElement('watermark', x, y, {
        text: watermarkText,
        color: document.getElementById('toolColor').value,
        fontSize: '48px',
        fontWeight: 'bold',
        opacity: 0.1,
        rotation: -45
    });

    addElementToCanvas(element);
}

// Select Element At Position
function selectElementAt(x, y) {
    // Find element at position
    const elementDiv = document.elementFromPoint(x, y);
    if (elementDiv && elementDiv.classList.contains('editor-element')) {
        const elementId = elementDiv.dataset.elementId;
        selectElement(elementId);
    }
}

// Erase Element At Position
function eraseElementAt(x, y) {
    const elementDiv = document.elementFromPoint(x, y);
    if (elementDiv && elementDiv.classList.contains('editor-element')) {
        const elementId = elementDiv.dataset.elementId;
        removeElement(elementId);
    }
}

// Remove Element
function removeElement(elementId) {
    if (!window.editorElements) return;

    window.editorElements = window.editorElements.filter(el => el.id !== elementId);
    renderEditorElements();
    console.log('🗑️ Element removed:', elementId);
}

// Select Element
function selectElement(elementId) {
    if (!window.editorElements) return;

    // Deselect all elements
    window.editorElements.forEach(el => el.selected = false);

    // Select the target element
    const element = window.editorElements.find(el => el.id === elementId);
    if (element) {
        element.selected = true;
        console.log('👆 Element selected:', elementId);
    }

    renderEditorElements();
}





// Create Element Div
function createElementDiv(element) {
    const div = document.createElement('div');
    div.className = 'editor-element draggable-editor-element';
    div.dataset.elementId = element.id;
    div.style.position = 'absolute';
    div.style.left = element.x + 'px';
    div.style.top = element.y + 'px';
    div.style.opacity = element.properties.opacity || 1;
    div.style.zIndex = '1000';
    div.style.cursor = 'move';
    div.style.userSelect = 'none';

    // Add selection styling
    if (element.selected) {
        div.style.border = '2px dashed #007bff';
        div.style.boxShadow = '0 0 10px rgba(0,123,255,0.3)';

        // Add delete button for selected elements
        addDeleteButton(div, element);
    }

    switch (element.type) {
        case 'text':
            div.innerHTML = element.properties.text;
            div.style.fontSize = element.properties.fontSize;
            div.style.color = element.properties.color;
            div.style.fontFamily = element.properties.fontFamily || 'Arial';
            div.style.fontWeight = 'bold';
            div.style.padding = '4px 8px';
            div.style.backgroundColor = 'rgba(255,255,255,0.9)';
            div.style.borderRadius = '4px';
            div.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
            div.style.border = '1px solid rgba(0,0,0,0.1)';
            div.style.cursor = 'move';
            div.style.userSelect = 'none';
            div.style.whiteSpace = 'nowrap';
            break;

        case 'rectangle':
            div.style.width = element.properties.width + 'px';
            div.style.height = element.properties.height + 'px';
            div.style.border = element.properties.strokeWidth + 'px solid ' + element.properties.borderColor;
            div.style.backgroundColor = element.properties.fillColor || 'transparent';
            div.style.borderRadius = '2px';
            div.style.cursor = 'move';
            div.style.boxSizing = 'border-box';
            break;

        case 'circle':
            const radius = element.properties.radius;
            div.style.width = (radius * 2) + 'px';
            div.style.height = (radius * 2) + 'px';
            div.style.border = '2px solid ' + element.properties.borderColor;
            div.style.borderRadius = '50%';
            div.style.backgroundColor = 'transparent';
            break;

        case 'highlight':
            div.style.width = element.properties.width + 'px';
            div.style.height = element.properties.height + 'px';
            div.style.backgroundColor = element.properties.backgroundColor;
            div.style.opacity = element.properties.opacity;
            div.style.borderRadius = '2px';
            div.style.cursor = 'move';
            div.style.border = '1px dashed rgba(0,0,0,0.2)';
            div.style.mixBlendMode = 'multiply';
            break;

        case 'comment':
            div.innerHTML = '💬 ' + element.properties.text;
            div.style.backgroundColor = element.properties.backgroundColor;
            div.style.padding = '8px 12px';
            div.style.borderRadius = '8px';
            div.style.fontSize = '12px';
            div.style.fontWeight = 'bold';
            div.style.maxWidth = '200px';
            div.style.wordWrap = 'break-word';
            break;

        case 'arrow':
            div.style.width = element.properties.width + 'px';
            div.style.height = element.properties.height + 'px';
            div.style.backgroundColor = element.properties.color;
            div.innerHTML = '→';
            div.style.display = 'flex';
            div.style.alignItems = 'center';
            div.style.justifyContent = 'center';
            div.style.fontSize = '20px';
            div.style.color = 'white';
            div.style.fontWeight = 'bold';
            break;

        case 'line':
            div.style.width = element.properties.width + 'px';
            div.style.height = element.properties.strokeWidth + 'px';
            div.style.backgroundColor = element.properties.color;
            break;

        case 'sticky-note':
            div.innerHTML = element.properties.text;
            div.style.width = element.properties.width + 'px';
            div.style.height = element.properties.height + 'px';
            div.style.backgroundColor = element.properties.backgroundColor;
            div.style.padding = '8px';
            div.style.fontSize = '12px';
            div.style.fontFamily = 'Arial, sans-serif';
            div.style.borderRadius = '4px';
            div.style.boxShadow = '2px 2px 8px rgba(0,0,0,0.2)';
            div.style.wordWrap = 'break-word';
            break;

        case 'callout':
            div.innerHTML = element.properties.text;
            div.style.width = element.properties.width + 'px';
            div.style.height = element.properties.height + 'px';
            div.style.backgroundColor = element.properties.backgroundColor;
            div.style.border = '2px solid ' + element.properties.borderColor;
            div.style.padding = '8px';
            div.style.fontSize = '12px';
            div.style.fontWeight = 'bold';
            div.style.borderRadius = '8px';
            div.style.display = 'flex';
            div.style.alignItems = 'center';
            div.style.justifyContent = 'center';
            div.style.textAlign = 'center';
            break;

        case 'image':
        case 'signature':
            const img = document.createElement('img');
            img.src = element.properties.src;
            img.style.width = element.properties.width + 'px';
            img.style.height = element.properties.height + 'px';
            img.style.objectFit = 'contain';
            img.style.borderRadius = '4px';
            div.appendChild(img);
            div.style.width = element.properties.width + 'px';
            div.style.height = element.properties.height + 'px';
            break;

        case 'stamp':
            div.innerHTML = element.properties.text;
            div.style.color = element.properties.color;
            div.style.fontSize = element.properties.fontSize;
            div.style.fontWeight = element.properties.fontWeight;
            div.style.border = element.properties.border + ' ' + element.properties.color;
            div.style.borderRadius = element.properties.borderRadius;
            div.style.padding = element.properties.padding;
            div.style.textAlign = 'center';
            div.style.textTransform = 'uppercase';
            div.style.letterSpacing = '1px';
            break;

        case 'watermark':
            div.innerHTML = element.properties.text;
            div.style.color = element.properties.color;
            div.style.fontSize = element.properties.fontSize;
            div.style.fontWeight = element.properties.fontWeight;
            div.style.transform = `rotate(${element.properties.rotation}deg)`;
            div.style.textAlign = 'center';
            div.style.textTransform = 'uppercase';
            div.style.letterSpacing = '2px';
            div.style.pointerEvents = 'none';
            break;

        case 'polygon':
            const sides = element.properties.sides;
            const polygonRadius = element.properties.radius;
            div.style.width = (polygonRadius * 2) + 'px';
            div.style.height = (polygonRadius * 2) + 'px';
            div.style.border = element.properties.strokeWidth + 'px solid ' + element.properties.color;
            div.style.borderRadius = '0';
            div.style.backgroundColor = 'transparent';
            // Create polygon shape using CSS clip-path
            const polygonPoints = [];
            for (let i = 0; i < sides; i++) {
                const angle = (i * 2 * Math.PI) / sides - Math.PI / 2;
                const x = 50 + 40 * Math.cos(angle);
                const y = 50 + 40 * Math.sin(angle);
                polygonPoints.push(`${x}% ${y}%`);
            }
            div.style.clipPath = `polygon(${polygonPoints.join(', ')})`;
            div.style.backgroundColor = element.properties.color;
            div.style.border = 'none';
            break;

        case 'freehand':
            // Create SVG for freehand drawing
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');

            // Calculate bounding box
            const points = element.properties.points;
            const minX = Math.min(...points.map(p => p.x));
            const minY = Math.min(...points.map(p => p.y));
            const maxX = Math.max(...points.map(p => p.x));
            const maxY = Math.max(...points.map(p => p.y));

            const width = maxX - minX + 20;
            const height = maxY - minY + 20;

            svg.setAttribute('width', width);
            svg.setAttribute('height', height);
            svg.style.position = 'absolute';

            // Create path data
            let pathData = `M ${points[0].x - minX + 10} ${points[0].y - minY + 10}`;
            for (let i = 1; i < points.length; i++) {
                pathData += ` L ${points[i].x - minX + 10} ${points[i].y - minY + 10}`;
            }

            path.setAttribute('d', pathData);
            path.setAttribute('stroke', element.properties.color);
            path.setAttribute('stroke-width', element.properties.strokeWidth);
            path.setAttribute('fill', 'none');
            path.setAttribute('stroke-linecap', 'round');
            path.setAttribute('stroke-linejoin', 'round');

            svg.appendChild(path);
            div.appendChild(svg);
            div.style.width = width + 'px';
            div.style.height = height + 'px';
            div.style.left = (element.x + minX - 10) + 'px';
            div.style.top = (element.y + minY - 10) + 'px';
            break;

        case 'underline':
            div.style.width = element.properties.width + 'px';
            div.style.height = element.properties.height + 'px';
            div.style.backgroundColor = element.properties.backgroundColor;
            div.style.borderRadius = '1px';
            break;

        case 'strikethrough':
            div.style.width = element.properties.width + 'px';
            div.style.height = element.properties.height + 'px';
            div.style.backgroundColor = element.properties.backgroundColor;
            div.style.borderRadius = '1px';
            break;
    }

    // Add selection styling if selected
    if (element.selected) {
        div.classList.add('selected');
        div.style.border = '2px dashed #e74c3c';
        div.style.boxShadow = '0 0 0 2px rgba(231, 76, 60, 0.3)';

        // Add resize handles
        addResizeHandles(div);
    }

    // Add click handler for selection
    div.addEventListener('click', (e) => {
        e.stopPropagation();
        selectElement(element.id);
    });

    // Add drag functionality
    makeDraggable(div, element);

    return div;
}

// Add Resize Handles
function addResizeHandles(elementDiv) {
    const handles = ['nw', 'ne', 'sw', 'se'];
    handles.forEach(handle => {
        const handleDiv = document.createElement('div');
        handleDiv.className = `resize-handle ${handle}`;
        handleDiv.style.position = 'absolute';
        handleDiv.style.width = '8px';
        handleDiv.style.height = '8px';
        handleDiv.style.background = '#e74c3c';
        handleDiv.style.border = '1px solid white';
        handleDiv.style.borderRadius = '50%';
        handleDiv.style.cursor = `${handle}-resize`;

        // Position handles
        switch (handle) {
            case 'nw':
                handleDiv.style.top = '-4px';
                handleDiv.style.left = '-4px';
                break;
            case 'ne':
                handleDiv.style.top = '-4px';
                handleDiv.style.right = '-4px';
                break;
            case 'sw':
                handleDiv.style.bottom = '-4px';
                handleDiv.style.left = '-4px';
                break;
            case 'se':
                handleDiv.style.bottom = '-4px';
                handleDiv.style.right = '-4px';
                break;
        }

        elementDiv.appendChild(handleDiv);
    });
}

// Make Element Draggable
function makeDraggable(elementDiv, element) {
    let isDragging = false;
    let startX, startY, startLeft, startTop;

    elementDiv.addEventListener('mousedown', (e) => {
        if (e.target.classList.contains('resize-handle')) return;

        isDragging = true;
        startX = e.clientX;
        startY = e.clientY;
        startLeft = parseInt(elementDiv.style.left) || 0;
        startTop = parseInt(elementDiv.style.top) || 0;

        elementDiv.style.cursor = 'grabbing';
        e.preventDefault();
    });

    document.addEventListener('mousemove', (e) => {
        if (!isDragging) return;

        const scale = (window.currentZoom || 100) / 100;
        const deltaX = (e.clientX - startX) / scale;
        const deltaY = (e.clientY - startY) / scale;

        const newLeft = startLeft + deltaX;
        const newTop = startTop + deltaY;

        elementDiv.style.left = newLeft + 'px';
        elementDiv.style.top = newTop + 'px';

        // Update element position
        element.x = newLeft;
        element.y = newTop;
    });

    document.addEventListener('mouseup', () => {
        if (isDragging) {
            isDragging = false;
            elementDiv.style.cursor = 'move';
            console.log('📍 Element moved to:', element.x, element.y);
        }
    });
}

// Add Delete Button to Selected Elements
function addDeleteButton(elementDiv, element) {
    const deleteBtn = document.createElement('div');
    deleteBtn.className = 'delete-element-btn';
    deleteBtn.innerHTML = '×';
    deleteBtn.style.position = 'absolute';
    deleteBtn.style.top = '-10px';
    deleteBtn.style.right = '-10px';
    deleteBtn.style.width = '20px';
    deleteBtn.style.height = '20px';
    deleteBtn.style.backgroundColor = '#e74c3c';
    deleteBtn.style.color = 'white';
    deleteBtn.style.borderRadius = '50%';
    deleteBtn.style.display = 'flex';
    deleteBtn.style.alignItems = 'center';
    deleteBtn.style.justifyContent = 'center';
    deleteBtn.style.cursor = 'pointer';
    deleteBtn.style.fontSize = '14px';
    deleteBtn.style.fontWeight = 'bold';
    deleteBtn.style.zIndex = '1001';
    deleteBtn.style.border = '2px solid white';
    deleteBtn.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';

    deleteBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        deleteElement(element.id);
    });

    elementDiv.appendChild(deleteBtn);
}

// Delete Element
function deleteElement(elementId) {
    if (!window.editorElements) return;

    const index = window.editorElements.findIndex(el => el.id === elementId);
    if (index !== -1) {
        window.editorElements.splice(index, 1);
        renderEditorElements();
        saveEditorState();
        console.log('🗑️ Element deleted:', elementId);
    }
}



// Editor History Management
function initializeEditorHistory() {
    window.editorHistory = [];
    window.editorHistoryIndex = -1;
    window.maxHistorySize = 50;
}

function saveEditorState() {
    if (!window.editorHistory) initializeEditorHistory();

    // Remove any future history if we're not at the end
    if (window.editorHistoryIndex < window.editorHistory.length - 1) {
        window.editorHistory = window.editorHistory.slice(0, window.editorHistoryIndex + 1);
    }

    // Add current state
    const state = JSON.parse(JSON.stringify(window.editorElements || []));
    window.editorHistory.push(state);
    window.editorHistoryIndex++;

    // Limit history size
    if (window.editorHistory.length > window.maxHistorySize) {
        window.editorHistory.shift();
        window.editorHistoryIndex--;
    }

    console.log('💾 Editor state saved, history size:', window.editorHistory.length);
}

function restoreEditorState(state) {
    window.editorElements = JSON.parse(JSON.stringify(state));
    renderEditorElements();
    console.log('🔄 Editor state restored');
}

// Editor Toolbar Functions




function saveEditedPdf() {
    if (!window.currentEditorFile) {
        alert('No PDF file loaded for editing.');
        return;
    }

    console.log('💾 Saving edited PDF...');

    try {
        // Create new jsPDF with edited elements
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        // Add edited elements to PDF
        window.editorElements.forEach(element => {
            addElementToPdf(doc, element);
        });

        // Save the edited PDF
        const fileName = `edited_${window.currentEditorFile.name}`;
        doc.save(fileName);

        console.log('✅ Edited PDF saved successfully');

    } catch (error) {
        console.error('❌ Error saving edited PDF:', error);
        alert('Error saving edited PDF. Please try again.');
    }
}

function addElementToPdf(doc, element) {
    const x = element.x * 0.75; // Convert pixels to PDF units
    const y = element.y * 0.75;

    switch (element.type) {
        case 'text':
            doc.setTextColor(element.properties.color);
            doc.setFontSize(parseInt(element.properties.fontSize));
            doc.text(element.properties.text, x, y);
            break;

        case 'rectangle':
            doc.setDrawColor(element.properties.borderColor);
            doc.rect(x, y, element.properties.width * 0.75, element.properties.height * 0.75);
            break;

        case 'circle':
            doc.setDrawColor(element.properties.borderColor);
            doc.circle(x + element.properties.radius * 0.75, y + element.properties.radius * 0.75, element.properties.radius * 0.75);
            break;
    }
}




















function loadPimpingGenericPreview(file, container) {
    container.innerHTML = `
        <div class="document-preview-wrapper">
            <div class="generic-preview-placeholder">
                <div class="doc-icon">📝</div>
                <h4>Document</h4>
                <p>${file.name}</p>
                <p class="doc-note">Document preview will be processed</p>
            </div>
            <div class="enhancement-overlay" id="enhancementOverlay"></div>
        </div>
    `;

    initializePimpingPreviewInteraction();
}

function initializePimpingPreviewInteraction() {
    const overlay = document.getElementById('enhancementOverlay');
    if (!overlay) return;

    console.log('✅ Pimping preview interaction initialized');

    // Apply current enhancements
    setTimeout(() => {
        updatePimpingPreview();
    }, 100);
}

// Update Pimping Preview
function updatePimpingPreview() {
    console.log('🔄 Updating pimping preview...');
    const overlay = document.getElementById('enhancementOverlay');
    if (!overlay) {
        console.warn('❌ Enhancement overlay not found');
        return;
    }

    // Get current settings
    const selectedFrame = document.querySelector('.frame-option.active')?.dataset.frame || 'elegant';
    const borderWidth = document.getElementById('borderWidth')?.value || 2;
    const borderColor = document.getElementById('borderColor')?.value || '#4a9eff';
    const borderStyle = document.getElementById('borderStyle')?.value || 'solid';
    const shadowEnabled = document.getElementById('enableShadow')?.checked || false;
    const shadowIntensity = document.getElementById('shadowIntensity')?.value || 30;
    const selectedBackground = document.querySelector('input[name="background"]:checked')?.value || 'none';

    console.log('Current settings:', {
        selectedFrame,
        borderWidth,
        borderColor,
        borderStyle,
        shadowEnabled,
        shadowIntensity,
        selectedBackground
    });

    // Apply frame styles
    let frameStyles = '';
    switch (selectedFrame) {
        case 'elegant':
            frameStyles = `
                border: 4px solid transparent;
                background: linear-gradient(white, white) padding-box,
                           linear-gradient(135deg, #667eea 0%, #764ba2 100%) border-box;
                box-shadow: inset 0 0 0 2px rgba(255,255,255,0.3);
            `;
            break;
        case 'modern':
            frameStyles = `
                border: 3px solid #4a9eff;
                background: linear-gradient(135deg, rgba(74, 158, 255, 0.1) 0%, rgba(0, 212, 255, 0.1) 100%);
            `;
            break;
        case 'classic':
            frameStyles = `
                border: 6px double #8b4513;
                background: linear-gradient(135deg, rgba(139, 69, 19, 0.1) 0%, rgba(218, 165, 32, 0.1) 100%);
            `;
            break;
        case 'luxury':
            frameStyles = `
                border: 4px solid #b8860b;
                background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 237, 78, 0.2) 100%);
                box-shadow: 0 0 0 2px #ffd700, inset 0 0 0 2px rgba(0,0,0,0.1);
            `;
            break;
        case 'creative':
            frameStyles = `
                border: 4px dashed #333;
                background: linear-gradient(45deg, rgba(255, 107, 107, 0.1) 0%, rgba(78, 205, 196, 0.1) 25%, rgba(69, 183, 209, 0.1) 50%, rgba(150, 206, 180, 0.1) 75%, rgba(255, 234, 167, 0.1) 100%);
                border-radius: 12px;
            `;
            break;
        default:
            frameStyles = `
                border: ${borderWidth}px ${borderStyle} ${borderColor};
            `;
    }

    // Apply shadow
    if (shadowEnabled) {
        const shadowOpacity = shadowIntensity / 100;
        frameStyles += `box-shadow: 0 8px 24px rgba(0,0,0,${shadowOpacity * 0.3});`;
    }

    // Apply background effects
    let backgroundStyles = '';
    switch (selectedBackground) {
        case 'subtle-gradient':
            backgroundStyles = 'background: linear-gradient(135deg, rgba(74, 158, 255, 0.05) 0%, rgba(102, 126, 234, 0.05) 100%);';
            break;
        case 'professional-texture':
            backgroundStyles = 'background: repeating-linear-gradient(45deg, rgba(0,0,0,0.02) 0px, rgba(0,0,0,0.02) 1px, transparent 1px, transparent 10px);';
            break;
        case 'watermark-pattern':
            backgroundStyles = 'background: radial-gradient(circle at 20% 80%, rgba(74, 158, 255, 0.03) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(102, 126, 234, 0.03) 0%, transparent 50%);';
            break;
    }

    // Apply all styles
    const finalStyles = frameStyles + backgroundStyles;
    console.log('Applying styles:', finalStyles);
    overlay.style.cssText = finalStyles;

    // Update enhancement summary
    updateEnhancementSummary();

    console.log('✅ Preview updated successfully');
}

// Reset PDF Preview - A4 Placeholder
function resetPdfPreview() {
    console.log('🔄 Resetting PDF preview to A4 placeholder');
    const previewContainer = document.getElementById('pimpingPreviewContainer');
    if (previewContainer) {
        // Create A4 placeholder with proper dimensions
        previewContainer.innerHTML = `
            <div class="a4-document-preview" style="
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                border: 3px dashed #6c757d;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                position: relative;
                overflow: hidden;
            ">
                <div class="upload-animation" style="text-align: center; z-index: 2; padding: 40px 20px;">
                    <div class="preview-icon" style="
                        font-size: 3rem;
                        margin-bottom: 20px;
                        color: #6c757d;
                        animation: float 3s ease-in-out infinite;
                    ">📄</div>
                    <h4 style="
                        color: #495057;
                        margin: 0 0 15px 0;
                        font-size: 1.2rem;
                        font-weight: 700;
                        text-align: center;
                    ">Upload PDF for A4 Preview</h4>
                    <p style="
                        color: #6c757d;
                        margin: 0 0 10px 0;
                        font-size: 0.9rem;
                        text-align: center;
                        line-height: 1.4;
                    ">Your enhanced document will appear here<br>in realistic A4 dimensions</p>
                    <p style="
                        color: #adb5bd;
                        margin: 0;
                        font-size: 0.8rem;
                        font-style: italic;
                        text-align: center;
                    ">✨ Real-time preview with professional effects</p>

                    <!-- A4 Size Indicator -->
                    <div style="
                        margin-top: 20px;
                        padding: 10px 15px;
                        background: rgba(52, 152, 219, 0.1);
                        border: 1px solid rgba(52, 152, 219, 0.3);
                        border-radius: 6px;
                        font-size: 0.75rem;
                        color: #3498db;
                        font-weight: 600;
                    ">
                        📐 A4 Format (210mm × 297mm)
                    </div>
                </div>

                <div class="background-pattern" style="
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: radial-gradient(circle at 20% 80%, rgba(108, 117, 125, 0.05) 0%, transparent 50%),
                               radial-gradient(circle at 80% 20%, rgba(108, 117, 125, 0.05) 0%, transparent 50%);
                    z-index: 1;
                "></div>
            </div>
        `;
    }

    // Hide preview controls
    const previewControls = document.getElementById('previewControls');
    if (previewControls) {
        previewControls.style.display = 'none';
    }

    // Hide download section
    const downloadSection = document.getElementById('downloadSection');
    if (downloadSection) {
        downloadSection.style.display = 'none';
    }
}

// Reset Document Pimping
function resetDocumentPimping() {
    // Reset file upload
    removePdfUpload();

    // Reset all controls
    resetEnhancements();

    // Clear stored data
    window.currentPimpingPDF = null;
}

// Reset Enhancements
function resetEnhancements() {
    // Reset frame to elegant (default)
    document.querySelectorAll('.frame-option').forEach(opt => opt.classList.remove('active'));
    const elegantOption = document.querySelector('.frame-option[data-frame="elegant"]');
    if (elegantOption) {
        elegantOption.classList.add('active');
    }

    // Reset background to none
    const noneBackground = document.querySelector('input[name="background"][value="none"]');
    if (noneBackground) {
        noneBackground.checked = true;
    }

    // Reset border controls
    const borderWidth = document.getElementById('borderWidth');
    const borderWidthValue = document.getElementById('borderWidthValue');
    const borderColor = document.getElementById('borderColor');
    const borderStyle = document.getElementById('borderStyle');

    if (borderWidth) borderWidth.value = 2;
    if (borderWidthValue) borderWidthValue.textContent = '2px';
    if (borderColor) borderColor.value = '#4a9eff';
    if (borderStyle) borderStyle.value = 'solid';

    // Reset shadow controls
    const enableShadow = document.getElementById('enableShadow');
    const shadowIntensity = document.getElementById('shadowIntensity');
    const shadowIntensityValue = document.getElementById('shadowIntensityValue');

    if (enableShadow) enableShadow.checked = true;
    if (shadowIntensity) shadowIntensity.value = 30;
    if (shadowIntensityValue) shadowIntensityValue.textContent = '30%';

    // Update preview
    updatePimpingPreview();
}

// Document Tools Functions
function showDocumentImport() {
    document.getElementById('documentImportModal').style.display = 'block';
}

function closeDocumentImport() {
    document.getElementById('documentImportModal').style.display = 'none';
    // Reset the import state
    resetDocumentImport();
}

// Document Import Variables
let importedDocument = null;
let documentPreviewCanvas = null;
let currentEditMode = null;
let placedElements = [];

// Initialize Document Import functionality
function initializeDocumentImport() {
    console.log('🔧 Initializing Document Import...');

    const fileInput = document.getElementById('documentUpload');
    const uploadArea = document.getElementById('documentUploadArea');

    console.log('📁 File input element:', fileInput);
    console.log('📤 Upload area element:', uploadArea);

    if (fileInput) {
        fileInput.addEventListener('change', handleDocumentUpload);
        console.log('✅ File input event listener added');
    } else {
        console.error('❌ File input element not found!');
    }

    if (uploadArea) {
        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('drag-over');
            console.log('📥 Drag over detected');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('drag-over');
            console.log('📤 Drag leave detected');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');
            console.log('📋 File dropped');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                console.log('📄 Processing dropped file:', files[0].name);
                handleDocumentFile(files[0]);
            }
        });

        console.log('✅ Upload area event listeners added');
    } else {
        console.error('❌ Upload area element not found!');
    }

    // Initialize tool drag functionality
    initializeToolDragFunctionality();

    // Test if preview container exists
    const previewContainer = document.getElementById('documentPreviewContainer');
    console.log('👁️ Preview container element:', previewContainer);

    console.log('✅ Document Import initialization complete');
}

// Initialize drag functionality for tools
function initializeToolDragFunctionality() {
    console.log('🛠️ Initializing tool drag functionality...');

    // Add drag event listeners to tool cards
    const toolCards = document.querySelectorAll('.draggable-tool');
    toolCards.forEach(card => {
        card.addEventListener('dragstart', handleToolDragStart);
        card.addEventListener('dragend', handleToolDragEnd);
    });

    console.log('✅ Tool drag functionality initialized for', toolCards.length, 'tools');
}

function handleDocumentUpload(event) {
    console.log('📁 File upload event triggered');
    const file = event.target.files[0];
    console.log('📄 Selected file:', file);

    if (file) {
        console.log('✅ Processing file:', file.name, 'Size:', file.size, 'Type:', file.type);
        handleDocumentFile(file);
    } else {
        console.log('❌ No file selected');
    }
}

function handleDocumentFile(file) {
    console.log('🔍 Handling document file:', file.name);
    console.log('📊 File details - Type:', file.type, 'Size:', file.size, 'bytes');

    // Validate file type
    const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/bmp'
    ];

    console.log('✅ Allowed types:', allowedTypes);
    console.log('🔍 File type check:', file.type, 'Allowed:', allowedTypes.includes(file.type));

    if (!allowedTypes.includes(file.type)) {
        console.error('❌ Unsupported file type:', file.type);
        alert('Unsupported file type. Please upload PDF, DOC, DOCX, TXT, or image files.');
        return;
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024;
    console.log('📏 Size check:', file.size, 'vs max:', maxSize);

    if (file.size > maxSize) {
        console.error('❌ File too large:', file.size, 'bytes');
        alert('File size too large. Please upload files smaller than 10MB.');
        return;
    }

    console.log('✅ File validation passed');
    importedDocument = file;

    console.log('📋 Displaying file info...');
    displayUploadedFileInfo(file);

    console.log('👁️ Loading document preview...');
    loadDocumentPreview(file);
}

function displayUploadedFileInfo(file) {
    const fileInfo = document.getElementById('uploadedFileInfo');
    const fileName = fileInfo.querySelector('.file-name');
    const fileSize = fileInfo.querySelector('.file-size');
    const uploadArea = document.getElementById('documentUploadArea');

    fileName.textContent = file.name;
    fileSize.textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB`;

    fileInfo.style.display = 'block';
    uploadArea.style.display = 'none';

    // Enable process button
    const processBtn = document.getElementById('processDocumentBtn');
    if (processBtn) {
        processBtn.disabled = false;
    }
}

function removeUploadedFile() {
    importedDocument = null;
    placedElements = [];

    const fileInfo = document.getElementById('uploadedFileInfo');
    const uploadArea = document.getElementById('documentUploadArea');
    const processBtn = document.getElementById('processDocumentBtn');
    const shareBtn = document.getElementById('shareDocumentBtn');

    fileInfo.style.display = 'none';
    uploadArea.style.display = 'block';

    if (processBtn) processBtn.disabled = true;
    if (shareBtn) shareBtn.disabled = true;

    // Clear preview
    const previewContainer = document.getElementById('documentPreviewContainer');
    if (previewContainer) {
        previewContainer.innerHTML = `
            <div class="preview-placeholder">
                <div class="preview-icon">📄</div>
                <h4>Upload a document to see preview</h4>
                <p>The document will appear here for editing</p>
            </div>
        `;
    }

    // Reset file input
    const fileInput = document.getElementById('documentUpload');
    if (fileInput) {
        fileInput.value = '';
    }
}

function loadDocumentPreview(file) {
    console.log('👁️ Loading document preview for:', file.name);
    const previewContainer = document.getElementById('documentPreviewContainer');

    if (!previewContainer) {
        console.error('❌ Preview container not found!');
        return;
    }

    console.log('✅ Preview container found:', previewContainer);
    console.log('🔍 File type analysis:', file.type);

    if (file.type.startsWith('image/')) {
        console.log('🖼️ Loading as image preview');
        loadImagePreview(file, previewContainer);
    } else if (file.type === 'application/pdf') {
        console.log('📄 Loading as PDF preview');
        loadPDFPreview(file, previewContainer);
    } else if (file.type === 'text/plain') {
        console.log('📝 Loading as text preview');
        loadTextPreview(file, previewContainer);
    } else {
        console.log('📋 Loading as generic preview');
        loadGenericPreview(file, previewContainer);
    }
}

function loadImagePreview(file, container) {
    console.log('🖼️ Starting image preview load for:', file.name);

    const reader = new FileReader();

    reader.onload = function(e) {
        console.log('📖 FileReader loaded successfully');
        console.log('🎯 Setting container HTML...');

        container.innerHTML = `
            <div class="document-preview-wrapper">
                <canvas id="documentPreviewCanvas" style="max-width: 100%; border: 1px solid #ddd; border-radius: 8px;"></canvas>
                <div class="preview-overlay" id="previewOverlay"></div>
            </div>
        `;

        console.log('✅ Container HTML set');

        const canvas = document.getElementById('documentPreviewCanvas');
        console.log('🎨 Canvas element:', canvas);

        if (!canvas) {
            console.error('❌ Canvas element not found after setting HTML!');
            return;
        }

        const ctx = canvas.getContext('2d');
        const img = new Image();

        img.onload = function() {
            console.log('🖼️ Image loaded successfully');
            console.log('📐 Original dimensions:', img.width, 'x', img.height);

            // Set canvas size to maintain aspect ratio
            const maxWidth = 600;
            const maxHeight = 800;
            let { width, height } = img;

            if (width > maxWidth) {
                height = (height * maxWidth) / width;
                width = maxWidth;
            }

            if (height > maxHeight) {
                width = (width * maxHeight) / height;
                height = maxHeight;
            }

            console.log('📐 Calculated dimensions:', width, 'x', height);

            canvas.width = width;
            canvas.height = height;

            ctx.drawImage(img, 0, 0, width, height);
            documentPreviewCanvas = canvas;

            console.log('✅ Image drawn to canvas successfully');
            console.log('🔧 Initializing preview interaction...');
            initializePreviewInteraction();
        };

        img.onerror = function() {
            console.error('❌ Failed to load image');
        };

        console.log('🔗 Setting image source...');
        img.src = e.target.result;
    };

    reader.onerror = function() {
        console.error('❌ FileReader error');
    };

    console.log('📖 Starting FileReader...');
    reader.readAsDataURL(file);
}

function loadPDFPreview(file, container) {
    console.log('📄 Loading PDF preview for:', file.name);

    // Check if PDF.js is available
    if (typeof pdfjsLib === 'undefined') {
        console.warn('⚠️ PDF.js not loaded, showing placeholder');
        container.innerHTML = `
            <div class="document-preview-wrapper">
                <div class="pdf-preview-placeholder">
                    <div class="pdf-icon">📄</div>
                    <h4>PDF Document</h4>
                    <p>${file.name}</p>
                    <p class="pdf-note">PDF.js library not loaded - showing placeholder</p>
                </div>
                <div class="preview-overlay" id="previewOverlay"></div>
            </div>
        `;
        initializePreviewInteraction();
        return;
    }

    // Show loading state
    container.innerHTML = `
        <div class="document-preview-wrapper">
            <div class="pdf-loading">
                <div class="loading-spinner">⏳</div>
                <h4>Loading PDF...</h4>
                <p>${file.name}</p>
            </div>
        </div>
    `;

    // Read file as array buffer for PDF.js
    const reader = new FileReader();

    reader.onload = function(e) {
        console.log('📖 PDF file read successfully');

        const typedArray = new Uint8Array(e.target.result);

        // Load PDF document
        pdfjsLib.getDocument(typedArray).promise.then(function(pdf) {
            console.log('📄 PDF loaded successfully, pages:', pdf.numPages);

            // Store PDF reference for zoom re-rendering
            window.currentPDFDoc = pdf;

            // Get first page for preview
            pdf.getPage(1).then(function(page) {
                console.log('📄 PDF page 1 loaded');

                // Calculate scale to fit container
                const viewport = page.getViewport({ scale: 1 });
                const maxWidth = 600;
                const maxHeight = 800;

                let scale = Math.min(maxWidth / viewport.width, maxHeight / viewport.height);
                scale = Math.min(scale, 2); // Cap at 2x scale

                const scaledViewport = page.getViewport({ scale: scale });

                console.log('📐 PDF viewport:', scaledViewport.width, 'x', scaledViewport.height);

                // Create canvas for PDF rendering
                container.innerHTML = `
                    <div class="document-preview-wrapper">
                        <div class="pdf-preview-header">
                            <h4>📄 ${file.name} (Page 1 of ${pdf.numPages})</h4>
                        </div>
                        <canvas id="documentPreviewCanvas" width="${scaledViewport.width}" height="${scaledViewport.height}" style="border: 1px solid #ddd; border-radius: 8px; max-width: 100%;"></canvas>
                        <div class="preview-overlay" id="previewOverlay"></div>
                    </div>
                `;

                const canvas = document.getElementById('documentPreviewCanvas');
                const ctx = canvas.getContext('2d');

                // Render PDF page to canvas
                const renderContext = {
                    canvasContext: ctx,
                    viewport: scaledViewport
                };

                page.render(renderContext).promise.then(function() {
                    console.log('✅ PDF page rendered successfully');
                    documentPreviewCanvas = canvas;
                    initializePreviewInteraction();
                }).catch(function(error) {
                    console.error('❌ PDF rendering error:', error);
                    showPDFError(container, file.name, 'Failed to render PDF page');
                });

            }).catch(function(error) {
                console.error('❌ PDF page loading error:', error);
                showPDFError(container, file.name, 'Failed to load PDF page');
            });

        }).catch(function(error) {
            console.error('❌ PDF loading error:', error);
            showPDFError(container, file.name, 'Failed to load PDF document');
        });
    };

    reader.onerror = function() {
        console.error('❌ File reading error');
        showPDFError(container, file.name, 'Failed to read PDF file');
    };

    console.log('📖 Reading PDF file...');
    reader.readAsArrayBuffer(file);
}

function showPDFError(container, fileName, errorMessage) {
    container.innerHTML = `
        <div class="document-preview-wrapper">
            <div class="pdf-preview-error">
                <div class="error-icon">❌</div>
                <h4>PDF Preview Error</h4>
                <p>${fileName}</p>
                <p class="error-message">${errorMessage}</p>
                <p class="error-note">You can still add signatures and elements to this document</p>
            </div>
            <div class="preview-overlay" id="previewOverlay"></div>
        </div>
    `;
    initializePreviewInteraction();
}

function loadTextPreview(file, container) {
    const reader = new FileReader();
    reader.onload = function(e) {
        const content = e.target.result;
        container.innerHTML = `
            <div class="document-preview-wrapper">
                <div class="text-preview">
                    <pre>${content.substring(0, 1000)}${content.length > 1000 ? '...' : ''}</pre>
                </div>
                <div class="preview-overlay" id="previewOverlay"></div>
            </div>
        `;

        initializePreviewInteraction();
    };
    reader.readAsText(file);
}

function loadGenericPreview(file, container) {
    container.innerHTML = `
        <div class="document-preview-wrapper">
            <div class="generic-preview-placeholder">
                <div class="doc-icon">📝</div>
                <h4>Document</h4>
                <p>${file.name}</p>
                <p class="doc-note">Document preview will be processed</p>
            </div>
            <div class="preview-overlay" id="previewOverlay"></div>
        </div>
    `;

    initializePreviewInteraction();
}

function initializePreviewInteraction() {
    const container = document.getElementById('documentPreviewContainer');
    if (!container) {
        console.error('❌ Document preview container not found for interaction setup!');
        return;
    }

    // Create or get the overlay for interactions
    let overlay = document.getElementById('previewOverlay');
    if (!overlay) {
        overlay = document.createElement('div');
        overlay.id = 'previewOverlay';
        overlay.className = 'preview-overlay';
        overlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: auto;
            z-index: 100;
            cursor: crosshair;
        `;
        container.appendChild(overlay);
        console.log('✅ Created preview overlay for interactions');
    }

    // Ensure container has proper positioning
    if (container.style.position !== 'relative' && container.style.position !== 'absolute') {
        container.style.position = 'relative';
    }

    // Remove any existing click listeners to prevent duplicates
    overlay.removeEventListener('click', handlePreviewClick);
    overlay.addEventListener('click', handlePreviewClick);

    console.log('✅ Preview interaction initialized - click handler attached to overlay');
    console.log('📊 Overlay element:', overlay);
    console.log('📊 Container element:', container);

    // Add a test click listener to verify the overlay is working
    overlay.addEventListener('mouseenter', () => {
        console.log('🖱️ Mouse entered overlay - interaction ready');
    });

    overlay.addEventListener('mouseleave', () => {
        console.log('🖱️ Mouse left overlay');
    });

    // Add drag and drop support for signatures
    overlay.addEventListener('dragover', handlePreviewDragOver);
    overlay.addEventListener('drop', handlePreviewDrop);
    overlay.addEventListener('dragleave', handlePreviewDragLeave);

    // Enable share button
    const shareBtn = document.getElementById('shareDocumentBtn');
    if (shareBtn) {
        shareBtn.disabled = false;
    }

    // Show zoom controls when document is loaded
    showPreviewZoomControls();
}

function handlePreviewClick(event) {
    console.log('🖱️ Preview clicked! Current edit mode:', currentEditMode);

    if (!currentEditMode) {
        console.log('❌ No edit mode selected - click ignored');
        return;
    }

    // Get the container and overlay for proper coordinate calculation
    const container = document.getElementById('documentPreviewContainer');
    const overlay = event.currentTarget;

    if (!container || !overlay) {
        console.error('❌ Container or overlay not found for coordinate calculation');
        return;
    }

    // Get bounding rectangles
    const containerRect = container.getBoundingClientRect();
    const overlayRect = overlay.getBoundingClientRect();

    // Calculate position relative to the container (accounting for any offset)
    const rawX = event.clientX - containerRect.left;
    const rawY = event.clientY - containerRect.top;

    // Account for container padding (1rem = 16px typically)
    const containerStyle = window.getComputedStyle(container);
    const paddingLeft = parseFloat(containerStyle.paddingLeft) || 0;
    const paddingTop = parseFloat(containerStyle.paddingTop) || 0;

    // Subtract padding from raw coordinates
    const paddingAdjustedX = rawX - paddingLeft;
    const paddingAdjustedY = rawY - paddingTop;

    // Account for zoom scaling
    const currentZoom = window.previewZoom || 100;
    const scale = currentZoom / 100;
    const x = paddingAdjustedX / scale;
    const y = paddingAdjustedY / scale;

    // Account for container scroll
    const scrollX = container.scrollLeft || 0;
    const scrollY = container.scrollTop || 0;
    const adjustedX = x + scrollX;
    const adjustedY = y + scrollY;

    console.log(`📍 Raw click: (${rawX}, ${rawY})`);
    console.log(`📍 Container padding: (${paddingLeft}, ${paddingTop})`);
    console.log(`📍 After padding adjustment: (${paddingAdjustedX}, ${paddingAdjustedY})`);
    console.log(`📍 Zoom scale: ${scale} (${currentZoom}%)`);
    console.log(`📍 Scroll offset: (${scrollX}, ${scrollY})`);
    console.log(`📍 Final position: (${adjustedX}, ${adjustedY})`);

    // Add visual debug indicator at click position
    showClickDebugIndicator(adjustedX, adjustedY, container);

    switch (currentEditMode) {
        case 'signature':
            console.log('✍️ Adding signature at position');
            addSignatureAtPosition(adjustedX, adjustedY);
            break;
        case 'text':
            console.log('📝 Adding text at position');
            addTextAtPosition(adjustedX, adjustedY);
            break;
        case 'stamp':
            console.log('🏷️ Adding stamp at position');
            addStampAtPosition(adjustedX, adjustedY);
            break;
        case 'date':
            console.log('📅 Adding date at position');
            addDateAtPosition(adjustedX, adjustedY);
            break;
        default:
            console.log('❌ Unknown edit mode:', currentEditMode);
    }
}

// Visual debug indicator to show where clicks are being registered
function showClickDebugIndicator(x, y, container) {
    // Remove any existing debug indicators
    const existingIndicators = container.querySelectorAll('.click-debug-indicator');
    existingIndicators.forEach(indicator => indicator.remove());

    // Create new debug indicator
    const indicator = document.createElement('div');
    indicator.className = 'click-debug-indicator';
    indicator.style.cssText = `
        position: absolute;
        left: ${x - 5}px;
        top: ${y - 5}px;
        width: 10px;
        height: 10px;
        background: red;
        border: 2px solid white;
        border-radius: 50%;
        z-index: 9999;
        pointer-events: none;
        box-shadow: 0 0 10px rgba(255, 0, 0, 0.8);
    `;

    container.appendChild(indicator);

    // Remove indicator after 2 seconds
    setTimeout(() => {
        if (indicator.parentNode) {
            indicator.remove();
        }
    }, 2000);

    console.log(`🎯 Debug indicator placed at (${x}, ${y})`);
}

// Tool mode functions
function enableSignatureMode() {
    console.log('🔧 Enabling signature mode...');

    // Check if signatures exist before enabling the mode
    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
    console.log(`📋 Found ${signatures.length} saved signatures`);

    if (signatures.length === 0) {
        // Show a one-time alert and don't enable the mode
        alert('No saved signatures found. Please create signatures in the Signature Tools first.');
        console.log('❌ Signature mode not enabled - no signatures available');
        return; // Exit without enabling signature mode
    }

    currentEditMode = 'signature';
    console.log('✅ Current edit mode set to:', currentEditMode);
    updateToolSelection('signature');
    showSignatureLibrary();
}

function enableTextMode() {
    console.log('🔧 Enabling text mode...');
    currentEditMode = 'text';
    console.log('✅ Current edit mode set to:', currentEditMode);
    updateToolSelection('text');
    hideSignatureLibrary();
}

function enableStampMode() {
    console.log('🔧 Enabling stamp mode...');
    currentEditMode = 'stamp';
    console.log('✅ Current edit mode set to:', currentEditMode);
    updateToolSelection('stamp');
    hideSignatureLibrary();
}

function enableDateMode() {
    console.log('🔧 Enabling date mode...');
    currentEditMode = 'date';
    console.log('✅ Current edit mode set to:', currentEditMode);
    updateToolSelection('date');
    hideSignatureLibrary();
}

// Comprehensive tool placement test function
function runToolPlacementTest() {
    console.log('🧪 STARTING COMPREHENSIVE TOOL PLACEMENT TEST...');

    // Test 1: Check if document is loaded
    const container = document.getElementById('documentPreviewContainer');
    if (!container) {
        alert('❌ Test Failed: Document preview container not found!');
        return;
    }
    console.log('✅ Test 1 passed: Document container found');

    // Test 2: Check if overlay exists
    const overlay = document.getElementById('previewOverlay');
    if (!overlay) {
        alert('❌ Test Failed: Preview overlay not found! Try importing a document first.');
        return;
    }
    console.log('✅ Test 2 passed: Preview overlay found');

    // Test 3: Check if signatures exist
    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
    console.log(`📋 Found ${signatures.length} signatures in localStorage`);

    // Test 4: Test each tool mode
    const testResults = [];

    // Test signature mode
    try {
        enableSignatureMode();
        if (currentEditMode === 'signature') {
            testResults.push('✅ Signature mode: PASS');
        } else {
            testResults.push('❌ Signature mode: FAIL');
        }
    } catch (e) {
        testResults.push('❌ Signature mode: ERROR - ' + e.message);
    }

    // Test text mode
    try {
        enableTextMode();
        if (currentEditMode === 'text') {
            testResults.push('✅ Text mode: PASS');
        } else {
            testResults.push('❌ Text mode: FAIL');
        }
    } catch (e) {
        testResults.push('❌ Text mode: ERROR - ' + e.message);
    }

    // Test stamp mode
    try {
        enableStampMode();
        if (currentEditMode === 'stamp') {
            testResults.push('✅ Stamp mode: PASS');
        } else {
            testResults.push('❌ Stamp mode: FAIL');
        }
    } catch (e) {
        testResults.push('❌ Stamp mode: ERROR - ' + e.message);
    }

    // Test date mode
    try {
        enableDateMode();
        if (currentEditMode === 'date') {
            testResults.push('✅ Date mode: PASS');
        } else {
            testResults.push('❌ Date mode: FAIL');
        }
    } catch (e) {
        testResults.push('❌ Date mode: ERROR - ' + e.message);
    }

    // Test 5: Test automatic placement
    console.log('🧪 Testing automatic element placement...');

    // Place a test signature automatically
    if (signatures.length > 0) {
        try {
            enableSignatureMode();
            addSignatureAtPosition(100, 100);
            testResults.push('✅ Auto signature placement: PASS');
        } catch (e) {
            testResults.push('❌ Auto signature placement: ERROR - ' + e.message);
        }
    }

    // Place test text
    try {
        enableTextMode();
        addElementToPreview('text', 200, 150, 'TEST TEXT');
        testResults.push('✅ Auto text placement: PASS');
    } catch (e) {
        testResults.push('❌ Auto text placement: ERROR - ' + e.message);
    }

    // Place test stamp
    try {
        enableStampMode();
        addElementToPreview('stamp', 300, 200, 'APPROVED');
        testResults.push('✅ Auto stamp placement: PASS');
    } catch (e) {
        testResults.push('❌ Auto stamp placement: ERROR - ' + e.message);
    }

    // Test 6: Test click simulation
    console.log('🧪 Testing click simulation...');
    try {
        enableTextMode();
        const overlay = document.getElementById('previewOverlay');
        if (overlay) {
            // Simulate a click event
            const clickEvent = new MouseEvent('click', {
                clientX: 150,
                clientY: 100,
                bubbles: true,
                cancelable: true
            });
            overlay.dispatchEvent(clickEvent);
            testResults.push('✅ Click simulation: PASS');
        } else {
            testResults.push('❌ Click simulation: FAIL - No overlay');
        }
    } catch (e) {
        testResults.push('❌ Click simulation: ERROR - ' + e.message);
    }

    // Display results
    const resultMessage = `🧪 TOOL PLACEMENT TEST RESULTS:\n\n${testResults.join('\n')}\n\nCheck console for detailed logs.`;
    alert(resultMessage);

    console.log('🧪 TEST COMPLETE - Results:', testResults);
    console.log('📊 Current edit mode after test:', currentEditMode);
    console.log('📊 Placed elements count:', placedElements ? placedElements.length : 'undefined');
}

function updateToolSelection(selectedTool) {
    const toolCards = document.querySelectorAll('.tool-card');
    toolCards.forEach(card => {
        card.classList.remove('selected');
    });

    const selectedCard = document.querySelector(`.tool-card[onclick*="${selectedTool}"]`);
    if (selectedCard) {
        selectedCard.classList.add('selected');
    }
}

function showSignatureLibrary() {
    const librarySection = document.getElementById('signatureLibrarySection');
    if (librarySection) {
        librarySection.style.display = 'block';
        loadSavedSignatures();
    }
}

function hideSignatureLibrary() {
    const librarySection = document.getElementById('signatureLibrarySection');
    if (librarySection) {
        librarySection.style.display = 'none';
    }
}

function loadSavedSignatures() {
    const library = document.getElementById('signatureLibrary');
    if (!library) return;

    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');

    if (signatures.length === 0) {
        library.innerHTML = '<p class="no-signatures">No saved signatures. Create some in Signature Tools first.</p>';
        return;
    }

    library.innerHTML = signatures.map((sig, index) => `
        <div class="signature-item draggable-signature"
             data-signature-index="${index}"
             draggable="true"
             onclick="selectSignature(${index})"
             title="Click to select or drag to document"
             style="display: flex; align-items: center; gap: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 8px; margin-bottom: 8px; cursor: pointer; background: white;">
            <img src="${sig.data}" alt="Signature ${index + 1}" style="max-width: 100px; max-height: 40px; pointer-events: none; border: 1px solid #eee; border-radius: 4px;">
            <span style="pointer-events: none; flex: 1; font-weight: 500;">${sig.name || `Signature ${index + 1}`}</span>
            <div class="drag-indicator" style="font-size: 12px; opacity: 0.6;">🖱️</div>
        </div>
    `).join('');

    // Add drag event listeners to signature items
    const signatureItems = library.querySelectorAll('.draggable-signature');
    signatureItems.forEach(item => {
        item.addEventListener('dragstart', handleSignatureDragStart);
        item.addEventListener('dragend', handleSignatureDragEnd);

        // Add hover effects
        item.addEventListener('mouseenter', function() {
            this.style.background = '#f8f9fa';
            this.style.borderColor = '#007bff';
        });

        item.addEventListener('mouseleave', function() {
            this.style.background = 'white';
            this.style.borderColor = '#ddd';
        });
    });

    console.log(`✅ Loaded ${signatures.length} signatures into library`);
}

// Drag and drop variables
let draggedSignatureData = null;
let draggedSignatureIndex = null;
let draggedToolType = null;

// Signature drag handlers
function handleSignatureDragStart(e) {
    const signatureIndex = parseInt(e.target.dataset.signatureIndex);
    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');

    if (signatures[signatureIndex]) {
        draggedSignatureData = signatures[signatureIndex].data;
        draggedSignatureIndex = signatureIndex;

        console.log('🖱️ Started dragging signature:', signatureIndex);

        // Add visual feedback
        e.target.style.opacity = '0.5';

        // Set drag data
        e.dataTransfer.setData('text/plain', 'signature');
        e.dataTransfer.effectAllowed = 'copy';
    }
}

function handleSignatureDragEnd(e) {
    console.log('🖱️ Signature drag ended');
    e.target.style.opacity = '1';
    draggedSignatureData = null;
    draggedSignatureIndex = null;
}

// Tool drag handlers
function handleToolDragStart(e) {
    const toolType = e.target.dataset.toolType;
    draggedToolType = toolType;

    console.log('🛠️ Started dragging tool:', toolType);

    // Add visual feedback
    e.target.style.opacity = '0.5';

    // Set drag data
    e.dataTransfer.setData('text/plain', toolType);
    e.dataTransfer.effectAllowed = 'copy';
}

function handleToolDragEnd(e) {
    console.log('🛠️ Tool drag ended');
    e.target.style.opacity = '1';
    draggedToolType = null;
}

// Preview area drag handlers
function handlePreviewDragOver(e) {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';

    // Add visual feedback
    const overlay = e.currentTarget;
    overlay.style.background = 'rgba(0, 123, 255, 0.1)';
    overlay.style.border = '2px dashed #007bff';
}

function handlePreviewDragLeave(e) {
    // Remove visual feedback
    const overlay = e.currentTarget;
    overlay.style.background = 'transparent';
    overlay.style.border = 'none';
}

function handlePreviewDrop(e) {
    e.preventDefault();
    console.log('📍 Item dropped on preview');

    // Remove visual feedback
    const overlay = e.currentTarget;
    overlay.style.background = 'transparent';
    overlay.style.border = 'none';

    // Calculate drop position relative to the preview container
    const rect = overlay.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    console.log('📍 Drop position:', x, y);

    if (draggedSignatureData) {
        console.log('✍️ Adding signature at drop position');
        // Add signature at drop position
        addElementToPreview('signature', x, y, draggedSignatureData);

        // Reset drag data
        draggedSignatureData = null;
        draggedSignatureIndex = null;
    } else if (draggedToolType) {
        console.log('🛠️ Adding tool element at drop position:', draggedToolType);

        // Handle different tool types
        switch (draggedToolType) {
            case 'text':
                const text = prompt('Enter text to add:') || 'Sample Text';
                addElementToPreview('text', x, y, text);
                break;
            case 'stamp':
                const stamps = ['APPROVED', 'REJECTED', 'CONFIDENTIAL', 'URGENT', 'RECEIVED', 'DRAFT', 'FINAL'];
                const stamp = stamps[Math.floor(Math.random() * stamps.length)];
                addElementToPreview('stamp', x, y, stamp);
                break;
            case 'date':
                const date = new Date().toLocaleDateString();
                addElementToPreview('date', x, y, date);
                break;
            default:
                console.log('❌ Unknown tool type:', draggedToolType);
        }

        // Reset drag data
        draggedToolType = null;
    }
}

// Element placement functions
function addSignatureAtPosition(x, y) {
    // Check if a signature is selected
    if (window.selectedImportSignature) {
        console.log('✅ Using selected signature:', window.selectedImportSignature.name);
        addElementToPreview('signature', x, y, window.selectedImportSignature.data);
        return;
    }

    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
    if (signatures.length === 0) {
        // Don't show alert here since it was already shown in enableSignatureMode
        console.log('❌ No signatures available - signature mode should not be active');
        // Reset the mode to prevent further clicks
        currentEditMode = null;
        updateToolSelection(null);
        hideSignatureLibrary();
        return;
    }

    // If no signature selected, show selection dialog
    showSignatureSelectionDialog(x, y);
}

function showSignatureSelectionDialog(x, y) {
    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');

    let dialogHTML = `
        <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.3); max-width: 400px;">
            <h3 style="margin: 0 0 15px 0; color: #2c3e50;">Select Signature</h3>
            <div style="max-height: 300px; overflow-y: auto;">
    `;

    signatures.forEach((sig, index) => {
        dialogHTML += `
            <div onclick="useSignatureAtPosition(${index}, ${x}, ${y})"
                 style="display: flex; align-items: center; gap: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 8px; margin-bottom: 10px; cursor: pointer; transition: all 0.3s ease;"
                 onmouseover="this.style.background='#f8f9fa'"
                 onmouseout="this.style.background='white'">
                <img src="${sig.data}" style="max-width: 80px; max-height: 30px; border: 1px solid #ddd; border-radius: 4px;">
                <span style="font-weight: 600; color: #2c3e50;">${sig.name}</span>
            </div>
        `;
    });

    dialogHTML += `
            </div>
            <div style="margin-top: 15px; text-align: center;">
                <button onclick="closeSignatureDialog()" style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 6px; cursor: pointer;">Cancel</button>
            </div>
        </div>
    `;

    // Create modal overlay
    const overlay = document.createElement('div');
    overlay.id = 'signatureSelectionOverlay';
    overlay.style.cssText = `
        position: fixed; top: 0; left: 0; right: 0; bottom: 0;
        background: rgba(0,0,0,0.5); z-index: 10000;
        display: flex; align-items: center; justify-content: center;
    `;
    overlay.innerHTML = dialogHTML;

    document.body.appendChild(overlay);
}

function useSignatureAtPosition(signatureIndex, x, y) {
    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
    const signature = signatures[signatureIndex];

    if (signature) {
        addElementToPreview('signature', x, y, signature.data);
        closeSignatureDialog();
    }
}

function closeSignatureDialog() {
    const overlay = document.getElementById('signatureSelectionOverlay');
    if (overlay) {
        document.body.removeChild(overlay);
    }
}

function addTextAtPosition(x, y) {
    const text = prompt('Enter text to add:');
    if (text) {
        addElementToPreview('text', x, y, text);
    }
}

function addStampAtPosition(x, y) {
    const stamps = ['APPROVED', 'REJECTED', 'CONFIDENTIAL', 'URGENT', 'RECEIVED'];
    const stamp = stamps[Math.floor(Math.random() * stamps.length)];
    addElementToPreview('stamp', x, y, stamp);
}

function addDateAtPosition(x, y) {
    const date = new Date().toLocaleDateString();
    addElementToPreview('date', x, y, date);
}

function addElementToPreview(type, x, y, data) {
    const element = {
        id: Date.now(),
        type: type,
        x: x,
        y: y,
        data: data
    };

    placedElements.push(element);
    renderElementOnPreview(element);
}

function renderElementOnPreview(element) {
    // Get the existing overlay (should already exist from initializePreviewInteraction)
    let overlay = document.getElementById('previewOverlay');
    if (!overlay) {
        console.error('❌ Preview overlay not found! Make sure initializePreviewInteraction was called.');
        return;
    }

    const elementDiv = document.createElement('div');
    elementDiv.className = `placed-element ${element.type}-element`;
    elementDiv.id = `element-${element.id}`;

    // Enhanced styling for immediate visibility and proper interaction
    elementDiv.style.cssText = `
        position: absolute;
        left: ${element.x}px;
        top: ${element.y}px;
        cursor: grab;
        border: 2px solid #007bff;
        padding: 4px;
        background: rgba(255,255,255,0.95);
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        z-index: 1000;
        user-select: none;
        pointer-events: auto;
        min-width: 20px;
        min-height: 20px;
    `;

    // Create content based on element type
    if (element.type === 'signature') {
        elementDiv.innerHTML = `<img src="${element.data}" style="max-width: 120px; max-height: 60px; display: block;" draggable="false">`;
    } else if (element.type === 'text') {
        elementDiv.innerHTML = `<span style="font-size: 16px; color: #333; font-weight: 500; white-space: nowrap;">${element.data}</span>`;
    } else if (element.type === 'stamp') {
        elementDiv.innerHTML = `<span style="font-size: 14px; color: #d63384; font-weight: bold; text-transform: uppercase; border: 2px solid #d63384; padding: 4px 8px; border-radius: 4px;">${element.data}</span>`;
    } else if (element.type === 'date') {
        elementDiv.innerHTML = `<span style="font-size: 14px; color: #198754; font-weight: 500;">${element.data}</span>`;
    } else {
        elementDiv.innerHTML = `<span style="font-size: 14px; color: #333;">${element.data}</span>`;
    }

    // Add interaction events
    elementDiv.addEventListener('dblclick', (e) => {
        e.stopPropagation();
        removeElement(element.id);
    });

    // Make element independently draggable
    makeElementIndependentlyDraggable(elementDiv, element);

    // Add to overlay and ensure it's visible
    overlay.appendChild(elementDiv);

    // Force a repaint to ensure visibility
    elementDiv.offsetHeight;

    console.log(`✅ Element rendered and made draggable: ${element.type} at (${element.x}, ${element.y})`);
}

function makeElementIndependentlyDraggable(elementDiv, element) {
    let isDragging = false;
    let startX = 0;
    let startY = 0;
    let initialLeft = 0;
    let initialTop = 0;

    // Mouse/touch start
    function startDrag(e) {
        e.preventDefault();
        e.stopPropagation();

        isDragging = true;
        elementDiv.style.cursor = 'grabbing';
        elementDiv.style.zIndex = '1001'; // Bring to front while dragging

        // Get initial positions
        const rect = elementDiv.getBoundingClientRect();
        const containerRect = document.getElementById('documentPreviewContainer').getBoundingClientRect();

        startX = (e.clientX || e.touches[0].clientX);
        startY = (e.clientY || e.touches[0].clientY);
        initialLeft = rect.left - containerRect.left;
        initialTop = rect.top - containerRect.top;

        // Add global event listeners
        document.addEventListener('mousemove', dragMove);
        document.addEventListener('mouseup', endDrag);
        document.addEventListener('touchmove', dragMove, { passive: false });
        document.addEventListener('touchend', endDrag);

        console.log(`🖱️ Started dragging ${element.type} element`);
    }

    function dragMove(e) {
        if (!isDragging) return;

        e.preventDefault();
        e.stopPropagation();

        const currentX = (e.clientX || e.touches[0].clientX);
        const currentY = (e.clientY || e.touches[0].clientY);

        const deltaX = currentX - startX;
        const deltaY = currentY - startY;

        const newLeft = initialLeft + deltaX;
        const newTop = initialTop + deltaY;

        // Apply zoom scaling if needed
        const scale = previewZoom / 100;
        const adjustedLeft = newLeft / scale;
        const adjustedTop = newTop / scale;

        // Update element position
        elementDiv.style.left = adjustedLeft + 'px';
        elementDiv.style.top = adjustedTop + 'px';

        // Update element data
        element.x = adjustedLeft;
        element.y = adjustedTop;
    }

    function endDrag(e) {
        if (!isDragging) return;

        isDragging = false;
        elementDiv.style.cursor = 'grab';
        elementDiv.style.zIndex = '1000'; // Reset z-index

        // Remove global event listeners
        document.removeEventListener('mousemove', dragMove);
        document.removeEventListener('mouseup', endDrag);
        document.removeEventListener('touchmove', dragMove);
        document.removeEventListener('touchend', endDrag);

        console.log(`✅ Finished dragging ${element.type} element to (${element.x}, ${element.y})`);
    }

    // Add event listeners to the element
    elementDiv.addEventListener('mousedown', startDrag);
    elementDiv.addEventListener('touchstart', startDrag, { passive: false });

    // Prevent default drag behavior
    elementDiv.addEventListener('dragstart', (e) => e.preventDefault());
    elementDiv.addEventListener('selectstart', (e) => e.preventDefault());
}

function removeElement(elementId) {
    placedElements = placedElements.filter(el => el.id !== elementId);
    const elementDiv = document.getElementById(`element-${elementId}`);
    if (elementDiv) {
        elementDiv.remove();
    }
}

function processDocument() {
    if (!importedDocument) {
        alert('Please upload a document first.');
        return;
    }

    // Create processed document with placed elements
    const processedData = {
        originalFile: importedDocument.name,
        elements: placedElements,
        processedAt: new Date().toISOString()
    };

    // In a real implementation, this would process the document
    console.log('Processing document:', processedData);

    alert(`Document processed successfully!\n\nOriginal: ${importedDocument.name}\nElements added: ${placedElements.length}\n\nDocument is ready for sharing.`);
}

function createTestDocument() {
    console.log('🧪 Creating test document...');

    // Create a test document for demonstration
    const testContent = `
        TEST DOCUMENT

        This is a sample document for testing the Import Document functionality.

        You can add signatures, text, stamps, and dates to this document.

        Features:
        • Upload various file formats
        • Add digital signatures
        • Insert text annotations
        • Apply stamps and dates
        • Share processed documents

        Created: ${new Date().toLocaleDateString()}
    `;

    const blob = new Blob([testContent], { type: 'text/plain' });
    const file = new File([blob], 'test-document.txt', { type: 'text/plain' });

    console.log('📄 Test file created:', file);
    handleDocumentFile(file);
}

function testImageUpload() {
    console.log('🖼️ Creating test image...');

    // Create a simple test image using canvas
    const canvas = document.createElement('canvas');
    canvas.width = 400;
    canvas.height = 300;
    const ctx = canvas.getContext('2d');

    // Draw a simple test image
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, 400, 300);

    ctx.fillStyle = '#333';
    ctx.font = '24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('TEST IMAGE', 200, 100);
    ctx.fillText('For Import Document', 200, 140);
    ctx.fillText('Preview Testing', 200, 180);

    // Add some visual elements
    ctx.strokeStyle = '#007bff';
    ctx.lineWidth = 3;
    ctx.strokeRect(50, 50, 300, 200);

    ctx.fillStyle = '#28a745';
    ctx.fillRect(150, 220, 100, 30);

    ctx.fillStyle = 'white';
    ctx.font = '14px Arial';
    ctx.fillText('Sample Content', 200, 240);

    // Convert canvas to blob and create file
    canvas.toBlob(function(blob) {
        const file = new File([blob], 'test-image.png', { type: 'image/png' });
        console.log('🖼️ Test image file created:', file);
        handleDocumentFile(file);
    }, 'image/png');
}

function testPDFUpload() {
    console.log('📄 Creating test PDF...');

    // Check if jsPDF is available
    if (typeof window.jspdf === 'undefined') {
        // Load jsPDF dynamically
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
        script.onload = () => {
            createTestPDF();
        };
        script.onerror = () => {
            alert('Failed to load PDF library. Please try again.');
        };
        document.head.appendChild(script);
    } else {
        createTestPDF();
    }
}

function createTestPDF() {
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Make PDF safe to prevent invalid argument errors
    makePDFSafe(doc);

    // Add content to PDF
    doc.setFontSize(24);
    doc.setFont('helvetica', 'bold');
    doc.text('TEST PDF DOCUMENT', 20, 30);

    doc.setFontSize(16);
    doc.setFont('helvetica', 'normal');
    doc.text('Import Document Preview Test', 20, 50);

    doc.setFontSize(12);
    doc.text('This is a sample PDF document for testing the Import Document functionality.', 20, 80);
    doc.text('You can add signatures, text, stamps, and dates to this document.', 20, 100);

    // Add some visual elements
    doc.setDrawColor(0, 123, 255);
    doc.setLineWidth(2);
    doc.rect(20, 120, 170, 80);

    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('Features:', 30, 140);

    doc.setFontSize(11);
    doc.setFont('helvetica', 'normal');
    doc.text('• PDF preview with PDF.js', 30, 155);
    doc.text('• Add digital signatures', 30, 170);
    doc.text('• Insert text annotations', 30, 185);
    doc.text('• Apply stamps and dates', 30, 200);

    // Add signature areas
    doc.setDrawColor(40, 167, 69);
    doc.setFillColor(232, 245, 232);
    doc.rect(20, 220, 80, 30, 'FD');
    doc.rect(110, 220, 80, 30, 'FD');

    doc.setFontSize(10);
    doc.setTextColor(40, 167, 69);
    doc.text('Signature Area 1', 35, 240);
    doc.text('Signature Area 2', 125, 240);

    // Add footer
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text(`Created: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`, 20, 280);

    // Convert to blob and create file
    const pdfBlob = doc.output('blob');
    const file = new File([pdfBlob], 'test-document.pdf', { type: 'application/pdf' });

    console.log('📄 Test PDF file created:', file);
    handleDocumentFile(file);
}

function resetDocumentImport() {
    importedDocument = null;
    documentPreviewCanvas = null;
    currentEditMode = null;
    placedElements = [];

    // Reset UI elements
    removeUploadedFile();
    hideSignatureLibrary();
    hidePreviewZoomControls();

    // Reset zoom level
    previewZoom = 100;

    const toolCards = document.querySelectorAll('.tool-card');
    toolCards.forEach(card => card.classList.remove('selected'));
}

// Document Sharing Functions
function shareDocument() {
    if (!importedDocument) {
        alert('Please upload and process a document first.');
        return;
    }

    document.getElementById('documentSharingModal').style.display = 'block';
    updateSharingPreview();
}

function closeDocumentSharing() {
    document.getElementById('documentSharingModal').style.display = 'none';
}

function updateSharingPreview() {
    const previewContainer = document.getElementById('sharingDocumentPreview');
    if (!previewContainer) return;

    previewContainer.innerHTML = `
        <div class="sharing-preview-content">
            <div class="document-info">
                <h4>📄 ${importedDocument.name}</h4>
                <p>Elements added: ${placedElements.length}</p>
                <p>Ready for sharing</p>
            </div>
        </div>
    `;
}

function shareViaEmail() {
    const email = document.getElementById('recipientEmail').value;
    const subject = document.getElementById('emailSubject').value || `Document from ${getActiveCompany().name}`;
    const message = document.getElementById('emailMessage').value || 'Please find the attached document.';

    if (!email) {
        alert('Please enter recipient email address.');
        return;
    }

    // Create mailto link
    const mailtoLink = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(message)}`;
    window.open(mailtoLink);

    alert('Email client opened. Please attach the document manually.');
}

function shareViaWhatsApp() {
    const number = document.getElementById('whatsappNumber').value;
    const message = document.getElementById('whatsappMessage').value || 'Hi! Please find the attached document.';

    if (!number) {
        alert('Please enter WhatsApp number.');
        return;
    }

    const whatsappUrl = `https://wa.me/${number.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

function shareViaWhatsAppWeb() {
    const message = document.getElementById('whatsappMessage').value || 'Hi! Please find the attached document.';
    const whatsappWebUrl = `https://web.whatsapp.com/send?text=${encodeURIComponent(message)}`;
    window.open(whatsappWebUrl, '_blank');
}

function copyDocumentLink() {
    // Generate a shareable link (in real implementation, this would be a proper URL)
    const link = `${window.location.origin}/shared-document/${Date.now()}`;

    navigator.clipboard.writeText(link).then(() => {
        alert('Document link copied to clipboard!');
    }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = link;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('Document link copied to clipboard!');
    });
}

function shareViaSMS() {
    const message = 'Please check the attached document.';
    const smsUrl = `sms:?body=${encodeURIComponent(message)}`;
    window.open(smsUrl);
}

function shareViaLinkedIn() {
    const message = 'Sharing a document via DocuGen Pro';
    const linkedinUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}&summary=${encodeURIComponent(message)}`;
    window.open(linkedinUrl, '_blank');
}

function shareViaTelegram() {
    const message = 'Please check the attached document.';
    const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(window.location.href)}&text=${encodeURIComponent(message)}`;
    window.open(telegramUrl, '_blank');
}

// Centralized Signature Management System
function initializeSignatureIntegration() {
    console.log('🔧 Initializing signature integration across all generators...');

    // Load signatures into all signature selectors
    loadSignaturesIntoSelectors();

    // Initialize signature preview functionality
    initializeSignaturePreviews();

    console.log('✅ Signature integration initialized');
}

function loadSignaturesIntoSelectors() {
    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
    console.log('📝 Loading', signatures.length, 'signatures into selectors');

    // Find all signature selector elements across the application
    const selectors = [
        'artistSignatureSelect',
        'clientSignatureSelect',
        'signatureSelect',
        'invoiceSignatureSelect',
        'receiptSignatureSelect',
        'quotationSignatureSelect',
        'contractSignatureSelect',
        'riderSignatureSelect',
        'annexureSignatureSelect'
    ];

    selectors.forEach(selectorId => {
        const selector = document.getElementById(selectorId);
        if (selector) {
            // Clear existing options except the first one
            while (selector.children.length > 1) {
                selector.removeChild(selector.lastChild);
            }

            // Add signature options
            signatures.forEach(signature => {
                const option = document.createElement('option');
                option.value = signature.id;
                option.textContent = signature.name;
                selector.appendChild(option);
            });

            console.log('✅ Loaded signatures into', selectorId);
        }
    });
}

function initializeSignaturePreviews() {
    // Add event listeners for signature preview functionality
    const previewElements = [
        { selectId: 'artistSignatureSelect', previewId: 'artistSignaturePreview', imgId: 'artistSignatureImg' },
        { selectId: 'clientSignatureSelect', previewId: 'clientSignaturePreview', imgId: 'clientSignatureImg' },
        { selectId: 'signatureSelect', previewId: 'signaturePreview', imgId: 'signatureImg' }
    ];

    previewElements.forEach(element => {
        const selector = document.getElementById(element.selectId);
        if (selector) {
            selector.addEventListener('change', () => {
                previewSelectedSignature(element.selectId, element.previewId, element.imgId);
            });
        }
    });
}

function previewSelectedSignature(selectId, previewId, imgId) {
    const selector = document.getElementById(selectId);
    const preview = document.getElementById(previewId);
    const img = document.getElementById(imgId);

    if (!selector || !preview || !img) return;

    const signatureId = selector.value;

    if (!signatureId) {
        preview.style.display = 'none';
        return;
    }

    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
    const signature = signatures.find(s => s.id == signatureId);

    if (signature) {
        img.src = signature.data;
        img.alt = signature.name;
        preview.style.display = 'block';
        console.log('👁️ Previewing signature:', signature.name);
    }
}

function getSelectedSignature(selectId) {
    const selector = document.getElementById(selectId);
    if (!selector || !selector.value) return null;

    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
    return signatures.find(s => s.id == selector.value);
}

function addSignatureToDocument(signatureId, x, y, width = 150, height = 75) {
    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
    const signature = signatures.find(s => s.id == signatureId);

    if (!signature) {
        console.error('❌ Signature not found:', signatureId);
        return false;
    }

    // Add signature to current document context
    const signatureElement = {
        id: Date.now(),
        signatureId: signatureId,
        data: signature.data,
        name: signature.name,
        type: 'signature',
        x: x || 100,
        y: y || 400,
        width: width,
        height: height
    };

    console.log('✅ Adding signature to document:', signature.name);
    return signatureElement;
}

// Enhanced signature selection for Import Document
function selectSignature(index) {
    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
    if (signatures[index]) {
        const signature = signatures[index];
        console.log('✅ Selected signature for import:', signature.name);

        // Store selected signature for click placement
        window.selectedImportSignature = signature;

        // Update UI to show selection
        const signatureItems = document.querySelectorAll('.signature-item');
        signatureItems.forEach((item, i) => {
            if (i === index) {
                item.style.background = '#e8f5e8';
                item.style.border = '2px solid #28a745';
            } else {
                item.style.background = 'transparent';
                item.style.border = 'none';
            }
        });

        alert(`✅ Signature "${signature.name}" selected!\n\nClick on the document to place it, or drag it directly.`);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeDocumentImport();
    initializeSignatureIntegration();

    // Add test signature if none exist (for testing purposes)
    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
    if (signatures.length === 0) {
        console.log('🧪 Adding test signature for demonstration');
        const testSignature = {
            id: Date.now(),
            name: 'Test Signature',
            data: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjgwIiB2aWV3Qm94PSIwIDAgMjAwIDgwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxwYXRoIGQ9Ik0xMCA0MEM0MCAyMCA4MCA2MCA5MCAzMCIgc3Ryb2tlPSIjMDAwIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz48dGV4dCB4PSIxMDAiIHk9IjUwIiBmb250LWZhbWlseT0iY3Vyc2l2ZSIgZm9udC1zaXplPSIyNCIgZmlsbD0iIzAwMCI+Sm9obiBEb2U8L3RleHQ+PC9zdmc+',
            type: 'test',
            created: new Date().toISOString()
        };
        localStorage.setItem('savedSignatures', JSON.stringify([testSignature]));
        console.log('✅ Test signature added to localStorage');
    }
});

function showSignatureTools() {
    document.getElementById('signatureToolsModal').style.display = 'block';
    initializeSignatureTools();

    // Clear any existing signature to start fresh
    setTimeout(() => {
        clearSignature();
        console.log('🎨 Signature tools opened - Ready to create new signature');
    }, 100);
}

function closeSignatureTools() {
    document.getElementById('signatureToolsModal').style.display = 'none';
}

function initializeSignatureToolsLater() {
    // This will be called when the modal is actually opened
    console.log('Signature tools initialization prepared');
}

function initializeSignatureTools() {
    console.log('=== INITIALIZING SIGNATURE TOOLS ===');

    // Initialize signature canvas
    initializeSignatureCanvas();

    // Initialize style controls
    initializeSignatureStyleControls();

    // Initialize text signature
    initializeTextSignature();

    // Initialize signature upload
    initializeSignatureUpload();

    // Load saved signatures
    loadSavedSignatures();

    console.log('✅ Signature tools fully initialized');
}

function initializeSignatureStyleControls() {
    const colorInput = document.getElementById('signatureColor');
    const thicknessInput = document.getElementById('signatureThickness');
    const smoothingInput = document.getElementById('signatureSmoothing');
    const thicknessValue = document.getElementById('thicknessValue');

    // Set default values
    if (colorInput) colorInput.value = '#000000';
    if (thicknessInput) thicknessInput.value = '2';
    if (smoothingInput) smoothingInput.checked = true;
    if (thicknessValue) thicknessValue.textContent = '2px';

    // Initialize style object
    window.signatureStyle = {
        color: '#000000',
        thickness: 2,
        smoothing: true
    };

    console.log('✅ Signature style controls initialized');
}

function initializeSignatureCanvas() {
    const canvas = document.getElementById('signatureCanvas');
    if (!canvas) {
        console.error('Signature canvas not found!');
        return;
    }

    const ctx = canvas.getContext('2d');

    // Create a new signature session
    signatureSession = {
        isDrawing: false,
        lastX: 0,
        lastY: 0,
        lastVelocity: 0,
        lastTime: 0,
        currentStroke: [],
        allStrokes: [], // Fresh array for this session
        sessionId: Date.now() // Unique session identifier
    };

    console.log('=== INITIALIZING NEW SIGNATURE SESSION ===');
    console.log('Session ID:', signatureSession.sessionId);

    // Set canvas background to white with signature line
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Add professional signature line
    drawSignatureLine();

    function drawSignatureLine() {
        ctx.save();
        ctx.beginPath();
        ctx.moveTo(50, canvas.height - 30);
        ctx.lineTo(canvas.width - 50, canvas.height - 30);
        ctx.strokeStyle = '#cccccc';
        ctx.lineWidth = 1;
        ctx.setLineDash([5, 5]);
        ctx.stroke();

        // Add "Sign here" text
        ctx.fillStyle = '#999999';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Sign here', canvas.width / 2, canvas.height - 10);
        ctx.restore();
    }

    function drawStroke(points) {
        if (points.length < 2) return;

        ctx.save();
        ctx.globalCompositeOperation = 'source-over';

        // Use style controls or defaults
        const style = window.signatureStyle || { color: '#000000', thickness: 2, smoothing: true };
        ctx.strokeStyle = style.color;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';

        if (style.smoothing && points.length >= 3) {
            // Smooth curve drawing
            ctx.beginPath();
            ctx.moveTo(points[0].x, points[0].y);

            for (let i = 1; i < points.length - 2; i++) {
                const point = points[i];
                const nextPoint = points[i + 1];

                // Calculate control points for smooth curves
                const xc = (point.x + nextPoint.x) / 2;
                const yc = (point.y + nextPoint.y) / 2;

                // Variable line width based on velocity and user setting
                const velocity = point.v || 1;
                const baseThickness = style.thickness;
                const lineWidth = Math.max(0.5, Math.min(baseThickness * 2, baseThickness + (2 - velocity * 1.5)));
                ctx.lineWidth = lineWidth;

                ctx.quadraticCurveTo(point.x, point.y, xc, yc);
                ctx.stroke();

                ctx.beginPath();
                ctx.moveTo(xc, yc);
            }

            // Draw the last segment
            if (points.length >= 2) {
                const lastPoint = points[points.length - 2];
                const finalPoint = points[points.length - 1];
                ctx.lineWidth = style.thickness;
                ctx.quadraticCurveTo(lastPoint.x, lastPoint.y, finalPoint.x, finalPoint.y);
                ctx.stroke();
            }
        } else {
            // Simple line drawing (no smoothing) or short strokes
            ctx.lineWidth = style.thickness;
            ctx.beginPath();
            ctx.moveTo(points[0].x, points[0].y);

            for (let i = 1; i < points.length; i++) {
                ctx.lineTo(points[i].x, points[i].y);
            }
            ctx.stroke();
        }

        ctx.restore();
    }

    function redrawAllStrokes() {
        // Clear canvas and redraw background
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        drawSignatureLine();

        // Redraw all completed strokes from current session
        signatureSession.allStrokes.forEach(stroke => {
            drawStroke(stroke);
        });

        // Draw current stroke if in progress
        if (signatureSession.isDrawing && signatureSession.currentStroke.length > 0) {
            drawStroke(signatureSession.currentStroke);
        }
    }

    function startDrawing(e) {
        signatureSession.isDrawing = true;

        if (!canvas || typeof canvas.getBoundingClientRect !== 'function') {
            console.warn('❌ Signature canvas not found or invalid in startDrawing');
            return;
        }
        const rect = canvas.getBoundingClientRect();
        signatureSession.lastX = e.clientX - rect.left;
        signatureSession.lastY = e.clientY - rect.top;
        signatureSession.lastTime = Date.now();
        signatureSession.lastVelocity = 0;

        // Start new stroke in current session
        signatureSession.currentStroke = [{ x: signatureSession.lastX, y: signatureSession.lastY, v: 1 }];

        // Add visual feedback
        canvas.classList.add('drawing');

        console.log('Started new stroke at:', signatureSession.lastX, signatureSession.lastY, 'Session:', signatureSession.sessionId);
    }

    function draw(e) {
        if (!signatureSession.isDrawing) return;


        if (!canvas || typeof canvas.getBoundingClientRect !== 'function') {
            console.warn('❌ Signature canvas not found or invalid in draw');
            return;
        }
        const rect = canvas.getBoundingClientRect();
        const currentX = e.clientX - rect.left;
        const currentY = e.clientY - rect.top;
        const currentTime = Date.now();

        // Calculate velocity for pressure simulation
        const distance = Math.sqrt(Math.pow(currentX - signatureSession.lastX, 2) + Math.pow(currentY - signatureSession.lastY, 2));
        const timeDelta = currentTime - signatureSession.lastTime;
        const velocity = timeDelta > 0 ? distance / timeDelta : 0;

        // Smooth velocity changes
        const smoothedVelocity = signatureSession.lastVelocity * 0.7 + velocity * 0.3;

        // Add point to current stroke in session
        signatureSession.currentStroke.push({
            x: currentX,
            y: currentY,
            v: Math.min(smoothedVelocity, 2) // Cap velocity for consistent lines
        });

        // Redraw all strokes including current one
        redrawAllStrokes();

        signatureSession.lastX = currentX;
        signatureSession.lastY = currentY;
        signatureSession.lastTime = currentTime;
        signatureSession.lastVelocity = smoothedVelocity;
    }

    function stopDrawing() {
        if (!signatureSession.isDrawing) return;
        signatureSession.isDrawing = false;

        // Remove visual feedback
        canvas.classList.remove('drawing');

        // Save completed stroke to session's allStrokes array
        if (signatureSession.currentStroke.length > 1) {
            signatureSession.allStrokes.push([...signatureSession.currentStroke]); // Create a copy of the stroke
            console.log('Completed stroke with', signatureSession.currentStroke.length, 'points. Total strokes in session:', signatureSession.allStrokes.length);
        }

        // Clear current stroke
        signatureSession.currentStroke = [];

        // Final redraw to ensure everything is rendered properly
        redrawAllStrokes();
    }

    // Enhanced mouse events
    canvas.addEventListener('mousedown', startDrawing);
    canvas.addEventListener('mousemove', draw);
    canvas.addEventListener('mouseup', stopDrawing);
    canvas.addEventListener('mouseout', stopDrawing);

    // Enhanced touch events for mobile with pressure simulation
    canvas.addEventListener('touchstart', (e) => {
        e.preventDefault();
        const touch = e.touches[0];

        if (!canvas || typeof canvas.getBoundingClientRect !== 'function') {
            console.warn('❌ Signature canvas not found or invalid in touchstart');
            return;
        }
        const rect = canvas.getBoundingClientRect();

        // Simulate mouse event with touch coordinates
        const mouseEvent = new MouseEvent('mousedown', {
            clientX: touch.clientX,
            clientY: touch.clientY
        });
        canvas.dispatchEvent(mouseEvent);
    });

    canvas.addEventListener('touchmove', (e) => {
        e.preventDefault();
        const touch = e.touches[0];

        // Simulate pressure based on touch force (if available)
        const force = touch.force || 1;
        const mouseEvent = new MouseEvent('mousemove', {
            clientX: touch.clientX,
            clientY: touch.clientY
        });

        canvas.dispatchEvent(mouseEvent);
    });

    canvas.addEventListener('touchend', (e) => {
        e.preventDefault();
        const mouseEvent = new MouseEvent('mouseup', {});
        canvas.dispatchEvent(mouseEvent);
    });

    // Store canvas reference and session-based drawing state
    window.signatureCanvas = canvas;
    window.currentSignatureSession = signatureSession;
    window.redrawAllStrokes = redrawAllStrokes;

    console.log('✅ Advanced signature canvas initialized - Session ready for new signature');
}



function showDocumentEditor() {
    alert('Document Editor - Coming soon!');
}

function showPDFTools() {
    alert('PDF Tools - Coming soon!');
}

function showTemplates() {
    alert('Template Manager - Coming soon!');
}

function showClients() {
    console.log('🏢 Opening Client Profiles Management...');
    const modal = document.getElementById('clientProfilesModal');
    if (modal) {
        modal.style.display = 'block';
        loadClientProfiles();
        hideClientForm(); // Start with list view
    } else {
        console.error('❌ Client Profiles modal not found');
    }
}

function showSharingAnalytics() {
    alert('Sharing Analytics - Coming soon!');
}

function showDocumentHistory() {
    alert('Document History - Coming soon!');
}

// This function is now implemented below in the Company Management System section

function viewAllDocuments() {
    alert('Document Library - Coming soon!');
}

// ===== CLIENT PROFILES MANAGEMENT SYSTEM =====

// Global client management state
let currentEditingClient = null;
let clientProfiles = {};

// Initialize client profiles from localStorage
function initializeClientProfiles() {
    console.log('🔄 Initializing client profiles...');
    const saved = localStorage.getItem('clientProfiles');
    if (saved) {
        try {
            clientProfiles = JSON.parse(saved);
            console.log(`✅ Loaded ${Object.keys(clientProfiles).length} client profiles`);
        } catch (error) {
            console.error('❌ Error parsing client profiles:', error);
            clientProfiles = {};
        }
    } else {
        // Add default mock profiles if none exist
        addMockClientProfiles();
    }
    updateClientProfileDropdown();
}

// Add mock client profiles for demonstration
function addMockClientProfiles() {
    console.log('🔍 Adding mock client profiles...');

    const mockProfiles = {
        "Stellar Events & Entertainment": {
            company: "Stellar Events & Entertainment",
            regNumber: "2023/123456/07",
            vatNumber: "**********",
            address: "456 Event Plaza\nEntertainment District\nCape Town, 8001",
            attention: "Sarah Mitchell",
            phone: "+27 21 555 0123",
            email: "<EMAIL>",
            website: "https://www.stellarevents.co.za",
            createdAt: new Date().toISOString()
        },
        "Rhythm Records": {
            company: "Rhythm Records",
            regNumber: "2022/987654/07",
            vatNumber: "**********",
            address: "789 Music Street\nSound City\nJohannesburg, 2000",
            attention: "Michael Johnson",
            phone: "+27 11 222 3333",
            email: "<EMAIL>",
            website: "https://www.rhythmrecords.co.za",
            createdAt: new Date().toISOString()
        },
        "Melody Productions": {
            company: "Melody Productions",
            regNumber: "2021/456789/07",
            vatNumber: "4456789123",
            address: "321 Harmony Avenue\nMelody Park\nDurban, 4001",
            attention: "Emma Davis",
            phone: "+27 31 777 8888",
            email: "<EMAIL>",
            website: "https://www.melodyproductions.co.za",
            createdAt: new Date().toISOString()
        },
        "Soundwave Studios": {
            company: "Soundwave Studios",
            regNumber: "2020/789123/07",
            vatNumber: "4789123456",
            address: "654 Audio Lane\nStudio Complex\nPretoria, 0001",
            attention: "David Wilson",
            phone: "+27 12 999 0000",
            email: "<EMAIL>",
            website: "https://www.soundwavestudios.co.za",
            createdAt: new Date().toISOString()
        },
        "Sunset Music Festival": {
            company: "Sunset Music Festival",
            regNumber: "2022/654321/07",
            vatNumber: "4876543210",
            address: "123 Festival Grounds\nMusic Valley\nCape Town, 8005",
            attention: "Lisa Johnson",
            phone: "+27 21 444 5555",
            email: "<EMAIL>",
            website: "https://www.sunsetfestival.co.za",
            createdAt: new Date().toISOString()
        },
        "Urban Beats Entertainment": {
            company: "Urban Beats Entertainment",
            regNumber: "2021/111222/07",
            vatNumber: "4333444555",
            address: "567 Hip Hop Street\nUrban District\nJohannesburg, 2001",
            attention: "Thabo Mthembu",
            phone: "+27 11 333 4444",
            email: "<EMAIL>",
            website: "https://www.urbanbeats.co.za",
            createdAt: new Date().toISOString()
        }
    };

    clientProfiles = mockProfiles;
    saveClientProfiles();
    console.log('✅ Mock client profiles added:', Object.keys(mockProfiles));
}

// Load and display client profiles in the modal
function loadClientProfiles() {
    console.log('📋 Loading client profiles for display...');
    const clientsGrid = document.getElementById('clientsGrid');
    if (!clientsGrid) {
        console.error('❌ Clients grid not found');
        return;
    }

    // Clear existing content
    clientsGrid.innerHTML = '';

    const clientNames = Object.keys(clientProfiles);

    if (clientNames.length === 0) {
        clientsGrid.innerHTML = `
            <div class="empty-state">
                <div style="text-align: center; padding: 40px; color: #6c757d;">
                    <div style="font-size: 48px; margin-bottom: 16px;">👥</div>
                    <h3>No Clients Yet</h3>
                    <p>Add your first client to get started with professional document management.</p>
                    <button type="button" class="btn-primary" onclick="showAddClientForm()">
                        <span>➕ Add First Client</span>
                    </button>
                </div>
            </div>
        `;
        return;
    }

    // Create client cards
    clientNames.forEach(clientName => {
        const client = clientProfiles[clientName];
        const clientCard = createClientCard(clientName, client);
        clientsGrid.appendChild(clientCard);
    });

    console.log(`✅ Displayed ${clientNames.length} client profiles`);
}

// Create a client card element
function createClientCard(clientName, client) {
    const card = document.createElement('div');
    card.className = 'client-card';
    card.setAttribute('data-client-name', clientName);

    // Format address for display (first line only)
    const addressFirstLine = client.address ? client.address.split('\n')[0] : 'No address';

    card.innerHTML = `
        <div class="client-card-header">
            <h4 class="client-name">${clientName}</h4>
            <div class="client-actions">
                <button class="client-action-btn" onclick="editClient('${clientName}')" title="Edit Client">
                    ✏️
                </button>
                <button class="client-action-btn" onclick="selectClientForDropdown('${clientName}')" title="Select Client">
                    ✅
                </button>
                <button class="client-action-btn" onclick="deleteClient('${clientName}')" title="Delete Client">
                    🗑️
                </button>
            </div>
        </div>
        <div class="client-info">
            <div><strong>Company:</strong> ${client.company || 'N/A'}</div>
            <div><strong>Contact:</strong> ${client.attention || 'N/A'}</div>
            <div><strong>Phone:</strong> ${client.phone || 'N/A'}</div>
            <div><strong>Address:</strong> ${addressFirstLine}</div>
            ${client.email ? `<div><strong>Email:</strong> ${client.email}</div>` : ''}
        </div>
    `;

    // Add click handler for card selection
    card.addEventListener('click', (e) => {
        if (!e.target.closest('.client-action-btn')) {
            selectClientCard(card);
        }
    });

    return card;
}

// Select a client card visually
function selectClientCard(card) {
    // Remove selection from other cards
    document.querySelectorAll('.client-card.selected').forEach(c => {
        c.classList.remove('selected');
    });

    // Select this card
    card.classList.add('selected');

    const clientName = card.getAttribute('data-client-name');
    console.log(`👤 Selected client: ${clientName}`);
}

// Show the add client form
function showAddClientForm() {
    console.log('➕ Showing add client form...');
    currentEditingClient = null;

    // Update form title
    const formTitle = document.getElementById('clientFormTitle');
    if (formTitle) {
        formTitle.textContent = '➕ Add New Client';
    }

    // Update save button text
    const saveBtn = document.getElementById('saveClientBtnText');
    if (saveBtn) {
        saveBtn.textContent = '💾 Save Client';
    }

    // Reset and show form
    resetClientForm();
    showClientFormSection();
}

// Show the client form section
function showClientFormSection() {
    const listSection = document.querySelector('.client-list-section');
    const formSection = document.getElementById('clientFormSection');

    if (listSection) listSection.style.display = 'none';
    if (formSection) formSection.style.display = 'block';
}

// Hide the client form section
function hideClientForm() {
    const listSection = document.querySelector('.client-list-section');
    const formSection = document.getElementById('clientFormSection');

    if (listSection) listSection.style.display = 'block';
    if (formSection) formSection.style.display = 'none';
}

// Reset the client form
function resetClientForm() {
    const form = document.getElementById('clientProfileForm');
    if (form) {
        form.reset();
    }
    currentEditingClient = null;
}

// Edit an existing client
function editClient(clientName) {
    console.log(`✏️ Editing client: ${clientName}`);
    const client = clientProfiles[clientName];
    if (!client) {
        console.error(`❌ Client not found: ${clientName}`);
        return;
    }

    currentEditingClient = clientName;

    // Update form title
    const formTitle = document.getElementById('clientFormTitle');
    if (formTitle) {
        formTitle.textContent = `✏️ Edit ${clientName}`;
    }

    // Update save button text
    const saveBtn = document.getElementById('saveClientBtnText');
    if (saveBtn) {
        saveBtn.textContent = '💾 Update Client';
    }

    // Populate form with client data
    populateClientForm(clientName, client);

    // Show form section
    showClientFormSection();
}

// Populate the client form with data
function populateClientForm(clientName, client) {
    const fields = {
        'clientProfileName': clientName,
        'clientCompanyName': client.company || '',
        'clientRegNumber': client.regNumber || '',
        'clientVatNumber': client.vatNumber || '',
        'clientAddress': client.address || '',
        'clientAttention': client.attention || '',
        'clientPhone': client.phone || '',
        'clientEmail': client.email || '',
        'clientWebsite': client.website || ''
    };

    Object.keys(fields).forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.value = fields[fieldId];
        }
    });
}

// Delete a client
function deleteClient(clientName) {
    if (confirm(`Are you sure you want to delete client "${clientName}"?\n\nThis action cannot be undone.`)) {
        console.log(`🗑️ Deleting client: ${clientName}`);
        delete clientProfiles[clientName];
        saveClientProfiles();
        loadClientProfiles();
        updateClientProfileDropdown();
        console.log(`✅ Client "${clientName}" deleted successfully`);
    }
}

// Select client for dropdown (and close modal)
function selectClientForDropdown(clientName) {
    console.log(`✅ Selecting client for dropdown: ${clientName}`);
    const dropdown = document.getElementById('clientProfileDropdown');
    if (dropdown) {
        dropdown.value = clientName;
        // Trigger change event to load client data
        dropdown.dispatchEvent(new Event('change'));
    }
    closeClientProfilesModal();
}

// Filter clients based on search input
function filterClients() {
    const searchInput = document.getElementById('clientSearchInput');
    const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
    const clientCards = document.querySelectorAll('.client-card');

    clientCards.forEach(card => {
        const clientName = card.getAttribute('data-client-name').toLowerCase();
        const clientInfo = card.querySelector('.client-info').textContent.toLowerCase();

        if (clientName.includes(searchTerm) || clientInfo.includes(searchTerm)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// Save client profiles to localStorage
function saveClientProfiles() {
    try {
        localStorage.setItem('clientProfiles', JSON.stringify(clientProfiles));
        console.log('💾 Client profiles saved to localStorage');
    } catch (error) {
        console.error('❌ Error saving client profiles:', error);
    }
}

// Close client profiles modal
function closeClientProfilesModal() {
    const modal = document.getElementById('clientProfilesModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Handle client form submission
function handleClientFormSubmit(event) {
    event.preventDefault();
    console.log('📝 Handling client form submission...');

    // Get form data
    const formData = {
        profileName: document.getElementById('clientProfileName')?.value?.trim(),
        company: document.getElementById('clientCompanyName')?.value?.trim(),
        regNumber: document.getElementById('clientRegNumber')?.value?.trim(),
        vatNumber: document.getElementById('clientVatNumber')?.value?.trim(),
        address: document.getElementById('clientAddress')?.value?.trim(),
        attention: document.getElementById('clientAttention')?.value?.trim(),
        phone: document.getElementById('clientPhone')?.value?.trim(),
        email: document.getElementById('clientEmail')?.value?.trim(),
        website: document.getElementById('clientWebsite')?.value?.trim()
    };

    // Validate required fields
    if (!formData.profileName) {
        alert('Please enter a profile name');
        return;
    }

    if (!formData.company) {
        alert('Please enter a company name');
        return;
    }

    if (!formData.address) {
        alert('Please enter an address');
        return;
    }

    if (!formData.phone) {
        alert('Please enter a phone number');
        return;
    }

    // Check if profile name already exists (and we're not editing)
    if (!currentEditingClient && clientProfiles[formData.profileName]) {
        alert(`A client profile with the name "${formData.profileName}" already exists. Please choose a different name.`);
        return;
    }

    // If editing, remove old entry if name changed
    if (currentEditingClient && currentEditingClient !== formData.profileName) {
        delete clientProfiles[currentEditingClient];
    }

    // Save client profile
    clientProfiles[formData.profileName] = {
        company: formData.company,
        regNumber: formData.regNumber,
        vatNumber: formData.vatNumber,
        address: formData.address,
        attention: formData.attention,
        phone: formData.phone,
        email: formData.email,
        website: formData.website,
        createdAt: currentEditingClient ? clientProfiles[currentEditingClient]?.createdAt : new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };

    // Save to localStorage
    saveClientProfiles();

    // Update UI
    updateClientProfileDropdown();
    loadClientProfiles();
    hideClientForm();

    // Show success message
    const action = currentEditingClient ? 'updated' : 'created';
    console.log(`✅ Client profile "${formData.profileName}" ${action} successfully`);

    // Reset editing state
    currentEditingClient = null;
}

// Update the client profile dropdown
function updateClientProfileDropdown() {
    console.log('🔄 Updating client profile dropdown...');
    const dropdown = document.getElementById('clientProfileDropdown');
    if (!dropdown) {
        console.error('❌ Client profile dropdown not found');
        return;
    }

    // Clear existing options except the first one
    dropdown.innerHTML = '<option value="">Select a client...</option>';

    // Add client options
    const clientNames = Object.keys(clientProfiles).sort();
    clientNames.forEach(clientName => {
        const option = document.createElement('option');
        option.value = clientName;
        option.textContent = clientName;
        dropdown.appendChild(option);
    });

    console.log(`✅ Added ${clientNames.length} clients to dropdown`);
}

// Handle dropdown change event
function handleClientDropdownChange() {
    const dropdown = document.getElementById('clientProfileDropdown');
    if (!dropdown) return;

    const selectedClient = dropdown.value;
    if (selectedClient && clientProfiles[selectedClient]) {
        console.log(`📋 Loading client from dropdown: ${selectedClient}`);
        loadClientProfileData(selectedClient, clientProfiles[selectedClient]);
    }
}

// Load client profile data into form fields (for invoice generation)
function loadClientProfileData(clientName, clientData) {
    console.log(`📋 Loading client profile data: ${clientName}`);

    // Map client data to form fields (this matches the existing invoice form structure)
    const fieldMappings = {
        'billToCompany': clientData.company,
        'billToRegNumber': clientData.regNumber,
        'billToVatNumber': clientData.vatNumber,
        'billToAddress': clientData.address,
        'billToAttention': clientData.attention,
        'billToPhone': clientData.phone
    };

    // Populate fields
    Object.keys(fieldMappings).forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field && fieldMappings[fieldId]) {
            field.value = fieldMappings[fieldId];
        }
    });

    console.log(`✅ Client profile "${clientName}" loaded into form`);
}

// ===== COMPANY MANAGEMENT SYSTEM =====

// Show company management modal
function showCompanyManager() {
    console.log('🏢 Opening Company Management...');
    const modal = document.getElementById('companyManagementModal');
    if (modal) {
        modal.style.display = 'block';
        loadCompanyProfiles();

        // Check if called from "Add Company" button - if so, show form directly
        const isAddCompanyCall = event && event.target && event.target.closest('a') &&
                                event.target.closest('a').textContent.includes('Add Company');

        if (isAddCompanyCall) {
            console.log('🆕 Opening directly to Add Company form...');
            setTimeout(() => {
                showAddCompanyForm();
            }, 100);
        } else {
            hideCompanyForm();
        }
    } else {
        console.error('❌ Company Management modal not found');
    }
}

// Close company management modal
function closeCompanyManagementModal() {
    const modal = document.getElementById('companyManagementModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Load company profiles
function loadCompanyProfiles() {
    console.log('🏢 Loading company profiles...');
    const companiesGrid = document.getElementById('companiesGrid');
    if (!companiesGrid) return;

    companiesGrid.innerHTML = `
        <div class="company-card active">
            <div class="company-card-header">
                <h4 class="company-name">DocuGen Pro Solutions</h4>
                <span class="company-status">Active</span>
            </div>
            <div class="company-details">
                <div><strong>Type:</strong> Software Solutions</div>
                <div><strong>Registration:</strong> 2024/123456/07</div>
                <div><strong>Email:</strong> <EMAIL></div>
                <div><strong>Phone:</strong> +27 11 123 4567</div>
            </div>
        </div>
        <div class="company-card">
            <div class="company-card-header">
                <h4 class="company-name">Add New Company</h4>
            </div>
            <div class="company-details" style="text-align: center; padding: 20px;">
                <button type="button" class="btn-primary" onclick="showAddCompanyForm()">
                    <span>➕ Create Company</span>
                </button>
            </div>
        </div>
    `;
}

// Show add company form (using existing Company Information Modal)
function showAddCompanyForm() {
    console.log('➕ Opening Add Company form...');

    // Open the existing Company Information Modal
    const modal = document.getElementById('companyInfoModal');
    if (modal) {
        modal.style.display = 'block';

        // Update modal title for "Add Company" mode
        const modalTitle = modal.querySelector('.modal-header h2');
        if (modalTitle) {
            modalTitle.textContent = '➕ Add New Company';
        }

        // Update submit button text
        const submitBtn = modal.querySelector('.btn-primary');
        if (submitBtn) {
            submitBtn.textContent = '💾 Save New Company';
        }

        // Clear the form for new company entry
        resetCompanyInfoForm();

        // Initialize logo upload functionality
        setTimeout(() => {
            initializeCompanyLogoUpload();
            console.log('✅ Logo upload functionality initialized for Add Company form');
        }, 100);

        console.log('✅ Add Company modal opened with existing layout');
    } else {
        console.error('❌ Company Information modal not found');
    }
}

// Reset the Company Information form for new company entry
function resetCompanyInfoForm() {
    console.log('🔄 Resetting company information form...');

    const form = document.getElementById('companyInfoForm');
    if (form) {
        form.reset();
    }

    // Reset logo preview
    const logoPreview = document.getElementById('companyLogoPreview');
    if (logoPreview) {
        logoPreview.innerHTML = `
            <div class="logo-placeholder">
                📷<br>
                <strong>Upload New Logo</strong><br>
                <small>Click "Choose File" above</small>
            </div>
        `;
    }

    // Reset current logo display
    const currentLogo = document.getElementById('currentCompanyLogo');
    if (currentLogo) {
        currentLogo.innerHTML = `
            <div class="logo-placeholder">
                📄<br>
                <small>New Company</small>
            </div>
        `;
    }

    // Reset current company name
    const currentName = document.getElementById('currentCompanyName');
    if (currentName) {
        currentName.textContent = 'New Company';
    }

    // Reset logo shape selector to default
    const logoShape = document.getElementById('companyLogoShape');
    if (logoShape) {
        logoShape.value = 'square';
    }

    // Reset logo preview class
    if (logoPreview) {
        logoPreview.className = 'company-logo-preview square';
    }

    console.log('✅ Company information form reset for new entry');
}

// Show company form section
function showCompanyFormSection() {
    const selectorSection = document.querySelector('.company-selector-section');
    const formSection = document.getElementById('companyFormSection');

    if (selectorSection) selectorSection.style.display = 'none';
    if (formSection) formSection.style.display = 'block';
}

// Hide company form section
function hideCompanyForm() {
    const selectorSection = document.querySelector('.company-selector-section');
    const formSection = document.getElementById('companyFormSection');

    if (selectorSection) selectorSection.style.display = 'block';
    if (formSection) formSection.style.display = 'none';
}

// Switch company form tabs
function switchCompanyTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab content
    const targetTab = document.getElementById(tabName + 'Tab');
    if (targetTab) {
        targetTab.classList.add('active');
    }

    // Activate corresponding tab button
    event.target.classList.add('active');
}

// Preview company logo
function previewCompanyLogo(input) {
    const preview = document.getElementById('logoPreview');
    if (!preview) return;

    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" alt="Company Logo">`;
            preview.classList.remove('empty');
        };
        reader.readAsDataURL(input.files[0]);
    } else {
        preview.innerHTML = '<div class="logo-preview empty">No logo selected</div>';
        preview.classList.add('empty');
    }
}

// Reset company form
function resetCompanyForm() {
    const form = document.getElementById('companyProfileForm');
    if (form) {
        form.reset();
    }

    // Reset logo preview
    const preview = document.getElementById('logoPreview');
    if (preview) {
        preview.innerHTML = '<div class="logo-preview empty">No logo selected</div>';
        preview.classList.add('empty');
    }

    // Reset to first tab
    switchCompanyTab('basic');
}

// Initialize event listeners when DOM is loaded
function initializeClientManagementEventListeners() {
    console.log('🔧 Initializing client management event listeners...');

    // Client form submission
    const clientForm = document.getElementById('clientProfileForm');
    if (clientForm) {
        clientForm.addEventListener('submit', handleClientFormSubmit);
    }

    // Client dropdown change
    const clientDropdown = document.getElementById('clientProfileDropdown');
    if (clientDropdown) {
        clientDropdown.addEventListener('change', handleClientDropdownChange);
    }

    // Company form submission
    const companyForm = document.getElementById('companyProfileForm');
    if (companyForm) {
        companyForm.addEventListener('submit', handleCompanyFormSubmit);
    }

    // Modal close handlers
    window.addEventListener('click', function(event) {
        const clientModal = document.getElementById('clientProfilesModal');
        const companyModal = document.getElementById('companyManagementModal');

        if (event.target === clientModal) {
            closeClientProfilesModal();
        }
        if (event.target === companyModal) {
            closeCompanyManagementModal();
        }
    });

    console.log('✅ Client management event listeners initialized');
}

// Handle company form submission
function handleCompanyFormSubmit(event) {
    event.preventDefault();
    console.log('🏢 Handling company form submission...');

    // Get form data from all tabs
    const formData = {
        // Basic Info Tab
        companyName: document.getElementById('companyName')?.value?.trim(),
        companyType: document.getElementById('companyType')?.value?.trim(),
        regNumber: document.getElementById('companyRegNumber')?.value?.trim(),
        vatNumber: document.getElementById('companyVatNumber')?.value?.trim(),
        address: document.getElementById('companyAddress')?.value?.trim(),
        phone: document.getElementById('companyPhone')?.value?.trim(),
        email: document.getElementById('companyEmail')?.value?.trim(),
        website: document.getElementById('companyWebsite')?.value?.trim(),

        // Banking Tab
        bankName: document.getElementById('bankName')?.value?.trim(),
        accountHolder: document.getElementById('accountHolder')?.value?.trim(),
        accountNumber: document.getElementById('accountNumber')?.value?.trim(),
        branchCode: document.getElementById('branchCode')?.value?.trim(),
        accountType: document.getElementById('accountType')?.value?.trim(),
        swiftCode: document.getElementById('swiftCode')?.value?.trim(),

        // Branding Tab
        brandColor: document.getElementById('brandColor')?.value?.trim(),
        companySlogan: document.getElementById('companySlogan')?.value?.trim()
    };

    // Validate required fields
    const requiredFields = [
        { field: 'companyName', name: 'Company Name' },
        { field: 'companyType', name: 'Company Type' },
        { field: 'address', name: 'Address' },
        { field: 'phone', name: 'Phone' },
        { field: 'email', name: 'Email' }
    ];

    for (const required of requiredFields) {
        if (!formData[required.field]) {
            alert(`Please enter ${required.name}`);
            return;
        }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
        alert('Please enter a valid email address');
        return;
    }

    // Save company profile (for now, just show success message)
    console.log('💾 Company profile data:', formData);

    // Show success message
    alert(`✅ Company "${formData.companyName}" has been successfully created!\n\n` +
          `📋 Company Details:\n` +
          `• Name: ${formData.companyName}\n` +
          `• Type: ${formData.companyType}\n` +
          `• Registration: ${formData.regNumber || 'Not provided'}\n` +
          `• Email: ${formData.email}\n` +
          `• Phone: ${formData.phone}\n\n` +
          `🏦 Banking: ${formData.bankName ? 'Configured' : 'Not configured'}\n` +
          `🎨 Branding: ${formData.brandColor ? 'Configured' : 'Not configured'}\n\n` +
          `This company profile is now ready for use in documents!`);

    // Reset form and close modal
    resetCompanyForm();
    closeCompanyManagementModal();

    console.log(`✅ Company profile "${formData.companyName}" created successfully`);
}

function loadRecentDocuments() {
    const container = document.getElementById('recentDocuments');
    container.innerHTML = `
        <div style="text-align: center; padding: 2rem; color: #6c757d;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">📄</div>
            <h3>No Recent Documents</h3>
            <p>Create your first document to see it here</p>
            <button class="btn-primary" onclick="openGenerator()" style="margin-top: 1rem;">
                Create Document
            </button>
        </div>
    `;
}

// Form submission handlers
document.addEventListener('submit', function(e) {
    if (e.target.id === 'companyInfoForm') {
        e.preventDefault();
        saveCompanyInfo();
    } else if (e.target.id === 'bankDetailsForm') {
        e.preventDefault();
        saveBankDetails();
    }
});

// Company Logo Upload Functionality
function initializeCompanyLogoUpload() {
    const logoUpload = document.getElementById('companyLogoUpload');
    const logoShape = document.getElementById('companyLogoShape');
    const logoPreview = document.getElementById('companyLogoPreview');

    if (logoUpload) {
        logoUpload.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    const img = document.createElement('img');
                    img.src = event.target.result;
                    img.onload = function() {
                        // Clear previous content
                        logoPreview.innerHTML = '';

                        // Add the image
                        logoPreview.appendChild(img);

                        // Add success indicator
                        const successIndicator = document.createElement('div');
                        successIndicator.className = 'logo-upload-success';
                        successIndicator.innerHTML = '✓';
                        logoPreview.appendChild(successIndicator);

                        // Apply current shape
                        const currentShape = logoShape ? logoShape.value : 'square';
                        logoPreview.className = `company-logo-preview ${currentShape}`;

                        // Store the logo data
                        const companyLogo = {
                            data: event.target.result,
                            shape: currentShape,
                            filename: file.name,
                            uploadDate: new Date().toISOString()
                        };
                        localStorage.setItem('companyLogo', JSON.stringify(companyLogo));

                        // Also store for the active company
                        const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';
                        localStorage.setItem(`companyLogo_${activeCompanyKey}`, event.target.result);

                        // Update all document previews immediately
                        updateAllDocumentPreviews();

                        // Update company preview in sidebar
                        displayCurrentCompanyLogo();
                        updateCompanyPreview();

                        console.log('✅ Company logo uploaded, saved, and all document previews updated');

                        // Show success message
                        const successMsg = document.createElement('div');
                        successMsg.style.cssText = 'position: fixed; top: 20px; right: 20px; background: #28a745; color: white; padding: 10px 20px; border-radius: 5px; z-index: 9999;';
                        successMsg.textContent = '✅ Logo updated! Will appear in all documents.';
                        document.body.appendChild(successMsg);
                        setTimeout(() => successMsg.remove(), 3000);
                    };
                };
                reader.readAsDataURL(file);
            }
        });
    }

    if (logoShape) {
        logoShape.addEventListener('change', function(e) {
            const currentShape = e.target.value;
            if (logoPreview) {
                logoPreview.className = `company-logo-preview ${currentShape}`;
            }

            // Update stored logo shape if logo exists
            const storedLogo = localStorage.getItem('companyLogo');
            if (storedLogo) {
                const logoData = JSON.parse(storedLogo);
                logoData.shape = currentShape;
                localStorage.setItem('companyLogo', JSON.stringify(logoData));

                // Update all document previews with new shape
                updateAllDocumentPreviews();

                console.log('✅ Logo shape updated and previews refreshed');
            }
        });
    }

    // Load existing logo if available
    loadStoredCompanyLogo();
}

function loadStoredCompanyLogo() {
    const storedLogo = localStorage.getItem('companyLogo');
    const logoPreview = document.getElementById('companyLogoPreview');
    const logoShape = document.getElementById('companyLogoShape');

    if (storedLogo && logoPreview) {
        const logoData = JSON.parse(storedLogo);

        // Clear previous content
        logoPreview.innerHTML = '';

        // Create and add image
        const img = document.createElement('img');
        img.src = logoData.data;
        logoPreview.appendChild(img);

        // Add success indicator
        const successIndicator = document.createElement('div');
        successIndicator.className = 'logo-upload-success';
        successIndicator.innerHTML = '✓';
        logoPreview.appendChild(successIndicator);

        // Apply stored shape
        logoPreview.className = `company-logo-preview ${logoData.shape}`;
        if (logoShape) {
            logoShape.value = logoData.shape;
        }
    }
}

function getStoredCompanyLogo() {
    const stored = localStorage.getItem('companyLogo');
    return stored ? JSON.parse(stored) : null;
}

// Function to update all document previews when logo changes
function updateAllDocumentPreviews() {
    const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';
    const companyInfo = getStoredCompanyInfo();
    const logoData = getStoredCompanyLogo();

    if (!companyInfo) return;

    // Update each document type preview
    const documentTypes = ['invoice', 'receipt', 'quotation', 'contract', 'rider', 'annexure'];

    documentTypes.forEach(type => {
        const previewContainer = document.querySelector(`[data-type="${type}"] .document-preview-container`);
        if (previewContainer) {
            // Regenerate preview with new logo
            const previewContent = generateDocumentPreview(type, companyInfo, logoData?.data);
            if (previewContent) {
                previewContainer.innerHTML = previewContent;
            }
        }
    });

    console.log('✅ All document previews updated with new logo');
}

// Unified document preview generator
function generateDocumentPreview(type, company, logo) {
    switch(type) {
        case 'invoice':
            return generateInvoicePreview(company, logo);
        case 'receipt':
            return generateReceiptPreview(company, logo);
        case 'quotation':
            return generateQuotationPreview(company, logo);
        case 'contract':
            return generateContractPreview(company, logo);
        case 'artist-agreement':
            return generateArtistAgreementPreview(company, logo);
        case 'rider':
            return generateTechnicalRiderPreview(company, logo);
        case 'annexure':
            return generateAnnexurePreview(company, logo);
        default:
            return null;
    }
}

// Enhanced function to get company info with logo
function getStoredCompanyInfo() {
    const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';
    const stored = localStorage.getItem(`companyInfo_${activeCompanyKey}`);

    if (stored) {
        const companyInfo = JSON.parse(stored);

        // If logo is not in company info, try to get it separately
        if (!companyInfo.logo) {
            const logoData = getStoredCompanyLogo();
            if (logoData) {
                companyInfo.logo = logoData;
            }
        }

        return companyInfo;
    }

    return null;
}

function saveCompanyInfo() {
    // Get the current logo data
    const logoData = getStoredCompanyLogo();

    const companyInfo = {
        name: document.getElementById('companyName').value,
        regNumber: document.getElementById('companyRegNumber').value,
        vatNumber: document.getElementById('companyVatNumber').value,
        address: document.getElementById('companyAddress').value,
        phone: document.getElementById('companyPhone').value,
        email: document.getElementById('companyEmail').value,
        website: document.getElementById('companyWebsite').value,
        representative: {
            name: document.getElementById('representativeName')?.value || '',
            title: document.getElementById('representativeTitle')?.value || '',
            email: document.getElementById('representativeEmail')?.value || ''
        },
        // Include logo data with company info
        logo: logoData
    };

    // Check if this is a new company (Add Company mode) or editing existing
    const modalTitle = document.querySelector('#companyInfoModal .modal-header h2');
    const isAddMode = modalTitle && modalTitle.textContent.includes('Add New Company');

    if (isAddMode) {
        // This is a new company - save it to the companies list
        saveNewCompanyToList(companyInfo);
    } else {
        // This is editing existing company info
        const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';

        // Save company info with logo
        localStorage.setItem(`companyInfo_${activeCompanyKey}`, JSON.stringify(companyInfo));

        // Also save logo separately for the active company (for backward compatibility)
        if (logoData) {
            localStorage.setItem(`companyLogo_${activeCompanyKey}`, logoData.data);
        }

        // Update all document previews to reflect the new logo
        updateAllDocumentPreviews();

        updateCompanyStatus();

        const companyName = testCompanies[activeCompanyKey]?.name || 'Company';
        alert(`${companyName} information and logo saved successfully! Logo will now appear in all documents.`);
    }

    closeCompanyInfo();
}

// Save new company to the companies list and update dropdown
function saveNewCompanyToList(companyInfo) {
    console.log('💾 Saving new company to list:', companyInfo.name);

    // Generate a unique ID for the company
    const companyId = generateCompanyId(companyInfo.name);

    // Get existing saved companies
    let savedCompanies = JSON.parse(localStorage.getItem('savedCompanies') || '[]');

    // Add the new company
    const newCompany = {
        id: companyId,
        name: companyInfo.name,
        regNumber: companyInfo.regNumber,
        vatNumber: companyInfo.vatNumber,
        address: companyInfo.address,
        phone: companyInfo.phone,
        email: companyInfo.email,
        website: companyInfo.website,
        representative: companyInfo.representative,
        logo: companyInfo.logo,
        dateCreated: new Date().toISOString()
    };

    savedCompanies.push(newCompany);

    // Save back to localStorage
    localStorage.setItem('savedCompanies', JSON.stringify(savedCompanies));

    // Update the company setup dropdown
    updateCompanySetupDropdown();

    // Show success message
    alert(`✅ Company "${companyInfo.name}" has been successfully created and added to your company list!\n\n` +
          `📋 Company Details:\n` +
          `• Name: ${companyInfo.name}\n` +
          `• Registration: ${companyInfo.regNumber || 'Not provided'}\n` +
          `• Email: ${companyInfo.email}\n` +
          `• Phone: ${companyInfo.phone}\n\n` +
          `🎯 Next Step: Go to Company Setup and select this company to use it for your account setup!`);

    console.log(`✅ Company "${companyInfo.name}" saved successfully with ID: ${companyId}`);
}

// Generate a unique company ID
function generateCompanyId(companyName) {
    const timestamp = Date.now();
    const nameSlug = companyName.toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '-')
        .substring(0, 20);
    return `${nameSlug}-${timestamp}`;
}

// Update the company setup dropdown with saved companies
function updateCompanySetupDropdown() {
    console.log('🔄 Updating company setup dropdown...');

    const dropdown = document.getElementById('companySetupSelect');
    if (!dropdown) {
        console.error('❌ Company setup dropdown not found');
        return;
    }

    // Get saved companies
    const savedCompanies = JSON.parse(localStorage.getItem('savedCompanies') || '[]');

    // Clear existing options except the first one
    dropdown.innerHTML = '<option value="">-- Choose a Company --</option>';

    // Add default test companies
    const testCompanies = [
        { id: 'docugen-pro', name: 'DocuGen Pro Solutions' },
        { id: 'bongomaffin', name: 'Bongomaffin' },
        { id: 'benjamin-music', name: 'Benjamin Music Initiatives' }
    ];

    testCompanies.forEach(company => {
        const option = document.createElement('option');
        option.value = company.id;
        option.textContent = company.name;
        option.style.color = '#666';
        dropdown.appendChild(option);
    });

    // Add separator if there are saved companies
    if (savedCompanies.length > 0) {
        const separator = document.createElement('option');
        separator.disabled = true;
        separator.textContent = '── Your Companies ──';
        separator.style.fontWeight = 'bold';
        separator.style.color = '#007bff';
        dropdown.appendChild(separator);

        // Add saved companies
        savedCompanies.forEach(company => {
            const option = document.createElement('option');
            option.value = company.id;
            option.textContent = `🏢 ${company.name}`;
            option.style.fontWeight = 'bold';
            option.style.color = '#28a745';
            dropdown.appendChild(option);
        });
    }

    console.log(`✅ Company dropdown updated with ${savedCompanies.length} saved companies`);
}

// Handle company selection for setup
function selectCompanyForSetup() {
    console.log('🏢 Company selected for setup...');

    const dropdown = document.getElementById('companySetupSelect');
    const preview = document.getElementById('companyPreview');
    const logoMini = document.getElementById('companyLogoMini');
    const nameMini = document.getElementById('companyNameMini');

    if (!dropdown || !preview) {
        console.error('❌ Company setup elements not found');
        return;
    }

    const selectedValue = dropdown.value;

    if (!selectedValue) {
        // No company selected
        preview.style.display = 'none';
        return;
    }

    // Get company data
    let companyData = null;

    // Check if it's a saved company
    const savedCompanies = JSON.parse(localStorage.getItem('savedCompanies') || '[]');
    const savedCompany = savedCompanies.find(c => c.id === selectedValue);

    if (savedCompany) {
        companyData = savedCompany;
    } else {
        // It's a test company
        const testCompaniesData = {
            'docugen-pro': { name: 'DocuGen Pro Solutions', logo: '📄' },
            'bongomaffin': { name: 'Bongomaffin', logo: '🎵' },
            'benjamin-music': { name: 'Benjamin Music Initiatives', logo: '🎼' }
        };
        companyData = testCompaniesData[selectedValue];
    }

    if (companyData) {
        // Update preview
        if (logoMini) {
            if (companyData.logo && companyData.logo.data) {
                logoMini.innerHTML = `<img src="${companyData.logo.data}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 4px;">`;
            } else {
                logoMini.textContent = companyData.logo || '🏢';
            }
        }

        if (nameMini) {
            nameMini.textContent = companyData.name;
        }

        preview.style.display = 'block';

        console.log(`✅ Company preview updated for: ${companyData.name}`);
    }
}

// Use selected company for account setup
function useSelectedCompany() {
    console.log('🎯 Using selected company for setup...');

    const dropdown = document.getElementById('companySetupSelect');
    if (!dropdown || !dropdown.value) {
        alert('Please select a company first!');
        return;
    }

    const selectedValue = dropdown.value;
    let companyData = null;

    // Get company data
    const savedCompanies = JSON.parse(localStorage.getItem('savedCompanies') || '[]');
    const savedCompany = savedCompanies.find(c => c.id === selectedValue);

    if (savedCompany) {
        companyData = savedCompany;

        // Set this as the active company by saving it to the current company info storage
        localStorage.setItem(`companyInfo_${selectedValue}`, JSON.stringify({
            name: companyData.name,
            regNumber: companyData.regNumber,
            vatNumber: companyData.vatNumber,
            address: companyData.address,
            phone: companyData.phone,
            email: companyData.email,
            website: companyData.website,
            representative: companyData.representative,
            logo: companyData.logo
        }));

        // Set as active company
        localStorage.setItem('activeTestCompany', selectedValue);

        // Update company status
        updateCompanyStatus();

        alert(`✅ Company "${companyData.name}" is now set up for your account!\n\n` +
              `📋 This company's information will now be used in:\n` +
              `• Document generation\n` +
              `• Invoice creation\n` +
              `• All business documents\n\n` +
              `🎯 You can now use the "Company Information" section to view or edit these details.`);

        console.log(`✅ Company "${companyData.name}" set as active company`);
    } else {
        // Handle test companies
        localStorage.setItem('activeTestCompany', selectedValue);
        updateCompanyStatus();

        const testCompaniesData = {
            'docugen-pro': 'DocuGen Pro Solutions',
            'bongomaffin': 'Bongomaffin',
            'benjamin-music': 'Benjamin Music Initiatives'
        };

        const companyName = testCompaniesData[selectedValue] || 'Selected Company';
        alert(`✅ Test company "${companyName}" is now active for your account!`);

        console.log(`✅ Test company "${companyName}" set as active`);
    }
}

function saveBankDetails() {
    const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';

    const bankDetails = {
        bankName: document.getElementById('bankName').value,
        accountHolder: document.getElementById('accountHolder').value,
        accountNumber: document.getElementById('accountNumber').value,
        branchCode: document.getElementById('branchCode').value,
        accountType: document.getElementById('accountType').value,
        swiftCode: document.getElementById('swiftCode').value,
        iban: document.getElementById('iban').value
    };

    localStorage.setItem(`bankDetails_${activeCompanyKey}`, JSON.stringify(bankDetails));
    updateCompanyStatus();
    closeBankDetails();

    const companyName = testCompanies[activeCompanyKey]?.name || 'Company';
    alert(`${companyName} banking details saved successfully!`);
}

// Signature style control functions
function updateSignatureStyle() {
    const colorInput = document.getElementById('signatureColor');
    const thicknessInput = document.getElementById('signatureThickness');
    const smoothingInput = document.getElementById('signatureSmoothing');
    const thicknessValue = document.getElementById('thicknessValue');

    if (colorInput && thicknessInput && thicknessValue) {
        // Update thickness display
        thicknessValue.textContent = thicknessInput.value + 'px';

        // Store style preferences
        window.signatureStyle = {
            color: colorInput.value,
            thickness: parseInt(thicknessInput.value),
            smoothing: smoothingInput ? smoothingInput.checked : true
        };

        console.log('Signature style updated:', window.signatureStyle);
    }
}

function clearSignature() {
    console.log('🗑️ CLEAR PRESSED - Starting completely fresh signature');

    const canvas = window.signatureCanvas;
    if (!canvas) return;

    // Clear the session
    if (signatureSession) {
        signatureSession.allStrokes = [];
        signatureSession.currentStroke = [];
        signatureSession.isDrawing = false;
        signatureSession.lastX = 0;
        signatureSession.lastY = 0;
        signatureSession.lastVelocity = 0;
        signatureSession.lastTime = 0;
    }

    // Redraw clean canvas
    if (window.redrawAllStrokes) {
        window.redrawAllStrokes();
    }

    // Visual feedback that canvas is ready for new signature
    canvas.style.borderColor = '#28a745';
    setTimeout(() => {
        canvas.style.borderColor = '#ddd';
    }, 500);

    console.log('✅ Canvas completely cleared - Ready for brand new signature');
}

function undoLastStroke() {
    if (!signatureSession || signatureSession.allStrokes.length === 0) {
        console.log('No strokes to undo');
        return;
    }

    const removedStroke = signatureSession.allStrokes.pop();
    console.log('Undid stroke with', removedStroke.length, 'points. Remaining strokes:', signatureSession.allStrokes.length);

    // Redraw canvas with remaining strokes
    if (window.redrawAllStrokes) {
        window.redrawAllStrokes();
    }

    console.log('✅ Last stroke undone');
}

function saveSignature() {
    const canvas = window.signatureCanvas;
    if (!canvas) return;

    // Check if we have any strokes in current session
    const hasStrokes = signatureSession && signatureSession.allStrokes.length > 0;

    if (!hasStrokes) {
        alert('Please draw a signature first.');
        return;
    }

    console.log('💾 Saving signature with', signatureSession.allStrokes.length, 'strokes from session:', signatureSession.sessionId);

    const signatureData = canvas.toDataURL();
    const signatureName = prompt('Enter a name for this signature:') || 'Signature ' + Date.now();

    if (signatureName) {
        saveSignatureToStorage(signatureData, signatureName, 'canvas');

        console.log('✅ Signature saved, now clearing for next signature');

        // Clear canvas for next signature
        clearSignature();

        // Reload signature library and refresh integration
        loadSavedSignatures();
        refreshSignatureIntegration();

        alert(`✅ Signature "${signatureName}" saved successfully!\n\n🎨 Canvas cleared and ready for your next signature.\n\n🔗 Signature is now available in all document generators!`);

        console.log('✅ Signature saved and new session ready');
    }
}

function saveSignatureToStorage(signatureData, name, type) {
    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');

    const newSignature = {
        id: Date.now(),
        name: name,
        data: signatureData,
        type: type,
        created: new Date().toISOString()
    };

    signatures.push(newSignature);
    localStorage.setItem('savedSignatures', JSON.stringify(signatures));

    console.log('✅ Signature saved to storage:', name);

    // Refresh signature integration across all areas
    refreshSignatureIntegration();
}

function refreshSignatureIntegration() {
    console.log('🔄 Refreshing signature integration...');

    // Refresh signature selectors
    loadSignaturesIntoSelectors();

    // Refresh Import Document signature library
    loadSavedSignatures();

    // Refresh main generator signatures if available
    if (typeof loadSavedSignaturesForAgreement === 'function') {
        loadSavedSignaturesForAgreement();
    }

    console.log('✅ Signature integration refreshed');
}

function loadSavedSignatures() {
    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
    const container = document.getElementById('savedSignaturesList');

    if (!container) return;

    if (signatures.length === 0) {
        container.innerHTML = '<p class="no-signatures">No saved signatures yet</p>';
        return;
    }

    container.innerHTML = signatures.map(sig => `
        <div class="signature-item" data-id="${sig.id}">
            <img src="${sig.data}" alt="${sig.name}" style="max-width: 150px; max-height: 75px; border: 1px solid #ddd; border-radius: 4px;">
            <div class="signature-info">
                <h4>${sig.name}</h4>
                <small>Created: ${new Date(sig.created).toLocaleDateString()}</small>
            </div>
            <div class="signature-actions">
                <button onclick="deleteSignature(${sig.id})" class="btn-danger small">Delete</button>
            </div>
        </div>
    `).join('');
}

function deleteSignature(id) {
    if (confirm('Are you sure you want to delete this signature?')) {
        const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
        const filtered = signatures.filter(sig => sig.id !== id);
        localStorage.setItem('savedSignatures', JSON.stringify(filtered));
        loadSavedSignatures();
    }
}

// Text Signature Functionality
function initializeTextSignature() {
    const textInput = document.getElementById('textSignature');
    const fontSelect = document.getElementById('signatureFont');
    const preview = document.getElementById('textSignaturePreview');

    function updatePreview() {
        if (!textInput || !fontSelect || !preview) return;

        const text = textInput.value.trim();
        const font = fontSelect.value;

        if (text) {
            preview.textContent = text;
            preview.className = `text-signature-preview ${font}`;
            preview.style.display = 'block';
        } else {
            preview.textContent = 'Preview will appear here';
            preview.className = 'text-signature-preview';
        }
    }

    if (textInput) textInput.addEventListener('input', updatePreview);
    if (fontSelect) fontSelect.addEventListener('change', updatePreview);

    console.log('✅ Text signature initialized');
}

function saveTextSignature() {
    const textInput = document.getElementById('textSignature');
    const fontSelect = document.getElementById('signatureFont');

    if (!textInput || !textInput.value.trim()) {
        alert('Please enter your name first.');
        return;
    }

    // Create canvas with text signature
    const canvas = document.createElement('canvas');
    canvas.width = 300;
    canvas.height = 100;
    const ctx = canvas.getContext('2d');

    // Set background to white
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Set font based on selection
    let fontFamily;
    switch (fontSelect.value) {
        case 'cursive':
            fontFamily = 'Brush Script MT, cursive';
            break;
        case 'script':
            fontFamily = 'Lucida Handwriting, cursive';
            break;
        case 'elegant':
            fontFamily = 'Edwardian Script ITC, cursive';
            break;
        default:
            fontFamily = 'cursive';
    }

    ctx.font = `36px ${fontFamily}`;
    ctx.fillStyle = '#000000';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(textInput.value, canvas.width / 2, canvas.height / 2);

    const signatureData = canvas.toDataURL();
    const signatureName = textInput.value + ' (' + fontSelect.options[fontSelect.selectedIndex].text + ')';

    saveSignatureToStorage(signatureData, signatureName, 'text');
    textInput.value = '';

    // Reset preview
    const preview = document.getElementById('textSignaturePreview');
    if (preview) {
        preview.textContent = 'Preview will appear here';
        preview.className = 'text-signature-preview';
    }

    loadSavedSignatures();
    refreshSignatureIntegration();
    alert('✅ Text signature saved successfully!\n\n🔗 Signature is now available in all document generators!');
}

// Signature Upload Functionality
function initializeSignatureUpload() {
    const uploadInput = document.getElementById('signatureUpload');
    const preview = document.getElementById('uploadedSignaturePreview');

    if (uploadInput) {
        uploadInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    const img = document.createElement('img');
                    img.src = event.target.result;
                    img.onload = function() {
                        preview.innerHTML = '';
                        preview.appendChild(img);
                        preview.style.display = 'block';

                        // Add save button
                        const saveBtn = document.createElement('button');
                        saveBtn.textContent = 'Save Uploaded Signature';
                        saveBtn.className = 'btn-primary';
                        saveBtn.style.marginTop = '10px';
                        saveBtn.onclick = function() {
                            const signatureName = prompt('Enter a name for this signature:') || file.name;
                            saveSignatureToStorage(event.target.result, signatureName, 'upload');
                            preview.style.display = 'none';
                            uploadInput.value = '';
                            loadSavedSignatures();
                            refreshSignatureIntegration();
                            alert('✅ Signature saved successfully!\n\n🔗 Signature is now available in all document generators!');
                        };
                        preview.appendChild(saveBtn);
                    };
                };
                reader.readAsDataURL(file);
            }
        });
    }

    console.log('✅ Signature upload initialized');
}

// Document Sharing Functionality
function shareDocument() {
    document.getElementById('documentSharingModal').style.display = 'block';
    console.log('📤 Document sharing modal opened');
}

function closeDocumentSharing() {
    document.getElementById('documentSharingModal').style.display = 'none';
}

function shareViaEmail() {
    const recipientEmail = document.getElementById('recipientEmail').value;
    const emailSubject = document.getElementById('emailSubject').value;
    const emailMessage = document.getElementById('emailMessage').value;
    const sendCopy = document.getElementById('sendCopy').checked;

    if (!recipientEmail) {
        alert('Please enter a recipient email address.');
        return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(recipientEmail)) {
        alert('Please enter a valid email address.');
        return;
    }

    console.log('Sharing via email:', recipientEmail);

    // Create mailto link with document info
    const companyInfo = getStoredCompanyInfo();
    const documentName = 'Document';

    const subject = encodeURIComponent(emailSubject || `Document from ${companyInfo?.name || 'DocuGen Pro'}`);
    const body = encodeURIComponent(`${emailMessage}

---
Document: ${documentName}
Generated with DocuGen Pro
${new Date().toLocaleString()}`);

    const mailtoLink = `mailto:${recipientEmail}?subject=${subject}&body=${body}`;
    window.open(mailtoLink);

    alert('Email client opened. Please attach the document and send.');
    closeDocumentSharing();
}

function shareViaWhatsApp() {
    const phoneNumber = document.getElementById('whatsappNumber').value;
    const message = document.getElementById('whatsappMessage').value;

    if (!phoneNumber) {
        alert('Please enter a phone number.');
        return;
    }

    const companyInfo = getStoredCompanyInfo();
    const fullMessage = encodeURIComponent(`${message}

Document from ${companyInfo?.name || 'DocuGen Pro'}
Generated: ${new Date().toLocaleString()}`);

    const whatsappUrl = `https://wa.me/${phoneNumber.replace(/[^\d]/g, '')}?text=${fullMessage}`;
    window.open(whatsappUrl, '_blank');
}

function shareViaWhatsAppWeb() {
    const message = document.getElementById('whatsappMessage').value;
    const companyInfo = getStoredCompanyInfo();
    const fullMessage = encodeURIComponent(`${message}

Document from ${companyInfo?.name || 'DocuGen Pro'}
Generated: ${new Date().toLocaleString()}`);

    const whatsappWebUrl = `https://web.whatsapp.com/send?text=${fullMessage}`;
    window.open(whatsappWebUrl, '_blank');
}

function copyDocumentLink() {
    const link = window.location.href;
    navigator.clipboard.writeText(link).then(() => {
        alert('Document link copied to clipboard!');
    }).catch(() => {
        alert('Unable to copy link. Please copy manually: ' + link);
    });
}

function shareViaSMS() {
    const message = 'Check out this document from DocuGen Pro';
    const smsUrl = `sms:?body=${encodeURIComponent(message)}`;
    window.open(smsUrl);
}

function shareViaLinkedIn() {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent('Document from DocuGen Pro');
    const linkedinUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${title}`;
    window.open(linkedinUrl, '_blank');
}

function shareViaTelegram() {
    const message = encodeURIComponent('Check out this document from DocuGen Pro');
    const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(window.location.href)}&text=${message}`;
    window.open(telegramUrl, '_blank');
}

// Test active company integration
function testActiveCompanyIntegration() {
    const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';
    const activeCompany = testCompanies[activeCompanyKey];

    if (!activeCompany) {
        alert('❌ No active company found!');
        return;
    }

    // Show current active company
    const message = `🧪 ACTIVE COMPANY TEST

✅ Current Active Company: ${activeCompany.name}
📋 Company Key: ${activeCompanyKey}
🎯 This company's data will be used in all documents.

🔄 Opening Document Generator to test integration...`;

    alert(message);

    // Open document generator in new tab
    window.open('index.html', '_blank');
}

// Document Pimping Enhancement Functions
function updateEnhancementSummary() {
    const enhancementList = document.getElementById('enhancementList');
    if (!enhancementList) return;

    const enhancements = [];

    // Check frame
    const selectedFrame = document.querySelector('.frame-option.active')?.dataset.frame;
    if (selectedFrame && selectedFrame !== 'none') {
        enhancements.push(`Frame: ${selectedFrame.charAt(0).toUpperCase() + selectedFrame.slice(1)}`);
    }

    // Check background
    const selectedBackground = document.querySelector('input[name="background"]:checked')?.value;
    if (selectedBackground && selectedBackground !== 'none') {
        enhancements.push(`Background: ${selectedBackground.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}`);
    }

    // Check shadow
    const shadowEnabled = document.getElementById('enableShadow')?.checked;
    if (shadowEnabled) {
        const intensity = document.getElementById('shadowIntensity')?.value;
        enhancements.push(`Drop Shadow: ${intensity}% intensity`);
    }

    // Check border
    const borderWidth = document.getElementById('borderWidth')?.value;
    if (borderWidth > 0) {
        const borderColor = document.getElementById('borderColor')?.value;
        const borderStyle = document.getElementById('borderStyle')?.value;
        enhancements.push(`Border: ${borderWidth}px ${borderStyle} ${borderColor}`);
    }

    // Update list
    enhancementList.innerHTML = enhancements.length > 0
        ? enhancements.map(e => `<li>${e}</li>`).join('')
        : '<li>No enhancements applied</li>';
}

function applyEnhancements() {
    if (!window.currentPimpingPDF) {
        alert('Please upload a PDF first.');
        return;
    }

    console.log('✨ Applying enhancements to PDF...');

    // Show download section
    document.getElementById('downloadSection').style.display = 'block';

    // Update enhancement summary
    updateEnhancementSummary();

    // Create enhanced PDF
    createEnhancedPDF();
}

function createEnhancedPDF() {
    try {
        // Get current settings
        const selectedFrame = document.querySelector('.frame-option.active')?.dataset.frame || 'none';
        const borderWidth = parseFloat(document.getElementById('borderWidth')?.value || 2);
        const borderColor = document.getElementById('borderColor')?.value || '#4a9eff';
        const borderStyle = document.getElementById('borderStyle')?.value || 'solid';
        const shadowEnabled = document.getElementById('enableShadow')?.checked || false;
        const shadowIntensity = parseFloat(document.getElementById('shadowIntensity')?.value || 30);
        const selectedBackground = document.querySelector('input[name="background"]:checked')?.value || 'none';

        // Create new jsPDF instance
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        // Add stylish frame to PDF
        addStylishFrameToPDF(doc, selectedFrame, {
            borderWidth,
            borderColor,
            borderStyle,
            shadowEnabled,
            shadowIntensity,
            background: selectedBackground
        });

        // Store enhanced PDF
        window.currentEnhancedPDF = doc;

        console.log('✅ Enhanced PDF created successfully');

    } catch (error) {
        console.error('❌ Error creating enhanced PDF:', error);
        alert('Error creating enhanced PDF. Please try again.');
    }
}

function addStylishFrameToPDF(doc, frameType, options) {
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;
    const margin = 10;

    // Add background effects
    if (options.background !== 'none') {
        addBackgroundEffects(doc, options.background, pageWidth, pageHeight);
    }

    // Add frame based on type
    switch (frameType) {
        case 'elegant':
            addElegantFrame(doc, pageWidth, pageHeight, margin);
            break;
        case 'modern':
            addModernFrame(doc, pageWidth, pageHeight, margin);
            break;
        case 'classic':
            addClassicFrame(doc, pageWidth, pageHeight, margin);
            break;
        case 'luxury':
            addLuxuryFrame(doc, pageWidth, pageHeight, margin);
            break;
        case 'creative':
            addCreativeFrame(doc, pageWidth, pageHeight, margin);
            break;
        default:
            addCustomFrame(doc, pageWidth, pageHeight, margin, options);
    }

    // Add shadow effect
    if (options.shadowEnabled) {
        addShadowEffect(doc, pageWidth, pageHeight, margin, options.shadowIntensity);
    }
}

function addElegantFrame(doc, width, height, margin) {
    // Elegant gradient-style frame
    doc.setDrawColor(102, 126, 234);
    doc.setLineWidth(3);
    doc.rect(margin, margin, width - 2 * margin, height - 2 * margin);

    // Inner elegant border
    doc.setDrawColor(118, 75, 162);
    doc.setLineWidth(1);
    doc.rect(margin + 5, margin + 5, width - 2 * margin - 10, height - 2 * margin - 10);
}

function addModernFrame(doc, width, height, margin) {
    // Modern blue frame
    doc.setDrawColor(74, 158, 255);
    doc.setLineWidth(4);
    doc.rect(margin, margin, width - 2 * margin, height - 2 * margin);

    // Corner accents
    const cornerSize = 15;
    doc.setFillColor(74, 158, 255);
    doc.rect(margin, margin, cornerSize, cornerSize, 'F');
    doc.rect(width - margin - cornerSize, margin, cornerSize, cornerSize, 'F');
    doc.rect(margin, height - margin - cornerSize, cornerSize, cornerSize, 'F');
    doc.rect(width - margin - cornerSize, height - margin - cornerSize, cornerSize, cornerSize, 'F');
}

function downloadEnhancedPDF() {
    if (!window.currentEnhancedPDF) {
        alert('Please apply enhancements first.');
        return;
    }

    try {
        const fileName = `enhanced_document_${new Date().getTime()}.pdf`;
        window.currentEnhancedPDF.save(fileName);
        console.log('✅ Enhanced PDF downloaded successfully');
    } catch (error) {
        console.error('❌ Error downloading enhanced PDF:', error);
        alert('Error downloading PDF. Please try again.');
    }
}

// Close modals when clicking outside
window.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal')) {
        e.target.style.display = 'none';
    }
});




