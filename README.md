# DocuGen Pro - Professional Document Generator

A comprehensive web application for generating professional business documents. Built with modern HTML5, CSS3, and JavaScript featuring a complete authentication system and support for 6 different document types.

## 🚀 New Features

### Authentication System
- **User Registration & Login:** Complete sign-up/sign-in system
- **Demo Accounts:** Pre-configured demo accounts for testing
- **Session Management:** Persistent user sessions with localStorage
- **User Dashboard:** Centralized document management interface

### Six Document Types
1. **📄 Invoices** - Professional invoices with VAT calculations
2. **🧾 Receipts** - Payment confirmations and transaction records
3. **💰 Quotations** - Price quotes with validity periods
4. **📋 Contracts** - Legal agreements with digital signatures
5. **🎵 Technical Riders** - Event technical requirements
6. **📎 Annexures** - Supporting documents and appendices

### Enhanced Features
- **Modern UI/UX:** Professional landing page and dashboard
- **Document Previews:** Full preview system with mock data
- **Responsive Design:** Mobile-optimized interface
- **Template Management:** Customizable document templates
- **Client Profiles:** Save and manage client information
- **Multi-Currency:** Support for ZAR, USD, EUR, GBP

## 🔐 Demo Login Credentials

**Primary Demo Account:**
- Email: `<EMAIL>`
- Password: `demo123`

**Additional Accounts:**
- `<EMAIL>` / `admin123`
- `<EMAIL>` / `test123`

## 🚀 Quick Start

### Option 1: Landing Page Experience
1. Open `landing.html` in a web browser
2. Explore the document type showcase
3. Preview any document with sample data
4. Sign in with demo credentials
5. Access the dashboard and create documents

### Option 2: Direct Access
1. Open `index.html` directly for the document generator
2. Fill in company and client information
3. Select document type (Invoice, Receipt, Quotation, etc.)
4. Add items/content and customize appearance
5. Generate professional PDF documents

## 📋 Document Types Guide

### 📄 Invoices
- Professional billing with automatic VAT calculations
- Client management and payment tracking
- Multiple currency support

### 🧾 Receipts
- Payment confirmations with transaction details
- Professional formatting with digital signatures

### 💰 Quotations
- Price quotes with validity periods
- Terms & conditions integration

### 📋 Contracts
- Legal agreements with customizable terms
- Digital signature support

### 🎵 Technical Riders
- Event technical requirements and specifications
- Equipment lists and setup details

### 📎 Annexures
- Supporting documents and appendices
- Cross-referencing capabilities

## Default Mock Data

- Company: Benjamin Music Initiatives
- Client: Rhythm Records Ltd
- VAT Rate: 15%
- Currency: South African Rand (R)

## Requirements

- Modern web browser with JavaScript enabled
- Internet connection (for jsPDF CDN)

## Technologies Used

- HTML5
- CSS3
- JavaScript
- jsPDF 2.5.1

## Project Structure

- `index.html` - Main application interface
- `script.js` - Main application JavaScript
- `styles.css` - Application styles
- `template-manager.js` - Template management functionality
- `backup/` - Contains backup and test files moved during cleanup

## Notes

- All monetary values are displayed in South African Rand (R)
- Empty fields are left blank in the PDF output
- The PDF is saved as `Invoice_<invoice-number>.pdf`
- Backup files and test files have been moved to the `backup/` directory for project organization
