/* Dashboard Styles for DocuGen Pro */

:root {
    --primary-color: #4a9eff;
    --secondary-color: #667eea;
    --accent-color: #764ba2;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    --border-color: #dee2e6;
    --text-color: #333;
    --text-muted: #6c757d;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --border-radius: 8px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Open Sans', sans-serif;
    background: #f5f7fa;
    color: var(--text-color);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Cosmic Web Background */
#cosmic-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

#canvas-bottom, #canvas-top {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Navigation */
.dashboard-nav {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    font-family: 'Montserrat', sans-serif;
    color: var(--primary-color);
    font-weight: 700;
}

.nav-user {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Dashboard Container */
.dashboard-container {
    display: flex;
    min-height: calc(100vh - 80px);
    max-width: 1400px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    margin-top: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 300px;
    background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
    border-right: 1px solid var(--border-color);
    padding: 2rem 1rem;
    overflow-y: auto;
    max-height: calc(100vh - 120px);
}

.sidebar-section {
    margin-bottom: 2rem;
}

.sidebar-section h3 {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

/* Document Menu */
.document-menu, .action-menu {
    list-style: none;
}

.document-menu li, .action-menu li {
    margin-bottom: 0.5rem;
}

.doc-link, .action-menu a {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0.75rem 1rem;
    text-decoration: none;
    color: var(--text-color);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    background: white;
    border: 1px solid transparent;
    text-align: left;
    gap: 2rem;
}

.doc-link:hover, .action-menu a:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(5px);
    box-shadow: var(--shadow);
}

.doc-link.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow);
}

.doc-icon, .action-icon {
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
    flex-shrink: 0;
}

/* Preview Header and Zoom Controls */
.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.preview-zoom-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow);
}

.zoom-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.4rem 0.6rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    min-width: 35px;
}

.zoom-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-1px);
}

#previewZoomLevel {
    font-weight: 600;
    color: var(--dark-color);
    min-width: 45px;
    text-align: center;
    font-size: 0.9rem;
}

/* High-quality rendering for zoomed documents */
.document-preview-container {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    image-rendering: pixelated;
    overflow: auto;
    max-height: 600px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: #f8f9fa;
    padding: 1rem;
    position: relative;
}

.document-preview-container canvas {
    image-rendering: auto;
    display: block;
    margin: 0 auto;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    background: white;
}

.document-preview-container img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    image-rendering: high-quality;
    display: block;
    margin: 0 auto;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

/* Smooth scrolling for zoomed content */
.document-preview-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.document-preview-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.document-preview-container::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

.document-preview-container::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* Preview Overlay for placed elements */
.preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none; /* Allow clicks to pass through to canvas */
    z-index: 100;
}

/* Placed elements on the overlay */
.placed-element {
    position: absolute;
    pointer-events: auto; /* Enable interaction with placed elements */
    user-select: none;
    transition: box-shadow 0.2s ease;
}

.placed-element:hover {
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3) !important;
}

.placed-element.signature-element img {
    pointer-events: none; /* Prevent image drag */
    user-select: none;
}

.placed-element.text-element span {
    pointer-events: none;
    user-select: none;
}

.placed-element.stamp-element span {
    pointer-events: none;
    user-select: none;
}

.placed-element.date-element span {
    pointer-events: none;
    user-select: none;
}

/* Company Status */
.company-status {
    margin-left: auto;
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    background: var(--success-color);
    color: white;
}

.company-status.empty {
    background: var(--warning-color);
    color: var(--dark-color);
}

.company-count {
    margin-left: auto;
    font-size: 0.8rem;
    color: var(--text-muted);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
    max-height: calc(100vh - 120px);
}

/* Dashboard Header */
.dashboard-header {
    margin-bottom: 2rem;
    text-align: center;
}

.dashboard-header h1 {
    font-family: 'Montserrat', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.dashboard-header p {
    font-size: 1.1rem;
    color: var(--text-muted);
    margin-bottom: 1.5rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Buttons */
.btn-primary, .btn-secondary, .btn-outline {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(74, 158, 255, 0.3);
}

.btn-secondary {
    background: var(--light-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--border-color);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

/* Info Cards */
.document-info {
    margin-bottom: 2rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.info-card {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    transition: transform 0.3s ease;
}

.info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.info-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.info-content h3 {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.info-content ul {
    list-style: none;
}

.info-content li {
    padding: 0.25rem 0;
    color: var(--text-muted);
}

.info-content li:before {
    content: "✓ ";
    color: var(--success-color);
    font-weight: bold;
    margin-right: 0.5rem;
}

/* Recent Section */
.recent-section {
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.section-header h2 {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark-color);
}

.btn-outline.small {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.recent-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

/* Quick Start */
.quick-start {
    margin-bottom: 2rem;
}

.quick-start h2 {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1.5rem;
    text-align: center;
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.step-card {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    border: 1px solid var(--border-color);
    transition: transform 0.3s ease;
}

.step-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.step-number {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    margin: 0 auto 1rem;
}

.step-card h3 {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.step-card p {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: var(--primary-color);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.3rem;
    font-weight: 600;
}

.close {
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover {
    opacity: 0.7;
}

/* Form Styles */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    padding: 1.5rem;
}

.form-section {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.form-section h3 {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--dark-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 158, 255, 0.1);
}

.form-actions {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Signature Tools Styles */
.signature-tools-content {
    max-width: 900px;
}

.signature-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    padding: 1.5rem;
}

.signature-section {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.signature-section h3 {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

.signature-style-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.style-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.style-group label {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 0.9rem;
}

.signature-canvas-container {
    text-align: center;
}

#signatureCanvas {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: white;
    cursor: crosshair;
}

#signatureCanvas.drawing {
    border-color: var(--primary-color);
}

.signature-controls {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Text Signature Styles */
.text-signature-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.text-signature-preview {
    min-height: 60px;
    background: white;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: var(--dark-color);
    padding: 1rem;
}

.text-signature-preview.cursive {
    font-family: 'Brush Script MT', cursive;
}

.text-signature-preview.script {
    font-family: 'Lucida Handwriting', cursive;
}

.text-signature-preview.elegant {
    font-family: 'Edwardian Script ITC', cursive;
}

/* Signature Upload Styles */
.signature-upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.signature-upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(74, 158, 255, 0.05);
}

.upload-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.signature-preview {
    margin-top: 1rem;
    text-align: center;
}

.signature-preview img {
    max-width: 200px;
    max-height: 100px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

/* Saved Signatures Styles */
.saved-signatures {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.signatures-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.signature-item {
    background: white;
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    text-align: center;
    transition: transform 0.3s ease;
}

.signature-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.signature-item img {
    max-width: 100%;
    max-height: 60px;
    margin-bottom: 0.5rem;
}

.signature-info h4 {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
    color: var(--dark-color);
}

.signature-info small {
    color: var(--text-muted);
    font-size: 0.8rem;
}

.signature-actions {
    margin-top: 0.5rem;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.btn-danger:hover {
    background: #c82333;
}

/* Company Logo Styles */
.company-logo-preview {
    width: 120px;
    height: 120px;
    margin: 0 auto;
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    background: white;
}

.company-logo-preview.round {
    border-radius: 50%;
}

.company-logo-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.logo-placeholder {
    text-align: center;
    color: var(--text-muted);
    font-size: 0.8rem;
}

.logo-upload-success {
    position: absolute;
    top: 5px;
    right: 5px;
    background: var(--success-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}

/* Document Sharing Styles */
.document-sharing-content {
    max-width: 1000px;
}

.document-sharing-body {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    padding: 1.5rem;
}

.sharing-options {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.sharing-section {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.sharing-section h3 {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

.btn-whatsapp, .btn-whatsapp-web {
    background: #25d366;
    color: white;
    margin-right: 0.5rem;
}

.btn-whatsapp:hover, .btn-whatsapp-web:hover {
    background: #128c7e;
}

.quick-share-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
}

.quick-share-btn {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.quick-share-btn:hover {
    background: var(--primary-color);
    color: white;
}

.analytics-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.analytics-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.analytics-icon {
    font-size: 1.5rem;
}

.analytics-info {
    flex: 1;
}

.analytics-info h4 {
    margin-bottom: 0.25rem;
    color: var(--dark-color);
}

.analytics-info p {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.sharing-preview {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.sharing-document-preview {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-placeholder {
    text-align: center;
    color: var(--text-muted);
}

.preview-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-container {
        flex-direction: column;
        margin: 1rem;
        border-radius: 10px;
    }

    .sidebar {
        width: 100%;
        max-height: none;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }

    .main-content {
        padding: 1rem;
    }

    .dashboard-header h1 {
        font-size: 2rem;
    }

    .info-grid,
    .steps-grid {
        grid-template-columns: 1fr;
    }

    .header-actions {
        flex-direction: column;
        align-items: center;
    }

    .signature-options {
        grid-template-columns: 1fr;
    }

    .document-sharing-body {
        grid-template-columns: 1fr;
    }

    .signature-style-controls {
        grid-template-columns: 1fr;
    }

    .quick-share-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Realistic Document Preview Styles */
.preview-content {
    max-width: 90%;
    max-height: 90%;
    width: 900px;
}

.realistic-preview {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    max-height: 70vh;
    overflow-y: auto;
}

.realistic-preview .document-header {
    border-bottom: 2px solid #eee;
    padding-bottom: 20px;
    margin-bottom: 20px;
}

.realistic-preview .preview-logo {
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.realistic-preview h1 {
    font-weight: bold;
    letter-spacing: 1px;
}

.realistic-preview h3 {
    margin-bottom: 10px;
    font-weight: 600;
}

.realistic-preview table {
    font-size: 14px;
}

.realistic-preview .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.realistic-preview .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Document-specific styling */
.invoice-preview .document-header h1 {
    color: #2c3e50;
}

.receipt-preview .document-header h1 {
    color: #28a745;
}

.quotation-preview .document-header h1 {
    color: #f39c12;
}

.artist-agreement-preview .document-header h1 {
    color: #8e44ad;
}

.technical-rider-preview .document-header h1 {
    color: #e74c3c;
}

.annexure-preview .document-header h1 {
    color: #6c757d;
}

.contract-preview .document-header h1 {
    color: #2c3e50;
}

/* Responsive preview */
@media (max-width: 768px) {
    .realistic-preview {
        padding: 20px;
        font-size: 14px;
    }

    .realistic-preview .parties-section {
        flex-direction: column;
    }

    .realistic-preview .parties-section > div {
        margin: 10px 0 !important;
    }
}

/* Document Import Styles */
.document-import-content {
    max-width: 1200px;
    max-height: 90vh;
    overflow-y: auto;
}

.document-import-body {
    padding: 20px;
}

.import-layout {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 30px;
    margin-bottom: 20px;
}

.import-left-panel {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.import-right-panel {
    background: #ffffff;
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.import-section {
    margin-bottom: 25px;
}

.import-section h3 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
}

.upload-area {
    border: 3px dashed #007bff;
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    background: #f8f9ff;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover,
.upload-area.drag-over {
    border-color: #28a745;
    background: #f0fff0;
}

.upload-placeholder {
    cursor: pointer;
}

.upload-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.upload-placeholder h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 1.2rem;
}

.upload-placeholder p {
    margin: 5px 0;
    color: #666;
    font-size: 0.9rem;
}

.file-size-limit {
    font-size: 0.8rem !important;
    color: #999 !important;
}

.uploaded-file-info {
    background: #e8f5e8;
    border: 2px solid #28a745;
    border-radius: 8px;
    padding: 15px;
}

.file-details {
    display: flex;
    align-items: center;
    gap: 15px;
}

.file-icon {
    font-size: 32px;
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.file-size {
    color: #666;
    font-size: 0.9rem;
}

.remove-file-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
}

.remove-file-btn:hover {
    background: #c82333;
    transform: scale(1.05);
}

.tool-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.tool-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tool-card:hover {
    border-color: #007bff;
    background: #f8f9ff;
    transform: translateY(-2px);
}

.tool-card.selected {
    border-color: #28a745;
    background: #e8f5e8;
}

.tool-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.tool-card h4 {
    margin: 0 0 5px 0;
    color: #2c3e50;
    font-size: 0.9rem;
    font-weight: 600;
}

.tool-card p {
    margin: 0;
    color: #666;
    font-size: 0.8rem;
}

.drag-indicator {
    position: absolute;
    top: 5px;
    right: 5px;
    font-size: 12px;
    opacity: 0.6;
    transition: all 0.3s ease;
}

.draggable-signature,
.draggable-tool {
    position: relative;
    cursor: grab;
}

.draggable-signature:active,
.draggable-tool:active {
    cursor: grabbing;
}

.draggable-signature:hover .drag-indicator,
.draggable-tool:hover .drag-indicator {
    opacity: 1;
    transform: scale(1.2);
}

.signature-item {
    position: relative;
}

.signature-item .drag-indicator {
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 10px;
}

.signature-library {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 10px;
    background: white;
}

.signature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.signature-item:hover {
    background: #f8f9fa;
}

.signature-item img {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 2px;
}

.signature-item span {
    font-size: 0.9rem;
    color: #2c3e50;
}

.no-signatures {
    text-align: center;
    color: #999;
    font-style: italic;
    margin: 20px 0;
}

.document-preview-section h3 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
}

.document-preview-container {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    min-height: 400px;
    background: #f8f9fa;
    position: relative;
    overflow: auto;
}

.preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    text-align: center;
}

.preview-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.preview-placeholder h4 {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 1.2rem;
}

.preview-placeholder p {
    margin: 0;
    color: #999;
    font-size: 1rem;
}

.document-preview-wrapper {
    position: relative;
    padding: 20px;
    text-align: center;
}

.preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    cursor: crosshair;
}

.placed-element {
    z-index: 10;
    user-select: none;
}

.placed-element:hover {
    border-color: #28a745 !important;
    background: rgba(40, 167, 69, 0.1) !important;
}

.pdf-preview-placeholder,
.generic-preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin: 20px;
}

.pdf-icon,
.doc-icon {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.7;
}

.pdf-note,
.doc-note {
    color: #666;
    font-style: italic;
    font-size: 0.9rem;
}

.text-preview {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 20px;
    text-align: left;
    max-height: 400px;
    overflow-y: auto;
}

.text-preview pre {
    margin: 0;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* PDF Preview Styles */
.pdf-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin: 20px;
}

.loading-spinner {
    font-size: 32px;
    margin-bottom: 15px;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.pdf-preview-header {
    text-align: center;
    margin-bottom: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.pdf-preview-header h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 1rem;
}

.pdf-preview-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 8px;
    margin: 20px;
}

.error-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.error-message {
    color: #e53e3e;
    font-weight: 600;
    margin: 5px 0;
}

.error-note {
    color: #666;
    font-style: italic;
    font-size: 0.9rem;
    margin-top: 10px;
}

/* Import Document Responsive Design */
@media (max-width: 768px) {
    .import-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .import-left-panel {
        order: 2;
    }

    .import-right-panel {
        order: 1;
    }

    .tool-grid {
        grid-template-columns: 1fr;
    }

    .document-import-content {
        max-width: 95vw;
    }
}


.document-pimping-content {
    max-width: 1600px;
    height: 95vh;
    max-height: 950px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 20px 30px;
    border-bottom: 3px solid #3498db;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.pimping-layout {
    display: flex;
    height: calc(100% - 80px);
    gap: 0;
    background: #f8f9fa;
}

.pimping-left-panel {
    flex: 0 0 420px;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    overflow-y: auto;
    padding: 0;
    border-right: 3px solid #3498db;
}

.pimping-right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0;
    background: #ecf0f1;
}

.pimping-section {
    background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    border-radius: 0;
    padding: 25px;
    margin-bottom: 0;
    border-bottom: 2px solid #3498db;
    position: relative;
}

.pimping-section:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #3498db 0%, #2980b9 100%);
}

.pimping-section h3 {
    margin: 0 0 20px 0;
    color: #ecf0f1;
    font-size: 1.2rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

.enhancement-group {
    margin-bottom: 30px;
    background: rgba(52, 73, 94, 0.3);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(52, 152, 219, 0.3);
    backdrop-filter: blur(10px);
}

.enhancement-group h4 {
    margin: 0 0 18px 0;
    color: #ecf0f1;
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.enhancement-group h4:before {
    content: '';
    width: 3px;
    height: 20px;
    background: linear-gradient(180deg, #3498db 0%, #2980b9 100%);
    border-radius: 2px;
}

/* Frame Options - Professional Editor Style */
.frame-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.frame-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 12px;
    border: 2px solid rgba(52, 152, 219, 0.3);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, rgba(236, 240, 241, 0.1) 0%, rgba(189, 195, 199, 0.1) 100%);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.frame-option:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.2), transparent);
    transition: left 0.6s;
}

.frame-option:hover:before {
    left: 100%;
}

.frame-option:hover {
    border-color: #3498db;
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1) 0%, rgba(41, 128, 185, 0.1) 100%);
}

.frame-option.active {
    border-color: #3498db;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(52, 152, 219, 0.4);
}

.frame-preview {
    width: 50px;
    height: 35px;
    border-radius: 8px;
    margin-bottom: 12px;
    position: relative;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.frame-option:hover .frame-preview {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.frame-none {
    background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
    border: 2px solid #95a5a6;
}

.frame-elegant {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: 3px solid #4a5568;
    box-shadow: inset 0 0 0 2px rgba(255,255,255,0.4), 0 4px 12px rgba(102, 126, 234, 0.3);
}

.frame-modern {
    background: linear-gradient(135deg, #4a9eff 0%, #00d4ff 100%);
    border: 2px solid #0099cc;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(74, 158, 255, 0.3);
}

.frame-classic {
    background: linear-gradient(135deg, #8b4513 0%, #daa520 100%);
    border: 4px double #8b4513;
    box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
}

.frame-luxury {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    border: 3px solid #b8860b;
    box-shadow: 0 0 0 2px #ffd700, inset 0 0 0 2px rgba(0,0,0,0.1), 0 4px 12px rgba(255, 215, 0, 0.4);
}

.frame-creative {
    background: linear-gradient(45deg, #ff6b6b 0%, #4ecdc4 25%, #45b7d1 50%, #96ceb4 75%, #ffeaa7 100%);
    border: 3px dashed #333;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

.frame-option span {
    font-size: 0.9rem;
    font-weight: 600;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* Background Options */
.background-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.option-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.option-item:hover {
    border-color: var(--primary-color);
    background: #f8f9ff;
}

.option-item input[type="radio"]:checked + .option-label {
    color: var(--primary-color);
    font-weight: 600;
}

.option-item input[type="radio"] {
    margin-right: 10px;
}

.option-label {
    font-size: 0.9rem;
}

/* Control Groups - Professional Editor Style */
.control-group {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 18px;
    background: rgba(44, 62, 80, 0.3);
    padding: 15px;
    border-radius: 10px;
    border: 1px solid rgba(52, 152, 219, 0.2);
}

.control-group label {
    min-width: 120px;
    font-size: 0.95rem;
    font-weight: 600;
    color: #ecf0f1;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.control-group input[type="range"] {
    flex: 1;
    margin: 0 15px;
    height: 6px;
    background: linear-gradient(90deg, #34495e 0%, #3498db 100%);
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

.control-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
    transition: all 0.3s ease;
}

.control-group input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.6);
}

.control-group input[type="color"] {
    width: 50px;
    height: 35px;
    border: 2px solid #3498db;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
}

.control-group input[type="color"]:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.control-group select {
    padding: 10px 15px;
    border: 2px solid #3498db;
    border-radius: 8px;
    font-size: 0.9rem;
    background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
    color: #2c3e50;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-group select:hover {
    border-color: #2980b9;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
}

.control-group span {
    min-width: 50px;
    color: #3498db;
    font-weight: 700;
    font-size: 0.9rem;
}

/* New Feature Badge */
.new-feature-badge {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 8px;
    font-weight: 600;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.doc-pimp-link {
    position: relative;
    overflow: visible;
}

.doc-pimp-link:hover .new-feature-badge {
    animation: bounce 0.6s ease;
}

@keyframes bounce {
    0%, 20%, 60%, 100% { transform: translateY(0) scale(1); }
    40% { transform: translateY(-3px) scale(1.1); }
    80% { transform: translateY(-1px) scale(1.05); }
}

/* Preview Section - Professional Editor Style */
.preview-section {
    flex: 1;
    background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
    border-radius: 0;
    padding: 30px;
    border-bottom: 3px solid #3498db;
    position: relative;
}

.preview-section:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3498db 0%, #2980b9 100%);
}

.preview-section h3 {
    margin: 0 0 25px 0;
    color: #2c3e50;
    font-size: 1.4rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.preview-section h3:before {
    content: '👁️';
    font-size: 1.5rem;
}

/* A4 Preview Container - Proper A4 Dimensions */
.preview-container {
    width: 100%;
    height: 600px;
    border: 3px solid #3498db;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    position: relative;
    overflow: auto;
    box-shadow: 0 15px 35px rgba(52, 152, 219, 0.2);
    transition: all 0.3s ease;
    padding: 20px;
}

.preview-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 45px rgba(52, 152, 219, 0.3);
}

/* A4 Document Preview - Real A4 Proportions (210mm x 297mm = 1:1.414 ratio) */
.a4-document-preview {
    width: 420px;
    height: 594px;
    background: white;
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    margin: 0 auto;
    transform-origin: center;
    transition: transform 0.3s ease;
}

.a4-document-preview:hover {
    transform: scale(1.02);
}

/* Responsive A4 scaling for smaller screens */
@media (max-width: 1200px) {
    .a4-document-preview {
        width: 350px;
        height: 495px;
    }
}

@media (max-width: 900px) {
    .a4-document-preview {
        width: 280px;
        height: 396px;
    }
}

.preview-placeholder {
    text-align: center;
    color: #7f8c8d;
    padding: 40px;
}

.preview-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.7;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes bounce {
    0%, 20%, 60%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    80% { transform: translateY(-5px); }
}

.preview-placeholder h4 {
    margin: 0 0 15px 0;
    color: #34495e;
    font-size: 1.3rem;
    font-weight: 600;
}

.preview-placeholder p {
    margin: 0;
    font-size: 1rem;
    color: #7f8c8d;
}

.preview-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
    padding: 15px;
    background: rgba(52, 152, 219, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(52, 152, 219, 0.2);
}

.preview-controls button {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
}

.preview-controls button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.preview-controls span {
    color: #2c3e50;
    font-weight: 700;
    font-size: 1.1rem;
    min-width: 60px;
    text-align: center;
}

/* Download Section - Professional Style */
.download-section {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border-radius: 0;
    padding: 30px;
    border-top: 3px solid #3498db;
    color: white;
}

.download-section h3 {
    margin: 0 0 25px 0;
    color: #ecf0f1;
    font-size: 1.4rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.download-section h3:before {
    content: '💾';
    font-size: 1.5rem;
}

.download-options {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
}

.download-options button {
    flex: 1;
    padding: 15px 25px;
    border: none;
    border-radius: 12px;
    font-weight: 700;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-success {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
}

.btn-success:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(39, 174, 96, 0.4);
}

.btn-outline {
    background: transparent;
    color: #3498db;
    border: 2px solid #3498db;
}

.btn-outline:hover {
    background: #3498db;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

.enhancement-summary {
    background: rgba(236, 240, 241, 0.1);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid rgba(52, 152, 219, 0.3);
    backdrop-filter: blur(10px);
}

.enhancement-summary h4 {
    margin: 0 0 15px 0;
    color: #ecf0f1;
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.enhancement-summary ul {
    margin: 0;
    padding-left: 25px;
}

.enhancement-summary li {
    margin-bottom: 8px;
    font-size: 0.95rem;
    color: #bdc3c7;
    font-weight: 500;
}

/* Pimping Actions - Professional Style */
.pimping-actions {
    display: flex;
    gap: 20px;
    margin-top: 30px;
    padding-top: 25px;
    border-top: 2px solid rgba(52, 152, 219, 0.3);
}

.pimping-actions button {
    flex: 1;
    padding: 15px 25px;
    border: none;
    border-radius: 12px;
    font-weight: 700;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(149, 165, 166, 0.3);
}

.btn-secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(149, 165, 166, 0.4);
}

.btn-primary {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(52, 152, 219, 0.4);
}

.btn-primary:disabled {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Upload Area - Professional Style */
.upload-area {
    border: 3px dashed #3498db;
    border-radius: 16px;
    padding: 40px 20px;
    text-align: center;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.05) 0%, rgba(41, 128, 185, 0.05) 100%);
    cursor: pointer;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.upload-area:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.1), transparent);
    transition: left 0.8s;
}

.upload-area:hover:before {
    left: 100%;
}

.upload-area:hover {
    border-color: #2980b9;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1) 0%, rgba(41, 128, 185, 0.1) 100%);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(52, 152, 219, 0.2);
}

.upload-placeholder {
    position: relative;
    z-index: 1;
}

.upload-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #3498db;
    animation: bounce 2s infinite;
}

.upload-placeholder h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 1.4rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.upload-placeholder p {
    margin: 0 0 10px 0;
    color: #7f8c8d;
    font-size: 1rem;
    font-weight: 500;
}

.file-size-limit {
    color: #95a5a6;
    font-size: 0.9rem;
    font-style: italic;
}

/* Uploaded File Info - Professional Style */
.uploaded-file-info {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    border-radius: 12px;
    padding: 20px;
    color: white;
    box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
}

.file-details {
    display: flex;
    align-items: center;
    gap: 15px;
}

.file-icon {
    font-size: 2rem;
    opacity: 0.9;
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.file-size {
    opacity: 0.8;
    font-size: 0.9rem;
}

.remove-file-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    font-weight: bold;
    transition: all 0.3s ease;
}

.remove-file-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}














/* Independent Document Management Styles */
.document-selector {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 10px;
    margin: 10px 0;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.selector-group {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.selector-group label {
    color: #fff;
    font-weight: bold;
    white-space: nowrap;
}

.selector-group select {
    flex: 1;
    min-width: 200px;
    padding: 5px 10px;
    border-radius: 4px;
    border: 1px solid #ddd;
    background: white;
}

.document-count {
    color: #ccc;
    font-size: 12px;
    white-space: nowrap;
}

.document-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.document-card {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s ease;
}

.document-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 8px rgba(0,123,255,0.2);
}

.document-card.active {
    border-color: #28a745;
    background: #f8fff9;
    box-shadow: 0 4px 8px rgba(40,167,69,0.2);
}

.document-icon {
    font-size: 48px;
    text-align: center;
    margin-bottom: 10px;
}

.document-info h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 16px;
    word-break: break-word;
}

.document-info p {
    margin: 5px 0;
    color: #666;
    font-size: 14px;
}

.document-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.document-actions button {
    flex: 1;
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}



/* Simplified PDF Signing Modal Styles */
.pdf-upload-section {
    margin-bottom: 2rem;
}

.pdf-upload-section h4 {
    margin: 0 0 1rem 0;
    color: #495057;
    font-size: 1.1rem;
    font-weight: 600;
}

.signature-section {
    margin-bottom: 2rem;
}

.signature-section h4 {
    margin: 0 0 1rem 0;
    color: #495057;
    font-size: 1.1rem;
    font-weight: 600;
}

.signature-tools {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.signature-btn {
    background: #007bff;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.signature-btn:hover {
    background: #0056b3;
}

.signature-btn.active {
    background: #28a745;
}

.signature-library-toggle .tool-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 0.75rem 1rem;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.signature-library-toggle .tool-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.signature-library-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

.signature-library-header h5 {
    margin: 0;
    color: #495057;
    font-size: 1rem;
    font-weight: 600;
}

.signature-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
    max-height: 200px;
    overflow-y: auto;
}

.zoom-level {
    font-weight: 600;
    color: #495057;
    min-width: 50px;
    text-align: center;
}

#pdfCanvas {
    width: 794px;  /* A4 width at 96 DPI */
    height: 1123px; /* A4 height at 96 DPI */
    max-width: 100%;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: white;
    cursor: crosshair;
    margin: 0 auto;
    display: block;
}

/* ===== CLIENT PROFILES MODAL STYLES ===== */
.client-profiles-content {
    max-width: 1200px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
}

.client-profiles-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.client-list-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.section-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2em;
    font-weight: 600;
}

.client-search {
    margin-bottom: 20px;
}

.client-search input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.client-search input:focus {
    outline: none;
    border-color: #4a9eff;
    box-shadow: 0 0 0 3px rgba(74, 158, 255, 0.1);
}

.clients-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

.client-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.client-card:hover {
    border-color: #4a9eff;
    box-shadow: 0 4px 12px rgba(74, 158, 255, 0.15);
    transform: translateY(-2px);
}

.client-card.selected {
    border-color: #4a9eff;
    background: linear-gradient(135deg, #4a9eff 0%, #667eea 100%);
    color: white;
    box-shadow: 0 6px 20px rgba(74, 158, 255, 0.3);
}

.client-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.client-name {
    font-size: 1.1em;
    font-weight: 600;
    margin: 0;
    color: #2c3e50;
}

.client-card.selected .client-name {
    color: white;
}

.client-actions {
    display: flex;
    gap: 8px;
}

.client-action-btn {
    background: none;
    border: none;
    padding: 6px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.client-action-btn:hover {
    background: rgba(74, 158, 255, 0.1);
}

.client-card.selected .client-action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.client-info {
    font-size: 13px;
    color: #6c757d;
    line-height: 1.4;
}

.client-card.selected .client-info {
    color: rgba(255, 255, 255, 0.9);
}

.client-form-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.client-form {
    max-width: 800px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 14px;
}

.required {
    color: #e74c3c;
    margin-right: 4px;
}

.form-group input,
.form-group textarea,
.form-group select {
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #4a9eff;
    box-shadow: 0 0 0 3px rgba(74, 158, 255, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 2px solid #e9ecef;
}

.btn-primary,
.btn-secondary {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #4a9eff 0%, #667eea 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #3a8eef 0%, #5a6eda 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(74, 158, 255, 0.3);
}

.btn-secondary {
    background: #f8f9fa;
    color: #6c757d;
    border: 2px solid #e9ecef;
}

.btn-secondary:hover {
    background: #e9ecef;
    color: #495057;
    transform: translateY(-1px);
}

/* ===== COMPANY MANAGEMENT MODAL STYLES ===== */
.company-management-content {
    max-width: 1400px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
}

.company-management-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.company-selector-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
}

.companies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.company-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 25px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.company-card:hover {
    border-color: #4a9eff;
    box-shadow: 0 6px 20px rgba(74, 158, 255, 0.15);
    transform: translateY(-3px);
}

.company-card.active {
    border-color: #28a745;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.company-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.company-name {
    font-size: 1.3em;
    font-weight: 700;
    margin: 0;
    color: #2c3e50;
}

.company-card.active .company-name {
    color: white;
}

.company-status {
    background: #28a745;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.company-card.active .company-status {
    background: rgba(255, 255, 255, 0.2);
}

.company-details {
    font-size: 14px;
    color: #6c757d;
    line-height: 1.5;
}

.company-card.active .company-details {
    color: rgba(255, 255, 255, 0.9);
}

.company-form-section {
    background: white;
    border-radius: 12px;
    padding: 30px;
    border: 1px solid #e9ecef;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.company-form {
    max-width: 900px;
}

.form-tabs {
    display: flex;
    gap: 2px;
    margin-bottom: 30px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
}

.tab-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    color: #6c757d;
    font-weight: 600;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background: white;
    color: #4a9eff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-btn:hover:not(.active) {
    background: rgba(74, 158, 255, 0.1);
    color: #4a9eff;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.logo-preview {
    margin-top: 15px;
    padding: 20px;
    border: 2px dashed #e9ecef;
    border-radius: 8px;
    text-align: center;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
}

.logo-preview img {
    max-width: 200px;
    max-height: 100px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.logo-preview.empty {
    color: #6c757d;
    font-style: italic;
}

/* ===== COMPANY SETUP SECTION ENHANCEMENTS ===== */
.action-menu li a {
    transition: all 0.3s ease;
}

.action-menu li a:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transform: translateX(3px);
}

/* Special styling for Add Company button */
.action-menu li a[onclick*="showAddCompanyForm"] {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 8px;
    margin-bottom: 8px;
}

.action-menu li a[onclick*="showAddCompanyForm"]:hover {
    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    transform: translateX(5px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.action-menu li a[onclick*="showAddCompanyForm"] .action-icon {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
}

.company-status.new {
    background: #28a745 !important;
    color: white !important;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Enhanced setup notice */
.setup-notice {
    position: relative;
    overflow: hidden;
}

.setup-notice::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .clients-grid,
    .companies-grid {
        grid-template-columns: 1fr;
    }

    .client-profiles-content,
    .company-management-content {
        width: 98%;
        margin: 1%;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn-primary,
    .btn-secondary {
        width: 100%;
        justify-content: center;
    }

    .action-menu li a[onclick*="showAddCompanyForm"] {
        text-align: center;
    }
}
