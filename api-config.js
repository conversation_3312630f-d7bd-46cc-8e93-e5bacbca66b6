/**
 * 🔑 API Configuration File for DocuGen Pro
 * Add your API keys here and they will be automatically loaded
 */

// 🚨 SECURITY NOTE: 
// This file contains API keys. Do NOT commit this to version control!
// Add api-config.js to your .gitignore file

const API_CONFIG = {
    // 🧠 DeepSeek API Configuration
    deepseek: {
        apiKey: '***********************************', // Replace with your actual API key
        enabled: true,
        model: 'deepseek-chat',
        maxTokens: 2000,
        temperature: 0.7
    },
    
    // 🤖 OpenAI API Configuration (Alternative)
    openai: {
        apiKey: 'YOUR_OPENAI_API_KEY_HERE', // Replace with your actual API key
        enabled: false,
        model: 'gpt-4',
        maxTokens: 2000,
        temperature: 0.7
    },
    
    // ⚡ Groq API Configuration (Fast Inference)
    groq: {
        apiKey: 'YOUR_GROQ_API_KEY_HERE', // Replace with your actual API key
        enabled: false,
        model: 'mixtral-8x7b-32768',
        maxTokens: 2000,
        temperature: 0.7
    }
};

// 🚀 Auto-setup function - automatically connects the enabled API
async function autoSetupAI() {
    console.log('🤖 Auto-setting up AI assistant...');
    
    // Try DeepSeek first
    if (API_CONFIG.deepseek.enabled && API_CONFIG.deepseek.apiKey !== 'YOUR_DEEPSEEK_API_KEY_HERE') {
        console.log('🧠 Setting up DeepSeek...');
        try {
            window.aiAssistant = await AIAssistant.setupDeepSeek(API_CONFIG.deepseek.apiKey);
            if (window.aiAssistant) {
                console.log('✅ DeepSeek connected successfully!');
                updateAIStatus('✅ DeepSeek AI Connected');
                return true;
            }
        } catch (error) {
            console.error('❌ DeepSeek setup failed:', error);
        }
    }
    
    // Try OpenAI if DeepSeek failed
    if (API_CONFIG.openai.enabled && API_CONFIG.openai.apiKey !== 'YOUR_OPENAI_API_KEY_HERE') {
        console.log('🤖 Setting up OpenAI...');
        try {
            window.aiAssistant = await AIAssistant.setupOpenAI(API_CONFIG.openai.apiKey);
            if (window.aiAssistant) {
                console.log('✅ OpenAI connected successfully!');
                updateAIStatus('✅ OpenAI Connected');
                return true;
            }
        } catch (error) {
            console.error('❌ OpenAI setup failed:', error);
        }
    }
    
    // Try Groq if others failed
    if (API_CONFIG.groq.enabled && API_CONFIG.groq.apiKey !== 'YOUR_GROQ_API_KEY_HERE') {
        console.log('⚡ Setting up Groq...');
        try {
            window.aiAssistant = await AIAssistant.setupGroq(API_CONFIG.groq.apiKey);
            if (window.aiAssistant) {
                console.log('✅ Groq connected successfully!');
                updateAIStatus('✅ Groq AI Connected');
                return true;
            }
        } catch (error) {
            console.error('❌ Groq setup failed:', error);
        }
    }
    
    console.log('⚠️ No AI assistant could be connected');
    updateAIStatus('⚠️ No AI Connected - <a href="ai-setup.html" target="_blank">Setup Required</a>');
    return false;
}

// 🔄 Update AI status in the UI
function updateAIStatus(message) {
    const statusElement = document.getElementById('aiStatus');
    const actionsElement = document.getElementById('aiQuickActions');
    
    if (statusElement) {
        statusElement.innerHTML = `<p>${message}</p>`;
    }
    
    if (actionsElement && window.aiAssistant) {
        actionsElement.style.display = 'block';
    }
}

// 🧪 Test AI connection
async function testAIConnection() {
    if (!window.aiAssistant) {
        console.log('❌ No AI assistant connected');
        return false;
    }
    
    try {
        const result = await window.aiAssistant.sendRequest('Hello! Can you help me debug JavaScript code for an invoice system?');
        if (result.success) {
            console.log('✅ AI test successful:', result.response.substring(0, 100) + '...');
            return true;
        } else {
            console.error('❌ AI test failed:', result.error);
            return false;
        }
    } catch (error) {
        console.error('❌ AI test error:', error);
        return false;
    }
}

// 🎯 Quick AI functions for common tasks
const QuickAI = {
    // Debug the sharing visibility issue
    async debugSharing() {
        if (!window.aiAssistant) {
            alert('Please configure your API key in api-config.js first');
            return;
        }
        
        const htmlStructure = `<div id="quickSharingOptions" class="quick-sharing-section" style="display: none;">`;
        const cssRules = `.quick-sharing-section { margin-top: 25px; padding: 25px; }`;
        const jsCode = `quickSharingSection.style.cssText = 'display: block !important;';`;
        
        console.log('🔧 AI debugging sharing issue...');
        const result = await window.aiAssistant.fixSharingIssue(htmlStructure, cssRules, jsCode);
        
        if (result.success) {
            console.log('✅ AI Debug Result:', result.response);
            return result.response;
        } else {
            console.error('❌ AI Debug Failed:', result.error);
            return null;
        }
    },
    
    // Optimize invoice code
    async optimizeCode(codeSection, feature) {
        if (!window.aiAssistant) {
            alert('Please configure your API key in api-config.js first');
            return;
        }
        
        console.log('🚀 AI optimizing code...');
        const result = await window.aiAssistant.optimizeInvoiceCode(codeSection, feature);
        
        if (result.success) {
            console.log('✅ AI Optimization Result:', result.response);
            return result.response;
        } else {
            console.error('❌ AI Optimization Failed:', result.error);
            return null;
        }
    },
    
    // Generate new features
    async generateFeature(description, requirements = []) {
        if (!window.aiAssistant) {
            alert('Please configure your API key in api-config.js first');
            return;
        }
        
        console.log('💡 AI generating feature...');
        const result = await window.aiAssistant.generateInvoiceFeature(description, requirements);
        
        if (result.success) {
            console.log('✅ AI Feature Generation Result:', result.response);
            return result.response;
        } else {
            console.error('❌ AI Feature Generation Failed:', result.error);
            return null;
        }
    }
};

// 🚀 Auto-initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for other scripts to load
    setTimeout(async () => {
        if (typeof AIAssistant !== 'undefined') {
            await autoSetupAI();
            
            // Test the connection
            if (window.aiAssistant) {
                await testAIConnection();
            }
        } else {
            console.log('⚠️ AIAssistant class not found. Make sure ai-assistant.js is loaded.');
        }
    }, 1000);
});

// 🌐 Make QuickAI available globally
window.QuickAI = QuickAI;

// 📤 Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { API_CONFIG, autoSetupAI, QuickAI };
}
