<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload & Sign Document - DocuSign Pro</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .upload-sign-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 80px rgba(102, 126, 234, 0.3);
            overflow: hidden;
            max-width: 1200px;
            width: 100%;
            min-height: 700px;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 30px;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            gap: 20px;
        }

        .step {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 20px;
            border-radius: 25px;
            background: #f8f9fa;
            color: #6c757d;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .step.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .step.completed {
            background: #28a745;
            color: white;
        }

        .step-content {
            flex: 1;
            display: none;
        }

        .step-content.active {
            display: block;
        }

        /* Upload Step */
        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 60px 30px;
            text-align: center;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        }

        .upload-area.dragover {
            border-color: #28a745;
            background: rgba(40, 167, 69, 0.1);
        }

        .upload-icon {
            font-size: 48px;
            margin-bottom: 20px;
            color: #667eea;
        }

        .upload-text h3 {
            font-size: 24px;
            color: #333;
            margin-bottom: 10px;
        }

        .upload-text p {
            color: #6c757d;
            font-size: 16px;
            margin-bottom: 20px;
        }

        .file-input {
            display: none;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(40, 167, 69, 0.4);
        }

        /* Document Preview */
        .document-preview {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .document-canvas-container {
            position: relative;
            display: inline-block;
            background: white;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin: 20px 0;
        }

        #documentCanvas {
            border-radius: 10px;
            cursor: crosshair;
            max-width: 100%;
            height: auto;
        }

        #documentCanvas.moving {
            cursor: move;
        }

        #documentCanvas.deleting {
            cursor: pointer;
        }

        /* Signature Tools */
        .signature-tools {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
        }

        .signature-tools h3 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .signature-canvas-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border: 2px solid #e9ecef;
        }

        #signatureCanvas {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            cursor: crosshair;
        }

        .signature-controls {
            margin-top: 15px;
            display: flex;
            flex-direction: column;
            gap: 15px;
            align-items: center;
        }

        .size-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255, 255, 255, 0.1);
            padding: 10px 15px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .size-controls label {
            font-weight: 600;
            color: #333;
            margin: 0;
            font-size: 14px;
        }

        .btn-size {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            border: 2px solid #007bff;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-size:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
        }

        .btn-size:active {
            transform: scale(0.95);
        }

        .size-display {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
            min-width: 80px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(40, 167, 69, 0.3);
        }

        .action-controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .additional-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 2px solid rgba(255, 255, 255, 0.1);
            flex-wrap: wrap;
        }

        .btn-donate {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .btn-donate:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
            background: linear-gradient(135deg, #ee5a24, #ff6b6b);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(116, 185, 255, 0.4);
            background: linear-gradient(135deg, #0984e3, #74b9ff);
        }

        /* Donate Modal Styles */
        .donate-modal-content {
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        .donate-header {
            text-align: center;
            padding: 30px 30px 20px;
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
        }

        .donate-header h2 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            background: linear-gradient(45deg, #fff, #f8f9fa);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .donate-content {
            padding: 30px;
        }

        .donate-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .donate-feature {
            text-align: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .donate-feature h4 {
            margin: 0 0 10px 0;
            font-size: 1.1rem;
        }

        .donate-feature p {
            margin: 0;
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .payment-methods {
            margin-bottom: 30px;
        }

        .payment-methods h3 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .payment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 15px;
        }

        .payment-btn {
            background: rgba(255, 255, 255, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 10px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            font-weight: 600;
        }

        .payment-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .payment-icon {
            font-size: 1.5rem;
        }

        .donation-amounts h4 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.2rem;
        }

        .amount-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            align-items: center;
        }

        .amount-btn {
            background: rgba(255, 255, 255, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            min-width: 60px;
        }

        .amount-btn:hover, .amount-btn.active {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-1px);
        }

        .custom-amount {
            background: rgba(255, 255, 255, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 15px;
            border-radius: 25px;
            width: 120px;
            text-align: center;
            font-weight: 600;
        }

        .custom-amount::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .custom-amount:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.2);
        }

        /* Page Navigation Styles */
        .page-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 15px 20px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(102, 126, 234, 0.1);
        }

        .page-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        #pageInfo {
            font-weight: 600;
            color: #495057;
            font-size: 1.1rem;
        }

        .page-limit-warning {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 0.9rem;
            color: #856404;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .warning-icon {
            font-size: 1.1rem;
        }

        .donate-link {
            color: #dc3545;
            font-weight: 600;
            text-decoration: none;
            border-bottom: 1px solid #dc3545;
            transition: all 0.3s ease;
        }

        .donate-link:hover {
            color: #c82333;
            border-bottom-color: #c82333;
            background: rgba(220, 53, 69, 0.1);
            padding: 2px 4px;
            border-radius: 4px;
        }

        .page-controls {
            display: flex;
            gap: 10px;
        }

        .btn-nav {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-nav:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, #764ba2, #667eea);
        }

        .btn-nav:disabled {
            background: #6c757d;
            cursor: not-allowed;
            opacity: 0.6;
            box-shadow: none;
        }

        @media (max-width: 768px) {
            .page-navigation {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .page-controls {
                justify-content: center;
            }

            .btn-nav {
                padding: 8px 16px;
                font-size: 0.9rem;
            }
        }

        .file-info {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .file-info h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .file-info p {
            color: #666;
            margin: 5px 0;
        }

        /* Signature Mode Controls */
        .signature-mode-controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .mode-btn {
            padding: 10px 20px;
            font-size: 14px;
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .mode-btn.active {
            opacity: 1;
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .mode-btn:hover {
            opacity: 1;
        }

        @media (max-width: 768px) {
            .upload-sign-container {
                margin: 10px;
                min-height: auto;
            }

            .main-content {
                padding: 20px;
            }

            .step-indicator {
                flex-direction: column;
                gap: 10px;
            }

            .action-buttons {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="upload-sign-container">
        <div class="header">
            <h1>📄 Upload & Sign Document</h1>
            <p>Upload your PDF document and add your signature in just a few clicks</p>
        </div>

        <div class="main-content">
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step active" id="step1">
                    <span>1️⃣</span>
                    <span>Upload PDF</span>
                </div>
                <div class="step" id="step2">
                    <span>2️⃣</span>
                    <span>Create Signature</span>
                </div>
                <div class="step" id="step3">
                    <span>3️⃣</span>
                    <span>Place & Export</span>
                </div>
            </div>

            <!-- Step 1: Upload Document -->
            <div class="step-content active" id="content1">
                <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">
                        <h3>Upload Your PDF Document</h3>
                        <p>Click here or drag and drop your PDF file</p>
                        <button class="btn btn-primary">Choose File</button>
                    </div>
                </div>
                <input type="file" id="fileInput" class="file-input" accept=".pdf" onchange="handleFileUpload(event)">
                
                <div id="fileInfo" class="file-info" style="display: none;">
                    <h4>📄 File Selected</h4>
                    <p id="fileName"></p>
                    <p id="fileSize"></p>
                    <button class="btn btn-success" onclick="nextStep()">Continue to Signature</button>
                </div>
            </div>

            <!-- Step 2: Create Signature -->
            <div class="step-content" id="content2">
                <div class="signature-tools">
                    <h3>✍️ Create Your Signature</h3>
                    <div class="signature-canvas-container">
                        <p style="margin-bottom: 15px; color: #666;">Draw your signature in the box below:</p>
                        <canvas id="signatureCanvas" width="400" height="150"></canvas>
                        <div class="signature-controls">
                            <div class="size-controls">
                                <label>Signature Size:</label>
                                <button class="btn btn-size" onclick="decreaseSignatureSize()" title="Decrease Size">➖</button>
                                <span class="size-display" id="sizeDisplay">Medium</span>
                                <button class="btn btn-size" onclick="increaseSignatureSize()" title="Increase Size">➕</button>
                            </div>
                            <div class="action-controls">
                                <button class="btn btn-primary" onclick="clearSignature()">Clear</button>
                                <button class="btn btn-success" onclick="saveSignature()">Save Signature</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Place Signature and Export -->
            <div class="step-content" id="content3">
                <div class="document-preview">
                    <h3>📋 Choose your action and interact with the document</h3>

                    <!-- Signature Mode Controls -->
                    <div class="signature-mode-controls" style="margin: 20px 0; text-align: center;">
                        <button class="btn btn-primary mode-btn active" onclick="setMode('place')" id="placeBtn">
                            ➕ Place Signature
                        </button>
                        <button class="btn btn-primary mode-btn" onclick="setMode('move')" id="moveBtn">
                            🔄 Move Signature
                        </button>
                        <button class="btn btn-primary mode-btn" onclick="setMode('delete')" id="deleteBtn">
                            🗑️ Delete Signature
                        </button>
                    </div>

                    <div id="modeInstructions" style="margin: 15px 0; padding: 10px; background: #e3f2fd; border-radius: 8px; text-align: center;">
                        <strong>Place Mode:</strong> Click anywhere on the document to place your signature
                    </div>

                    <!-- Page Navigation -->
                    <div class="page-navigation" id="pageNavigation" style="display: none;">
                        <div class="page-info">
                            <span id="pageInfo">Page 1 of 1</span>
                            <div class="page-limit-warning" id="pageLimitWarning" style="display: none;">
                                <span class="warning-icon">⚠️</span>
                                <span>Free users limited to 4 pages. <a href="#" onclick="showDonateModal()" class="donate-link">Donate $5+</a> to unlock unlimited pages!</span>
                            </div>
                        </div>
                        <div class="page-controls">
                            <button class="btn btn-nav" onclick="previousPage()" id="prevBtn" disabled>⬅️ Previous</button>
                            <button class="btn btn-nav" onclick="nextPage()" id="nextBtn" disabled>Next ➡️</button>
                        </div>
                    </div>

                    <div class="document-canvas-container">
                        <canvas id="documentCanvas"></canvas>
                    </div>
                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="clearSignatures()">Clear All Signatures</button>
                        <button class="btn btn-success" onclick="exportPDF()">📥 Export Signed PDF</button>
                    </div>

                    <!-- Additional Actions -->
                    <div class="additional-actions">
                        <button class="btn btn-secondary" onclick="returnToMainMenu()">🏠 Return to Main Menu</button>
                        <button class="btn btn-donate" onclick="showDonateModal()">💝 Support Our Free Tools</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf-lib/1.17.1/pdf-lib.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>

    <script>
        // Global variables
        let currentStep = 1;
        let uploadedFile = null;
        let pdfDoc = null;
        let signatureDataURL = null;
        let placedSignatures = [];
        let isDrawing = false;
        let currentMode = 'place'; // 'place', 'move', 'delete'
        let selectedSignature = null;
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };
        let animationFrameId = null;
        let needsRedraw = false;
        let baseCanvasImageData = null;

        // Signature size control
        let signatureSize = 2; // 1=Small, 2=Medium, 3=Large, 4=Extra Large
        const signatureSizes = {
            1: { width: 80, height: 40, name: 'Small' },
            2: { width: 120, height: 60, name: 'Medium' },
            3: { width: 160, height: 80, name: 'Large' },
            4: { width: 200, height: 100, name: 'Extra Large' }
        };

        // Multi-page variables
        let currentPage = 1;
        let totalPages = 1;
        let hasUnlimitedPages = localStorage.getItem('hasUnlimitedPages') === 'true'; // Check if user has donated $5+
        const FREE_PAGE_LIMIT = 4;

        // PDF.js worker
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

        // Initialize signature canvas
        document.addEventListener('DOMContentLoaded', function() {
            initializeSignatureCanvas();
            setupDragAndDrop();
            updateSizeDisplay(); // Initialize size display
        });

        function setupDragAndDrop() {
            const uploadArea = document.querySelector('.upload-area');

            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0 && files[0].type === 'application/pdf') {
                    handleFileUpload({ target: { files: files } });
                } else {
                    alert('Please upload a PDF file only.');
                }
            });
        }

        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            if (file.type !== 'application/pdf') {
                alert('Please upload a PDF file only.');
                return;
            }

            uploadedFile = file;

            // Show file info
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = `Size: ${(file.size / 1024 / 1024).toFixed(2)} MB`;
            document.getElementById('fileInfo').style.display = 'block';

            // Load PDF for preview
            loadPDFPreview(file);
        }

        async function loadPDFPreview(file) {
            try {
                const arrayBuffer = await file.arrayBuffer();
                const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;
                pdfDoc = pdf;
                totalPages = pdf.numPages;
                currentPage = 1;

                // Check if document exceeds free limit
                const exceedsLimit = totalPages > FREE_PAGE_LIMIT && !hasUnlimitedPages;

                // Show page navigation if more than 1 page
                if (totalPages > 1) {
                    document.getElementById('pageNavigation').style.display = 'flex';
                } else {
                    document.getElementById('pageNavigation').style.display = 'none';
                }

                // Render first page
                await renderCurrentPage();

                // Add event handlers for signature interaction (only once)
                const canvas = document.getElementById('documentCanvas');
                if (!canvas.hasEventListeners) {
                    canvas.addEventListener('click', handleCanvasClick);
                    canvas.addEventListener('mousedown', handleCanvasMouseDown);
                    canvas.addEventListener('mousemove', handleCanvasMouseMove);
                    canvas.addEventListener('mouseup', handleCanvasMouseUp);
                    canvas.hasEventListeners = true;
                }

                // Show warning if exceeds free limit
                if (exceedsLimit) {
                    document.getElementById('pageLimitWarning').style.display = 'flex';
                }

                return Promise.resolve();

            } catch (error) {
                console.error('Error loading PDF:', error);
                alert('Error loading PDF. Please try again.');
                return Promise.reject(error);
            }
        }

        async function renderCurrentPage() {
            try {
                // Check if current page exceeds free limit
                if (currentPage > FREE_PAGE_LIMIT && !hasUnlimitedPages) {
                    alert(`Free users are limited to ${FREE_PAGE_LIMIT} pages. Please donate $5+ to unlock unlimited pages!`);
                    currentPage = Math.min(currentPage, FREE_PAGE_LIMIT);
                    updatePageNavigation();
                    return;
                }

                const page = await pdfDoc.getPage(currentPage);
                const canvas = document.getElementById('documentCanvas');
                const ctx = canvas.getContext('2d');

                const viewport = page.getViewport({ scale: 1.5 });
                canvas.width = viewport.width;
                canvas.height = viewport.height;

                await page.render({
                    canvasContext: ctx,
                    viewport: viewport
                }).promise;

                // Cache the base canvas for smooth dragging
                cacheBaseCanvas();

                // Redraw signatures for current page
                redrawSignatures();

                // Update page navigation
                updatePageNavigation();
            } catch (error) {
                console.error('Error rendering page:', error);
                alert('Error rendering page. Please try again.');
            }
        }

        function updatePageNavigation() {
            document.getElementById('pageInfo').textContent = `Page ${currentPage} of ${totalPages}`;

            // Update navigation buttons
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');

            prevBtn.disabled = currentPage <= 1;

            // For free users, limit navigation to FREE_PAGE_LIMIT
            const maxAllowedPage = hasUnlimitedPages ? totalPages : Math.min(totalPages, FREE_PAGE_LIMIT);
            nextBtn.disabled = currentPage >= maxAllowedPage;

            // Show/hide warning
            const warning = document.getElementById('pageLimitWarning');
            if (totalPages > FREE_PAGE_LIMIT && !hasUnlimitedPages) {
                warning.style.display = 'flex';
            } else {
                warning.style.display = 'none';
            }
        }

        function previousPage() {
            if (currentPage > 1) {
                currentPage--;
                renderCurrentPage();
            }
        }

        function nextPage() {
            const maxAllowedPage = hasUnlimitedPages ? totalPages : Math.min(totalPages, FREE_PAGE_LIMIT);
            if (currentPage < maxAllowedPage) {
                currentPage++;
                renderCurrentPage();
            } else if (!hasUnlimitedPages && totalPages > FREE_PAGE_LIMIT) {
                // Show donation prompt
                if (confirm(`Free users are limited to ${FREE_PAGE_LIMIT} pages.\n\nWould you like to donate $5+ to unlock unlimited pages?`)) {
                    showDonateModal();
                }
            }
        }

        function initializeSignatureCanvas() {
            const canvas = document.getElementById('signatureCanvas');
            const ctx = canvas.getContext('2d');

            // Enhanced smooth drawing settings
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 3;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            ctx.globalCompositeOperation = 'source-over';

            let drawing = false;
            let points = [];
            let lastPoint = null;

            canvas.addEventListener('mousedown', startDrawing);
            canvas.addEventListener('mousemove', draw);
            canvas.addEventListener('mouseup', stopDrawing);
            canvas.addEventListener('mouseout', stopDrawing);

            // Touch events for mobile
            canvas.addEventListener('touchstart', handleTouch);
            canvas.addEventListener('touchmove', handleTouch);
            canvas.addEventListener('touchend', stopDrawing);

            function startDrawing(e) {
                drawing = true;
                const rect = canvas.getBoundingClientRect();
                const point = {
                    x: e.clientX - rect.left,
                    y: e.clientY - rect.top
                };

                points = [point];
                lastPoint = point;

                // Start the path
                ctx.beginPath();
                ctx.moveTo(point.x, point.y);
            }

            function draw(e) {
                if (!drawing) return;

                const rect = canvas.getBoundingClientRect();
                const currentPoint = {
                    x: e.clientX - rect.left,
                    y: e.clientY - rect.top
                };

                points.push(currentPoint);

                // Draw smooth curves using quadratic Bézier curves
                if (points.length >= 3) {
                    const len = points.length;
                    const p0 = points[len - 3];
                    const p1 = points[len - 2];
                    const p2 = points[len - 1];

                    // Calculate control point and end point for smooth curve
                    const cp = {
                        x: p1.x,
                        y: p1.y
                    };

                    const end = {
                        x: (p1.x + p2.x) / 2,
                        y: (p1.y + p2.y) / 2
                    };

                    // Draw smooth quadratic curve
                    ctx.beginPath();
                    ctx.moveTo(lastPoint.x, lastPoint.y);
                    ctx.quadraticCurveTo(cp.x, cp.y, end.x, end.y);
                    ctx.stroke();

                    lastPoint = end;
                } else if (points.length === 2) {
                    // For the first segment, draw a simple line
                    ctx.beginPath();
                    ctx.moveTo(lastPoint.x, lastPoint.y);
                    ctx.lineTo(currentPoint.x, currentPoint.y);
                    ctx.stroke();
                    lastPoint = currentPoint;
                }
            }

            function stopDrawing() {
                if (drawing && points.length > 0) {
                    // Draw final segment to complete the stroke
                    const lastIdx = points.length - 1;
                    if (lastIdx > 0) {
                        const finalPoint = points[lastIdx];
                        ctx.beginPath();
                        ctx.moveTo(lastPoint.x, lastPoint.y);
                        ctx.lineTo(finalPoint.x, finalPoint.y);
                        ctx.stroke();
                    }
                }

                drawing = false;
                points = [];
                lastPoint = null;
            }

            function handleTouch(e) {
                e.preventDefault();
                const touch = e.touches[0];
                const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' :
                                                 e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                });
                canvas.dispatchEvent(mouseEvent);
            }
        }

        function clearSignature() {
            const canvas = document.getElementById('signatureCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function saveSignature() {
            const canvas = document.getElementById('signatureCanvas');
            const ctx = canvas.getContext('2d');

            // Check if canvas is empty
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const isEmpty = imageData.data.every(pixel => pixel === 0);

            if (isEmpty) {
                alert('Please draw your signature first.');
                return;
            }

            signatureDataURL = canvas.toDataURL();
            nextStep();
        }

        // Signature size control functions
        function increaseSignatureSize() {
            if (signatureSize < 4) {
                signatureSize++;
                updateSizeDisplay();
                updateExistingSignatures();
            }
        }

        function decreaseSignatureSize() {
            if (signatureSize > 1) {
                signatureSize--;
                updateSizeDisplay();
                updateExistingSignatures();
            }
        }

        function updateSizeDisplay() {
            const sizeDisplay = document.getElementById('sizeDisplay');
            const currentSizeInfo = signatureSizes[signatureSize];
            sizeDisplay.textContent = currentSizeInfo.name;

            // Update display color based on size
            const colors = {
                1: 'linear-gradient(135deg, #17a2b8, #138496)', // Small - Info
                2: 'linear-gradient(135deg, #28a745, #20c997)', // Medium - Success
                3: 'linear-gradient(135deg, #ffc107, #e0a800)', // Large - Warning
                4: 'linear-gradient(135deg, #dc3545, #c82333)'  // Extra Large - Danger
            };
            sizeDisplay.style.background = colors[signatureSize];
        }

        function updateExistingSignatures() {
            // Update all existing signatures to new size
            const currentSizeInfo = signatureSizes[signatureSize];
            placedSignatures.forEach(sig => {
                // Keep the center position, update dimensions
                const centerX = sig.x + sig.width / 2;
                const centerY = sig.y + sig.height / 2;

                sig.width = currentSizeInfo.width;
                sig.height = currentSizeInfo.height;
                sig.x = centerX - sig.width / 2;
                sig.y = centerY - sig.height / 2;
            });

            // Redraw canvas with updated sizes
            if (placedSignatures.length > 0) {
                redrawCanvasSimple();
            }
        }

        function handleCanvasClick(event) {
            if (isDragging) return; // Don't handle click if we were dragging

            const canvas = document.getElementById('documentCanvas');
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            if (currentMode === 'place') {
                if (!signatureDataURL) {
                    alert('Please create your signature first.');
                    return;
                }
                placeSignature(x, y);
            } else if (currentMode === 'delete') {
                deleteSignatureAt(x, y);
            }
        }

        function handleCanvasMouseDown(event) {
            if (currentMode !== 'move') return;

            const canvas = document.getElementById('documentCanvas');
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            selectedSignature = getSignatureAt(x, y);
            if (selectedSignature) {
                isDragging = true;
                dragOffset.x = x - selectedSignature.x;
                dragOffset.y = y - selectedSignature.y;
                canvas.style.cursor = 'grabbing';
            }
        }

        function handleCanvasMouseMove(event) {
            const canvas = document.getElementById('documentCanvas');
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            if (currentMode === 'move') {
                if (isDragging && selectedSignature) {
                    // Update signature position smoothly
                    selectedSignature.x = x - dragOffset.x;
                    selectedSignature.y = y - dragOffset.y;

                    // Schedule smooth redraw using requestAnimationFrame
                    if (!needsRedraw) {
                        needsRedraw = true;
                        requestAnimationFrame(smoothRedraw);
                    }
                } else {
                    // Change cursor when hovering over signatures
                    const signature = getSignatureAt(x, y);
                    canvas.style.cursor = signature ? 'grab' : 'default';
                }
            } else if (currentMode === 'delete') {
                // Change cursor when hovering over signatures
                const signature = getSignatureAt(x, y);
                canvas.style.cursor = signature ? 'pointer' : 'default';
            }
        }

        function handleCanvasMouseUp(event) {
            if (isDragging) {
                isDragging = false;
                selectedSignature = null;
                needsRedraw = false; // Stop any pending redraws

                // Final redraw to ensure clean state
                redrawCanvasSimple();

                const canvas = document.getElementById('documentCanvas');
                canvas.style.cursor = currentMode === 'move' ? 'default' : 'crosshair';
            }
        }

        function placeSignature(x, y) {
            // Use current signature size settings
            const currentSizeInfo = signatureSizes[signatureSize];
            const signatureWidth = currentSizeInfo.width;
            const signatureHeight = currentSizeInfo.height;

            // Store signature position
            const signature = {
                x: x - signatureWidth/2,
                y: y - signatureHeight/2,
                width: signatureWidth,
                height: signatureHeight,
                page: currentPage, // Track which page this signature belongs to
                id: Date.now() // Unique ID for each signature
            };

            placedSignatures.push(signature);
            redrawCanvasSimple();
        }

        function getSignatureAt(x, y) {
            // Check signatures in reverse order (top to bottom) for current page only
            for (let i = placedSignatures.length - 1; i >= 0; i--) {
                const sig = placedSignatures[i];
                if (sig.page === currentPage &&
                    x >= sig.x && x <= sig.x + sig.width &&
                    y >= sig.y && y <= sig.y + sig.height) {
                    return sig;
                }
            }
            return null;
        }

        function deleteSignatureAt(x, y) {
            const signature = getSignatureAt(x, y);
            if (signature) {
                const index = placedSignatures.indexOf(signature);
                placedSignatures.splice(index, 1);
                redrawCanvasSimple();
            }
        }

        async function redrawCanvas() {
            if (!uploadedFile || !pdfDoc) return;

            try {
                // Re-render the PDF page without reloading
                const page = await pdfDoc.getPage(1);
                const canvas = document.getElementById('documentCanvas');
                const ctx = canvas.getContext('2d');

                const viewport = page.getViewport({ scale: 1.5 });
                canvas.width = viewport.width;
                canvas.height = viewport.height;

                // Clear and re-render PDF
                await page.render({
                    canvasContext: ctx,
                    viewport: viewport
                }).promise;

                // Draw all signatures on top
                if (signatureDataURL && placedSignatures.length > 0) {
                    const img = new Image();
                    img.onload = function() {
                        placedSignatures.forEach(sig => {
                            ctx.drawImage(img, sig.x, sig.y, sig.width, sig.height);
                        });
                    };
                    img.src = signatureDataURL;
                }

            } catch (error) {
                console.error('Error redrawing canvas:', error);
                // Fallback: try to reload the PDF
                try {
                    await loadPDFPreview(uploadedFile);
                    // Redraw signatures after PDF reload
                    if (signatureDataURL && placedSignatures.length > 0) {
                        const canvas = document.getElementById('documentCanvas');
                        const ctx = canvas.getContext('2d');
                        const img = new Image();
                        img.onload = function() {
                            placedSignatures.forEach(sig => {
                                ctx.drawImage(img, sig.x, sig.y, sig.width, sig.height);
                            });
                        };
                        img.src = signatureDataURL;
                    }
                } catch (fallbackError) {
                    console.error('Fallback redraw failed:', fallbackError);
                }
            }
        }

        function clearSignatures() {
            placedSignatures = [];
            redrawCanvasSimple();
        }

        // Optimized smooth redraw function for drag operations
        function smoothRedraw() {
            if (!needsRedraw) return;
            needsRedraw = false;

            const canvas = document.getElementById('documentCanvas');
            const ctx = canvas.getContext('2d');

            // Use cached base image if available, otherwise redraw from PDF
            if (baseCanvasImageData) {
                // Restore base PDF image
                ctx.putImageData(baseCanvasImageData, 0, 0);

                // Draw all signatures on top
                if (signatureDataURL && placedSignatures.length > 0) {
                    const img = new Image();
                    img.onload = function() {
                        placedSignatures.forEach(sig => {
                            ctx.drawImage(img, sig.x, sig.y, sig.width, sig.height);
                        });
                    };
                    img.src = signatureDataURL;
                }
            } else {
                // Fallback to full redraw
                redrawCanvasSimple();
            }
        }

        // Simplified redraw function that doesn't reload PDF
        function redrawCanvasSimple() {
            if (!pdfDoc || !signatureDataURL) return;

            const canvas = document.getElementById('documentCanvas');
            const ctx = canvas.getContext('2d');

            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Re-render current PDF page
            pdfDoc.getPage(currentPage).then(page => {
                const viewport = page.getViewport({ scale: 1.5 });

                page.render({
                    canvasContext: ctx,
                    viewport: viewport
                }).promise.then(() => {
                    // Cache the base PDF image for smooth dragging
                    baseCanvasImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

                    // Draw signatures for current page only
                    redrawSignatures();
                }).catch(error => {
                    console.error('Error in simplified redraw:', error);
                });
            }).catch(error => {
                console.error('Error getting PDF page:', error);
            });
        }

        // Function to redraw signatures for current page
        function redrawSignatures() {
            if (!signatureDataURL || placedSignatures.length === 0) return;

            const canvas = document.getElementById('documentCanvas');
            const ctx = canvas.getContext('2d');

            // Filter signatures for current page
            const currentPageSignatures = placedSignatures.filter(sig => sig.page === currentPage);

            if (currentPageSignatures.length > 0) {
                const img = new Image();
                img.onload = function() {
                    currentPageSignatures.forEach(sig => {
                        ctx.drawImage(img, sig.x, sig.y, sig.width, sig.height);
                    });
                };
                img.src = signatureDataURL;
            }
        }

        // Cache base canvas when PDF is first loaded
        function cacheBaseCanvas() {
            const canvas = document.getElementById('documentCanvas');
            const ctx = canvas.getContext('2d');
            baseCanvasImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        }

        function setMode(mode) {
            currentMode = mode;
            const canvas = document.getElementById('documentCanvas');

            // Update button states
            document.querySelectorAll('.mode-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(mode + 'Btn').classList.add('active');

            // Update canvas cursor and class
            canvas.className = canvas.className.replace(/\b(moving|deleting)\b/g, '');

            if (mode === 'place') {
                canvas.style.cursor = 'crosshair';
                document.getElementById('modeInstructions').innerHTML =
                    '<strong>Place Mode:</strong> Click anywhere on the document to place your signature';
            } else if (mode === 'move') {
                canvas.style.cursor = 'default';
                canvas.classList.add('moving');
                document.getElementById('modeInstructions').innerHTML =
                    '<strong>Move Mode:</strong> Click and drag signatures to move them around the document';
            } else if (mode === 'delete') {
                canvas.style.cursor = 'default';
                canvas.classList.add('deleting');
                document.getElementById('modeInstructions').innerHTML =
                    '<strong>Delete Mode:</strong> Click on any signature to delete it from the document';
            }
        }

        async function exportPDF() {
            if (!uploadedFile || !signatureDataURL || placedSignatures.length === 0) {
                alert('Please upload a PDF, create a signature, and place it on the document.');
                return;
            }

            try {
                const arrayBuffer = await uploadedFile.arrayBuffer();
                const pdfDoc = await PDFLib.PDFDocument.load(arrayBuffer);
                const pages = pdfDoc.getPages();
                const firstPage = pages[0];

                // Convert signature to PNG
                const signatureImage = await pdfDoc.embedPng(signatureDataURL);

                // Add signatures to PDF
                placedSignatures.forEach(sig => {
                    const { width, height } = firstPage.getSize();
                    const canvas = document.getElementById('documentCanvas');

                    // Convert canvas coordinates to PDF coordinates
                    const scaleX = width / canvas.width;
                    const scaleY = height / canvas.height;

                    const pdfX = sig.x * scaleX;
                    const pdfY = height - (sig.y * scaleY) - (sig.height * scaleY);

                    firstPage.drawImage(signatureImage, {
                        x: pdfX,
                        y: pdfY,
                        width: sig.width * scaleX,
                        height: sig.height * scaleY,
                    });
                });

                // Save PDF
                const pdfBytes = await pdfDoc.save();
                const blob = new Blob([pdfBytes], { type: 'application/pdf' });
                const url = URL.createObjectURL(blob);

                const a = document.createElement('a');
                a.href = url;
                a.download = `signed_${uploadedFile.name}`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                alert('✅ PDF exported successfully!');

            } catch (error) {
                console.error('Error exporting PDF:', error);
                alert('Error exporting PDF. Please try again.');
            }
        }

        function nextStep() {
            if (currentStep < 3) {
                // Hide current step
                document.getElementById(`content${currentStep}`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.add('completed');

                // Show next step
                currentStep++;
                document.getElementById(`content${currentStep}`).classList.add('active');
                document.getElementById(`step${currentStep}`).classList.add('active');
            }
        }

        function previousStep() {
            if (currentStep > 1) {
                // Hide current step
                document.getElementById(`content${currentStep}`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.remove('active');

                // Show previous step
                currentStep--;
                document.getElementById(`content${currentStep}`).classList.add('active');
                document.getElementById(`step${currentStep}`).classList.remove('completed');
                document.getElementById(`step${currentStep}`).classList.add('active');
            }
        }

        // Return to Main Menu Function
        function returnToMainMenu() {
            if (confirm('Are you sure you want to return to the main menu? Any unsaved work will be lost.')) {
                window.location.href = 'landing.html';
            }
        }

        // Donate Modal Functions
        let selectedAmount = 25; // Default amount

        function showDonateModal() {
            document.getElementById('donateModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeDonateModal() {
            document.getElementById('donateModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function selectAmount(amount) {
            selectedAmount = amount;
            // Remove active class from all amount buttons
            document.querySelectorAll('.amount-btn').forEach(btn => btn.classList.remove('active'));
            // Add active class to selected button
            event.target.classList.add('active');
            // Clear custom amount input
            document.querySelector('.custom-amount').value = '';
        }

        function selectCustomAmount(amount) {
            if (amount && amount > 0) {
                selectedAmount = parseFloat(amount);
                // Remove active class from all amount buttons
                document.querySelectorAll('.amount-btn').forEach(btn => btn.classList.remove('active'));
            }
        }

        function processPayment(method) {
            const methodNames = {
                'paypal': 'PayPal',
                'stripe': 'Credit Card',
                'crypto': 'Cryptocurrency',
                'bank': 'Bank Transfer'
            };

            // Check if donation is $5 or more to unlock unlimited pages
            if (selectedAmount >= 5) {
                hasUnlimitedPages = true;
                localStorage.setItem('hasUnlimitedPages', 'true'); // Persist the unlock

                alert(`Thank you for your generous support! 💝\n\nPayment Method: ${methodNames[method]}\nAmount: $${selectedAmount}\n\n🎉 UNLIMITED PAGES UNLOCKED! 🎉\nYou can now sign documents with any number of pages!\n\nThis is a demo. In a real implementation, you would be redirected to the ${methodNames[method]} payment processor.`);

                // Update page navigation to reflect unlimited access
                updatePageNavigation();

                // Hide the page limit warning
                document.getElementById('pageLimitWarning').style.display = 'none';
            } else {
                alert(`Thank you for your support! 💝\n\nPayment Method: ${methodNames[method]}\nAmount: $${selectedAmount}\n\nTo unlock unlimited pages, please donate $5 or more.\n\nThis is a demo. In a real implementation, you would be redirected to the ${methodNames[method]} payment processor.`);
            }

            // Close the modal
            closeDonateModal();

            // In a real implementation, you would redirect to the payment processor:
            // window.open(`https://paypal.com/donate?amount=${selectedAmount}`, '_blank');
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('donateModal');
            if (event.target == modal) {
                closeDonateModal();
            }
        }
    </script>

    <!-- Donate Modal -->
    <div id="donateModal" class="modal">
        <div class="modal-content donate-modal-content">
            <span class="close" onclick="closeDonateModal()">&times;</span>
            <div class="donate-header">
                <h2>💝 Support Our Free Tools</h2>
                <p>Help us keep document signing free for everyone!</p>
            </div>

            <div class="donate-content">
                <div class="donate-info">
                    <div class="donate-feature">
                        <div class="feature-icon">🎁</div>
                        <h4>Free Tools</h4>
                        <p>Keep document signing free for everyone</p>
                    </div>
                    <div class="donate-feature">
                        <div class="feature-icon">🚀</div>
                        <h4>New Features</h4>
                        <p>Fund development of new templates and tools</p>
                    </div>
                    <div class="donate-feature">
                        <div class="feature-icon">🛠️</div>
                        <h4>Maintenance</h4>
                        <p>Support server costs and ongoing improvements</p>
                    </div>
                </div>

                <div class="payment-methods">
                    <h3>Choose Your Payment Method</h3>
                    <div class="payment-grid">
                        <button class="payment-btn" onclick="processPayment('paypal')">
                            <span class="payment-icon">💳</span>
                            PayPal
                        </button>
                        <button class="payment-btn" onclick="processPayment('stripe')">
                            <span class="payment-icon">💳</span>
                            Credit Card
                        </button>
                        <button class="payment-btn" onclick="processPayment('crypto')">
                            <span class="payment-icon">₿</span>
                            Cryptocurrency
                        </button>
                        <button class="payment-btn" onclick="processPayment('bank')">
                            <span class="payment-icon">🏦</span>
                            Bank Transfer
                        </button>
                    </div>
                </div>

                <div class="donation-amounts">
                    <h4>Select Amount</h4>
                    <div class="amount-buttons">
                        <button class="amount-btn" onclick="selectAmount(5)">$5</button>
                        <button class="amount-btn" onclick="selectAmount(10)">$10</button>
                        <button class="amount-btn active" onclick="selectAmount(25)">$25</button>
                        <button class="amount-btn" onclick="selectAmount(50)">$50</button>
                        <button class="amount-btn" onclick="selectAmount(100)">$100</button>
                        <input type="number" class="custom-amount" placeholder="Custom amount" min="1" onchange="selectCustomAmount(this.value)">
                    </div>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
