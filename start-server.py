#!/usr/bin/env python3
"""
🚀 DocuGen Pro Local Server Starter
Simple HTTP server to run the invoice application without CORS issues
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

# Configuration
PORT = 8000
HOST = 'localhost'

def main():
    print("🚀 DocuGen Pro Local Server")
    print("=" * 50)
    
    # Get current directory
    current_dir = Path.cwd()
    print(f"📁 Serving files from: {current_dir}")
    
    # Check if landing.html exists
    if not (current_dir / 'landing.html').exists():
        print("❌ Error: landing.html not found in current directory")
        print("💡 Make sure you're running this script from the project root folder")
        print(f"   Expected location: {current_dir / 'landing.html'}")
        return
    
    # Check if index.html exists
    if not (current_dir / 'index.html').exists():
        print("❌ Error: index.html not found in current directory")
        print("💡 Make sure you're running this script from the project root folder")
        print(f"   Expected location: {current_dir / 'index.html'}")
        return
    
    print(f"🏠 Landing page: ✅ Found")
    print(f"📄 Main app: ✅ Found")
    print()
    
    # Create HTTP server
    handler = http.server.SimpleHTTPRequestHandler
    
    try:
        with socketserver.TCPServer((HOST, PORT), handler) as httpd:
            server_url = f"http://{HOST}:{PORT}"
            
            print(f"🌐 Server starting at: {server_url}")
            print()
            print("📋 Available URLs:")
            print(f"   🏠 Landing Page:       {server_url}/landing.html")
            print(f"   📄 Invoice App:        {server_url}/index.html")
            print(f"   📊 Dashboard:          {server_url}/dashboard2.html")
            
            print()
            print("🎯 Quick Start Instructions:")
            print("   1. Landing page will open automatically")
            print("   2. Sign up or sign in to access the app")
            print("   3. Generate an invoice to test basic functionality")
            print("   4. Verify document generation works properly")
            print("   5. Test sharing and export features")
            
            print()
            print("⏹️  Press Ctrl+C to stop the server")
            print("=" * 50)
            
            # Open browser automatically to landing page
            try:
                webbrowser.open(f"{server_url}/landing.html")
                print("🌐 Opening landing page in browser...")
            except Exception as e:
                print(f"⚠️  Could not open browser automatically: {e}")
                print(f"   Please manually open: {server_url}/landing.html")
            
            print()
            print("🚀 Server is running! Waiting for requests...")
            print()
            
            # Start serving
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n")
        print("⏹️  Server stopped by user")
        print("👋 Thanks for using DocuGen Pro!")
        
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ Error: Port {PORT} is already in use")
            print("💡 Solutions:")
            print(f"   1. Stop any other server running on port {PORT}")
            print(f"   2. Try a different port by editing this script")
            print(f"   3. Wait a moment and try again")
        else:
            print(f"❌ Error starting server: {e}")
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
