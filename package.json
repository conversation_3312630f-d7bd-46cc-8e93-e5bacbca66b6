{"name": "docugen-pro", "version": "1.4.0", "description": "Professional Document Generation Platform - Complete invoice, receipt, quotation, contract, rider, and annexure generator with authentication, logo upload, payment terms, and template management", "main": "landing.html", "scripts": {"start": "python3 start-server.py", "serve": "python3 -m http.server 8000", "open": "open landing.html", "build": "echo 'Build complete - static files ready for deployment'", "test": "echo 'No tests specified'"}, "keywords": ["invoice", "receipt", "quotation", "contract", "document-generator", "pdf-generator", "business-tools", "authentication", "template-manager", "payment-terms", "logo-upload", "professional-documents", "docusign"], "author": "DocuGen Pro Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/docugen-pro/docugen-pro.git"}, "homepage": "https://docugen-pro.github.io", "dependencies": {"jspdf": "^2.5.1", "html2canvas": "^1.4.1", "qrcode": "^1.5.1"}, "devDependencies": {}, "engines": {"node": ">=14.0.0", "python": ">=3.6.0"}, "files": ["landing.html", "index.html", "dashboard2.html", "script.js", "landing-script.js", "dashboard-script.js", "template-manager.js", "ai-assistant.js", "api-config.js", "styles.css", "landing-styles.css", "dashboard-styles.css", "start-server.py", "README.md", "CHANGELOG.md"], "browserslist": ["> 1%", "last 2 versions", "not dead"]}