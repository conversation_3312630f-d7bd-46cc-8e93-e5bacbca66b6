// Simplified PDF Signing System
// Only handles PDF import, signature placement, and PDF export

// A4 dimensions at 96 DPI (standard web resolution)
const A4_WIDTH = 794;  // 210mm at 96 DPI
const A4_HEIGHT = 1123; // 297mm at 96 DPI

let currentPdf = null;
let pdfDocument = null;
let pdfCanvas = null;
let pdfContext = null;
let currentZoom = 100;
let placedSignatures = [];

// Initialize the simplified PDF signing system
function initializeSimplifiedPdfSigning() {
    console.log('🔧 Initializing Simplified PDF Signing...');
    
    const pdfInput = document.getElementById('pdfUpload');
    if (pdfInput) {
        pdfInput.addEventListener('change', handlePdfUpload);
        console.log('✅ PDF upload listener added');
    }
    
    // Initialize PDF.js
    if (typeof pdfjsLib !== 'undefined') {
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
        console.log('✅ PDF.js worker configured');
    }
}

// Handle PDF file upload
function handlePdfUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    console.log('📄 Processing PDF:', file.name);
    
    // Validate file type
    if (file.type !== 'application/pdf') {
        alert('Please select a PDF file only.');
        return;
    }
    
    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
        alert('File size too large. Please upload files smaller than 10MB.');
        return;
    }
    
    currentPdf = file;
    displayPdfInfo(file);
    loadPdfForSigning(file);
}

// Display PDF file information
function displayPdfInfo(file) {
    const fileInfo = document.getElementById('uploadedPdfInfo');
    const fileName = fileInfo.querySelector('.file-name');
    const fileSize = fileInfo.querySelector('.file-size');
    const uploadArea = document.getElementById('pdfUploadArea');
    
    fileName.textContent = file.name;
    fileSize.textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB`;
    
    fileInfo.style.display = 'block';
    uploadArea.style.display = 'none';
    
    // Show signature section
    document.getElementById('signatureSection').style.display = 'block';
    document.getElementById('pdfPreviewSection').style.display = 'block';
}

// Remove uploaded PDF
function removeUploadedPdf() {
    currentPdf = null;
    pdfDocument = null;
    placedSignatures = [];
    
    const fileInfo = document.getElementById('uploadedPdfInfo');
    const uploadArea = document.getElementById('pdfUploadArea');
    const exportBtn = document.getElementById('exportPdfBtn');
    
    fileInfo.style.display = 'none';
    uploadArea.style.display = 'block';
    
    document.getElementById('signatureSection').style.display = 'none';
    document.getElementById('pdfPreviewSection').style.display = 'none';
    
    if (exportBtn) exportBtn.disabled = true;
    
    // Clear preview
    const previewContainer = document.getElementById('pdfPreviewContainer');
    if (previewContainer) {
        previewContainer.innerHTML = `
            <div class="preview-placeholder">
                <div class="preview-icon">📄</div>
                <h4>Upload a PDF to see preview</h4>
                <p>Click anywhere on the PDF to place your signature</p>
            </div>
        `;
    }
    
    // Reset file input
    const fileInput = document.getElementById('pdfUpload');
    if (fileInput) {
        fileInput.value = '';
    }
}

// Load PDF for signing
async function loadPdfForSigning(file) {
    console.log('📄 Loading PDF for signing...');
    
    try {
        const arrayBuffer = await file.arrayBuffer();
        const loadingTask = pdfjsLib.getDocument(arrayBuffer);
        pdfDocument = await loadingTask.promise;
        
        console.log('✅ PDF loaded successfully');
        console.log(`📊 PDF has ${pdfDocument.numPages} pages`);
        
        // Render first page
        await renderPdfPage(1);
        
        // Enable export button
        const exportBtn = document.getElementById('exportPdfBtn');
        if (exportBtn) exportBtn.disabled = false;
        
    } catch (error) {
        console.error('❌ Error loading PDF:', error);
        alert('Error loading PDF. Please try a different file.');
    }
}

// Render PDF page
async function renderPdfPage(pageNumber) {
    console.log(`📄 Rendering page ${pageNumber}...`);
    
    try {
        const page = await pdfDocument.getPage(pageNumber);
        const viewport = page.getViewport({ scale: currentZoom / 100 });
        
        // Create or get canvas
        const container = document.getElementById('pdfPreviewContainer');
        let canvas = container.querySelector('canvas');
        
        if (!canvas) {
            canvas = document.createElement('canvas');
            canvas.id = 'pdfCanvas';
            container.innerHTML = '';
            container.appendChild(canvas);
            
            // Add click handler for signature placement
            canvas.addEventListener('click', handleCanvasClick);
        }
        
        pdfCanvas = canvas;
        pdfContext = canvas.getContext('2d');
        
        // Set canvas size to A4 dimensions
        // Scale the viewport to fit A4 if needed
        const scaleX = A4_WIDTH / viewport.width;
        const scaleY = A4_HEIGHT / viewport.height;
        const scale = Math.min(scaleX, scaleY);

        canvas.width = A4_WIDTH;
        canvas.height = A4_HEIGHT;
        canvas.style.width = A4_WIDTH + 'px';
        canvas.style.height = A4_HEIGHT + 'px';

        // Scale the context to fit the PDF content within A4 dimensions
        pdfContext.scale(scale, scale);

        // Center the content if needed
        const offsetX = (a4Width - viewport.width * scale) / 2;
        const offsetY = (a4Height - viewport.height * scale) / 2;
        pdfContext.translate(offsetX / scale, offsetY / scale);
        
        // Render PDF page
        const renderContext = {
            canvasContext: pdfContext,
            viewport: viewport
        };
        
        await page.render(renderContext).promise;
        console.log('✅ PDF page rendered successfully');
        
        // Re-render placed signatures
        renderPlacedSignatures();
        
    } catch (error) {
        console.error('❌ Error rendering PDF page:', error);
    }
}

// Handle canvas click for signature placement
function handleCanvasClick(event) {
    if (!pdfCanvas) return;
    
    const rect = pdfCanvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    console.log(`📍 Canvas clicked at (${x}, ${y})`);
    
    // Get selected signature from library
    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
    if (signatures.length === 0) {
        alert('No signatures found. Please create a signature first using the Signature Tools.');
        return;
    }
    
    // For now, use the first signature
    const signature = signatures[0];
    placeSignature(x, y, signature);
}

// Place signature on PDF
function placeSignature(x, y, signature) {
    const signatureData = {
        id: Date.now(),
        x: x,
        y: y,
        signature: signature,
        width: 120,
        height: 60
    };
    
    placedSignatures.push(signatureData);
    renderPlacedSignatures();
    
    console.log('✍️ Signature placed at', x, y);
}

// Render all placed signatures
function renderPlacedSignatures() {
    if (!pdfContext || !pdfCanvas) return;
    
    placedSignatures.forEach(sig => {
        const img = new Image();
        img.onload = function() {
            pdfContext.drawImage(img, sig.x, sig.y, sig.width, sig.height);
        };
        img.src = sig.signature.data;
    });
}

// Zoom functions
function zoomIn() {
    currentZoom = Math.min(currentZoom + 25, 200);
    updateZoomDisplay();
    if (pdfDocument) renderPdfPage(1);
}

function zoomOut() {
    currentZoom = Math.max(currentZoom - 25, 50);
    updateZoomDisplay();
    if (pdfDocument) renderPdfPage(1);
}

function resetZoom() {
    currentZoom = 100;
    updateZoomDisplay();
    if (pdfDocument) renderPdfPage(1);
}

function updateZoomDisplay() {
    const zoomLevel = document.getElementById('zoomLevel');
    if (zoomLevel) zoomLevel.textContent = currentZoom + '%';
}

// Export signed PDF
async function exportSignedPdf() {
    if (!currentPdf || placedSignatures.length === 0) {
        alert('Please upload a PDF and add at least one signature before exporting.');
        return;
    }
    
    console.log('📥 Exporting signed PDF...');
    
    try {
        // For now, create a simple download of the original PDF
        // In a real implementation, this would merge the signatures into the PDF
        const url = URL.createObjectURL(currentPdf);
        const a = document.createElement('a');
        a.href = url;
        a.download = currentPdf.name.replace('.pdf', '_signed.pdf');
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        console.log('✅ PDF exported successfully');
        alert('PDF exported successfully! Note: This is a demo version. In production, signatures would be embedded in the PDF.');
        
    } catch (error) {
        console.error('❌ Error exporting PDF:', error);
        alert('Error exporting PDF. Please try again.');
    }
}

// Modal functions
function closePdfSigning() {
    const modal = document.getElementById('documentImportModal');
    if (modal) modal.style.display = 'none';
    
    // Reset state
    removeUploadedPdf();
}

// Enable signature mode (simplified)
function enableSignatureMode() {
    const btn = document.getElementById('signatureBtn');
    if (btn) btn.classList.add('active');
    console.log('✍️ Signature mode enabled');
}

// Toggle signature library
function toggleSignatureLibrary() {
    const library = document.getElementById('signatureLibrary');
    if (library) {
        const isVisible = library.style.display !== 'none';
        library.style.display = isVisible ? 'none' : 'block';
        
        if (!isVisible) {
            loadSignatureLibrary();
        }
    }
}

// Load signature library
function loadSignatureLibrary() {
    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
    const signatureList = document.getElementById('signatureList');
    
    if (!signatureList) return;
    
    if (signatures.length === 0) {
        signatureList.innerHTML = '<p class="no-signatures">No saved signatures. Create some in Signature Tools first.</p>';
        return;
    }
    
    signatureList.innerHTML = signatures.map((sig, index) => `
        <div class="signature-item" onclick="selectSignature(${index})">
            <img src="${sig.data}" alt="${sig.name}" style="max-width: 100px; max-height: 50px;">
            <p>${sig.name}</p>
        </div>
    `).join('');
}

// Select signature from library
function selectSignature(index) {
    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
    if (signatures[index]) {
        console.log('✅ Signature selected:', signatures[index].name);
        alert(`Signature "${signatures[index].name}" selected! Click on the PDF to place it.`);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeSimplifiedPdfSigning();
});
