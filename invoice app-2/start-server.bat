@echo off
REM 🚀 DocuGen Pro Local Server Starter (Windows)
REM Simple HTTP server to run the invoice application without CORS issues

echo.
echo 🚀 DocuGen Pro Local Server
echo ==================================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Python not found
    echo 💡 Please install Python from https://python.org
    echo    Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

REM Check if index.html exists
if not exist "index.html" (
    echo ❌ Error: index.html not found in current directory
    echo 💡 Make sure you're running this script from the invoice app folder
    pause
    exit /b 1
)

echo 📁 Serving files from: %CD%
echo 📄 Invoice app: ✅ Found

if exist "invoice-tester.html" (
    echo 🧪 Invoice tester: ✅ Found
) else (
    echo 🧪 Invoice tester: ❌ Not found
)

if exist "invoice-tester-standalone.html" (
    echo 🧪 Standalone tester: ✅ Found
) else (
    echo 🧪 Standalone tester: ❌ Not found
)

echo.
echo 🌐 Starting server at: http://localhost:8000
echo.
echo 📋 Available URLs:
echo    🏠 Landing Page:       http://localhost:8000/landing.html
echo    📄 Invoice App:        http://localhost:8000/index.html
if exist "invoice-tester.html" (
    echo    🧪 Invoice Tester:     http://localhost:8000/invoice-tester.html
)
if exist "invoice-tester-standalone.html" (
    echo    🧪 Standalone Tester:  http://localhost:8000/invoice-tester-standalone.html
)
echo.
echo 🎯 Quick Start Instructions:
echo    1. Landing page will open automatically
echo    2. Sign up or sign in to access the app
echo    3. Generate an invoice to test basic functionality
echo    4. Verify quick sharing appears below generate button
echo    5. Test email and WhatsApp sharing
if exist "invoice-tester.html" (
    echo    6. Open Invoice Tester for automated testing
)
echo.
echo ⏹️  Press Ctrl+C to stop the server
echo ==================================================
echo.

REM Open browser automatically to landing page
start http://localhost:8000/landing.html
echo 🌐 Opening landing page in browser...
echo.
echo 🚀 Server is running! Waiting for requests...
echo.

REM Start Python HTTP server
python -m http.server 8000
