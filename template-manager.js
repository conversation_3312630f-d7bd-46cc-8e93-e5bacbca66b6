// Template Manager System
// This file contains all the functionality for managing templates

// Template structure
/*
{
    id: string,
    name: string,
    description: string,
    dateCreated: string,
    lastModified: string,
    settings: {
        colorTheme: string,
        customColor: string,
        layout: string,
        fontHeading: string,
        fontBody: string,
        // Additional settings can be added here
    },
    preview: string // Base64 encoded image
}
*/

// Global variables
let templates = [];
let currentTemplateId = null;
let isEditingTemplate = false;

// Initialize the template manager
function initializeTemplateManager() {
    // Load templates from localStorage
    loadTemplates();

    // Set up event listeners
    setupTemplateManagerListeners();

    // Show/hide the appropriate elements
    updateTemplateUI();
}

// Load templates from localStorage
function loadTemplates() {
    const savedTemplates = localStorage.getItem('invoiceTemplates');
    if (savedTemplates) {
        templates = JSON.parse(savedTemplates);
    } else {
        // Add default templates if none exist
        createDefaultTemplates();
    }
}

// Create default templates
function createDefaultTemplates() {
    const defaultTemplates = [
        {
            id: generateUUID(),
            name: "Professional Blue",
            description: "A clean, professional template with blue accents",
            dateCreated: new Date().toISOString(),
            lastModified: new Date().toISOString(),
            settings: {
                colorTheme: "default",
                customColor: "",
                layout: "modern",
                fontHeading: "Montserrat",
                fontBody: "Open Sans"
            },
            preview: "" // Will be generated
        },
        {
            id: generateUUID(),
            name: "Classic Business",
            description: "Traditional business template with formal styling",
            dateCreated: new Date().toISOString(),
            lastModified: new Date().toISOString(),
            settings: {
                colorTheme: "default",
                customColor: "",
                layout: "classic",
                fontHeading: "Times New Roman",
                fontBody: "Georgia"
            },
            preview: "" // Will be generated
        },
        {
            id: generateUUID(),
            name: "Modern Green",
            description: "Eco-friendly template with green accents",
            dateCreated: new Date().toISOString(),
            lastModified: new Date().toISOString(),
            settings: {
                colorTheme: "green",
                customColor: "",
                layout: "modern",
                fontHeading: "Montserrat",
                fontBody: "Open Sans"
            },
            preview: "" // Will be generated
        }
    ];

    templates = defaultTemplates;
    saveTemplates();
}

// Save templates to localStorage
function saveTemplates() {
    localStorage.setItem('invoiceTemplates', JSON.stringify(templates));
}

// Set up event listeners for the template manager
function setupTemplateManagerListeners() {
    // Open template manager button
    const manageTemplatesBtn = document.getElementById('manageTemplates');
    if (manageTemplatesBtn) {
        manageTemplatesBtn.addEventListener('click', openTemplateManager);
    }

    // Close template manager button
    const closeTemplateBtn = document.querySelector('.close-template');
    if (closeTemplateBtn) {
        closeTemplateBtn.addEventListener('click', closeTemplateManager);
    }

    // Tab buttons
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabId = this.getAttribute('data-tab');
            switchTab(tabId);
        });
    });

    // Template form submission
    const templateForm = document.getElementById('templateForm');
    if (templateForm) {
        templateForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveTemplate();
        });
    }

    // Reset template form button
    const resetTemplateBtn = document.querySelector('.reset-template-btn');
    if (resetTemplateBtn) {
        resetTemplateBtn.addEventListener('click', resetTemplateForm);
    }

    // Custom color toggle
    const templateColorTheme = document.getElementById('templateColorTheme');
    if (templateColorTheme) {
        templateColorTheme.addEventListener('change', function() {
            toggleCustomColorControls(this.value === 'custom');
            updateTemplatePreview();
        });
    }

    // Update preview on any setting change
    const templateSettings = document.querySelectorAll('#templateForm select, #templateForm input');
    templateSettings.forEach(setting => {
        setting.addEventListener('change', updateTemplatePreview);
        if (setting.type === 'text' || setting.type === 'color') {
            setting.addEventListener('input', updateTemplatePreview);
        }
    });

    // Import template button
    const importTemplateBtn = document.getElementById('importTemplateBtn');
    if (importTemplateBtn) {
        importTemplateBtn.addEventListener('click', importTemplate);
    }

    // Export templates button
    const exportTemplateBtn = document.getElementById('exportTemplateBtn');
    if (exportTemplateBtn) {
        exportTemplateBtn.addEventListener('click', exportSelectedTemplates);
    }

    // Import template file input
    const importTemplateFile = document.getElementById('importTemplateFile');
    if (importTemplateFile) {
        importTemplateFile.addEventListener('change', handleTemplateFileImport);
    }
}

// Open the template manager modal
function openTemplateManager() {
    const modal = document.getElementById('templateModal');
    if (modal) {
        modal.style.display = 'block';
        renderTemplateGrid();
        renderExportTemplateList();
    }
}

// Close the template manager modal
function closeTemplateManager() {
    const modal = document.getElementById('templateModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Switch between tabs in the template manager
function switchTab(tabId) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(tab => {
        tab.style.display = 'none';
    });

    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(button => {
        button.classList.remove('active');
    });

    // Show the selected tab content
    const selectedTab = document.getElementById(tabId);
    if (selectedTab) {
        selectedTab.style.display = 'block';
    }

    // Add active class to the clicked tab button
    const activeButton = document.querySelector(`.tab-btn[data-tab="${tabId}"]`);
    if (activeButton) {
        activeButton.classList.add('active');
    }

    // If switching to create tab, reset the form if not editing
    if (tabId === 'create-template' && !isEditingTemplate) {
        resetTemplateForm();
    }
}

// Generate a UUID for template IDs
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// Update the template UI based on current state
function updateTemplateUI() {
    renderTemplateGrid();
    renderExportTemplateList();

    // Show/hide no templates message
    const noTemplatesMessage = document.getElementById('noTemplatesMessage');
    if (noTemplatesMessage) {
        noTemplatesMessage.style.display = templates.length === 0 ? 'block' : 'none';
    }
}

// Toggle custom color controls visibility
function toggleCustomColorControls(show) {
    const customColorControls = document.getElementById('customColorControls');
    if (customColorControls) {
        customColorControls.style.display = show ? 'block' : 'none';
    }
}

// Update the template preview based on current settings
function updateTemplatePreview() {
    const previewContainer = document.getElementById('templatePreview');
    if (!previewContainer) return;

    // Get current settings
    const colorTheme = document.getElementById('templateColorTheme').value;
    const customColor = document.getElementById('templateCustomColor').value;
    const layout = document.getElementById('templateLayout').value;
    const fontHeading = document.getElementById('templateFontHeading').value;
    const fontBody = document.getElementById('templateFontBody').value;

    // Create a preview HTML
    let previewHTML = `
        <div class="preview-document ${layout}" style="font-family: ${fontBody};">
            <div class="preview-header" style="color: ${colorTheme === 'custom' ? customColor : getThemeColor(colorTheme)};">
                <h1 style="font-family: ${fontHeading};">INVOICE</h1>
            </div>
            <div class="preview-content">
                <div class="preview-section">
                    <h2 style="font-family: ${fontHeading};">Client</h2>
                    <p>Company Name</p>
                </div>
                <div class="preview-section">
                    <h2 style="font-family: ${fontHeading};">Items</h2>
                    <div class="preview-table"></div>
                </div>
                <div class="preview-section">
                    <h2 style="font-family: ${fontHeading};">Total</h2>
                    <p>$1,000.00</p>
                </div>
            </div>
        </div>
    `;

    previewContainer.innerHTML = previewHTML;

    // Update color preview if using custom color
    if (colorTheme === 'custom') {
        const colorPreview = document.getElementById('templateColorPreview');
        if (colorPreview) {
            colorPreview.style.backgroundColor = customColor;
        }
    }
}

// Get color for a theme
function getThemeColor(theme) {
    switch(theme) {
        case 'green': return '#28a745';
        case 'purple': return '#6f42c1';
        case 'orange': return '#fd7e14';
        case 'teal': return '#20c997';
        default: return '#007bff'; // Default blue
    }
}

// Render the template grid
function renderTemplateGrid() {
    const templateGrid = document.getElementById('templateGrid');
    if (!templateGrid) return;

    // Clear the grid
    templateGrid.innerHTML = '';

    // Add each template to the grid
    templates.forEach(template => {
        const templateCard = document.createElement('div');
        templateCard.className = 'template-card';
        templateCard.dataset.id = template.id;

        templateCard.innerHTML = `
            <div class="template-card-header">
                <h3 class="template-card-title">${template.name}</h3>
                <p class="template-card-description">${template.description || ''}</p>
            </div>
            <div class="template-card-preview">
                ${template.preview ? `<img src="${template.preview}" alt="${template.name}">` :
                `<div class="preview-placeholder" style="color: ${template.settings.colorTheme === 'custom' ?
                template.settings.customColor : getThemeColor(template.settings.colorTheme)};">
                    <span>${template.settings.layout.charAt(0).toUpperCase() + template.settings.layout.slice(1)}</span>
                </div>`}
            </div>
            <div class="template-card-actions">
                <button class="use-template-btn" data-id="${template.id}"><span>Use</span></button>
                <button class="edit-template-btn" data-id="${template.id}"><span>Edit</span></button>
                <button class="delete-template-btn" data-id="${template.id}"><span>Delete</span></button>
            </div>
        `;

        templateGrid.appendChild(templateCard);
    });

    // Add event listeners to the buttons
    const useButtons = document.querySelectorAll('.use-template-btn');
    useButtons.forEach(button => {
        button.addEventListener('click', function() {
            const templateId = this.getAttribute('data-id');
            useTemplate(templateId);
        });
    });

    const editButtons = document.querySelectorAll('.edit-template-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const templateId = this.getAttribute('data-id');
            editTemplate(templateId);
        });
    });

    const deleteButtons = document.querySelectorAll('.delete-template-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const templateId = this.getAttribute('data-id');
            deleteTemplate(templateId);
        });
    });
}

// Render the export template list
function renderExportTemplateList() {
    const exportList = document.getElementById('exportTemplateList');
    if (!exportList) return;

    // Clear the list
    exportList.innerHTML = '';

    // Add each template to the list
    templates.forEach(template => {
        const listItem = document.createElement('div');
        listItem.className = 'export-template-item';

        listItem.innerHTML = `
            <input type="checkbox" id="export-${template.id}" value="${template.id}">
            <label for="export-${template.id}">${template.name}</label>
        `;

        exportList.appendChild(listItem);
    });
}

// Use a template
function useTemplate(templateId) {
    const template = templates.find(t => t.id === templateId);
    if (!template) return;

    // Apply the template settings to the form
    document.getElementById('colorTheme').value = template.settings.colorTheme;
    document.getElementById('invoiceTemplate').value = template.settings.layout;

    // Apply the template
    applyTheme(template.settings.colorTheme);
    applyTemplate(template.settings.layout);

    // Apply custom fonts if supported
    applyFonts(template.settings.fontHeading, template.settings.fontBody);

    // Close the modal
    closeTemplateManager();

    // Show success message
    alert(`Template "${template.name}" applied successfully!`);
}

// Edit a template
function editTemplate(templateId) {
    const template = templates.find(t => t.id === templateId);
    if (!template) return;

    // Set editing mode
    isEditingTemplate = true;
    currentTemplateId = templateId;

    // Fill the form with template data
    document.getElementById('templateName').value = template.name;
    document.getElementById('templateDescription').value = template.description || '';
    document.getElementById('templateColorTheme').value = template.settings.colorTheme;
    document.getElementById('templateLayout').value = template.settings.layout;
    document.getElementById('templateFontHeading').value = template.settings.fontHeading;
    document.getElementById('templateFontBody').value = template.settings.fontBody;

    // Handle custom color
    if (template.settings.colorTheme === 'custom') {
        document.getElementById('templateCustomColor').value = template.settings.customColor;
        toggleCustomColorControls(true);
    } else {
        toggleCustomColorControls(false);
    }

    // Update preview
    updateTemplatePreview();

    // Switch to create tab
    switchTab('create-template');
}

// Delete a template
function deleteTemplate(templateId) {
    if (!confirm('Are you sure you want to delete this template?')) return;

    // Remove the template from the array
    templates = templates.filter(t => t.id !== templateId);

    // Save the updated templates
    saveTemplates();

    // Update the UI
    updateTemplateUI();
}

// Save a template
function saveTemplate() {
    // Get form values
    const name = document.getElementById('templateName').value;
    const description = document.getElementById('templateDescription').value;
    const colorTheme = document.getElementById('templateColorTheme').value;
    const customColor = document.getElementById('templateCustomColor').value;
    const layout = document.getElementById('templateLayout').value;
    const fontHeading = document.getElementById('templateFontHeading').value;
    const fontBody = document.getElementById('templateFontBody').value;

    // Validate form
    if (!name) {
        alert('Please enter a template name');
        return;
    }

    // Create template object
    const template = {
        id: isEditingTemplate ? currentTemplateId : generateUUID(),
        name,
        description,
        dateCreated: isEditingTemplate ? templates.find(t => t.id === currentTemplateId).dateCreated : new Date().toISOString(),
        lastModified: new Date().toISOString(),
        settings: {
            colorTheme,
            customColor: colorTheme === 'custom' ? customColor : '',
            layout,
            fontHeading,
            fontBody
        },
        preview: generateTemplatePreview()
    };

    // Add or update the template
    if (isEditingTemplate) {
        const index = templates.findIndex(t => t.id === currentTemplateId);
        if (index !== -1) {
            templates[index] = template;
        }
    } else {
        templates.push(template);
    }

    // Save the templates
    saveTemplates();

    // Reset the form and editing state
    isEditingTemplate = false;
    currentTemplateId = null;
    resetTemplateForm();

    // Update the UI
    updateTemplateUI();

    // Switch to saved templates tab
    switchTab('saved-templates');

    // Show success message
    alert(`Template "${name}" ${isEditingTemplate ? 'updated' : 'saved'} successfully!`);
}

// Reset the template form
function resetTemplateForm() {
    const form = document.getElementById('templateForm');
    if (form) {
        form.reset();
    }

    // Reset editing state
    isEditingTemplate = false;
    currentTemplateId = null;

    // Hide custom color controls
    toggleCustomColorControls(false);

    // Update preview
    updateTemplatePreview();
}

// Generate a preview image for a template
function generateTemplatePreview() {
    // In a real implementation, this would capture the preview as an image
    // For now, we'll return an empty string
    return '';
}

// Apply fonts to the document
function applyFonts(headingFont, bodyFont) {
    // This would apply the fonts to the document
    // For now, we'll just log the fonts
    console.log(`Applying fonts: Heading - ${headingFont}, Body - ${bodyFont}`);
}

// Import a template
function importTemplate() {
    const importCode = document.getElementById('importTemplateCode').value;
    if (!importCode) {
        alert('Please enter a template code or select a file to import');
        return;
    }

    try {
        // Parse the template code
        const importedTemplates = JSON.parse(importCode);

        // Validate the imported templates
        if (!Array.isArray(importedTemplates)) {
            throw new Error('Invalid template format');
        }

        // Add each template
        let importCount = 0;
        importedTemplates.forEach(template => {
            // Validate required fields
            if (!template.name || !template.settings) {
                console.warn('Skipping invalid template:', template);
                return;
            }

            // Generate a new ID to avoid conflicts
            template.id = generateUUID();

            // Add the template
            templates.push(template);
            importCount++;
        });

        // Save the templates
        saveTemplates();

        // Update the UI
        updateTemplateUI();

        // Show success message
        alert(`Successfully imported ${importCount} template(s)`);

        // Clear the import code
        document.getElementById('importTemplateCode').value = '';
    } catch (error) {
        alert(`Error importing templates: ${error.message}`);
    }
}

// Handle template file import
function handleTemplateFileImport(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        document.getElementById('importTemplateCode').value = e.target.result;
    };
    reader.readAsText(file);
}

// Export selected templates
function exportSelectedTemplates() {
    const checkboxes = document.querySelectorAll('#exportTemplateList input[type="checkbox"]:checked');
    if (checkboxes.length === 0) {
        alert('Please select at least one template to export');
        return;
    }

    // Get the selected template IDs
    const selectedIds = Array.from(checkboxes).map(cb => cb.value);

    // Filter the templates to export
    const templatesToExport = templates.filter(t => selectedIds.includes(t.id));

    // Convert to JSON
    const exportData = JSON.stringify(templatesToExport, null, 2);

    // Create a download link
    const blob = new Blob([exportData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'invoice-templates.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// Initialize the template manager when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeTemplateManager();
});
