/* Privacy & Terms Modal Enhancements */
#privacyModal .modal-content,
#termsModal .modal-content {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
  padding: 36px 32px 28px 32px;
  max-width: 540px;
  margin: auto;
  font-family: 'Open Sans', Arial, sans-serif;
  color: #23272f;
  position: relative;
  animation: modalFadeIn 0.3s;
}

#privacyModal h2, #termsModal h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 18px;
  color: #2a7be4;
  letter-spacing: 0.5px;
}

#privacyModal p, #termsModal p, #privacyModal ul, #termsModal ul {
  font-size: 1.08rem;
  margin-bottom: 14px;
  line-height: 1.7;
}

#privacyModal ul, #termsModal ul {
  padding-left: 22px;
  margin-bottom: 18px;
}

#privacyModal ul li, #termsModal ul li {
  margin-bottom: 8px;
  list-style: disc inside;
}

#privacyModal a, #termsModal a {
  color: #2a7be4;
  text-decoration: underline;
  transition: color 0.2s;
}
#privacyModal a:hover, #termsModal a:hover {
  color: #174a8c;
}

#privacyModal .close, #termsModal .close {
  position: absolute;
  top: 18px;
  right: 24px;
  font-size: 2rem;
  color: #aaa;
  cursor: pointer;
  transition: color 0.2s;
}
#privacyModal .close:hover, #termsModal .close:hover {
  color: #2a7be4;
}

@media (max-width: 600px) {
  #privacyModal .modal-content,
  #termsModal .modal-content {
    padding: 18px 8px 16px 8px;
    max-width: 98vw;
  }
  #privacyModal h2, #termsModal h2 {
    font-size: 1.3rem;
  }
}
/* --- Help Center Modal Enhancements --- */
#helpModal.modal {
  position: fixed;
  z-index: 10000;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(30, 32, 38, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s;
}
#helpModal .modal-content {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.25);
  padding: 2.5rem 2rem 2rem 2rem;
  max-width: 440px;
  width: 90vw;
  text-align: center;
  position: relative;
  animation: modalPopIn 0.3s cubic-bezier(.4,1.6,.6,1) 1;
}
#helpModal .modal-content h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1.2rem;
  color: #2d3a4a;
  letter-spacing: 1px;
}
#helpModal .close {
  position: absolute;
  top: 18px;
  right: 22px;
  font-size: 2rem;
  color: #888;
  cursor: pointer;
  transition: color 0.2s;
  font-weight: 700;
}
#helpModal .close:hover {
  color: #e74c3c;
}
#helpModal .form-group {
  margin-bottom: 1.2rem;
  text-align: left;
}
#helpModal label {
  display: block;
  font-size: 1rem;
  color: #2d3a4a;
  margin-bottom: 0.4rem;
  font-weight: 600;
}
#helpModal input[type="text"],
#helpModal input[type="email"],
#helpModal textarea {
  width: 100%;
  padding: 0.7rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  background: #f8fafc;
  color: #222;
  transition: border 0.2s;
  box-sizing: border-box;
}
#helpModal input[type="text"]:focus,
#helpModal input[type="email"]:focus,
#helpModal textarea:focus {
  border: 1.5px solid #4f8cff;
  outline: none;
}
#helpModal textarea {
  min-height: 90px;
  resize: vertical;
}
#helpModal .btn-primary.full-width {
  width: 100%;
  margin-top: 0.5rem;
  padding: 0.9rem 0;
  font-size: 1.1rem;
  border-radius: 8px;
  background: linear-gradient(90deg, #4f8cff 0%, #2d3a4a 100%);
  color: #fff;
  font-weight: 700;
  border: none;
  cursor: pointer;
  transition: background 0.2s;
}
#helpModal .btn-primary.full-width:hover {
  background: linear-gradient(90deg, #2d3a4a 0%, #4f8cff 100%);
}
/* --- About Modal Enhancements --- */
#aboutModal.modal {
  position: fixed;
  z-index: 10000;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(30, 32, 38, 0.75); /* semi-transparent dark overlay */
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s;
}
#aboutModal .modal-content {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.25);
  padding: 2.5rem 2rem 2rem 2rem;
  max-width: 480px;
  width: 90vw;
  text-align: center;
  position: relative;
  animation: modalPopIn 0.3s cubic-bezier(.4,1.6,.6,1) 1;
}
#aboutModal .modal-content h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #2d3a4a;
  letter-spacing: 1px;
}
#aboutModal .modal-content p {
  font-size: 1.08rem;
  color: #444;
  line-height: 1.7;
  margin-bottom: 0;
}
#aboutModal .close {
  position: absolute;
  top: 18px;
  right: 22px;
  font-size: 2rem;
  color: #888;
  cursor: pointer;
  transition: color 0.2s;
  font-weight: 700;
}
#aboutModal .close:hover {
  color: #e74c3c;
}
@keyframes modalPopIn {
  0% { transform: scale(0.85) translateY(40px); opacity: 0; }
  100% { transform: scale(1) translateY(0); opacity: 1; }
}


/* Landing Page Styles for DocuGen Pro */

:root {
    --primary-color: #4a9eff;
    --secondary-color: #667eea;
    --accent-color: #764ba2;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    --border-color: #dee2e6;
    --text-color: #333;
    --text-muted: #6c757d;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --border-radius: 8px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Open Sans', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background: #f8f9fa;
}

/* Navigation */
.navbar {
    background: white;
    box-shadow: var(--shadow);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    font-family: 'Montserrat', sans-serif;
    color: var(--primary-color);
    font-weight: 700;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    transition: color 0.3s;
}

.nav-link:hover {
    color: var(--primary-color);
}

/* Buttons */
.btn-primary, .btn-secondary, .btn-outline {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(74, 158, 255, 0.3);
}

.btn-secondary {
    background: var(--light-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--border-color);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

.btn-primary.large, .btn-secondary.large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.full-width {
    width: 100%;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 120px 0 80px;
    margin-top: 80px;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-content h1 {
    font-family: 'Montserrat', sans-serif;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Document Preview */
.document-preview {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    color: var(--text-color);
    transform: rotate(5deg);
    transition: transform 0.3s ease;
}

.document-preview:hover {
    transform: rotate(0deg);
}

.preview-header {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

.preview-line {
    height: 8px;
    background: #e9ecef;
    margin-bottom: 8px;
    border-radius: 4px;
}

.preview-line.short {
    width: 60%;
}

.preview-table {
    margin-top: 1rem;
}

.table-row {
    height: 6px;
    background: #f8f9fa;
    margin-bottom: 4px;
    border-radius: 3px;
}

/* Sections */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

section {
    padding: 80px 0;
}

section h2 {
    font-family: 'Montserrat', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: var(--text-muted);
    margin-bottom: 3rem;
}

/* Free Upload & Sign Section */
.free-upload-sign {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.free-sign-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.free-sign-text h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #fff, #f8f9fa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.free-sign-text p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.free-features {
    list-style: none;
    padding: 0;
    margin: 30px 0;
}

.free-features li {
    padding: 10px 0;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.btn-free-sign {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 18px 40px;
    font-size: 1.2rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-free-sign:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(40, 167, 69, 0.4);
    background: linear-gradient(135deg, #20c997, #28a745);
}

.free-sign-preview {
    display: flex;
    justify-content: center;
    align-items: center;
}

.preview-mockup {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 30px;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.mockup-header {
    text-align: center;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.3);
}

.mockup-step {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px 20px;
    margin: 10px 0;
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.mockup-step.active {
    background: linear-gradient(135deg, #28a745, #20c997);
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

@media (max-width: 768px) {
    .free-sign-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .free-sign-text h2 {
        font-size: 2rem;
    }

    .btn-free-sign {
        padding: 15px 30px;
        font-size: 1.1rem;
    }
}

/* Document Types Grid */
.types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.type-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid var(--border-color);
}

.type-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.type-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.type-card h3 {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.type-card p {
    color: var(--text-muted);
    margin-bottom: 1.5rem;
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-item {
    text-align: center;
    padding: 1.5rem;
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.feature-item h3 {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.feature-item p {
    color: var(--text-muted);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    position: relative;
}

.preview-content {
    max-width: 800px;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    position: absolute;
    right: 20px;
    top: 15px;
    cursor: pointer;
    z-index: 1;
}

.close:hover {
    color: black;
}

/* Auth Forms */
.auth-form {
    padding: 2rem;
}

.auth-form h2 {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-align: center;
    color: var(--dark-color);
}

.auth-form p {
    text-align: center;
    color: var(--text-muted);
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--dark-color);
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 158, 255, 0.1);
}

.auth-switch {
    text-align: center;
    margin-top: 1.5rem;
    color: var(--text-muted);
}

.auth-switch a {
    color: var(--primary-color);
    text-decoration: none;
}

.auth-switch a:hover {
    text-decoration: underline;
}

/* Footer */
.footer {
    background: var(--dark-color);
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3, .footer-section h4 {
    font-family: 'Montserrat', sans-serif;
    margin-bottom: 1rem;
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    display: block;
    margin-bottom: 0.5rem;
    transition: color 0.3s;
}

.footer-section a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid #555;
    padding-top: 1rem;
    text-align: center;
    color: #ccc;
}

.attribution {
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .nav-menu {
        flex-direction: column;
        gap: 1rem;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .types-grid, .features-grid {
        grid-template-columns: 1fr;
    }
}

/* A4 Paper Document Preview Styles */
.realistic-preview {
    /* A4 aspect ratio: 210mm x 297mm (1:1.414) */
    width: 100%;
    max-width: 600px;
    aspect-ratio: 210 / 297;
    margin: 0 auto;

    /* Paper-like appearance */
    background: #ffffff;
    background-image:
        linear-gradient(90deg, transparent 0%, transparent 95%, rgba(0,0,0,0.02) 95%, rgba(0,0,0,0.02) 100%),
        linear-gradient(0deg, transparent 0%, transparent 95%, rgba(0,0,0,0.02) 95%, rgba(0,0,0,0.02) 100%);
    background-size: 20px 20px;

    /* A4 paper styling */
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 40px 35px;

    /* Paper shadow effect */
    box-shadow:
        0 8px 16px rgba(0,0,0,0.1),
        0 4px 8px rgba(0,0,0,0.06),
        0 2px 4px rgba(0,0,0,0.04),
        inset 0 1px 0 rgba(255,255,255,0.8);

    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0) scale(1);
    font-family: 'Times New Roman', 'Segoe UI', serif;
    line-height: 1.5;
    color: #2c3e50;
    font-size: 14px;

    /* Scrollable content */
    overflow-y: auto;
    max-height: 80vh;
}

/* Paper texture overlay */
.realistic-preview::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 50%, rgba(120,119,108,0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(120,119,108,0.03) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(120,119,108,0.03) 0%, transparent 50%);
    pointer-events: none;
    opacity: 0.5;
}

/* A4 Paper hover effects */
.realistic-preview::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
        #007bff 0%,
        #28a745 25%,
        #ffc107 50%,
        #dc3545 75%,
        #007bff 100%);
    background-size: 400% 400%;
    animation: gradientShift 4s ease-in-out infinite;
    opacity: 0;
    transition: opacity 0.4s ease;
    border-radius: 10px;
    z-index: -1;
}

.realistic-preview:hover {
    transform: translateY(-8px) rotateX(5deg) rotateY(2deg);
    box-shadow:
        0 25px 50px rgba(0,0,0,0.15),
        0 15px 30px rgba(0,0,0,0.1),
        0 8px 16px rgba(0,0,0,0.08),
        inset 0 1px 0 rgba(255,255,255,0.9);
    border-color: #d0d0d0;
}

.realistic-preview:hover::before {
    opacity: 1;
}

.realistic-preview:hover::after {
    opacity: 0.8;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* A4 Document Header */
.document-header {
    position: relative;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
}

.document-header::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: #007bff;
    border-radius: 1px;
    animation: expandLine 1.5s ease-out;
}

@keyframes expandLine {
    from { width: 0; }
    to { width: 80px; }
}

/* A4 Document Logo Styling */
.preview-logo {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;
    position: relative;
    float: left;
    margin-right: 20px;
    margin-bottom: 10px;
}

.preview-logo::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #007bff, #28a745, #ffc107, #dc3545);
    border-radius: 10px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.preview-logo:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.preview-logo:hover::after {
    opacity: 1;
}

/* Company info in header */
.company-info {
    flex: 1;
    min-width: 200px;
}

.company-info h1 {
    margin: 0 0 5px 0;
    font-size: 1.8rem;
    font-weight: 700;
}

.company-info p {
    margin: 2px 0;
    font-size: 12px;
    color: #666;
    line-height: 1.3;
}

/* Document title and meta */
.document-title-section {
    text-align: right;
    min-width: 150px;
}

.document-title-section h1 {
    margin: 0 0 10px 0;
    font-size: 2rem;
    font-weight: 800;
    letter-spacing: 2px;
}

.document-meta {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* A4 Document Typography */
.realistic-preview h1 {
    font-family: 'Times New Roman', serif;
    font-weight: 700;
    letter-spacing: 1px;
    color: #2c3e50;
    margin-bottom: 15px;
    position: relative;
    text-transform: uppercase;
}

.realistic-preview h2 {
    font-family: 'Times New Roman', serif;
    font-weight: 600;
    font-size: 1.3rem;
    color: #2c3e50;
    margin: 20px 0 10px 0;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 5px;
}

.realistic-preview h3 {
    font-family: 'Times New Roman', serif;
    font-weight: 600;
    font-size: 1.1rem;
    color: #2c3e50;
    margin: 15px 0 8px 0;
    position: relative;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.realistic-preview h3::before {
    content: '';
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 18px;
    background: #007bff;
    border-radius: 1px;
}

.realistic-preview p {
    font-family: 'Times New Roman', serif;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    margin: 8px 0;
}

.realistic-preview strong {
    font-weight: 600;
    color: #2c3e50;
}

.realistic-preview small {
    font-size: 12px;
    color: #666;
}

/* A4 Document Tables */
.invoice-table, .quote-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #d0d0d0;
    margin: 20px 0;
    font-family: 'Times New Roman', serif;
    font-size: 13px;
    background: #ffffff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.invoice-table thead, .quote-table thead {
    background: #f8f9fa;
    border-bottom: 2px solid #d0d0d0;
}

.invoice-table th, .quote-table th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.5px;
    color: #2c3e50;
    border: 1px solid #d0d0d0;
    padding: 12px 10px;
    text-align: left;
    background: #f8f9fa;
}

.invoice-table td, .quote-table td {
    border: 1px solid #e0e0e0;
    padding: 10px;
    vertical-align: top;
    transition: background-color 0.2s ease;
}

.invoice-table tbody tr:hover td, .quote-table tbody tr:hover td {
    background-color: #f9f9f9;
}

.invoice-table tbody tr:nth-child(even), .quote-table tbody tr:nth-child(even) {
    background-color: #fafafa;
}

/* Table alignment */
.invoice-table td:nth-child(2), .quote-table td:nth-child(2),
.invoice-table td:nth-child(3), .quote-table td:nth-child(3),
.invoice-table td:nth-child(4), .quote-table td:nth-child(4) {
    text-align: right;
}

.invoice-table th:nth-child(2), .quote-table th:nth-child(2),
.invoice-table th:nth-child(3), .quote-table th:nth-child(3),
.invoice-table th:nth-child(4), .quote-table th:nth-child(4) {
    text-align: right;
}

/* Enhanced Section Styling */
.invoice-details, .quote-details, .receipt-details {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 16px;
    padding: 25px;
    border: 2px solid transparent;
    background-clip: padding-box;
    box-shadow: 0 6px 16px rgba(0,0,0,0.08);
    position: relative;
    animation: slideInRight 0.7s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(20px); }
    to { opacity: 1; transform: translateX(0); }
}

.payment-info, .quote-terms, .contract-terms {
    background: linear-gradient(135deg, #e8f4fd 0%, #f8f9fa 100%);
    border-radius: 16px;
    padding: 25px;
    border-left: 6px solid #007bff;
    box-shadow:
        0 8px 20px rgba(0,123,255,0.15),
        0 4px 8px rgba(0,123,255,0.08);
    position: relative;
    animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes pulseGlow {
    0%, 100% { box-shadow: 0 8px 20px rgba(0,123,255,0.15), 0 4px 8px rgba(0,123,255,0.08); }
    50% { box-shadow: 0 12px 30px rgba(0,123,255,0.25), 0 6px 12px rgba(0,123,255,0.15); }
}

/* Enhanced Signature Section */
.signature-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 20px;
    padding: 30px;
    margin: 35px 0;
    border: 3px solid transparent;
    background-clip: padding-box;
    position: relative;
    overflow: hidden;
    animation: signatureEntrance 1s ease-out;
}

@keyframes signatureEntrance {
    from { opacity: 0; transform: translateY(30px) scale(0.95); }
    to { opacity: 1; transform: translateY(0) scale(1); }
}

.signature-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #007bff, #28a745, #ffc107, #dc3545);
    margin: -3px;
    border-radius: 20px;
    z-index: -1;
    animation: rotateBorder 4s linear infinite;
}

@keyframes rotateBorder {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.signature-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.4rem;
}

.signature-section h3::before {
    content: '✍️';
    font-size: 28px;
    background: none;
    width: auto;
    height: auto;
    position: static;
    transform: none;
    animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* Enhanced Action Buttons */
.preview-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 35px;
    flex-wrap: wrap;
    animation: buttonsSlideUp 1.2s ease-out;
}

@keyframes buttonsSlideUp {
    from { opacity: 0; transform: translateY(40px); }
    to { opacity: 1; transform: translateY(0); }
}

.preview-actions button {
    padding: 16px 32px;
    border-radius: 16px;
    font-weight: 700;
    font-size: 15px;
    letter-spacing: 1px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    min-width: 180px;
    text-transform: uppercase;
}

.preview-actions button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s ease;
}

.preview-actions button:hover::before {
    left: 100%;
}

.preview-actions .btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    box-shadow:
        0 8px 20px rgba(0,123,255,0.4),
        0 4px 8px rgba(0,123,255,0.2);
    border: 2px solid transparent;
}

.preview-actions .btn-primary:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow:
        0 15px 35px rgba(0,123,255,0.5),
        0 8px 16px rgba(0,123,255,0.3);
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
}

.preview-actions .btn-outline {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    color: #007bff;
    border: 3px solid #007bff;
    box-shadow:
        0 8px 20px rgba(0,123,255,0.2),
        0 4px 8px rgba(0,123,255,0.1);
}

.preview-actions .btn-outline:hover {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    transform: translateY(-4px) scale(1.05);
    box-shadow:
        0 15px 35px rgba(0,123,255,0.4),
        0 8px 16px rgba(0,123,255,0.2);
    border-color: #0056b3;
}

/* Document Type Specific Colors with Glow Effects */
.invoice-preview h1 {
    color: #007bff;
    text-shadow: 0 3px 6px rgba(0,123,255,0.3), 0 0 20px rgba(0,123,255,0.2);
}
.receipt-preview h1 {
    color: #28a745;
    text-shadow: 0 3px 6px rgba(40,167,69,0.3), 0 0 20px rgba(40,167,69,0.2);
}
.quotation-preview h1 {
    color: #ffc107;
    text-shadow: 0 3px 6px rgba(255,193,7,0.3), 0 0 20px rgba(255,193,7,0.2);
}
.contract-preview h1 {
    color: #6f42c1;
    text-shadow: 0 3px 6px rgba(111,66,193,0.3), 0 0 20px rgba(111,66,193,0.2);
}
.technical-rider-preview h1 {
    color: #dc3545;
    text-shadow: 0 3px 6px rgba(220,53,69,0.3), 0 0 20px rgba(220,53,69,0.2);
}
.annexure-preview h1 {
    color: #6c757d;
    text-shadow: 0 3px 6px rgba(108,117,125,0.3), 0 0 20px rgba(108,117,125,0.2);
}

/* Enhanced Totals Section */
.invoice-totals, .quote-totals, .receipt-total {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 16px;
    padding: 25px;
    border: 2px solid #e9ecef;
    box-shadow:
        0 8px 20px rgba(0,0,0,0.08),
        0 4px 8px rgba(0,0,0,0.04);
    position: relative;
    animation: totalsPulse 3s ease-in-out infinite;
}

@keyframes totalsPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

.invoice-totals::before, .quote-totals::before, .receipt-total::before {
    content: '💰';
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 24px;
    background: linear-gradient(135deg, #28a745, #20c997);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(40,167,69,0.3);
    animation: moneyFloat 2s ease-in-out infinite;
}

@keyframes moneyFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-5px) rotate(5deg); }
}

/* Enhanced Modal Styling */
.modal-content .realistic-preview {
    max-height: 80vh;
    margin: 0;
    border-radius: 0 0 12px 12px;
}

/* A4 Responsive Design */
@media (max-width: 768px) {
    .realistic-preview {
        max-width: 90vw;
        aspect-ratio: 210 / 297;
        padding: 30px 25px;
        margin: 15px auto;
        font-size: 13px;
    }

    .preview-actions {
        flex-direction: column;
        align-items: center;
        gap: 12px;
        margin-top: 25px;
    }

    .preview-actions button {
        width: 100%;
        max-width: 280px;
        padding: 12px 20px;
        font-size: 14px;
    }

    .document-header {
        flex-direction: column;
        text-align: center;
        padding-bottom: 15px;
    }

    .preview-logo {
        float: none !important;
        display: block;
        margin: 0 auto 15px;
        width: 50px !important;
        height: 50px !important;
    }

    .company-info, .document-title-section {
        text-align: center;
        min-width: auto;
    }

    .company-info h1 {
        font-size: 1.5rem;
    }

    .document-title-section h1 {
        font-size: 1.6rem;
    }

    .signature-section {
        padding: 20px;
        margin: 20px 0;
        border-radius: 12px;
    }

    .signature-section h3 {
        font-size: 1.1rem;
        text-align: center;
        justify-content: center;
    }

    .realistic-preview h2 {
        font-size: 1.2rem;
    }

    .realistic-preview h3 {
        font-size: 1rem;
    }

    .invoice-table, .quote-table {
        font-size: 11px;
    }

    .invoice-table th, .quote-table th,
    .invoice-table td, .quote-table td {
        padding: 8px 6px;
    }
}

@media (max-width: 480px) {
    .realistic-preview {
        max-width: 95vw;
        padding: 25px 20px;
        font-size: 12px;
    }

    .company-info h1, .document-title-section h1 {
        font-size: 1.3rem;
    }

    .realistic-preview h2 {
        font-size: 1.1rem;
    }

    .realistic-preview h3 {
        font-size: 0.95rem;
    }

    .invoice-table, .quote-table {
        font-size: 10px;
    }

    .invoice-table th, .quote-table th,
    .invoice-table td, .quote-table td {
        padding: 6px 4px;
    }

    .preview-logo {
        width: 40px !important;
        height: 40px !important;
    }
}

/* Print-like appearance for larger screens */
@media (min-width: 1200px) {
    .realistic-preview {
        max-width: 700px;
        padding: 50px 45px;
        font-size: 15px;
    }

    .company-info h1 {
        font-size: 2rem;
    }

    .document-title-section h1 {
        font-size: 2.2rem;
    }
}

/* Free Signing Section Styles */
.free-signing-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.free-signing-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.free-signing-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    padding: 50px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
}

.free-signing-header {
    text-align: center;
    margin-bottom: 40px;
}

.gift-icon {
    font-size: 64px;
    margin-bottom: 20px;
    animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.free-signing-header h2 {
    color: white;
    font-size: 2.5rem;
    margin: 0 0 15px 0;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.free-signing-header p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.2rem;
    margin: 0;
}

.free-signing-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.free-feature {
    background: rgba(255, 255, 255, 0.1);
    padding: 30px;
    border-radius: 16px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.free-feature:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.free-feature-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.free-feature h4 {
    color: white;
    font-size: 1.3rem;
    margin: 0 0 10px 0;
    font-weight: 600;
}

.free-feature p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-size: 1rem;
}

.free-signing-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.btn-free-primary {
    background: white;
    color: #667eea;
    border: none;
    padding: 18px 36px;
    border-radius: 12px;
    font-weight: 700;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    min-width: 220px;
}

.btn-free-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    background: #f8f9fa;
}

.btn-donate {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid white;
    padding: 18px 36px;
    border-radius: 12px;
    font-weight: 700;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    min-width: 220px;
}

.btn-donate:hover {
    background: white;
    color: #667eea;
    transform: translateY(-3px);
}

.free-signing-note {
    text-align: center;
    padding: 25px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.free-signing-note p {
    color: rgba(255, 255, 255, 0.9);
    margin: 0 0 10px 0;
    font-size: 1rem;
}

.upgrade-hint {
    margin-top: 15px !important;
}

.upgrade-hint a {
    color: white;
    text-decoration: underline;
    font-weight: 600;
    transition: all 0.3s ease;
}

.upgrade-hint a:hover {
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

/* Free Signing Tool Modal Styles */
.free-signing-content {
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
}

.free-signing-tool {
    padding: 30px;
}

.tool-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.tool-header h2 {
    color: #2c3e50;
    margin: 0 0 10px 0;
    font-size: 2rem;
}

.tool-header p {
    color: #666;
    margin: 0;
    font-size: 1.1rem;
}

.signing-steps {
    position: relative;
}

.step {
    margin-bottom: 30px;
}

.step-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.step-number {
    width: 40px;
    height: 40px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
}

.step.active .step-number {
    background: #28a745;
}

.step h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.4rem;
}

.upload-area {
    border: 3px dashed #667eea;
    border-radius: 12px;
    padding: 40px;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #28a745;
    background: #e8f5e8;
}

.upload-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.upload-area p {
    margin: 0 0 20px 0;
    color: #666;
    font-size: 1.1rem;
}

.file-info {
    background: #e8f5e8;
    padding: 20px;
    border-radius: 12px;
    border: 2px solid #28a745;
}

/* Document Signing Area */
.document-signing-area {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 30px;
    margin-bottom: 30px;
}

.document-viewer {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.document-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.document-viewer h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2rem;
}

.document-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.zoom-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 6px 10px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    min-width: 35px;
}

.zoom-btn:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

#zoomLevel {
    font-weight: 600;
    color: #2c3e50;
    min-width: 45px;
    text-align: center;
    font-size: 14px;
}

.document-display {
    position: relative;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    overflow: auto;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    height: 500px;
    cursor: grab;
}

.document-display:active {
    cursor: grabbing;
}

.document-container {
    position: relative;
    transform-origin: 0 0;
    transition: transform 0.3s ease;
    background: white;
    border-radius: 6px;
    margin: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

#documentCanvas {
    width: 100%;
    height: auto;
    display: block;
    cursor: crosshair;
}

.signature-drop-zones {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.signature-on-document {
    position: absolute;
    border: 2px dashed #007bff;
    background: rgba(0,123,255,0.1);
    cursor: move;
    pointer-events: all;
    border-radius: 4px;
    padding: 5px;
    transition: all 0.3s ease;
}

.signature-on-document:hover {
    border-color: #28a745;
    background: rgba(40,167,69,0.1);
    transform: scale(1.02);
}

.signature-on-document.dragging {
    opacity: 0.8;
    transform: scale(1.05);
    z-index: 1000;
}

.signature-on-document img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.signature-on-document .signature-text {
    font-family: cursive;
    font-size: 24px;
    color: #2c3e50;
    text-align: center;
    line-height: 1;
}

.signature-tools {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    height: fit-content;
}

.signature-tools h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 1.2rem;
}

.signature-instructions {
    margin-top: 20px;
    padding: 15px;
    background: #e8f4fd;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.signature-instructions p {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-weight: 600;
}

.signature-instructions ul {
    margin: 0;
    padding-left: 20px;
    color: #666;
}

.signature-instructions li {
    margin-bottom: 5px;
    font-size: 14px;
}

.signing-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.signature-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.tab-btn {
    padding: 12px 20px;
    border: none;
    background: transparent;
    color: #666;
    font-weight: 600;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

.signature-tab {
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.signature-canvas-wrapper {
    position: relative;
    display: inline-block;
}

#signatureCanvas {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
    cursor: crosshair;
    transition: all 0.3s ease;
}

#signatureCanvas.drag-mode {
    cursor: grab;
    border-color: #007bff;
    box-shadow: 0 0 15px rgba(0,123,255,0.3);
}

#signatureCanvas.dragging {
    cursor: grabbing;
    opacity: 0.8;
    transform: scale(1.05);
}

.drag-hint {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,123,255,0.9);
    color: white;
    padding: 15px;
    border-radius: 12px;
    text-align: center;
    pointer-events: none;
    z-index: 10;
    animation: pulse 2s ease-in-out infinite;
}

.drag-hint .drag-icon {
    font-size: 32px;
    margin-bottom: 8px;
}

.drag-hint p {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
}

@keyframes pulse {
    0%, 100% { opacity: 0.9; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 1; transform: translate(-50%, -50%) scale(1.05); }
}

.canvas-controls {
    margin-top: 15px;
}

.signature-input {
    width: 100%;
    max-width: 400px;
    padding: 15px;
    font-size: 24px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    text-align: center;
    margin-bottom: 15px;
}

.font-selector {
    padding: 10px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 15px;
}

.typed-preview {
    min-height: 60px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    width: 100%;
    max-width: 400px;
}

.signature-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

.export-section {
    text-align: center;
    padding: 30px;
}

.success-message {
    background: #e8f5e8;
    padding: 30px;
    border-radius: 12px;
    border: 2px solid #28a745;
    margin-bottom: 30px;
}

.success-icon {
    font-size: 64px;
    margin-bottom: 15px;
}

.success-message h4 {
    color: #28a745;
    margin: 0 0 10px 0;
    font-size: 1.5rem;
}

.export-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.btn-primary.large {
    padding: 18px 36px;
    font-size: 18px;
    min-width: 250px;
}

.upgrade-prompt {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 12px;
    margin-top: 20px;
}

.upgrade-prompt h4 {
    margin: 0 0 10px 0;
    font-size: 1.3rem;
}

.upgrade-prompt p {
    margin: 0 0 20px 0;
    opacity: 0.9;
}

.btn-upgrade {
    background: white;
    color: #667eea;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-upgrade:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Donate Modal Styles */
.donate-content {
    max-width: 600px;
}

.donate-section {
    padding: 30px;
}

.donate-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.heart-icon {
    font-size: 64px;
    margin-bottom: 20px;
    animation: heartbeat 2s ease-in-out infinite;
}

@keyframes heartbeat {
    0%, 50%, 100% { transform: scale(1); }
    25%, 75% { transform: scale(1.1); }
}

.donate-header h2 {
    color: #2c3e50;
    margin: 0 0 10px 0;
    font-size: 2rem;
}

.donate-header p {
    color: #666;
    margin: 0;
    font-size: 1.1rem;
}

.donate-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.donate-feature {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.donate-feature .feature-icon {
    font-size: 32px;
    margin-bottom: 10px;
}

.donate-feature h4 {
    margin: 0 0 8px 0;
    color: #2c3e50;
    font-size: 1.1rem;
}

.donate-feature p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.donate-amounts {
    margin-bottom: 30px;
}

.donate-amounts h4 {
    text-align: center;
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 1.3rem;
}

.amount-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.amount-btn {
    padding: 15px;
    border: 2px solid #667eea;
    background: white;
    color: #667eea;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.amount-btn:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

.custom-amount {
    display: flex;
    gap: 10px;
    justify-content: center;
    align-items: center;
}

.custom-amount input {
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    width: 150px;
    font-size: 1rem;
}

.paypal-section {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    margin-bottom: 20px;
}

.paypal-section p {
    margin: 0 0 15px 0;
    color: #666;
}

.donate-note {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
}

.donate-note p {
    margin: 0;
    font-size: 1rem;
}

/* Responsive Design for Free Signing */
@media (max-width: 768px) {
    .free-signing-card {
        padding: 30px 20px;
    }

    .free-signing-header h2 {
        font-size: 2rem;
    }

    .free-signing-features {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .free-signing-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn-free-primary,
    .btn-donate {
        width: 100%;
        max-width: 300px;
    }

    .free-signing-tool {
        padding: 20px;
    }

    .signature-tools {
        padding: 20px;
    }

    .signature-tabs {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1;
        min-width: 100px;
    }

    #signatureCanvas {
        width: 100%;
        max-width: 350px;
        height: 120px;
    }

    .signature-actions {
        flex-direction: column;
        align-items: center;
    }

    .export-actions {
        flex-direction: column;
        align-items: center;
    }

    .amount-buttons {
        grid-template-columns: repeat(2, 1fr);
    }

    .custom-amount {
        flex-direction: column;
    }

    .custom-amount input {
        width: 100%;
        max-width: 200px;
    }

    .document-signing-area {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .document-viewer {
        order: 1;
    }

    .signature-tools {
        order: 2;
        padding: 20px;
    }

    #documentCanvas {
        max-height: 400px;
    }

    .signing-actions {
        flex-direction: column;
        align-items: center;
    }
}

.realistic-preview .document-header {
    border-bottom: 2px solid #eee;
    padding-bottom: 20px;
    margin-bottom: 20px;
}

.realistic-preview .preview-logo {
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.realistic-preview h1 {
    font-weight: bold;
    letter-spacing: 1px;
}

.realistic-preview h3 {
    margin-bottom: 10px;
    font-weight: 600;
}

.realistic-preview table {
    font-size: 14px;
}

.realistic-preview .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.realistic-preview .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Document-specific styling for landing page */
.invoice-preview .document-header h1 {
    color: #2c3e50;
}

.receipt-preview .document-header h1 {
    color: #28a745;
}

.quotation-preview .document-header h1 {
    color: #f39c12;
}

.technical-rider-preview .document-header h1 {
    color: #e74c3c;
}

.annexure-preview .document-header h1 {
    color: #6c757d;
}

.contract-preview .document-header h1 {
    color: #2c3e50;
}

/* Landing page specific responsive adjustments */
@media (max-width: 768px) {
    .realistic-preview {
        padding: 20px;
        font-size: 14px;
    }

    .realistic-preview .parties-section {
        flex-direction: column;
    }

    .realistic-preview .parties-section > div {
        margin: 10px 0 !important;
    }

    .realistic-preview .quote-details,
    .realistic-preview .invoice-details {
        flex-direction: column;
        gap: 15px;
    }

    .realistic-preview .schedule-requirements > div {
        grid-template-columns: 1fr !important;
    }
}

/* Saved Signatures Section for Free Signing Tool */
.saved-signatures-section {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.saved-signatures-section h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 1rem;
}

.saved-signatures-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 10px;
    max-height: 150px;
    overflow-y: auto;
}

.saved-signature-item {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.saved-signature-item:hover {
    border-color: #007bff;
    background: #f8f9ff;
    transform: translateY(-2px);
}

.saved-signature-item.selected {
    border-color: #28a745;
    background: #e8f5e8;
}

.saved-signature-item img {
    max-width: 100%;
    max-height: 40px;
    object-fit: contain;
    margin-bottom: 5px;
}

.saved-signature-item .signature-name {
    font-size: 10px;
    color: #666;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.saved-signature-item .drag-indicator {
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 8px;
    opacity: 0.6;
}

.no-signatures {
    grid-column: 1 / -1;
    text-align: center;
    color: #999;
    font-style: italic;
    margin: 20px 0;
}

/* Responsive adjustments for saved signatures */
@media (max-width: 768px) {
    .saved-signatures-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 8px;
    }
}

/* Free Upload & Sign Section - SCREAMING FREE! */
.free-upload-sign {
    background: linear-gradient(135deg, #FF6B35 0%, #F7931E 25%, #FFD23F 50%, #06FFA5 75%, #4ECDC4 100%);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
    padding: 100px 0;
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.free-upload-sign::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.free-sign-content {
    position: relative;
    z-index: 2;
}

/* Big FREE Header */
.free-header {
    margin-bottom: 60px;
}

.free-title {
    font-size: 4rem;
    font-weight: 900;
    margin: 0 0 20px 0;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.3);
    animation: pulse 2s ease-in-out infinite alternate;
    background: linear-gradient(45deg, #FFD700, #FFF, #FFD700);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

@keyframes pulse {
    from { transform: scale(1); }
    to { transform: scale(1.05); }
}

.free-subtitle {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 20px 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.free-description {
    font-size: 1.4rem;
    font-weight: 600;
    margin: 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.2);
    padding: 15px 30px;
    border-radius: 50px;
    display: inline-block;
    backdrop-filter: blur(10px);
}

/* Features Grid */
.free-features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin: 60px 0;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.feature-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 30px 20px;
    text-align: center;
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
}

.feature-card h3 {
    font-size: 1.3rem;
    font-weight: 700;
    margin: 0 0 10px 0;
    color: white;
}

.feature-card p {
    font-size: 1rem;
    margin: 0;
    opacity: 0.9;
    color: white;
}

/* Big CTA Section */
.free-cta {
    margin-top: 60px;
}

.btn-free-sign {
    background: linear-gradient(45deg, #FF6B35, #F7931E, #FFD23F);
    background-size: 300% 300%;
    animation: gradientShift 3s ease infinite;
    color: #000;
    border: none;
    padding: 25px 60px;
    font-size: 1.5rem;
    font-weight: 900;
    border-radius: 60px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    text-transform: uppercase;
    letter-spacing: 2px;
    border: 3px solid #FFF;
    position: relative;
    overflow: hidden;
}

.btn-free-sign::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.btn-free-sign:hover::before {
    left: 100%;
}

.btn-free-sign:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

.cta-subtext {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 20px 0 0 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 20px;
    border-radius: 25px;
    display: inline-block;
    backdrop-filter: blur(5px);
}

/* Mobile Responsiveness for FREE Section */
@media (max-width: 768px) {
    .free-title {
        font-size: 2.5rem;
    }

    .free-subtitle {
        font-size: 1.8rem;
    }

    .free-description {
        font-size: 1.1rem;
        padding: 12px 20px;
    }

    .free-features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        margin: 40px 0;
    }

    .btn-free-sign {
        padding: 20px 40px;
        font-size: 1.2rem;
    }

    .cta-subtext {
        font-size: 1rem;
    }
}
