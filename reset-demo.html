<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Demo Data</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            text-align: center;
        }
        .reset-btn {
            background: #f44336;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .reset-btn:hover {
            background: #d32f2f;
        }
        .nav-btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .nav-btn:hover {
            background: #3367d6;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            background: #f5f5f5;
        }
    </style>
</head>
<body>
    <h1>🔄 Reset Demo Data</h1>
    <p>This page allows you to reset all demo data and start fresh with the freemium system.</p>
    
    <div class="status" id="status">
        Ready to reset...
    </div>
    
    <button class="reset-btn" onclick="resetAllData()">🗑️ Clear All Data & Reload</button>
    
    <div style="margin-top: 30px;">
        <h3>Navigation</h3>
        <a href="landing.html" class="nav-btn">🏠 Landing Page</a>
        <a href="dashboard2.html" class="nav-btn">📊 Dashboard</a>
        <a href="test-freemium.html" class="nav-btn">🧪 Test Page</a>
    </div>
    
    <script>
        function resetAllData() {
            const status = document.getElementById('status');
            
            try {
                // Clear all localStorage
                localStorage.clear();
                
                status.innerHTML = '✅ All data cleared successfully!<br>Reloading page in 2 seconds...';
                status.style.background = '#e8f5e8';
                status.style.color = '#2e7d32';
                
                // Reload after 2 seconds
                setTimeout(() => {
                    window.location.href = 'landing.html';
                }, 2000);
                
            } catch (error) {
                status.innerHTML = '❌ Error clearing data: ' + error.message;
                status.style.background = '#ffebee';
                status.style.color = '#c62828';
            }
        }
        
        // Show current localStorage contents
        document.addEventListener('DOMContentLoaded', function() {
            const keys = Object.keys(localStorage);
            const status = document.getElementById('status');
            
            if (keys.length === 0) {
                status.innerHTML = '✅ No data found in localStorage';
                status.style.background = '#e8f5e8';
            } else {
                status.innerHTML = `📦 Found ${keys.length} items in localStorage:<br>` + 
                                 keys.map(key => `• ${key}`).join('<br>');
                status.style.background = '#fff3e0';
            }
        });
    </script>
</body>
</html>
