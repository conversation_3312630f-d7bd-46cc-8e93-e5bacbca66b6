// Google Authentication Integration for DocuGen Pro
console.log('🔐 Google Auth initializing...');

class GoogleAuthSystem {
    constructor() {
        this.isInitialized = false;
        this.currentUser = null;
        this.clientId = null; // Will be set to demo mode for now
        this.demoMode = true; // Enable demo mode for development
        this.init();
    }
    
    async init() {
        try {
            if (this.demoMode) {
                console.log('🔧 Running in demo mode - Google Auth disabled');
                this.initializeFallbackAuth();
                this.checkExistingSession();
                this.isInitialized = true;
                console.log('✅ Google Auth initialized successfully (Demo Mode)');
                return;
            }

            // Load Google Identity Services
            await this.loadGoogleIdentityServices();

            // Initialize Google Sign-In
            this.initializeGoogleSignIn();

            // Check if user is already signed in
            this.checkExistingSession();

            this.isInitialized = true;
            console.log('✅ Google Auth initialized successfully');
        } catch (error) {
            console.error('❌ Google Auth initialization failed:', error);
            // Fallback to email/password auth
            this.initializeFallbackAuth();
        }
    }
    
    loadGoogleIdentityServices() {
        return new Promise((resolve, reject) => {
            // Check if already loaded
            if (window.google && window.google.accounts) {
                resolve();
                return;
            }
            
            // Load Google Identity Services script
            const script = document.createElement('script');
            script.src = 'https://accounts.google.com/gsi/client';
            script.async = true;
            script.defer = true;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }
    
    initializeGoogleSignIn() {
        if (this.demoMode) {
            console.log('🔧 Skipping Google Sign-In initialization (Demo Mode)');
            this.renderDemoSignInButtons();
            return;
        }

        if (!window.google || !window.google.accounts) {
            console.warn('Google Identity Services not available');
            return;
        }

        // Initialize Google Sign-In
        google.accounts.id.initialize({
            client_id: this.clientId,
            callback: this.handleGoogleSignIn.bind(this),
            auto_select: false,
            cancel_on_tap_outside: false
        });

        // Render sign-in buttons
        this.renderSignInButtons();
    }

    renderDemoSignInButtons() {
        // Create demo Google Sign-In buttons for development
        const landingSignInContainer = document.getElementById('google-signin-landing');
        if (landingSignInContainer) {
            landingSignInContainer.innerHTML = `
                <button class="demo-google-btn" onclick="googleAuth.demoGoogleSignIn('signin')">
                    <img src="data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" alt="Google">
                    Sign in with Google (Demo)
                </button>
            `;
        }

        const signupSignInContainer = document.getElementById('google-signin-signup');
        if (signupSignInContainer) {
            signupSignInContainer.innerHTML = `
                <button class="demo-google-btn signup" onclick="googleAuth.demoGoogleSignIn('signup')">
                    <img src="data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" alt="Google">
                    Sign up with Google (Demo)
                </button>
            `;
        }
    }

    demoGoogleSignIn(type) {
        // Demo Google Sign-In for development
        const demoUser = {
            sub: 'demo_' + Date.now(),
            email: type === 'signup' ? '<EMAIL>' : '<EMAIL>',
            name: type === 'signup' ? 'New Demo User' : 'Demo User',
            picture: 'https://via.placeholder.com/96x96/4285f4/ffffff?text=DU'
        };

        this.handleGoogleSignIn({ credential: this.createDemoJWT(demoUser) });
    }

    createDemoJWT(userInfo) {
        // Create a demo JWT token for development
        const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
        const payload = btoa(JSON.stringify(userInfo));
        const signature = btoa('demo_signature');
        return `${header}.${payload}.${signature}`;
    }

    renderSignInButtons() {
        // Render Google Sign-In button on landing page
        const landingSignInContainer = document.getElementById('google-signin-landing');
        if (landingSignInContainer) {
            google.accounts.id.renderButton(landingSignInContainer, {
                theme: 'outline',
                size: 'large',
                text: 'signin_with',
                shape: 'rectangular',
                logo_alignment: 'left'
            });
        }
        
        // Render Google Sign-In button on signup modal
        const signupSignInContainer = document.getElementById('google-signin-signup');
        if (signupSignInContainer) {
            google.accounts.id.renderButton(signupSignInContainer, {
                theme: 'filled_blue',
                size: 'large',
                text: 'signup_with',
                shape: 'rectangular',
                logo_alignment: 'left'
            });
        }
    }
    
    handleGoogleSignIn(response) {
        try {
            // Decode JWT token
            const userInfo = this.parseJWT(response.credential);
            
            // Create user session
            this.createUserSession(userInfo);
            
            // Initialize freemium system for new user
            this.initializeFreemiumForUser(userInfo);
            
            // Redirect to dashboard
            this.redirectToDashboard();
            
        } catch (error) {
            console.error('Google Sign-In error:', error);
            this.showAuthError('Failed to sign in with Google. Please try again.');
        }
    }
    
    parseJWT(token) {
        try {
            const base64Url = token.split('.')[1];
            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
            const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
            }).join(''));
            
            return JSON.parse(jsonPayload);
        } catch (error) {
            throw new Error('Invalid JWT token');
        }
    }
    
    createUserSession(userInfo) {
        const userData = {
            id: userInfo.sub,
            email: userInfo.email,
            name: userInfo.name,
            picture: userInfo.picture,
            provider: 'google',
            signInTime: new Date().toISOString()
        };
        
        // Store user data
        localStorage.setItem('currentUser', JSON.stringify(userData));
        this.currentUser = userData;
        
        // Update UI
        this.updateUIForSignedInUser(userData);
    }
    
    initializeFreemiumForUser(userInfo) {
        // Check if user already has token data
        const existingTokenData = localStorage.getItem(`tokens_${userInfo.email}`);
        
        if (!existingTokenData) {
            // New user - initialize with signup bonus
            if (window.freemiumSystem) {
                freemiumSystem.initializeNewUser(userInfo.email, userInfo.name);
            }
        } else {
            // Existing user - load their data
            if (window.freemiumSystem) {
                freemiumSystem.currentUser = { email: userInfo.email, name: userInfo.name };
                freemiumSystem.loadUserTokens();
                freemiumSystem.updateTokenDisplay();
            }
        }
    }
    
    updateUIForSignedInUser(userData) {
        // Update welcome message
        const welcomeElements = document.querySelectorAll('#userWelcome');
        welcomeElements.forEach(element => {
            element.textContent = `Welcome, ${userData.name}`;
        });
        
        // Update profile picture if available
        const profilePictures = document.querySelectorAll('.user-profile-picture');
        profilePictures.forEach(img => {
            if (userData.picture) {
                img.src = userData.picture;
                img.style.display = 'block';
            }
        });
        
        // Hide sign-in buttons, show sign-out
        this.toggleAuthButtons(true);
    }
    
    toggleAuthButtons(isSignedIn) {
        const signInButtons = document.querySelectorAll('.auth-signin');
        const signOutButtons = document.querySelectorAll('.auth-signout');
        
        signInButtons.forEach(btn => {
            btn.style.display = isSignedIn ? 'none' : 'block';
        });
        
        signOutButtons.forEach(btn => {
            btn.style.display = isSignedIn ? 'block' : 'none';
        });
    }
    
    checkExistingSession() {
        const userData = localStorage.getItem('currentUser');
        if (userData) {
            this.currentUser = JSON.parse(userData);
            this.updateUIForSignedInUser(this.currentUser);
            
            // Initialize freemium system
            if (window.freemiumSystem) {
                freemiumSystem.currentUser = this.currentUser;
                freemiumSystem.loadUserTokens();
                freemiumSystem.updateTokenDisplay();
            }
        }
    }
    
    signOut() {
        // Google Sign-Out
        if (window.google && google.accounts) {
            google.accounts.id.disableAutoSelect();
        }
        
        // Clear local storage
        localStorage.removeItem('currentUser');
        this.currentUser = null;
        
        // Update UI
        this.toggleAuthButtons(false);
        
        // Reset welcome message
        const welcomeElements = document.querySelectorAll('#userWelcome');
        welcomeElements.forEach(element => {
            element.textContent = 'Welcome, User';
        });
        
        // Redirect to landing page
        if (window.location.pathname !== '/landing.html') {
            window.location.href = 'landing.html';
        }
        
        this.showAuthSuccess('Successfully signed out');
    }
    
    redirectToDashboard() {
        // Show success message
        this.showAuthSuccess('Successfully signed in! Redirecting to dashboard...');
        
        // Redirect after short delay
        setTimeout(() => {
            window.location.href = 'dashboard2.html';
        }, 1500);
    }
    
    // Fallback authentication for when Google Auth is not available
    initializeFallbackAuth() {
        console.log('🔄 Initializing fallback email authentication...');
        
        // Create simple email/password forms
        this.createFallbackAuthForms();
    }
    
    createFallbackAuthForms() {
        // This would create email/password forms as fallback
        // For now, we'll just enable a simple email-based auth
        console.log('📧 Fallback email auth ready');
    }
    
    // Simple email authentication fallback
    signInWithEmail(email, password) {
        // Simple validation
        if (!email || !email.includes('@')) {
            this.showAuthError('Please enter a valid email address');
            return;
        }
        
        // Create user session (simplified)
        const userData = {
            id: 'email_' + Date.now(),
            email: email,
            name: email.split('@')[0],
            picture: null,
            provider: 'email',
            signInTime: new Date().toISOString()
        };
        
        this.createUserSession(userData);
        this.initializeFreemiumForUser(userData);
        this.redirectToDashboard();
    }
    
    signUpWithEmail(email, password, name) {
        // Simple validation
        if (!email || !email.includes('@')) {
            this.showAuthError('Please enter a valid email address');
            return;
        }
        
        if (!name || name.trim().length < 2) {
            this.showAuthError('Please enter your full name');
            return;
        }
        
        // Create user session
        const userData = {
            id: 'email_' + Date.now(),
            email: email,
            name: name.trim(),
            picture: null,
            provider: 'email',
            signInTime: new Date().toISOString()
        };
        
        this.createUserSession(userData);
        this.initializeFreemiumForUser(userData);
        this.redirectToDashboard();
    }
    
    showAuthError(message) {
        const notification = document.createElement('div');
        notification.className = 'notification error';
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => notification.classList.add('show'), 100);
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 4000);
    }
    
    showAuthSuccess(message) {
        const notification = document.createElement('div');
        notification.className = 'notification success';
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => notification.classList.add('show'), 100);
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
    
    // Check if user is authenticated
    isAuthenticated() {
        return this.currentUser !== null;
    }
    
    // Get current user
    getCurrentUser() {
        return this.currentUser;
    }
    
    // Require authentication for protected actions
    requireAuth(callback) {
        if (this.isAuthenticated()) {
            callback();
        } else {
            this.showAuthError('Please sign in to use this feature');
            this.showSignInModal();
        }
    }
    
    showSignInModal() {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.display = 'block';
        modal.innerHTML = `
            <div class="modal-content auth-modal">
                <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                <h2>🔐 Sign In Required</h2>
                <p>Please sign in to access this feature</p>
                
                <div class="auth-options">
                    <div id="modal-google-signin" class="google-signin-container"></div>
                    
                    <div class="auth-divider">
                        <span>or</span>
                    </div>
                    
                    <form class="email-auth-form" onsubmit="googleAuth.handleEmailSignIn(event)">
                        <input type="email" placeholder="Email address" required>
                        <input type="password" placeholder="Password" required>
                        <button type="submit" class="btn-primary">Sign In</button>
                    </form>
                    
                    <p class="auth-switch">
                        Don't have an account? 
                        <a href="#" onclick="googleAuth.showSignUpModal()">Sign up here</a>
                    </p>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
        
        // Render Google Sign-In button in modal
        if (this.isInitialized && window.google) {
            setTimeout(() => {
                const container = document.getElementById('modal-google-signin');
                if (container) {
                    google.accounts.id.renderButton(container, {
                        theme: 'outline',
                        size: 'large',
                        text: 'signin_with',
                        shape: 'rectangular'
                    });
                }
            }, 100);
        }
    }
    
    showSignUpModal() {
        // Close existing modal
        const existingModal = document.querySelector('.auth-modal');
        if (existingModal) {
            existingModal.closest('.modal').remove();
        }
        
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.display = 'block';
        modal.innerHTML = `
            <div class="modal-content auth-modal">
                <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                <h2>🚀 Create Account</h2>
                <p>Join DocuGen Pro and get 25 free tokens!</p>
                
                <div class="auth-options">
                    <div id="modal-google-signup" class="google-signin-container"></div>
                    
                    <div class="auth-divider">
                        <span>or</span>
                    </div>
                    
                    <form class="email-auth-form" onsubmit="googleAuth.handleEmailSignUp(event)">
                        <input type="text" placeholder="Full Name" required>
                        <input type="email" placeholder="Email address" required>
                        <input type="password" placeholder="Password" required>
                        <button type="submit" class="btn-primary">Create Account</button>
                    </form>
                    
                    <p class="auth-switch">
                        Already have an account? 
                        <a href="#" onclick="googleAuth.showSignInModal()">Sign in here</a>
                    </p>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
        
        // Render Google Sign-Up button in modal
        if (this.isInitialized && window.google) {
            setTimeout(() => {
                const container = document.getElementById('modal-google-signup');
                if (container) {
                    google.accounts.id.renderButton(container, {
                        theme: 'filled_blue',
                        size: 'large',
                        text: 'signup_with',
                        shape: 'rectangular'
                    });
                }
            }, 100);
        }
    }
    
    handleEmailSignIn(event) {
        event.preventDefault();
        const form = event.target;
        const email = form.querySelector('input[type="email"]').value;
        const password = form.querySelector('input[type="password"]').value;
        
        this.signInWithEmail(email, password);
        form.closest('.modal').remove();
    }
    
    handleEmailSignUp(event) {
        event.preventDefault();
        const form = event.target;
        const name = form.querySelector('input[type="text"]').value;
        const email = form.querySelector('input[type="email"]').value;
        const password = form.querySelector('input[type="password"]').value;
        
        this.signUpWithEmail(email, password, name);
        form.closest('.modal').remove();
    }
}

// Initialize Google Auth
const googleAuth = new GoogleAuthSystem();

// Export for global use
window.googleAuth = googleAuth;

// Global logout function
function logout() {
    googleAuth.signOut();
}

console.log('✅ Google Auth system loaded successfully');
