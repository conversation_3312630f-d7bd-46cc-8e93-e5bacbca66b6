// Freemium Token System for DocuGen Pro
console.log('🪙 Freemium System initializing...');

class FreemiumSystem {
    constructor() {
        this.currentUser = null;
        this.userTokens = null; // Cache for user token data
        this.tokenConfig = {
            // Free signup bonus tokens
            signupBonus: 25,
            
            // Token costs for actions
            costs: {
                document_generation: {
                    invoice: 2,
                    receipt: 1,
                    quotation: 2,
                    contract: 3,
                    rider: 2,
                    annexure: 1
                },
                pdf_operations: {
                    export: 1,
                    download: 1,
                    signature: 1
                },
                company_management: {
                    create_company: 2,
                    upload_logo: 1,
                    client_profile: 1
                }
            },
            
            // Subscription packages
            subscriptions: {
                starter: {
                    name: "Starter Pack",
                    tokens: 50,
                    price: 9.99,
                    duration: 30, // days
                    features: ["Basic document generation", "PDF export", "Email support"]
                },
                professional: {
                    name: "Professional Pack",
                    tokens: 150,
                    price: 24.99,
                    duration: 30,
                    features: ["All document types", "Unlimited PDF operations", "Priority support", "Custom branding"]
                },
                enterprise: {
                    name: "Enterprise Pack",
                    tokens: 500,
                    price: 79.99,
                    duration: 30,
                    features: ["Unlimited everything", "API access", "Dedicated support", "White-label solution"]
                }
            }
        };
        
        this.init();
    }
    
    init() {
        console.log('🔧 Freemium System init() called');

        // Check if user is logged in
        const userData = localStorage.getItem('currentUser');
        if (userData) {
            console.log('👤 Found existing user data:', userData);
            this.currentUser = JSON.parse(userData);
            this.loadUserTokens();
            this.updateTokenDisplay();
        } else {
            console.log('👤 No user data found');
        }
    }
    
    // Initialize tokens for new user
    initializeNewUser(userEmail, userName) {
        const tokenData = {
            email: userEmail,
            name: userName,
            tokens: this.tokenConfig.signupBonus,
            signupDate: new Date().toISOString(),
            lastActivity: new Date().toISOString(),
            subscription: null,
            tokenHistory: [
                {
                    action: 'signup_bonus',
                    amount: this.tokenConfig.signupBonus,
                    description: 'Welcome bonus for signing up!',
                    timestamp: new Date().toISOString(),
                    balance: this.tokenConfig.signupBonus
                }
            ],
            totalEarned: this.tokenConfig.signupBonus,
            totalSpent: 0
        };
        
        localStorage.setItem(`tokens_${userEmail}`, JSON.stringify(tokenData));
        this.currentUser = { email: userEmail, name: userName };
        this.userTokens = tokenData; // Store token data in instance
        this.updateTokenDisplay();
        
        // Show welcome message
        this.showWelcomeModal();
        
        return tokenData;
    }
    
    // Load user token data
    loadUserTokens() {
        if (!this.currentUser) {
            console.log('❌ No current user to load tokens for');
            return null;
        }

        const tokenKey = `tokens_${this.currentUser.email}`;
        console.log('🔍 Loading tokens for key:', tokenKey);

        const tokenData = localStorage.getItem(tokenKey);
        if (tokenData) {
            const parsed = JSON.parse(tokenData);
            console.log('✅ Loaded token data:', parsed);
            this.userTokens = parsed; // Store the loaded data
            return parsed;
        } else {
            console.log('❌ No token data found for user');
            return null;
        }
    }
    
    // Get current token balance
    getTokenBalance() {
        if (!this.currentUser) {
            console.log('❌ No current user for token balance');
            return 0;
        }

        // Try to use cached data first
        if (this.userTokens) {
            console.log('🪙 Using cached token balance:', this.userTokens.tokens);
            return this.userTokens.tokens;
        }

        // Otherwise load fresh data
        const tokenData = this.loadUserTokens();
        const balance = tokenData ? tokenData.tokens : 0;
        console.log('🪙 Fresh token balance:', balance);
        return balance;
    }
    
    // Check if user has enough tokens for action
    hasEnoughTokens(category, action) {
        const cost = this.getActionCost(category, action);
        const balance = this.getTokenBalance();
        return balance >= cost;
    }
    
    // Get cost for specific action
    getActionCost(category, action) {
        if (this.tokenConfig.costs[category] && this.tokenConfig.costs[category][action]) {
            return this.tokenConfig.costs[category][action];
        }
        return this.tokenConfig.costs[category] || 1;
    }
    
    // Consume tokens for action
    consumeTokens(category, action, description = '') {
        const tokenData = this.loadUserTokens();
        if (!tokenData) return false;
        
        const cost = this.getActionCost(category, action);
        
        if (tokenData.tokens < cost) {
            this.showInsufficientTokensModal(cost, tokenData.tokens);
            return false;
        }
        
        // Deduct tokens
        tokenData.tokens -= cost;
        tokenData.totalSpent += cost;
        tokenData.lastActivity = new Date().toISOString();
        
        // Add to history
        tokenData.tokenHistory.push({
            action: `${category}_${action}`,
            amount: -cost,
            description: description || `${category.replace('_', ' ')} - ${action}`,
            timestamp: new Date().toISOString(),
            balance: tokenData.tokens
        });
        
        // Save updated data
        localStorage.setItem(`tokens_${this.currentUser.email}`, JSON.stringify(tokenData));
        
        // Update display
        this.updateTokenDisplay();
        
        // Show success notification
        this.showNotification(`Used ${cost} tokens for ${action}`, 'success');
        
        return true;
    }
    
    // Add tokens (for purchases)
    addTokens(amount, source, description = '') {
        const tokenData = this.loadUserTokens();
        if (!tokenData) return false;
        
        tokenData.tokens += amount;
        tokenData.totalEarned += amount;
        tokenData.lastActivity = new Date().toISOString();
        
        // Add to history
        tokenData.tokenHistory.push({
            action: source,
            amount: amount,
            description: description || `Added ${amount} tokens`,
            timestamp: new Date().toISOString(),
            balance: tokenData.tokens
        });
        
        // Save updated data
        localStorage.setItem(`tokens_${this.currentUser.email}`, JSON.stringify(tokenData));
        
        // Update display
        this.updateTokenDisplay();
        
        // Show success notification
        this.showNotification(`Added ${amount} tokens to your account!`, 'success');
        
        return true;
    }
    
    // Update token display in UI
    updateTokenDisplay() {
        const balance = this.getTokenBalance();
        console.log('🔄 Updating token display, balance:', balance);

        const tokenElements = document.querySelectorAll('.token-balance');
        console.log('📊 Found token elements:', tokenElements.length);

        tokenElements.forEach(element => {
            element.textContent = balance;
            console.log('✅ Updated token element:', element);
        });

        // Update token counter color based on balance
        const tokenCounters = document.querySelectorAll('.token-counter');
        tokenCounters.forEach(counter => {
            counter.classList.remove('low-tokens', 'no-tokens');
            if (balance <= 5) {
                counter.classList.add('low-tokens');
            }
            if (balance === 0) {
                counter.classList.add('no-tokens');
            }
        });
    }
    
    // Show welcome modal for new users
    showWelcomeModal() {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.display = 'block';
        modal.innerHTML = `
            <div class="modal-content welcome-modal">
                <h2>🎉 Welcome to DocuGen Pro!</h2>
                <div class="welcome-content">
                    <div class="token-gift">
                        <div class="token-icon">🪙</div>
                        <h3>You've received ${this.tokenConfig.signupBonus} FREE tokens!</h3>
                        <p>Use these tokens to explore all our features:</p>
                    </div>
                    <div class="feature-costs">
                        <div class="cost-item">📄 Generate Invoice: 2 tokens</div>
                        <div class="cost-item">🧾 Generate Receipt: 1 token</div>
                        <div class="cost-item">💰 Generate Quotation: 2 tokens</div>
                        <div class="cost-item">📋 Generate Contract: 3 tokens</div>
                        <div class="cost-item">✍️ PDF Signing: 1 token</div>
                    </div>
                    <p class="welcome-note">When you run out of tokens, choose from our affordable subscription packages!</p>
                    <button class="btn-primary" onclick="this.closest('.modal').remove()">Start Exploring!</button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    // Show subscription packages modal (for purchase tokens button)
    showSubscriptionModal() {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.display = 'block';
        modal.innerHTML = `
            <div class="modal-content subscription-modal">
                <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                <h2>💳 Choose Your Token Package</h2>
                <div class="subscription-intro">
                    <p>Select the perfect token package for your needs. All packages include premium features and priority support!</p>
                    <div class="current-balance">
                        <span class="balance-label">Current Balance:</span>
                        <span class="balance-amount">🪙 ${this.getTokenBalance()} tokens</span>
                    </div>
                </div>
                <div class="subscription-grid">
                    ${this.generateSubscriptionCards()}
                </div>
                <div class="subscription-footer">
                    <p class="money-back">💯 30-day money-back guarantee</p>
                    <p class="secure-payment">🔒 Secure payment processing</p>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    // Show insufficient tokens modal
    showInsufficientTokensModal(required, current) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.display = 'block';
        modal.innerHTML = `
            <div class="modal-content insufficient-tokens-modal">
                <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                <h2>⚠️ Insufficient Tokens</h2>
                <div class="token-status">
                    <p>You need <strong>${required} tokens</strong> for this action</p>
                    <p>You currently have <strong>${current} tokens</strong></p>
                </div>
                <div class="upgrade-options">
                    <h3>Choose a subscription package:</h3>
                    <div class="subscription-grid">
                        ${this.generateSubscriptionCards()}
                    </div>
                </div>
                <button class="btn-secondary" onclick="this.closest('.modal').remove()">Cancel</button>
            </div>
        `;
        document.body.appendChild(modal);
    }
    
    // Generate subscription cards HTML
    generateSubscriptionCards() {
        return Object.entries(this.tokenConfig.subscriptions).map(([key, sub]) => `
            <div class="subscription-card" data-package="${key}">
                <h4>${sub.name}</h4>
                <div class="token-amount">${sub.tokens} Tokens</div>
                <div class="price">$${sub.price}</div>
                <div class="duration">${sub.duration} days</div>
                <ul class="features">
                    ${sub.features.map(feature => `<li>${feature}</li>`).join('')}
                </ul>
                <button class="btn-primary" onclick="freemiumSystem.showPaymentModal('${key}')">
                    Purchase
                </button>
            </div>
        `).join('');
    }
    
    // Show payment modal
    showPaymentModal(packageKey) {
        const subscription = this.tokenConfig.subscriptions[packageKey];
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.display = 'block';
        modal.innerHTML = `
            <div class="modal-content payment-modal">
                <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                <h2>💳 Purchase ${subscription.name}</h2>
                <div class="purchase-summary">
                    <div class="package-details">
                        <h3>${subscription.tokens} Tokens</h3>
                        <p class="price">$${subscription.price}</p>
                        <p class="duration">Valid for ${subscription.duration} days</p>
                    </div>
                </div>
                
                <div class="payment-methods">
                    <h3>Choose Payment Method:</h3>
                    
                    <div class="payment-option" onclick="freemiumSystem.processPayment('${packageKey}', 'stripe')">
                        <div class="payment-header">
                            <h4>💳 Credit/Debit Cards</h4>
                            <div class="card-icons">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCA0MCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iIzAwNTFBNSIvPgo8cGF0aCBkPSJNMTYuNzUgMTJIMTMuMjVWMTBIMTYuNzVWMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="Visa">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCA0MCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iI0VCMDAxQiIvPgo8cGF0aCBkPSJNMTYuNzUgMTJIMTMuMjVWMTBIMTYuNzVWMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="Mastercard">
                            </div>
                        </div>
                        <p>Secure payment with Stripe</p>
                    </div>
                    
                    <div class="payment-option" onclick="freemiumSystem.processPayment('${packageKey}', 'paypal')">
                        <div class="payment-header">
                            <h4>🅿️ PayPal</h4>
                        </div>
                        <p>Pay with your PayPal account</p>
                    </div>
                    
                    <div class="payment-option" onclick="freemiumSystem.processPayment('${packageKey}', 'crypto')">
                        <div class="payment-header">
                            <h4>₿ Cryptocurrency</h4>
                        </div>
                        <p>Bitcoin, Ethereum, and more</p>
                    </div>
                </div>
                
                <div class="payment-note">
                    <p>🔒 All payments are secure and encrypted. Tokens will be added to your account immediately after payment confirmation.</p>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }
    
    // Process payment (demo implementation)
    processPayment(packageKey, method) {
        const subscription = this.tokenConfig.subscriptions[packageKey];
        
        // Close current modal
        document.querySelector('.payment-modal').closest('.modal').remove();
        
        // Show processing modal
        const processingModal = document.createElement('div');
        processingModal.className = 'modal';
        processingModal.style.display = 'block';
        processingModal.innerHTML = `
            <div class="modal-content processing-modal">
                <h2>🔄 Processing Payment...</h2>
                <div class="processing-animation">
                    <div class="spinner"></div>
                </div>
                <p>Please wait while we process your ${method} payment of $${subscription.price}</p>
            </div>
        `;
        document.body.appendChild(processingModal);
        
        // Simulate payment processing
        setTimeout(() => {
            processingModal.remove();
            
            // Add tokens to account
            this.addTokens(
                subscription.tokens, 
                'purchase', 
                `Purchased ${subscription.name} - ${subscription.tokens} tokens`
            );
            
            // Show success modal
            this.showPaymentSuccessModal(subscription);
            
        }, 3000);
    }
    
    // Show payment success modal
    showPaymentSuccessModal(subscription) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.display = 'block';
        modal.innerHTML = `
            <div class="modal-content success-modal">
                <h2>✅ Payment Successful!</h2>
                <div class="success-content">
                    <div class="token-added">
                        <div class="token-icon">🪙</div>
                        <h3>+${subscription.tokens} Tokens Added!</h3>
                        <p>Your new balance: <strong>${this.getTokenBalance()} tokens</strong></p>
                    </div>
                    <div class="package-info">
                        <h4>${subscription.name}</h4>
                        <p>Valid for ${subscription.duration} days</p>
                        <ul>
                            ${subscription.features.map(feature => `<li>✓ ${feature}</li>`).join('')}
                        </ul>
                    </div>
                </div>
                <button class="btn-primary" onclick="this.closest('.modal').remove()">Continue Using DocuGen Pro</button>
            </div>
        `;
        document.body.appendChild(modal);
    }
    
    // Show token history modal
    showTokenHistory() {
        const tokenData = this.loadUserTokens();
        if (!tokenData) return;
        
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.display = 'block';
        modal.innerHTML = `
            <div class="modal-content history-modal">
                <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                <h2>📊 Token Usage History</h2>
                <div class="history-stats">
                    <div class="stat">
                        <h3>${tokenData.tokens}</h3>
                        <p>Current Balance</p>
                    </div>
                    <div class="stat">
                        <h3>${tokenData.totalEarned}</h3>
                        <p>Total Earned</p>
                    </div>
                    <div class="stat">
                        <h3>${tokenData.totalSpent}</h3>
                        <p>Total Spent</p>
                    </div>
                </div>
                <div class="history-list">
                    <h3>Recent Activity</h3>
                    <div class="history-items">
                        ${tokenData.tokenHistory.slice(-10).reverse().map(item => `
                            <div class="history-item ${item.amount > 0 ? 'earned' : 'spent'}">
                                <div class="history-details">
                                    <div class="history-action">${item.description}</div>
                                    <div class="history-date">${new Date(item.timestamp).toLocaleDateString()}</div>
                                </div>
                                <div class="history-amount ${item.amount > 0 ? 'positive' : 'negative'}">
                                    ${item.amount > 0 ? '+' : ''}${item.amount}
                                </div>
                                <div class="history-balance">${item.balance} tokens</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }
    
    // Show notification
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Show notification
        setTimeout(() => notification.classList.add('show'), 100);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    // Force create demo user with tokens (for testing)
    createDemoUser() {
        console.log('🧪 Force creating demo user...');
        const demoUser = {
            email: '<EMAIL>',
            name: 'Demo User'
        };

        // Clear any existing data
        localStorage.removeItem('currentUser');
        localStorage.removeItem(`tokens_${demoUser.email}`);

        // Create fresh user
        localStorage.setItem('currentUser', JSON.stringify(demoUser));
        this.currentUser = demoUser;

        // Initialize with tokens
        const tokenData = this.initializeNewUser(demoUser.email, demoUser.name);
        console.log('✅ Demo user created with tokens:', tokenData);

        return tokenData;
    }
}

// Initialize freemium system
console.log('🔧 Creating Freemium System...');
const freemiumSystem = new FreemiumSystem();

// Export for global use
window.freemiumSystem = freemiumSystem;

console.log('✅ Freemium System loaded successfully');
console.log('🔍 Freemium system available globally:', !!window.freemiumSystem);

// Force initial token display update after DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 DOM loaded, force updating token display...');

    // Create a demo user for testing if none exists
    if (!localStorage.getItem('currentUser')) {
        console.log('🧪 Creating demo user for testing...');
        const demoUser = {
            email: '<EMAIL>',
            name: 'Demo User'
        };
        localStorage.setItem('currentUser', JSON.stringify(demoUser));
        window.freemiumSystem.currentUser = demoUser;

        // Initialize new user with tokens
        const tokenData = window.freemiumSystem.initializeNewUser(demoUser.email, demoUser.name);
        console.log('✅ Demo user created with token data:', tokenData);

        // Force update display
        window.freemiumSystem.updateTokenDisplay();
    } else {
        console.log('🔍 Existing user found, loading tokens...');
        const userData = JSON.parse(localStorage.getItem('currentUser'));
        window.freemiumSystem.currentUser = userData;
        window.freemiumSystem.loadUserTokens();

        // Force update display
        window.freemiumSystem.updateTokenDisplay();
    }

    setTimeout(() => {
        if (window.freemiumSystem) {
            window.freemiumSystem.updateTokenDisplay();
        }
    }, 500);
});
