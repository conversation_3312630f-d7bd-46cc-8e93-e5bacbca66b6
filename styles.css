:root {
    --primary-color: #3498db;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Open Sans', sans-serif;
    line-height: 1.6;
    padding: 20px;
    background-color: #1e90ff; /* Changed to a nice blue color (dodger blue) */
    color: #fff; /* Changed text color to white for better contrast */
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2); /* Enhanced shadow for better contrast with blue background */
    color: #333; /* Reset text color to dark for the container content */
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
}

h1 {
    text-align: center;
    margin-bottom: 30px;
    color: #0056b3; /* Changed to a darker blue for better contrast */
    font-weight: 700;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1); /* Added subtle text shadow */
    letter-spacing: 0.5px;
}

.app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.app-header h1 {
    margin-bottom: 0;
    text-align: left;
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.btn-secondary {
    background: transparent;
    color: #007bff;
    border: 2px solid #007bff;
    padding: 8px 16px;
    border-radius: 4px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #007bff;
    color: white;
}

.btn-outline {
    background: transparent;
    color: #666;
    border: 1px solid #ddd;
    padding: 8px 16px;
    border-radius: 4px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-outline:hover {
    background: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
}

.section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    position: relative;
}

.section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, #007bff, #00c6ff);
    border-radius: 8px 8px 0 0;
}

h2 {
    margin-bottom: 20px;
    color: #444;
    font-size: 1.2em;
    letter-spacing: 0.3px;
}

.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    color: #666;
}

input[type="text"],
input[type="email"],
input[type="number"],
input[type="date"],
input[type="time"],
input[type="tel"],
select,
textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    font-family: 'Open Sans', sans-serif;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="number"]:focus,
input[type="date"]:focus,
input[type="time"]:focus,
input[type="tel"]:focus,
select:focus,
textarea:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

input[readonly] {
    background-color: #f5f5f5;
    cursor: not-allowed;
}

textarea {
    resize: vertical;
}

button {
    background-color: #007bff;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 500;
    letter-spacing: 0.3px;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
}

button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease-out, height 0.6s ease-out;
    z-index: 1;
}

button:hover {
    background-color: #0056b3;
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

button:hover::before {
    width: 300px;
    height: 300px;
}

button:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.1s ease;
}

button span {
    position: relative;
    z-index: 2;
}

#addItem {
    margin-top: 10px;
    background-color: #28a745;
}

#addItem:hover {
    background-color: #218838;
}

#generatePDF {
    background-color: #007bff;
    font-size: 18px;
    padding: 12px 24px;
    margin-top: 20px;
    width: 100%;
    max-width: 300px;
    display: block;
    margin-left: auto;
    margin-right: auto;
    border-radius: 6px;
}

#generatePDF:hover {
    background-color: #0056b3;
    transform: translateY(-4px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.item-row {
    display: grid;
    grid-template-columns: 3fr 1fr 1fr 1fr auto;
    gap: 10px;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
    align-items: end;
}

/* Delete Item Button */
.delete-item-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    height: 40px;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 25px; /* Align with input fields */
}

.delete-item-btn:hover {
    background: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.delete-item-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);
}

.delete-item-btn span {
    font-size: 14px;
}

/* Custom Payment Terms */
#customPaymentTermsGroup {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    margin-top: 10px;
    transition: all 0.3s ease;
}

#customPaymentTermsGroup label {
    color: #007bff;
    font-weight: 600;
}

#customPaymentTerms {
    border: 2px solid #007bff;
    border-radius: 6px;
    padding: 10px;
    font-size: 14px;
    transition: all 0.3s ease;
}

#customPaymentTerms:focus {
    border-color: #0056b3;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    outline: none;
}

/* Contract Logo Styling */
.contract-logo-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    margin-bottom: 20px;
}

.contract-logo-section h3 {
    color: #007bff;
    margin-bottom: 15px;
}

/* Highlight Upload Contract Logo label */
.contract-logo-section label[for="contractLogoUpload"] {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 8px 15px;
    border-radius: 6px;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 10px;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 12px;
}

.contract-logo-section label[for="contractLogoUpload"]:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.contract-logo-section label[for="contractLogoUpload"]::before {
    content: "📁 ";
    margin-right: 5px;
}

/* Style the file input */
#contractLogoUpload {
    background: #f8f9fa;
    border: 2px solid #007bff;
    border-radius: 8px;
    padding: 12px;
    width: 100%;
    font-size: 14px;
    transition: all 0.3s ease;
    cursor: pointer;
}

#contractLogoUpload:hover {
    background: #e3f2fd;
    border-color: #0056b3;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}

#contractLogoUpload:focus {
    outline: none;
    border-color: #0056b3;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* File input styling for better UX */
#contractLogoUpload::file-selector-button {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    margin-right: 12px;
    cursor: pointer;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

#contractLogoUpload::file-selector-button:hover {
    background: linear-gradient(135deg, #20c997, #17a2b8);
    transform: translateY(-1px);
}

/* Style the logo frame selector */
.contract-logo-section label[for="contractLogoShape"] {
    background: linear-gradient(135deg, #6f42c1, #5a32a3);
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 8px;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

#contractLogoShape {
    background: #f8f9fa;
    border: 2px solid #6f42c1;
    border-radius: 6px;
    padding: 10px;
    width: 100%;
    font-size: 14px;
    font-weight: 600;
    color: #6f42c1;
    transition: all 0.3s ease;
    cursor: pointer;
}

#contractLogoShape:hover {
    background: #f3e5f5;
    border-color: #5a32a3;
    box-shadow: 0 2px 8px rgba(111, 66, 193, 0.2);
}

#contractLogoShape:focus {
    outline: none;
    border-color: #5a32a3;
    box-shadow: 0 0 0 3px rgba(111, 66, 193, 0.1);
}

.contract-logo-preview {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    margin-top: 15px;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
}

.logo-placeholder {
    color: #666;
    font-style: italic;
}

.logo-upload-success {
    color: #28a745;
    text-align: center;
    padding: 20px;
    background: #d4edda;
    border-radius: 6px;
    border: 2px solid #28a745;
}

.contract-logo-preview-img {
    max-width: 150px;
    max-height: 100px;
    object-fit: contain;
}

.contract-logo-preview-img.round {
    border-radius: 50%;
    border: 3px solid #007bff;
}

.contract-logo-preview-img.square {
    border-radius: 8px;
    border: 3px solid #007bff;
}

.contract-logo-display-img {
    object-fit: contain;
}

.contract-logo-display-img.round {
    border-radius: 50%;
    border: 2px solid #333;
}

.contract-logo-display-img.square {
    border-radius: 6px;
    border: 2px solid #333;
}

.contract-party {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 15px 0;
    border-left: 4px solid #28a745;
}

.contract-party h4 {
    color: #28a745;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.contract-party small {
    display: block;
    color: #666;
    font-style: italic;
    margin-top: 5px;
}

.contract-agreement-date {
    background: #fff3cd;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #ffc107;
    margin: 20px 0;
}

.contract-agreement-date label {
    margin: 0 5px;
    font-weight: normal;
}

.contract-agreement-date input {
    margin: 0 5px;
    display: inline-block;
    width: auto;
}

.contract-signatures {
    background: #e7f3ff;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.signature-block {
    margin-bottom: 15px;
}

.signature-block label {
    font-weight: bold;
    color: #007bff;
}

.logo-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.logo-preview {
    width: 200px;
    height: 200px;
    border: 2px dashed #ddd;
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    transition: border-radius 0.3s ease;
}

.logo-preview.square {
    border-radius: 10px;
}

.logo-preview.circle {
    border-radius: 50%;
}

.logo-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.logo-shape {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 200px;
    margin-top: 5px;
}

.file-input {
    margin: 10px 0;
}

.header-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 0;
    margin-bottom: 30px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.header-section .app-header {
    padding: 0 30px;
    margin-bottom: 0;
}

.header-section .app-header h1 {
    color: white;
    font-weight: 700;
    font-size: 28px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
}

.header-section .header-actions {
    gap: 15px;
}

.header-section .btn-secondary {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
}

.header-section .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.header-section .btn-outline {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.4);
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
}

.header-section .btn-outline:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.header-section .active-company-indicator {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
}

/* Document Tabs Navigation */
.document-tabs-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: 0;
    position: relative;
}

.document-tabs-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-top: 15px solid rgba(255, 215, 0, 0.8);
    z-index: 5;
    animation: arrowPulse 2s ease-in-out infinite alternate;
}

@keyframes arrowPulse {
    0% {
        opacity: 0.6;
        transform: translateX(-50%) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translateX(-50%) scale(1);
    }
}

.document-tabs {
    display: flex;
    justify-content: center;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.document-tabs::-webkit-scrollbar {
    display: none;
}

.tab-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.9);
    padding: 20px 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    position: relative;
    border-bottom: 3px solid transparent;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.tab-btn:hover:not(.active) {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateY(-2px);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
    border-bottom-color: rgba(255, 215, 0, 0.5);
    border-bottom-width: 2px;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.25) 100%);
    color: white;
    border-bottom-color: #ffd700;
    border-bottom-width: 4px;
    box-shadow:
        0 -2px 15px rgba(255, 215, 0, 0.5),
        0 4px 20px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    transform: translateY(-3px);
    border-radius: 8px 8px 0 0;
    position: relative;
    z-index: 10;
}

.tab-btn.active::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -4px;
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.3) 0%, rgba(255, 255, 255, 0.2) 100%);
    border-radius: 10px 10px 0 0;
    z-index: -1;
    animation: activeGlow 2s ease-in-out infinite alternate;
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 3px;
    height: 20px;
    background: linear-gradient(180deg, #ffd700 0%, rgba(255, 215, 0, 0.3) 100%);
    border-radius: 0 0 2px 2px;
    z-index: 15;
    animation: connectionLine 2s ease-in-out infinite alternate;
}

@keyframes connectionLine {
    0% {
        height: 15px;
        opacity: 0.7;
    }
    100% {
        height: 25px;
        opacity: 1;
    }
}

@keyframes activeGlow {
    0% {
        box-shadow: 0 0 5px rgba(255, 215, 0, 0.3);
    }
    100% {
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
    }
}

.tab-btn.active .tab-icon {
    transform: scale(1.1);
    filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.4));
}

.tab-btn.active .tab-label {
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tab-icon {
    font-size: 26px;
    margin-bottom: 4px;
    filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.3));
}

.tab-label {
    font-size: 15px;
    font-weight: 700;
    text-align: center;
    white-space: nowrap;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.3px;
}

.active-document-info {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 255, 255, 0.95) 100%);
    color: #2c3e50;
    padding: 20px 30px;
    display: flex;
    align-items: center;
    gap: 15px;
    justify-content: center;
    font-weight: 700;
    font-size: 18px;
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.1),
        0 2px 10px rgba(0, 0, 0, 0.15);
    border-top: 2px solid rgba(255, 215, 0, 0.6);
    position: relative;
    overflow: hidden;
}

.active-document-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

.current-doc-icon {
    font-size: 26px;
    filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.2));
    animation: iconPulse 2s ease-in-out infinite alternate;
}

@keyframes iconPulse {
    0% { transform: scale(1); }
    100% { transform: scale(1.05); }
}

.current-doc-name {
    color: #1e3a8a;
    font-weight: 800;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
    letter-spacing: 0.5px;
    position: relative;
    z-index: 1;
}

/* Tab Animation */
@keyframes tabActivate {
    from {
        transform: translateY(5px);
        opacity: 0.7;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.tab-btn.active {
    animation: tabActivate 0.3s ease;
}

/* Mobile Responsive Tabs */
@media (max-width: 768px) {
    .header-section .app-header {
        padding: 0 20px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-section .app-header h1 {
        font-size: 24px;
        margin-bottom: 10px;
    }

    .header-section .header-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .document-tabs {
        justify-content: flex-start;
        padding: 0 10px;
    }

    .tab-btn {
        min-width: 100px;
        padding: 15px 20px;
    }

    .tab-icon {
        font-size: 22px;
    }

    .tab-label {
        font-size: 13px;
        font-weight: 700;
    }

    .active-document-info {
        padding: 15px 20px;
        font-size: 16px;
    }

    .current-doc-name {
        font-size: 16px;
    }
}

.settings-btn {
    background-color: #6c757d;
    color: white;
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.settings-btn:hover {
    background-color: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.settings-btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.7s ease;
}

.settings-btn:hover::after {
    left: 100%;
}

.settings-buttons {
    display: flex;
    gap: 10px;
}

.client-select {
    width: 100%;
    padding: 8px;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.buttons-group {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.delete-btn {
    background-color: #dc3545;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.delete-btn:hover {
    background-color: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.delete-btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

.use-btn {
    background-color: #007bff;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.use-btn:hover {
    background-color: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.use-btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 700px;
    border-radius: 8px;
    max-height: 90vh;
    overflow-y: auto;
}

/* PDF Preview Modal Styles */
.pdf-preview-content {
    width: 90%;
    max-width: 900px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    height: 85vh;
    margin: 2% auto;
}

.pdf-preview-container {
    flex: 1;
    overflow: hidden;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f5f5f5;
}

#pdfPreviewFrame {
    width: 100%;
    height: 100%;
    border: none;
}

.pdf-actions {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.pdf-actions button {
    flex: 1;
    padding: 12px;
}

.download-btn {
    background-color: #28a745;
}

.download-btn:hover {
    background-color: #218838;
}

.share-btn {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.share-btn:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
}

.share-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.share-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.share-btn:hover::before {
    left: 100%;
}

/* Template Manager Modal Styles */
.template-modal-content {
    width: 90%;
    max-width: 900px;
    padding: 20px;
    max-height: 85vh;
}

.template-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid #ddd;
}

.tab-btn {
    background-color: transparent;
    color: #666;
    border: none;
    padding: 10px 15px;
    margin-right: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
}

.tab-btn:hover {
    background-color: #f5f5f5;
    color: #333;
}

.tab-btn.active {
    color: #007bff;
    border-bottom: 2px solid #007bff;
    background-color: transparent;
}

.tab-content {
    overflow-y: auto;
    max-height: calc(85vh - 150px);
}

/* Template Grid */
.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.template-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.template-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.template-card-header {
    padding: 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
}

.template-card-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.template-card-description {
    color: #666;
    font-size: 13px;
    margin-top: 5px;
}

.template-card-preview {
    height: 150px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
}

.template-card-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.template-card-actions {
    display: flex;
    padding: 10px;
    border-top: 1px solid #eee;
}

.template-card-actions button {
    flex: 1;
    padding: 8px;
    font-size: 13px;
}

.use-template-btn {
    background-color: #007bff;
}

.edit-template-btn {
    background-color: #6c757d;
    margin: 0 5px;
}

.delete-template-btn {
    background-color: #dc3545;
}

/* No templates message */
.no-templates-message {
    text-align: center;
    padding: 30px;
    color: #666;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin: 20px 0;
}

/* Template Form */
.template-settings {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.template-settings h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    color: #444;
}

.template-preview {
    margin-bottom: 20px;
}

.template-preview h3 {
    margin-bottom: 10px;
    font-size: 16px;
    color: #444;
}

.template-preview-container {
    height: 200px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #fff;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.template-actions {
    display: flex;
    gap: 10px;
}

.save-template-btn {
    background-color: #28a745;
    flex: 2;
}

.reset-template-btn {
    background-color: #6c757d;
    flex: 1;
}

/* Import/Export Styles */
.import-section, .export-section {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.import-section h3, .export-section h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
    color: #444;
}

.import-file-container {
    margin: 15px 0;
}

input[type="file"] {
    display: none;
}

.file-input-label {
    display: inline-block;
    padding: 8px 12px;
    background-color: #6c757d;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.file-input-label:hover {
    background-color: #5a6268;
}

.import-btn, .export-btn {
    background-color: #17a2b8;
    width: 100%;
    margin-top: 10px;
}

.export-template-list {
    margin: 15px 0;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    background-color: white;
}

.export-template-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #eee;
}

.export-template-item:last-child {
    border-bottom: none;
}

.export-template-item input[type="checkbox"] {
    margin-right: 10px;
}

/* Template Preview Styles */
.preview-document {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    font-size: 10px;
    overflow: hidden;
    background-color: white;
}

.preview-header {
    text-align: center;
    padding: 5px;
    border-bottom: 1px solid #eee;
}

.preview-header h1 {
    margin: 0;
    font-size: 14px;
}

.preview-content {
    flex: 1;
    padding: 5px;
    display: flex;
    flex-direction: column;
}

.preview-section {
    margin-bottom: 5px;
    border: 1px solid #eee;
    padding: 3px;
}

.preview-section h2 {
    margin: 0 0 3px 0;
    font-size: 12px;
    border-bottom: 1px solid #eee;
}

.preview-table {
    height: 30px;
    background-color: #f8f9fa;
}

/* Template layout variations */
.preview-document.modern .preview-section {
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: none;
}

.preview-document.classic {
    background-color: #fffdf5;
}

.preview-document.classic .preview-section {
    border: 1px solid #000;
    border-radius: 0;
}

.preview-document.compact .preview-section {
    margin-bottom: 2px;
    padding: 2px;
}

.preview-document.detailed .preview-section {
    padding: 5px;
}

/* Preview placeholder for template cards */
.preview-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    opacity: 0.5;
    background-color: #f8f9fa;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.close, .close-pdf-preview {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover, .close-pdf-preview:hover {
    color: #000;
}

.save-btn {
    background-color: #28a745;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 20px;
    position: relative;
    overflow: hidden;
}

.save-btn:hover {
    background-color: #218838;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.save-btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.save-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.7s ease;
}

.save-btn:hover::before {
    left: 100%;
}

/* Terms and Conditions Styling */
textarea#termsConditions,
textarea#notes {
    font-size: 14px;
    line-height: 1.5;
    padding: 10px;
    background-color: #f9f9f9;
}

textarea#termsConditions:focus,
textarea#notes:focus {
    background-color: #fff;
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

select#paymentTerms {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background-color: #fff;
}

select#paymentTerms:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

#dueDate {
    background-color: #f5f5f5;
    cursor: not-allowed;
}



/* Document Type Selector */
.toggle-container {
    margin: 15px 0;
}

.document-type-selector {
    position: relative;
    display: inline-block;
    width: 100%;
    max-width: 250px;
}

.document-type-select {
    width: 100%;
    padding: 10px 15px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 14px;
    border-radius: 6px;
    border: 1px solid #ddd;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    cursor: pointer;
    transition: all 0.3s ease;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px;
}

.document-type-select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.document-type-select option {
    font-weight: 500;
    padding: 10px;
}

/* Custom styling for each document type */
.document-type-select.invoice-mode {
    border-color: #007bff;
    background-color: rgba(0, 123, 255, 0.05);
}

.document-type-select.receipt-mode {
    border-color: #28a745;
    background-color: rgba(40, 167, 69, 0.05);
}

.document-type-select.quotation-mode {
    border-color: #6f42c1;
    background-color: rgba(111, 66, 193, 0.05);
}

.document-type-select.contract-mode {
    border-color: #ff8c00; /* Dark Orange */
    background-color: rgba(255, 140, 0, 0.05);
}

.document-type-select.rider-mode {
    border-color: #4682b4; /* Steel Blue */
    background-color: rgba(70, 130, 180, 0.05);
}

.document-type-select.annexure-mode {
    border-color: #808080; /* Gray */
    background-color: rgba(128, 128, 128, 0.05);
}

/* Profile Controls */
.profile-controls {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.profile-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.profile-button:hover {
    background-color: #0069d9;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.profile-button:active {
    transform: translateY(0);
    box-shadow: none;
}

.client-profile-row {
    display: flex;
    gap: 10px;
    align-items: flex-end;
    margin-bottom: 10px;
}

.client-profile-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

#clientProfileDropdown {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

/* Styling Section */
.styling-section {
    background-color: #f9f9f9;
}

.color-theme {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

/* Template selection with manage button */
.template-selection-group .template-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.invoice-template {
    flex: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.template-manager-btn {
    background-color: #6c757d;
    padding: 8px 12px;
    font-size: 13px;
}

.template-manager-btn:hover {
    background-color: #5a6268;
}

/* Color Picker Styles */
.color-picker-container {
    display: flex;
    align-items: center;
}

input[type="color"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 50px;
    height: 30px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
}

input[type="color"]::-webkit-color-swatch-wrapper {
    padding: 0;
}

input[type="color"]::-webkit-color-swatch {
    border: none;
    border-radius: 4px;
}

.color-preview {
    display: inline-block;
    width: 30px;
    height: 30px;
    border-radius: 4px;
    border: 1px solid #666;
    background-color: #f5f5f5;
    margin-left: 10px;
    box-shadow: 0 0 3px rgba(0,0,0,0.2);
}

.background-preview {
    margin-top: 15px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
}

.preview-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

#backgroundPreview {
    width: 100%;
    height: 60px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f5f5f5;
}

/* Slider Styles */
.slider {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 100%;
    height: 10px;
    border-radius: 5px;
    background: #ddd;
    outline: none;
    margin: 10px 0;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
}

#opacityValue {
    display: inline-block;
    width: 40px;
    text-align: right;
    color: #666;
}

/* Signature Styles */
.signature-container {
    margin-top: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

#signatureCanvas {
    background-color: #fff;
    width: 100%;
    max-width: 400px;
    border-bottom: 1px solid #eee;
    cursor: crosshair;
}

.signature-tools {
    padding: 10px;
    background-color: #f5f5f5;
    text-align: right;
}

#clearSignature {
    background-color: #6c757d;
    color: white;
    padding: 5px 10px;
    font-size: 14px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

#clearSignature:hover {
    background-color: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

#clearSignature:active {
    transform: translateY(1px);
    box-shadow: none;
    transition: all 0.1s ease;
}

#clearSignature::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: -100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

#clearSignature:hover::after {
    left: 100%;
}

/* Checkbox Styles */
input[type="checkbox"] {
    margin-right: 8px;
    vertical-align: middle;
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.checkbox-label {
    color: #666;
    font-size: 14px;
}

/* Color picker controls container */
#colorPickerControls {
    transition: opacity 0.3s ease;
    padding: 10px;
    margin-top: 5px;
    border: 1px solid #eee;
    border-radius: 4px;
    background-color: #fafafa;
}

/* Initial state for color picker controls */
#colorPickerControls {
    opacity: 0.5;
}

/* Theme Colors */
.theme-green button {
    background-color: #28a745;
}

.theme-green button:hover {
    background-color: #218838;
}

.theme-purple button {
    background-color: #6f42c1;
}

.theme-purple button:hover {
    background-color: #5e37a6;
}

.theme-orange button {
    background-color: #fd7e14;
}

.theme-orange button:hover {
    background-color: #e96b02;
}

.theme-teal button {
    background-color: #20c997;
}

.theme-teal button:hover {
    background-color: #1ba87e;
}

/* Invoice Templates */
.template-modern .section {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: none;
}

.template-classic .section {
    border: 2px solid #333;
    border-radius: 0;
    background-color: #fffdf5;
}

.template-classic h2 {
    font-family: 'Times New Roman', Times, serif;
    text-transform: uppercase;
    letter-spacing: 1px;
}

@media (max-width: 768px) {
    .item-row {
        grid-template-columns: 1fr;
        position: relative;
    }

    .delete-item-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        margin-top: 0;
        width: 35px;
        height: 35px;
        font-size: 12px;
    }

    .container {
        padding: 15px;
        margin: 0;
        border-radius: 0;
        width: 100%;
        max-width: 100%;
    }

    body {
        padding: 0;
    }

    h1 {
        font-size: 1.8rem;
    }

    h2 {
        font-size: 1.1rem;
    }

    .section {
        padding: 15px;
        margin-bottom: 20px;
    }

    textarea#termsConditions,
    textarea#notes {
        font-size: 16px; /* Better for mobile */
    }

    #signatureCanvas {
        height: 120px;
    }

    /* Improve form controls for mobile */
    input[type="text"],
    input[type="email"],
    input[type="number"],
    input[type="date"],
    input[type="time"],
    input[type="tel"],
    select,
    textarea {
        font-size: 16px; /* Prevents iOS zoom on focus */
        padding: 10px;
    }

    button {
        width: 100%;
        padding: 12px;
        margin-top: 5px;
        margin-bottom: 5px;
    }

    /* PDF Preview Modal adjustments for mobile */
    .pdf-preview-content {
        width: 95%;
        padding: 10px;
        margin: 0;
        height: 100vh;
        max-height: 100vh;
        border-radius: 0;
    }

    .pdf-actions {
        flex-direction: column;
    }

    .modal-content {
        width: 95%;
        margin: 0 auto;
        padding: 15px;
        max-height: 100vh;
        border-radius: 0;
    }
}

/* Contract Page Styles */
#contractPage1Section {
    border: 1px solid #ddd;
    padding: 20px;
    border-radius: 8px;
    background-color: #f9f9f9;
}

#contractPage1Section h2 {
    text-align: center;
    color: #333;
    margin-bottom: 25px;
}

.contract-header #contractLogo {
    display: block;
    margin-left: auto;
    margin-right: auto;
    max-width: 250px; /* Adjusted for better visibility */
    margin-bottom: 15px;
}

.contract-header p,
.contract-header h3 {
    text-align: center;
    margin-bottom: 10px;
}

.contract-header h3 {
    font-size: 1.6em;
    font-weight: 600;
    color: #222;
}

.contract-agreement-date {
    display: flex;
    align-items: center;
    justify-content: center; /* Center align the items */
    gap: 10px; /* Space between elements */
    margin-bottom: 25px;
    flex-wrap: wrap; /* Allow wrapping on smaller screens */
}

.contract-agreement-date label {
    font-weight: 500;
}

.contract-agreement-date input[type="text"] {
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 0.95em;
}

.contract-party {
    margin-bottom: 25px;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: #fff;
}

.contract-party p {
    margin-bottom: 8px;
    line-height: 1.6;
}

.contract-party .form-group {
    margin-top: 10px;
}

.contract-party .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.contract-party .form-group input[type="text"] {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
}

.contract-signatures {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px dashed #ccc;
    flex-wrap: wrap; /* Allow wrapping */
}

.signature-block {
    width: 48%; /* Adjust as needed */
    margin-bottom: 20px; /* Space for wrapped items */
}

.signature-block label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #444;
}

.signature-block input[type="text"] {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
    background-color: #f0f0f0; /* Light grey for signature area */
    min-height: 50px; /* Make it look like a signature box */
}

/* Responsive adjustments for contract page */
@media (max-width: 768px) {
    .contract-agreement-date {
        flex-direction: column;
        align-items: flex-start;
    }

    .contract-agreement-date input[type="text"] {
        width: 100%;
        margin-bottom: 10px;
    }

    .contract-signatures {
        flex-direction: column;
    }

    .signature-block {
        width: 100%;
        margin-bottom: 20px;
    }
}

/* Technical Rider Specific Styles */
.rider-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

/* Artist Agreement Definitions Styles */
.definition-group {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin: 15px 0;
    transition: all 0.3s ease;
}

.definition-group:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(74, 158, 255, 0.1);
}

.definition-term {
    color: var(--primary-color);
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 15px 0;
    padding: 8px 12px;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-radius: 6px;
    border-left: 4px solid var(--primary-color);
}

.artist-definition {
    background: linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%);
    border: 2px solid #ff9800;
}

.artist-definition .definition-term {
    background: linear-gradient(135deg, #ff9800 0%, #e91e63 100%);
    color: white;
    border-left: 4px solid #f57c00;
}

.artist-preview {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 12px;
    margin-top: 10px;
    font-size: 14px;
    color: #333;
}

.artist-preview span {
    color: #d32f2f;
    font-weight: 500;
}

.page-description {
    background: #e8f5e8;
    border: 1px solid #4caf50;
    border-radius: 6px;
    padding: 12px;
    margin: 15px 0;
    color: #2e7d32;
    font-style: italic;
}

.definition-group textarea {
    min-height: 60px;
    resize: vertical;
}

.definition-group .form-group {
    margin-bottom: 10px;
}

.definition-group .form-group:last-child {
    margin-bottom: 0;
}

/* Custom Definition Styles */
.custom-definition {
    background: linear-gradient(135deg, #f3e5f5 0%, #e8eaf6 100%);
    border: 2px dashed #9c27b0;
    position: relative;
}

.custom-definition .remove-definition {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #f44336;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-definition .remove-definition:hover {
    background: #d32f2f;
}

/* Responsive adjustments for definitions */
@media (max-width: 768px) {
    .definition-group {
        padding: 15px;
        margin: 10px 0;
    }

    .definition-term {
        font-size: 14px;
        padding: 6px 10px;
    }

    .artist-preview {
        font-size: 13px;
        padding: 10px;
    }
}

.rider-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 25px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
}

.rider-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #4682b4 0%, #5a9fd4 100%);
    border-radius: 12px 12px 0 0;
}

.rider-section:hover {
    border-color: #4682b4;
    box-shadow: 0 4px 16px rgba(70, 130, 180, 0.15);
    transform: translateY(-2px);
}

.rider-section h3 {
    color: #1a252f; /* Fallback color */
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 800;
    border-bottom: 4px solid #4682b4;
    padding-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
}

/* Gradient text effect for supported browsers */
@supports (background-clip: text) or (-webkit-background-clip: text) {
    .rider-section h3 {
        background: linear-gradient(135deg, #4682b4 0%, #5a9fd4 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        color: transparent;
    }
}

.rider-section h3::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, #4682b4 0%, #5a9fd4 100%);
    border-radius: 2px;
}

.rider-section .form-group {
    margin-bottom: 20px;
}

.rider-section .form-group label {
    color: #2c3e50;
    font-weight: 700;
    font-size: 1rem;
    margin-bottom: 10px;
    display: block;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-left: 4px solid #4682b4;
    padding-left: 15px;
    background: linear-gradient(90deg, rgba(70, 130, 180, 0.15) 0%, rgba(70, 130, 180, 0.05) 50%, transparent 100%);
    padding-top: 8px;
    padding-bottom: 8px;
    border-radius: 0 6px 6px 0;
    position: relative;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.rider-section .form-group label::before {
    content: '▶';
    color: #4682b4;
    font-weight: bold;
    margin-right: 8px;
    font-size: 0.8rem;
}

.rider-section select[multiple] {
    height: 120px;
}

.rider-section select,
.rider-section input,
.rider-section textarea {
    border: 2px solid #dee2e6;
    border-radius: 6px;
    padding: 10px 12px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    background-color: #fff;
}

.rider-section select:focus,
.rider-section input:focus,
.rider-section textarea:focus {
    outline: none;
    border-color: #4682b4;
    box-shadow: 0 0 0 3px rgba(70, 130, 180, 0.2);
    background-color: #f8fbff;
}

.rider-section select:hover,
.rider-section input:hover,
.rider-section textarea:hover {
    border-color: #4682b4;
    background-color: #f8fbff;
}

.rider-preview-section {
    background: linear-gradient(135deg, #4682b4 0%, #5a9fd4 100%);
    color: white;
    padding: 25px;
    border-radius: 12px;
    margin: 25px 0;
    text-align: center;
}

.rider-preview-section h3 {
    margin-bottom: 15px;
    font-size: 1.3rem;
    border-bottom: none;
}

.rider-features-list {
    list-style: none;
    padding: 0;
    margin: 15px 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 8px;
    text-align: left;
}

.rider-features-list li {
    padding: 8px 0;
    font-size: 0.95rem;
    opacity: 0.95;
}

.section-description {
    color: #6c757d;
    font-style: italic;
    margin-bottom: 20px;
    font-size: 0.95rem;
}

/* Technical Rider Mode Styling */
.rider-mode .section:not(#technicalRiderSection):not(.styling-section) {
    display: none !important;
}

.rider-mode #technicalRiderSection {
    display: block !important;
}

/* Custom Requirements Styling */
.custom-requirements-section {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

.custom-requirements-section h4 {
    color: #2c3e50;
    font-size: 1rem;
    margin-bottom: 12px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-left: 4px solid #28a745;
    padding-left: 12px;
    background: linear-gradient(90deg, rgba(40, 167, 69, 0.1) 0%, transparent 100%);
    padding-top: 6px;
    padding-bottom: 6px;
    border-radius: 0 4px 4px 0;
}

.custom-requirements-container {
    margin-bottom: 15px;
    min-height: 20px;
}

.custom-requirement-row {
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.custom-requirement-input-group {
    display: flex;
    gap: 8px;
    align-items: center;
}

.custom-requirement-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    background-color: #fff;
    transition: border-color 0.3s ease;
}

.custom-requirement-input:focus {
    outline: none;
    border-color: #4682b4;
    box-shadow: 0 0 0 2px rgba(70, 130, 180, 0.25);
}

.custom-requirement-input::placeholder {
    color: #6c757d;
    font-style: italic;
}

.add-requirement-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.add-requirement-btn:hover {
    background: linear-gradient(135deg, #218838 0%, #1ba87e 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.add-requirement-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(40, 167, 69, 0.3);
}

.remove-requirement-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 6px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
    min-width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-requirement-btn:hover {
    background: #c82333;
    transform: scale(1.1);
}

.remove-requirement-btn:active {
    transform: scale(0.95);
}

/* Empty state for custom requirements */
.custom-requirements-container:empty::before {
    content: "No custom requirements added yet. Click the button below to add your specific needs.";
    color: #6c757d;
    font-style: italic;
    font-size: 13px;
    display: block;
    padding: 10px;
    text-align: center;
    background-color: #f8f9fa;
    border: 1px dashed #dee2e6;
    border-radius: 4px;
}

/* Custom Input Containers for Dropdowns */
.custom-input-container {
    margin-top: 8px;
    padding: 10px;
    background-color: #f8f9fa;
    border: 1px solid #4682b4;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.custom-input-container .custom-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    background-color: #fff;
    transition: border-color 0.3s ease;
}

.custom-input-container .custom-input:focus {
    outline: none;
    border-color: #4682b4;
    box-shadow: 0 0 0 2px rgba(70, 130, 180, 0.25);
}

.custom-input-container .custom-input::placeholder {
    color: #6c757d;
    font-style: italic;
}

/* Dropdown options styling */
select option[value="custom"] {
    background-color: #e8f4f8;
    color: #4682b4;
    font-weight: 700;
    font-style: italic;
}

/* Enhanced dropdown styling */
.rider-section select {
    background-image: linear-gradient(45deg, transparent 50%, #4682b4 50%),
                      linear-gradient(135deg, #4682b4 50%, transparent 50%);
    background-position: calc(100% - 20px) calc(1em + 2px),
                         calc(100% - 15px) calc(1em + 2px);
    background-size: 5px 5px, 5px 5px;
    background-repeat: no-repeat;
    padding-right: 40px;
}

.rider-section select:focus {
    background-image: linear-gradient(45deg, transparent 50%, #fff 50%),
                      linear-gradient(135deg, #fff 50%, transparent 50%);
}

/* Animation for custom input reveal */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Document Sharing Modal Styles */
.sharing-modal-content {
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
}

.sharing-container {
    padding: 20px 0;
}

.sharing-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #4682b4;
}

.sharing-section h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Document Preview Styles */
.document-info-container {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
}

.document-preview {
    display: flex;
    align-items: center;
    gap: 15px;
}

.document-icon {
    font-size: 2.5rem;
    color: #4682b4;
}

.document-details {
    flex: 1;
}

.document-name {
    font-weight: 600;
    font-size: 1.1rem;
    color: #2c3e50;
    margin-bottom: 5px;
}

.document-type {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 3px;
}

.document-size {
    color: #6c757d;
    font-size: 0.85rem;
}

/* Email Sharing Styles */
.email-sharing-container .form-group {
    margin-bottom: 15px;
}

.email-sharing-container label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.email-sharing-container input,
.email-sharing-container textarea {
    width: 100%;
    padding: 10px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.email-sharing-container input:focus,
.email-sharing-container textarea:focus {
    outline: none;
    border-color: #4682b4;
    box-shadow: 0 0 0 3px rgba(70, 130, 180, 0.1);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.btn-email {
    background: #007bff;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
    font-size: 14px;
}

.btn-email:hover {
    background: #0056b3;
}

/* WhatsApp Sharing Styles */
.whatsapp-sharing-container .form-group {
    margin-bottom: 15px;
}

.whatsapp-sharing-container label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.whatsapp-sharing-container input,
.whatsapp-sharing-container textarea {
    width: 100%;
    padding: 10px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.whatsapp-sharing-container input:focus,
.whatsapp-sharing-container textarea:focus {
    outline: none;
    border-color: #25D366;
    box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.1);
}

.whatsapp-sharing-container small {
    color: #6c757d;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

.whatsapp-options {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-whatsapp {
    background: #25D366;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
    font-size: 14px;
}

.btn-whatsapp:hover {
    background: #128C7E;
}

.btn-whatsapp-web {
    background: #075E54;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
    font-size: 14px;
}

.btn-whatsapp-web:hover {
    background: #064e45;
}

/* Quick Share Options */
.quick-share-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

.quick-share-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 13px;
    text-align: center;
}

.quick-share-btn:hover {
    background: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.quick-share-btn:active {
    transform: translateY(0);
}

/* Sharing Instructions */
.sharing-instructions {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
}

.instruction-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.instruction-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.instruction-icon {
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
}

.instruction-text {
    color: #6c757d;
    font-size: 13px;
    line-height: 1.4;
}

/* Modal Footer */
.modal-footer {
    padding: 20px 0 0 0;
    border-top: 1px solid #e9ecef;
    text-align: right;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s ease;
}

.btn-secondary:hover {
    background: #5a6268;
}

/* Close button for sharing modal */
.close-sharing {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-sharing:hover,
.close-sharing:focus {
    color: #000;
    text-decoration: none;
}

/* Responsive design for sharing modal */
@media (max-width: 768px) {
    .sharing-modal-content {
        max-width: 95%;
        margin: 5% auto;
    }

    .sharing-section {
        padding: 15px;
        margin-bottom: 20px;
    }

    .whatsapp-options {
        flex-direction: column;
    }

    .btn-whatsapp,
    .btn-whatsapp-web {
        width: 100%;
    }

    .quick-share-container {
        grid-template-columns: repeat(2, 1fr);
    }

    .document-preview {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .instruction-item {
        flex-direction: column;
        text-align: center;
        gap: 5px;
    }
}

/* Responsive design for technical rider */
@media (max-width: 768px) {
    .rider-form-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .rider-features-list {
        grid-template-columns: 1fr;
    }

    .rider-section {
        padding: 15px;
    }

    .rider-preview-section {
        padding: 20px;
        margin: 20px 0;
    }

    .custom-requirement-input-group {
        flex-direction: column;
        gap: 5px;
    }

    .custom-requirement-input {
        width: 100%;
    }

    .remove-requirement-btn {
        align-self: flex-end;
        width: 100%;
        max-width: 120px;
    }

    .add-requirement-btn {
        width: 100%;
        justify-content: center;
        padding: 10px;
    }
}

/* Quick Sharing Section Styles - Fixed with Grok's solution */
.quick-sharing-section {
    margin-top: 25px;
    padding: 25px;
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
    border-radius: 15px;
    border: 3px solid #4682b4;
    box-shadow: 0 4px 12px rgba(70, 130, 180, 0.15);
    animation: slideDownBounce 0.6s ease;
    position: relative;
}

/* AI-Enhanced Quick Sharing Visibility Controls */
.quick-sharing-section.visible {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.quick-sharing-section.hidden {
    display: none !important;
}

/* AI-Recommended Animation for smoother appearance */
.quick-sharing-section.fade-in {
    animation: fadeInUp 0.5s ease-in-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* AI-Recommended: Ensure no other CSS is overriding */
#quickSharingOptions {
    transition: all 0.3s ease;
}

/* Backup visibility override */
.quick-sharing-section[style*="display: block"] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* AI Assistant Panel Styles */
.ai-toggle-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-size: 24px;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    z-index: 1000;
}

.ai-toggle-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.ai-panel {
    position: fixed;
    bottom: 90px;
    right: 20px;
    width: 400px;
    max-height: 500px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 999;
    overflow: hidden;
    animation: slideUpFade 0.3s ease;
}

.ai-panel-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-panel-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.ai-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ai-panel-content {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.ai-status {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    text-align: center;
}

.ai-status a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}

.ai-status a:hover {
    text-decoration: underline;
}

.ai-quick-actions {
    display: grid;
    gap: 10px;
    margin-bottom: 15px;
}

.ai-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
}

.ai-btn:hover {
    background: linear-gradient(135deg, #218838 0%, #1ba87e 100%);
    transform: translateY(-2px);
}

.ai-output {
    background: #2d3748;
    color: #e2e8f0;
    padding: 15px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    max-height: 200px;
    overflow-y: auto;
    margin-top: 15px;
}

.ai-message {
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #4a5568;
}

.ai-response {
    margin-top: 5px;
    padding: 8px;
    background: #1a202c;
    border-radius: 4px;
    white-space: pre-wrap;
}

@keyframes slideUpFade {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile responsiveness for AI panel */
@media (max-width: 768px) {
    .ai-panel {
        width: calc(100vw - 40px);
        right: 20px;
        left: 20px;
    }

    .ai-toggle-btn {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}

/* Company Profile Styles */
.company-profile-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    border-left: 4px solid #4682b4;
}

.company-profile-actions {
    display: flex;
    gap: 10px;
}

.company-profile-actions .btn-outline {
    background: white;
    color: #4682b4;
    border: 2px solid #4682b4;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.company-profile-actions .btn-outline:hover {
    background: #4682b4;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(70, 130, 180, 0.3);
}

/* Company Profile Modal */
.company-profile-content {
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.company-profile-form {
    padding: 20px 0;
}

.company-profile-form .form-group {
    margin-bottom: 20px;
}

.company-profile-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

.company-profile-form input,
.company-profile-form textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.company-profile-form input:focus,
.company-profile-form textarea:focus {
    outline: none;
    border-color: #4682b4;
    box-shadow: 0 0 0 3px rgba(70, 130, 180, 0.1);
}

.company-profile-form textarea {
    resize: vertical;
    min-height: 80px;
}

/* Modal Footer Buttons */
.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px 0 0 0;
    border-top: 1px solid #e9ecef;
    margin-top: 20px;
}

.btn-primary {
    background: linear-gradient(135deg, #4682b4 0%, #5a9fd4 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #3a6d96 0%, #4a8bc2 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(70, 130, 180, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

/* Company Profile Status Indicator */
.company-profile-status {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #d4edda;
    color: #155724;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 15px;
}

.company-profile-status.not-configured {
    background: #fff3cd;
    color: #856404;
}

/* Mobile Responsive Company Profile */
@media (max-width: 768px) {
    .company-profile-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .company-profile-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .company-profile-actions .btn-outline {
        font-size: 12px;
        padding: 6px 12px;
    }

    .company-profile-content {
        max-width: 95vw;
        margin: 20px auto;
    }

    .modal-footer {
        flex-direction: column;
        gap: 10px;
    }

    .btn-primary,
    .btn-secondary {
        width: 100%;
        justify-content: center;
    }
}

.quick-sharing-section::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #4682b4, #5a9fd4, #4682b4);
    border-radius: 15px;
    z-index: -1;
    animation: borderGlow 2s ease-in-out infinite alternate;
}

.sharing-divider {
    text-align: center;
    margin-bottom: 15px;
    position: relative;
}

.sharing-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #dee2e6, transparent);
}

.divider-text {
    background: #f8f9fa;
    padding: 0 15px;
    color: #495057;
    font-weight: 600;
    font-size: 14px;
    position: relative;
    z-index: 1;
}

.quick-sharing-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.quick-share-email-btn,
.quick-share-whatsapp-btn,
.quick-share-more-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.quick-share-email-btn {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.quick-share-email-btn:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
}

.quick-share-whatsapp-btn {
    background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(37, 211, 102, 0.3);
}

.quick-share-whatsapp-btn:hover {
    background: linear-gradient(135deg, #128C7E 0%, #075E54 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(37, 211, 102, 0.4);
}

.quick-share-more-btn {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
}

.quick-share-more-btn:hover {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.4);
}

/* Quick Forms */
.quick-form {
    margin-top: 15px;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    animation: slideDown 0.3s ease;
}

.quick-form-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 12px 15px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.quick-form-header h4 {
    margin: 0;
    color: #495057;
    font-size: 16px;
    font-weight: 600;
}

.close-quick-form {
    background: none;
    border: none;
    font-size: 20px;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-quick-form:hover {
    background: #e9ecef;
    color: #495057;
}

.quick-form-content {
    padding: 15px;
}

.form-row {
    margin-bottom: 12px;
}

.form-row input,
.form-row textarea {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-row input:focus,
.form-row textarea:focus {
    outline: none;
    border-color: #4682b4;
    box-shadow: 0 0 0 3px rgba(70, 130, 180, 0.1);
}

.form-row small {
    color: #6c757d;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

.form-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-send-email,
.btn-send-whatsapp,
.btn-send-whatsapp-web {
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-send-email {
    background: #007bff;
    color: white;
}

.btn-send-email:hover {
    background: #0056b3;
}

.btn-send-whatsapp {
    background: #25D366;
    color: white;
}

.btn-send-whatsapp:hover {
    background: #128C7E;
}

.btn-send-whatsapp-web {
    background: #075E54;
    color: white;
}

.btn-send-whatsapp-web:hover {
    background: #064e45;
}

/* Enhanced Animations */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDownBounce {
    0% {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    50% {
        opacity: 0.8;
        transform: translateY(5px) scale(1.02);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes borderGlow {
    0% {
        opacity: 0.5;
        transform: scale(1);
    }
    100% {
        opacity: 0.8;
        transform: scale(1.01);
    }
}

/* Responsive Design for Quick Sharing */
@media (max-width: 768px) {
    .quick-sharing-buttons {
        flex-direction: column;
        align-items: center;
    }

    .quick-share-email-btn,
    .quick-share-whatsapp-btn,
    .quick-share-more-btn {
        width: 100%;
        max-width: 250px;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn-send-email,
    .btn-send-whatsapp,
    .btn-send-whatsapp-web {
        width: 100%;
    }
}

/* Digital Signature Integration Styles */
.signature-integration {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
    border: 2px solid var(--primary-color);
    border-radius: 12px;
    padding: 25px;
    margin: 20px 0;
}

.signature-selection-group {
    margin-bottom: 20px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.signature-selector {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-top: 8px;
}

.signature-selector select {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.signature-preview-container {
    margin-top: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
}

.signature-preview-box {
    border: 2px dashed #ddd;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
    background: white;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.signature-preview-box img {
    max-width: 200px;
    max-height: 60px;
    object-fit: contain;
}

.signature-options {
    margin-top: 20px;
    padding: 15px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
}

.signature-options label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: #856404;
}

.signature-options input[type="checkbox"] {
    width: 18px;
    height: 18px;
}

/* Custom sections styling */
.custom-section {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 15px 0;
    background: #f9f9f9;
}

.custom-section .btn-remove {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
}

.custom-section .btn-remove:hover {
    background: #c82333;
}

/* Responsive design for signature integration */
@media (max-width: 768px) {
    .signature-selector {
        flex-direction: column;
        align-items: stretch;
    }

    .signature-selector select {
        margin-bottom: 10px;
    }

    .signature-integration {
        padding: 15px;
    }

    .signature-selection-group {
        padding: 10px;
    }
}

/* Document Import Modal Styles */
.document-import-content {
    width: 95%;
    max-width: 1200px;
    height: 90vh;
    max-height: 800px;
}

.document-import-body {
    padding: 20px;
    height: calc(100% - 120px);
    overflow-y: auto;
}

.import-layout {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 20px;
    height: calc(100% - 80px);
}

.import-left-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.import-right-panel {
    display: flex;
    flex-direction: column;
}

.import-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.import-section h3 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 16px;
    font-weight: 600;
}

/* Upload Area Styles */
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    background: white;
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: #007bff;
    background: #f8f9ff;
}

.upload-placeholder {
    cursor: pointer;
}

.upload-icon {
    font-size: 48px;
    margin-bottom: 10px;
}

.upload-placeholder h4 {
    margin: 10px 0;
    color: #495057;
}

.upload-placeholder p {
    margin: 5px 0;
    color: #6c757d;
    font-size: 14px;
}

.file-size-limit {
    font-size: 12px !important;
    color: #868e96 !important;
}

/* Uploaded File Info */
.uploaded-file-info {
    margin-top: 15px;
    padding: 15px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
}

.file-details {
    display: flex;
    align-items: center;
    gap: 10px;
}

.file-icon {
    font-size: 24px;
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: #495057;
}

.file-size {
    font-size: 12px;
    color: #6c757d;
}

.remove-file-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 8px;
    cursor: pointer;
    font-size: 12px;
}

.remove-file-btn:hover {
    background: #c82333;
}

/* Tool Grid Styles */
.tool-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.tool-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tool-card:hover {
    border-color: #3498db;
    background: rgba(52, 152, 219, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.tool-card .tool-icon {
    font-size: 32px;
    margin-bottom: 10px;
}

.tool-card h4 {
    color: #333;
    margin-bottom: 8px;
    font-size: 1rem;
}

.tool-card p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

.tool-card.active {
    border-color: #3498db;
    background: rgba(52, 152, 219, 0.1);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Signature Library Styles */
.signature-library {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    max-height: 200px;
    overflow-y: auto;
}

.signature-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.signature-item:hover {
    border-color: #007bff;
    background: #f8f9ff;
}

.signature-preview {
    width: 100%;
    height: 40px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    margin-bottom: 5px;
    background: white;
}

.signature-name {
    font-size: 12px;
    color: #495057;
    text-align: center;
}

.no-signatures {
    grid-column: 1 / -1;
    text-align: center;
    color: #6c757d;
    font-style: italic;
    margin: 0;
}

/* Document Preview Styles */
.document-preview-section {
    height: 100%;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.document-preview-container {
    height: calc(100% - 40px);
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: auto;
    position: relative;
}

.preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6c757d;
}

.preview-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.preview-placeholder h4 {
    margin: 10px 0;
    color: #495057;
}

.preview-placeholder p {
    margin: 0;
    font-size: 14px;
}

/* Document Canvas */
.document-canvas {
    position: relative;
    min-height: 100%;
    background: white;
    padding: 20px;
}

.document-page {
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin: 0 auto 20px auto;
    position: relative;
    overflow: hidden;
}

/* Editable Elements */
.editable-text {
    position: absolute;
    border: 2px dashed transparent;
    padding: 2px;
    cursor: text;
    min-width: 50px;
    min-height: 20px;
}

.editable-text:hover {
    border-color: #007bff;
    background: rgba(0, 123, 255, 0.1);
}

.editable-text.selected {
    border-color: #007bff;
    background: rgba(0, 123, 255, 0.1);
}

.editable-signature {
    position: absolute;
    border: 2px dashed transparent;
    cursor: move;
}

.editable-signature:hover {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.1);
}

.editable-signature.selected {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.1);
}

/* Form Actions */
.document-import-body .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px 0 0 0;
    border-top: 1px solid #e9ecef;
    margin-top: 20px;
}

.btn-success {
    background: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.btn-success:hover {
    background: #218838;
}

.btn-success:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

/* Responsive Design for Import Modal */
@media (max-width: 768px) {
    .import-layout {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .tool-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 10px;
    }

    .tool-card {
        padding: 15px;
    }

    .tool-card .tool-icon {
        font-size: 24px;
        margin-bottom: 8px;
    }

    .tool-card h4 {
        font-size: 0.9rem;
    }

    .tool-card p {
        font-size: 0.8rem;
    }

    .signature-library {
        grid-template-columns: 1fr;
    }

    .document-import-content {
        width: 98%;
        height: 95vh;
    }

    .document-import-body .form-actions {
        flex-direction: column;
    }

    .btn-primary,
    .btn-secondary,
    .btn-success {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .tool-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 8px;
    }

    .tool-card {
        padding: 12px;
    }

    .tool-card .tool-icon {
        font-size: 20px;
    }

    .tool-card h4 {
        font-size: 0.8rem;
    }

    .tool-card p {
        font-size: 0.7rem;
    }
}

/* Active Company Display Styles */
.section-description {
    color: #666;
    font-style: italic;
    margin-bottom: 20px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #007bff;
}

.active-company-display {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.company-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.info-item.full-width {
    grid-column: 1 / -1;
}

.info-item label {
    font-weight: 600;
    color: #495057;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 5px;
}

.info-item span {
    background: white;
    padding: 10px 12px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    color: #333;
    font-weight: 500;
    min-height: 40px;
    display: flex;
    align-items: center;
}

.company-note {
    background: #e3f2fd;
    padding: 12px;
    border-radius: 6px;
    border-left: 4px solid #2196f3;
    margin-top: 15px;
}

.company-note small {
    color: #1976d2;
    font-weight: 500;
}

.company-note a {
    color: #1976d2;
    text-decoration: none;
    font-weight: 600;
}

.company-note a:hover {
    text-decoration: underline;
}

/* Loading state for company info */
.info-item span:contains("Loading...") {
    background: #f8f9fa;
    color: #6c757d;
    font-style: italic;
    animation: pulse 1.5s ease-in-out infinite alternate;
}

@keyframes pulse {
    from {
        opacity: 0.6;
    }
    to {
        opacity: 1;
    }
}

/* Digital Signature Integration Styles */
.signature-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

.signature-modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease;
}

.signature-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 12px 12px 0 0;
}

.signature-modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.2rem;
}

.signature-modal-close {
    font-size: 24px;
    font-weight: bold;
    color: #999;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.signature-modal-close:hover {
    background: #e9ecef;
    color: #333;
}

.signature-modal-body {
    padding: 25px;
}

.signature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.signature-option {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
}

.signature-option:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
    transform: translateY(-2px);
}

.signature-preview {
    width: 100%;
    height: 60px;
    object-fit: contain;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: #f8f9fa;
    margin-bottom: 10px;
}

.signature-info {
    text-align: center;
}

.signature-info strong {
    display: block;
    color: #333;
    margin-bottom: 5px;
}

.signature-info small {
    color: #666;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.signature-modal-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

/* Signature field indicators */
input[data-signature-data],
textarea[data-signature-data] {
    border-left: 3px solid #28a745 !important;
    background-color: #f8fff8 !important;
}

input[title*="signature" i],
textarea[title*="signature" i] {
    border-left: 3px solid #007bff !important;
}

/* Animation keyframes */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}
