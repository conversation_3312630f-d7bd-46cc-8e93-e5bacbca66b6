<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Freemium System Test</title>
    <link rel="stylesheet" href="freemium-styles.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #3367d6;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            background: #e8f5e8;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            border-color: #f44336;
        }
    </style>
</head>
<body>
    <h1>🪙 Freemium System Test Page</h1>
    
    <div class="test-section">
        <h2>Token Display Test</h2>
        <div class="token-counter" onclick="freemiumSystem.showTokenHistory()">
            🪙 <span class="token-balance">0</span>
        </div>
        <div class="token-widget">
            <div class="token-widget-header">
                <div class="token-widget-title">🪙 Token Balance</div>
                <div class="token-widget-balance"><span class="token-balance">0</span> tokens</div>
            </div>
            <div class="token-actions">
                <button class="token-btn purchase" onclick="freemiumSystem.showSubscriptionModal()">
                    💳 Purchase Tokens
                </button>
                <button class="token-btn history" onclick="freemiumSystem.showTokenHistory()">
                    📊 Token History
                </button>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>System Status</h2>
        <div id="status-display" class="status">Loading...</div>
        <button class="test-button" onclick="checkSystemStatus()">Check Status</button>
        <button class="test-button" onclick="createTestUser()">Create Test User</button>
        <button class="test-button" onclick="testTokenConsumption()">Test Token Usage</button>
        <button class="test-button" onclick="clearStorage()">Clear Storage</button>
    </div>

    <div class="test-section">
        <h2>Authentication Test</h2>
        <div id="google-signin-test" class="google-signin-container"></div>
        <button class="test-button" onclick="testGoogleAuth()">Test Google Auth</button>
        <button class="test-button" onclick="testEmailAuth()">Test Email Auth</button>
    </div>

    <div class="test-section">
        <h2>Debug Console</h2>
        <div id="debug-console" style="background: #f0f0f0; padding: 10px; border-radius: 5px; font-family: monospace; height: 200px; overflow-y: auto;"></div>
        <button class="test-button" onclick="clearDebugConsole()">Clear Console</button>
    </div>

    <script src="freemium-system.js"></script>
    <script src="google-auth.js"></script>
    <script>
        // Debug console
        function debugLog(message) {
            const console = document.getElementById('debug-console');
            const timestamp = new Date().toLocaleTimeString();
            console.innerHTML += `[${timestamp}] ${message}<br>`;
            console.scrollTop = console.scrollHeight;
        }

        function clearDebugConsole() {
            document.getElementById('debug-console').innerHTML = '';
        }

        // Override console.log to capture debug messages
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            debugLog(args.join(' '));
        };

        function checkSystemStatus() {
            const status = document.getElementById('status-display');
            let statusText = '';
            
            if (window.freemiumSystem) {
                statusText += '✅ Freemium System: Loaded<br>';
                statusText += `🪙 Current Balance: ${freemiumSystem.getTokenBalance()}<br>`;
                statusText += `👤 Current User: ${freemiumSystem.currentUser ? freemiumSystem.currentUser.email : 'None'}<br>`;
            } else {
                statusText += '❌ Freemium System: Not Loaded<br>';
            }
            
            if (window.googleAuth) {
                statusText += '✅ Google Auth: Loaded<br>';
                statusText += `🔐 Demo Mode: ${googleAuth.demoMode ? 'Yes' : 'No'}<br>`;
                statusText += `👤 Authenticated: ${googleAuth.isAuthenticated() ? 'Yes' : 'No'}<br>`;
            } else {
                statusText += '❌ Google Auth: Not Loaded<br>';
            }
            
            const tokenElements = document.querySelectorAll('.token-balance');
            statusText += `📊 Token Elements Found: ${tokenElements.length}<br>`;
            
            status.innerHTML = statusText;
        }

        function createTestUser() {
            if (window.freemiumSystem) {
                const testUser = {
                    email: '<EMAIL>',
                    name: 'Test User'
                };
                freemiumSystem.initializeNewUser(testUser.email, testUser.name);
                debugLog('✅ Test user created with 25 tokens');
                checkSystemStatus();
            } else {
                debugLog('❌ Freemium system not available');
            }
        }

        function testTokenConsumption() {
            if (window.freemiumSystem) {
                const success = freemiumSystem.consumeTokens('document_generation', 'invoice', 'Test invoice generation');
                debugLog(`🪙 Token consumption test: ${success ? 'Success' : 'Failed'}`);
                checkSystemStatus();
            } else {
                debugLog('❌ Freemium system not available');
            }
        }

        function testGoogleAuth() {
            if (window.googleAuth) {
                googleAuth.demoGoogleSignIn('signup');
                debugLog('🔐 Demo Google Sign-In triggered');
                checkSystemStatus();
            } else {
                debugLog('❌ Google Auth not available');
            }
        }

        function testEmailAuth() {
            debugLog('📧 Email auth test - not implemented yet');
        }

        function clearStorage() {
            localStorage.clear();
            debugLog('🗑️ Local storage cleared');
            location.reload();
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('🚀 Test page loaded');
            setTimeout(() => {
                checkSystemStatus();
                if (window.freemiumSystem) {
                    freemiumSystem.updateTokenDisplay();
                }
            }, 1000);
        });
    </script>
</body>
</html>
