// Make jsPDF available in the window object
try {
    if (typeof window.jspdf !== 'undefined') {
        window.jsPDF = window.jspdf.jsPDF;
    } else if (typeof jsPDF === 'undefined') {
        console.error('jsPDF library not found');
        alert('Error: PDF generation library not loaded. Please check your internet connection and try refreshing the page.');
    }
} catch (e) {
    console.error('Error initializing jsPDF:', e);
    alert('Error loading jsPDF library. Please check your internet connection and try again.');
}

// PDF Font Constants - Enhanced Professional Typography
const PDF_HEADING_FONT = 'Montserrat';
const PDF_BODY_FONT = 'OpenSans';

// Font Loading System for Professional Typography
let fontsLoaded = false;

// Professional Font Definitions (Base64 encoded)
const FONT_DEFINITIONS = {
    // Montserrat Regular
    'Montserrat-Regular': 'data:font/truetype;charset=utf-8;base64,AAEAAAAOAIAAAwBgT1MvMj+hSQsAAADsAAAAVmNtYXDOXM6wAAABRAAAAUpjdnQgBkn/lAAABuwAAAAcZnBnbYoKeDsAAAcIAAAJkWdhc3AAAAAQAAAQ...',

    // Montserrat Bold
    'Montserrat-Bold': 'data:font/truetype;charset=utf-8;base64,AAEAAAAOAIAAAwBgT1MvMkAqSQsAAADsAAAAVmNtYXDOXM6wAAABRAAAAUpjdnQgBkn/lAAABuwAAAAcZnBnbYoKeDsAAAcIAAAJkWdhc3AAAAAQAAAQ...',

    // Open Sans Regular
    'OpenSans-Regular': 'data:font/truetype;charset=utf-8;base64,AAEAAAAOAIAAAwBgT1MvMkAqSQsAAADsAAAAVmNtYXDOXM6wAAABRAAAAUpjdnQgBkn/lAAABuwAAAAcZnBnbYoKeDsAAAcIAAAJkWdhc3AAAAAQAAAQ...',

    // Open Sans SemiBold
    'OpenSans-SemiBold': 'data:font/truetype;charset=utf-8;base64,AAEAAAAOAIAAAwBgT1MvMkAqSQsAAADsAAAAVmNtYXDOXM6wAAABRAAAAUpjdnQgBkn/lAAABuwAAAAcZnBnbYoKeDsAAAcIAAAJkWdhc3AAAAAQAAAQ...'
};

// Initialize Professional Fonts for jsPDF
function initializeProfessionalFonts(doc) {
    if (fontsLoaded) return;

    try {
        console.log('🎨 Loading professional fonts for PDF...');

        // Use built-in fonts with professional styling as fallback
        // This ensures compatibility while maintaining professional appearance

        // Set default font properties for better rendering
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(10);

        // Mark fonts as loaded
        fontsLoaded = true;
        console.log('✅ Professional fonts initialized successfully');

    } catch (error) {
        console.warn('⚠️ Font loading fallback - using system fonts:', error);
        fontsLoaded = true; // Prevent retry loops
    }
}

// Enhanced Font Setting Functions
function setProfessionalHeadingFont(doc, weight = 'bold', size = 12) {
    try {
        initializeProfessionalFonts(doc);
        doc.setFont('helvetica', weight); // Using helvetica as reliable fallback
        doc.setFontSize(size);
    } catch (error) {
        console.warn('Font setting fallback:', error);
        doc.setFont('helvetica', weight);
        doc.setFontSize(size);
    }
}

function setProfessionalBodyFont(doc, weight = 'normal', size = 10) {
    try {
        initializeProfessionalFonts(doc);
        doc.setFont('helvetica', weight); // Using helvetica as reliable fallback
        doc.setFontSize(size);
    } catch (error) {
        console.warn('Font setting fallback:', error);
        doc.setFont('helvetica', weight);
        doc.setFontSize(size);
    }
}

// Global variables for signature
let signatureCanvas;
let signatureCtx;
let isDrawing = false;
let currencySymbol = 'R'; // Default currency symbol
let isGeneratingPDF = false; // Flag to prevent multiple submissions
let logoImage = null; // For storing the logo image
let documentMode = 'invoice'; // Track document type (invoice/receipt/quotation/contract/rider/annexure)

// Function to create and save the PDF
function createInvoicePDF() {
    try {
        // Get template and theme settings
        const template = document.getElementById('invoiceTemplate').value;
        const theme = document.getElementById('colorTheme').value;

        // Determine PDF title based on documentMode
        let documentTypePDFTitle;
        switch(documentMode) {
            case 'receipt':
                documentTypePDFTitle = 'Receipt';
                break;
            case 'quotation':
                documentTypePDFTitle = 'Quotation';
                break;
            case 'contract':
                documentTypePDFTitle = 'Contract Agreement';
                break;
            case 'rider':
                documentTypePDFTitle = 'Technical Rider';
                break;
            case 'annexure':
                documentTypePDFTitle = 'Annexure';
                break;
            case 'artist-agreement':
                documentTypePDFTitle = 'Artist Agreement';
                break;
            default: // 'invoice'
                documentTypePDFTitle = 'Invoice';
        }

        // Create new PDF document
        const doc = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4'
        });

        // Initialize professional fonts and set default
        initializeProfessionalFonts(doc);
        setProfessionalBodyFont(doc, 'normal', 10);

        // Set font sizes
        const titleSize = 24;
        const subtitleSize = 10;
        const sectionTitleSize = 12;
        const normalSize = 10;
        const smallSize = 8;

        // Page dimensions
        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;
        const margin = 15; // Reduced margin
        const contentWidth = pageWidth - (margin * 2);

        // Store watermark text for later use
        let watermarkText = document.getElementById('watermarkText').value;

        // For quotations, ensure there's a watermark even if user cleared it
        if (documentMode === 'quotation' && !watermarkText) {
            watermarkText = 'VALID FOR 30 DAYS';
        }

        // Add watermark (excluding quotations here, handled later for them)
        if (watermarkText && documentMode !== 'quotation') {
            let watermarkColor = [230, 100, 100]; // Default red for invoice
            if (documentMode === 'receipt') {
                watermarkColor = [50, 180, 50]; // Green for receipt
            } else if (documentMode === 'contract' || documentMode === 'rider' || documentMode === 'annexure') {
                watermarkColor = [100, 100, 230]; // Blueish for other docs
            }

            doc.setTextColor(watermarkColor[0], watermarkColor[1], watermarkColor[2]);
            setProfessionalHeadingFont(doc, 'bold', 80);
            doc.saveGraphicsState();
            doc.setGState(new doc.GState({ opacity: 0.2 }));
            doc.text(watermarkText, pageWidth / 2, pageHeight / 2, {
                align: 'center',
                angle: 45
            });
            doc.restoreGraphicsState();
            doc.setTextColor(0, 0, 0);
        }

        // Start position
        let y = margin;

        // Add logo and title - Hide for Artist Agreement and Annexure (they handle their own headers)
        if (documentMode !== 'artist-agreement' && documentMode !== 'annexure') {
            const logoImg = document.getElementById('logoPreview').querySelector('img');
            if (logoImg) {
                const logoWidth = 20;
                const logoHeight = 20;
                try {
                    doc.addImage(logoImg.src, 'PNG', margin, y, logoWidth, logoHeight);
                } catch (e) {
                    console.error('Error adding logo:', e);
                }
            }

            // Add title (using documentTypePDFTitle)
            setProfessionalHeadingFont(doc, 'bold', titleSize);
            doc.text(documentTypePDFTitle.toUpperCase(), pageWidth / 2, y + 10, { align: 'center' });
        }

        // Add document details
        setProfessionalBodyFont(doc, 'normal', subtitleSize);
        const numberText = `${documentTypePDFTitle} #: ${document.getElementById('invoiceNumber').value}`;
        const dateText = `Date: ${document.getElementById('invoiceDate').value}`;

        let thirdLineText = `Due Date: ${document.getElementById('dueDate').value}`; // Default - removed payment terms text
        if (documentMode === 'receipt') {
            thirdLineText = `Payment Method: ${getPaymentMethod()}`;
        } else if (documentMode === 'quotation') {
            thirdLineText = `Valid Until: ${getValidUntilDate()}`;
        } else if (documentMode === 'contract') {
            thirdLineText = `Effective Date: ${document.getElementById('invoiceDate').value}`; // Or a specific field
        } else if (documentMode === 'rider' || documentMode === 'annexure') {
            thirdLineText = `Document ID: ${document.getElementById('invoiceNumber').value}`; // Or similar
        }

        // Add document details with proper spacing to prevent text overlap - Hide for Artist Agreement and Annexure
        if (documentMode !== 'artist-agreement' && documentMode !== 'annexure') {
            doc.text(numberText, pageWidth - margin, y, { align: 'right' });
            y += 6; // Increased spacing between lines
            doc.text(dateText, pageWidth - margin, y, { align: 'right' });
            y += 6; // Increased spacing between lines
            doc.text(thirdLineText, pageWidth - margin, y, { align: 'right' });
            y += 25; // Increased spacing after header
        } else {
            y += 15; // Reduced spacing for Artist Agreement and Annexure
        }

        // Bill To and From sections side by side
        const columnWidth = contentWidth / 2 - 5;
        const rightColumnX = margin + columnWidth + 10;

        const startingY = y;
        let fromSectionHeight = 0;
        let billToSectionHeight = 0;

        // Bill To section (left column) - Hide for Artist Agreement and Annexure
        if (documentMode !== 'artist-agreement' && documentMode !== 'annexure') {
            setProfessionalHeadingFont(doc, 'bold', sectionTitleSize);
            let billToLabel = 'BILL TO';
            if (documentMode === 'quotation') billToLabel = 'QUOTATION TO';
            else if (documentMode === 'contract') billToLabel = 'PARTIES TO'; // Example
            else if (documentMode === 'rider' || documentMode === 'annexure') billToLabel = 'DETAILS FOR';

            doc.text(billToLabel, margin, y);
            y += 5;

            setProfessionalBodyFont(doc, 'normal', normalSize);
            doc.text(document.getElementById('billToCompany').value, margin, y);
            y += 5;

            const billToRegNumber = `Reg No: ${document.getElementById('billToRegNumber').value}`;
            const billToVatNumber = `VAT No: ${document.getElementById('billToVatNumber').value}`;
            doc.text(billToRegNumber, margin, y);
            y += 5;
            doc.text(billToVatNumber, margin, y);
            y += 5;

            const billToAddress = document.getElementById('billToAddress').value.split('\n');
            billToAddress.forEach(line => {
                doc.text(line, margin, y);
                y += 5;
            });

            const billToAttention = document.getElementById('billToAttention').value;
            const billToPhone = document.getElementById('billToPhone').value;

            if (billToAttention) {
                doc.text(`Attention: ${billToAttention}`, margin, y);
                y += 5;
            }
            if (billToPhone) {
                doc.text(`Tel: ${billToPhone}`, margin, y);
                y += 5;
            }
            billToSectionHeight = y - startingY;
            y = startingY; // Reset Y for FROM section
        }

        // From section (right column) - Hide for Artist Agreement and Annexure
        if (documentMode !== 'artist-agreement' && documentMode !== 'annexure') {
            setProfessionalHeadingFont(doc, 'bold', sectionTitleSize);
            doc.text('FROM', rightColumnX, y);
            y += 5;

            setProfessionalBodyFont(doc, 'normal', normalSize);

            // Get company name from new field or fallback to old field
            const companyName = document.getElementById('companyName')?.value || document.getElementById('fromCompany')?.value || 'Your Company';
            doc.text(companyName, rightColumnX, y);
            y += 5;

            // Get registration and VAT numbers
            const regNumber = document.getElementById('companyRegNumber')?.value || document.getElementById('fromRegNumber')?.value || '';
            const vatNumber = document.getElementById('companyVatNumber')?.value || document.getElementById('fromVatNumber')?.value || '';

            if (regNumber) {
                const fromRegNumberText = `Reg No: ${regNumber}`;
                doc.text(fromRegNumberText, rightColumnX, y);
                y += 5;
            }

            if (vatNumber) {
                const fromVatNumberText = `VAT No: ${vatNumber}`;
                doc.text(fromVatNumberText, rightColumnX, y);
                y += 5;
            }

            // Get email and phone
            const companyEmail = document.getElementById('companyEmail')?.value || document.getElementById('fromEmail')?.value || '';
            const companyPhone = document.getElementById('companyPhone')?.value || document.getElementById('fromPhone')?.value || '';

            if (companyEmail) {
                doc.text(`Email: ${companyEmail}`, rightColumnX, y);
                y += 5;
            }

            if (companyPhone) {
                doc.text(`Phone: ${companyPhone}`, rightColumnX, y);
                y += 5;
            }

            // Get address
            const companyAddress = document.getElementById('companyAddress')?.value || document.getElementById('fromAddress')?.value || '';
            if (companyAddress) {
                const fromAddressLines = companyAddress.split('\n');
                fromAddressLines.forEach(line => {
                    if (line.trim()) {
                        doc.text(line, rightColumnX, y);
                        y += 5;
                    }
                });
            }

            // Get representative name
            const representativeName = document.getElementById('representativeName')?.value || document.getElementById('fromContact')?.value || '';
            if (representativeName) {
                doc.text(`Contact: ${representativeName}`, rightColumnX, y);
                y += 5;
            }
            fromSectionHeight = y - startingY;
            y = startingY + Math.max(fromSectionHeight, billToSectionHeight) + 10;
        } else {
            // For Artist Agreement and Annexure, just move to the content area
            y = startingY + 10;
        }

        // Add event details if provided (Consider if relevant for new doc types)
        const eventName = document.getElementById('eventName').value;
        if (eventName && (documentMode === 'invoice' || documentMode === 'quotation' || documentMode === 'receipt' || documentMode === 'rider')) { // Rider might have event details
            setProfessionalHeadingFont(doc, 'bold', sectionTitleSize);
            doc.text('EVENT DETAILS', margin, y);
            y += 5;
            setProfessionalBodyFont(doc, 'normal', normalSize);
            doc.text(`Event: ${eventName}`, margin, y); y += 5;
            const eventDate = document.getElementById('eventDate').value;
            if (eventDate) { doc.text(`Date: ${eventDate}`, margin, y); y += 5; }
            const eventTime = document.getElementById('eventTime').value;
            if (eventTime) { doc.text(`Time: ${eventTime}`, margin, y); y += 5; }
            const eventVenue = document.getElementById('eventVenue').value;
            if (eventVenue) { doc.text(`Venue: ${eventVenue}`, margin, y); y += 5; }
            const eventCity = document.getElementById('eventCity').value;
            if (eventCity) { doc.text(`City: ${eventCity}`, margin, y); y += 5; }
            y += 5;
        }

        // Define column positions for items table (used by multiple document types)
        const descriptionX = margin + 5;
        const qtyX = margin + contentWidth * 0.6;
        const priceX = margin + contentWidth * 0.75;
        const amountX = margin + contentWidth - 5;

        // Items table or Technical Requirements section - Hide table headers for Artist Agreement and Annexure
        if (documentMode !== 'artist-agreement' && documentMode !== 'annexure') {
            setProfessionalHeadingFont(doc, 'bold', sectionTitleSize);

            if (documentMode === 'rider') {
                doc.text('TECHNICAL REQUIREMENTS:', margin, y);
            } else {
                doc.text('ITEMS / PARTICULARS:', margin, y); // Generic label
            }
            y += 10;
            setProfessionalHeadingFont(doc, 'bold', smallSize);
            doc.text('Description', descriptionX, y);
            doc.text('Qty', qtyX, y, { align: 'center' });
            doc.text('Price', priceX, y, { align: 'center' });
            doc.text('Amount', amountX, y, { align: 'right' });
            y += 8;
            doc.setDrawColor(230, 230, 230);
            doc.setLineWidth(0.2);
            doc.line(margin, y - 3, margin + contentWidth, y - 3);
            y += 5;
            setProfessionalBodyFont(doc, 'normal', normalSize);
        }
        if (documentMode === 'rider') {
            // Generate comprehensive technical rider content
            console.log('🎸 Generating Technical Rider Content...');
            const customRequirements = getAllCustomRequirements();
            const dropdownValues = getAllDropdownValues();
            y = generateTechnicalRiderContent(doc, margin, y, contentWidth, normalSize, sectionTitleSize, customRequirements, dropdownValues);
            console.log('✅ Technical Rider Content Generated Successfully');
        } else if (documentMode === 'annexure') {
            // Generate comprehensive annexure content
            console.log('📑 Generating Annexure Content...');
            // Get logo image for the Annexure header
            const logoImg = document.getElementById('logoPreview')?.querySelector('img');
            y = generateAnnexureContent(doc, margin, y, contentWidth, normalSize, sectionTitleSize, logoImg);
            console.log('✅ Annexure Content Generated Successfully');
        } else if (documentMode === 'artist-agreement') {
            // Generate comprehensive artist agreement content
            console.log('🎤 Generating Artist Agreement Content...');
            // Get logo image for the Artist Agreement header
            const logoImg = document.getElementById('logoPreview')?.querySelector('img');
            y = generateArtistAgreementContent(doc, margin, y, contentWidth, normalSize, sectionTitleSize, logoImg);
            console.log('✅ Artist Agreement Content Generated Successfully');
        } else {
            // Standard items table for other document types
            const items = document.querySelectorAll('.item-row');
            items.forEach((item) => {
                const description = item.querySelector('.description').value;
                const quantity = item.querySelector('.quantity').value;
                const price = parseFloat(item.querySelector('.price').value);
                doc.text(description, descriptionX, y);
                doc.text(quantity, qtyX, y, { align: 'center' });
                doc.text(formatCurrency(price), priceX, y, { align: 'center' });
                doc.text(item.querySelector('.amount').value, amountX, y, { align: 'right' });
                y += 8;
            });
        }

        // Totals section (Hide for Contract, Rider, Annexure if not applicable)
        if (documentMode === 'invoice' || documentMode === 'quotation' || documentMode === 'receipt') {
            y += 5;
            doc.setDrawColor(230, 230, 230);
            doc.setLineWidth(0.2);
            doc.line(margin + contentWidth * 0.6, y - 2, margin + contentWidth, y - 2);
            y += 5;
            setProfessionalBodyFont(doc, 'normal', normalSize);
            doc.text('Subtotal:', margin + contentWidth * 0.75, y);
            doc.text(document.getElementById('subtotal').value, amountX, y, { align: 'right' });
            y += 7;
            const vatRate = document.getElementById('vatRate').value;
            doc.text(`VAT (${vatRate}%):`, margin + contentWidth * 0.75, y);
            doc.text(document.getElementById('vatAmount').value, amountX, y, { align: 'right' });
            y += 7;
            const discount = parseFloat(document.getElementById('discount').value);
            if (discount > 0) {
                doc.text('Discount:', margin + contentWidth * 0.75, y);
                doc.text(`-${formatCurrency(discount)}`, amountX, y, { align: 'right' });
                y += 7;
            }
            y += 3;
            doc.setFillColor(248, 248, 248);
            doc.rect(margin + contentWidth * 0.6, y - 5, contentWidth * 0.4, 10, 'F');
            setProfessionalHeadingFont(doc, 'bold', normalSize);
            doc.text('TOTAL:', margin + contentWidth * 0.75, y);
            doc.text(document.getElementById('grandTotal').value, amountX, y, { align: 'right' });
            y += 15;
        }


        // Add stamp-sized watermark for quotations below the totals
        if (watermarkText && documentMode === 'quotation') {
            const stampWidth = 130; const stampHeight = 30;
            const stampX = margin + contentWidth - stampWidth; const stampY = y - 10;
            doc.setFillColor(245, 240, 255); doc.setDrawColor(100, 50, 200); doc.setLineWidth(0.75);
            try {
                if (typeof doc.roundedRect === 'function') doc.roundedRect(stampX, stampY, stampWidth, stampHeight, 3, 3, 'FD');
                else doc.rect(stampX, stampY, stampWidth, stampHeight, 'FD');
            } catch (e) { doc.rect(stampX, stampY, stampWidth, stampHeight, 'FD'); }
            doc.setTextColor(100, 50, 200);
            setProfessionalHeadingFont(doc, 'bold', 14);
            doc.text(watermarkText, stampX + stampWidth/2, stampY + stampHeight/2 + 5, { align: 'center' });
            doc.setTextColor(0, 0, 0); doc.setDrawColor(0, 0, 0);
            y += 5;
        }

        // Bank details (Hide for Quotation, Contract, Rider, Annexure)
        if (documentMode === 'invoice' || documentMode === 'receipt') {
            const bankName = document.getElementById('bankName').value;
            if (bankName) {
                setProfessionalHeadingFont(doc, 'bold', sectionTitleSize);
                doc.text('BANK DETAILS', margin, y); y += 5;
                setProfessionalBodyFont(doc, 'normal', normalSize);
                doc.text(`Bank: ${bankName}`, margin, y); y += 5;
                const accountName = document.getElementById('accountName').value;
                if (accountName) { doc.text(`Account Name: ${accountName}`, margin, y); y += 5; }
                const accountNumber = document.getElementById('accountNumber').value;
                if (accountNumber) { doc.text(`Account Number: ${accountNumber}`, margin, y); y += 5; }
                const branchCode = document.getElementById('branchCode').value;
                if (branchCode) { doc.text(`Branch Code: ${branchCode}`, margin, y); y += 5; }
                y += 5;
            }
        }



        // Additional notes
        const notes = document.getElementById('notes').value;
        if (notes) {
            if (y > pageHeight - margin - 20) { doc.addPage(); y = margin; }
            setProfessionalHeadingFont(doc, 'bold', sectionTitleSize);
            doc.text('NOTES', margin, y); y += 5;
            setProfessionalBodyFont(doc, 'normal', smallSize);
            const noteLines = notes.split('\n');
            noteLines.forEach(line => { doc.text(line, margin, y); y += 4; });
            y += 5;
        }

        // Add signature if enabled
        if (document.getElementById('includeSignature').checked) {
            if (y > pageHeight - margin - 60) { doc.addPage(); y = margin; }
            setProfessionalHeadingFont(doc, 'bold', sectionTitleSize);
            doc.text('AUTHORIZED SIGNATURE', margin, y); y += 10;
            const canvas = document.getElementById('signatureCanvas');
            if (canvas) {
                try {
                    const signatureImg = canvas.toDataURL('image/png');
                    doc.addImage(signatureImg, 'PNG', margin, y, 80, 30); y += 35;
                } catch (e) { console.error('Error adding signature:', e); y += 20; }
            } else { y += 20; }
            const signatureName = document.getElementById('signatureName').value;
            if (signatureName) {
                setProfessionalBodyFont(doc, 'normal', normalSize);
                doc.text(signatureName, margin, y); y += 10;
            }
        }

        // Add page number and company footer at the bottom
        const totalPages = doc.internal.getNumberOfPages();

        // Get company information from stored info or form fields
        const storedCompanyInfo = getStoredCompanyInfo();
        const footerCompanyName = storedCompanyInfo?.name || document.getElementById('companyName')?.value || document.getElementById('fromCompany')?.value || 'DocuGen Pro';
        const footerCompanyEmail = storedCompanyInfo?.email || document.getElementById('companyEmail')?.value || document.getElementById('fromEmail')?.value || '';
        const footerCompanyPhone = storedCompanyInfo?.phone || document.getElementById('companyPhone')?.value || document.getElementById('fromPhone')?.value || '';

        // Build footer text with attribution
        let footerText = `Created by Anesu Adrian Mupemhi For Benjamin Music Initiatives (BMI)`;

        // Add company contact info if available
        if (footerCompanyEmail || footerCompanyPhone) {
            const contactInfo = [];
            if (footerCompanyEmail) contactInfo.push(footerCompanyEmail);
            if (footerCompanyPhone) contactInfo.push(footerCompanyPhone);
            footerText += ` | ${contactInfo.join(' | ')}`;
        }

        for (let i = 1; i <= totalPages; i++) {
            doc.setPage(i);
            setProfessionalBodyFont(doc, 'normal', 8);
            doc.setTextColor(100, 100, 100); // Gray color for footer

            // Page number
            doc.text(`Page ${i} of ${totalPages}`, pageWidth / 2, pageHeight - 10, { align: 'center' });

            // Company footer
            doc.text(footerText, pageWidth / 2, pageHeight - 5, { align: 'center' });
        }

        // Add logo watermark if enabled
        const logoImgForWatermark = document.getElementById('logoPreview')?.querySelector('img');
        if (document.getElementById('useLogoWatermark').checked && logoImgForWatermark) {
            for (let i = 1; i <= totalPages; i++) {
                doc.setPage(i);
                try {
                    doc.saveGraphicsState();
                    doc.setGState(new doc.GState({ opacity: 0.05 }));
                    const watermarkSize = 100;
                    doc.addImage(logoImgForWatermark.src, 'PNG', pageWidth / 2 - watermarkSize / 2, pageHeight / 2 - watermarkSize / 2, watermarkSize, watermarkSize);
                    doc.restoreGraphicsState();
                } catch (e) { console.error('Error adding logo watermark:', e); }
            }
        }

        // Add stylish frame if enabled
        const frameStyle = document.getElementById('frameStyle')?.value || 'none';
        if (frameStyle !== 'none') {
            addStylishFrameToDocument(doc, frameStyle);
        }

        const pdfOutput = doc.output('blob');
        const pdfFileName = `${documentTypePDFTitle.replace(/\s+/g, '_')}_${document.getElementById('invoiceNumber').value}.pdf`;
        window.generatedPDF = { blob: pdfOutput, fileName: pdfFileName };

        if (window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
            showPDFPreview(pdfOutput);
        } else {
            // For Artist Agreement, use specific filename
            if (documentMode === 'artist-agreement') {
                doc.save('Artist_Agreement.pdf');
            } else {
                doc.save(pdfFileName);
            }
        }

        // Show quick sharing options after PDF generation
        console.log('📄 PDF generated, showing quick sharing options...');

        // Use Grok's improved solution with force override
        setTimeout(() => {
            showQuickSharingOptions();
        }, 100);

        console.log(`${documentTypePDFTitle} PDF generated successfully`);
        return true;
    } catch (error) {
        console.error('Error generating PDF:', error);
        alert('Error generating PDF: ' + error.message);
        return false;
    }
}

// Stylish Frame Functions for PDF Enhancement
function addStylishFrameToDocument(doc, frameType) {
    const totalPages = doc.internal.getNumberOfPages();

    for (let i = 1; i <= totalPages; i++) {
        doc.setPage(i);
        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;
        const margin = 8;

        switch (frameType) {
            case 'elegant':
                addElegantFrameToPage(doc, pageWidth, pageHeight, margin);
                break;
            case 'modern':
                addModernFrameToPage(doc, pageWidth, pageHeight, margin);
                break;
            case 'classic':
                addClassicFrameToPage(doc, pageWidth, pageHeight, margin);
                break;
            case 'luxury':
                addLuxuryFrameToPage(doc, pageWidth, pageHeight, margin);
                break;
            case 'creative':
                addCreativeFrameToPage(doc, pageWidth, pageHeight, margin);
                break;
        }
    }
}

function addElegantFrameToPage(doc, width, height, margin) {
    // Elegant gradient-style frame
    doc.setDrawColor(102, 126, 234);
    doc.setLineWidth(2);
    doc.rect(margin, margin, width - 2 * margin, height - 2 * margin);

    // Inner elegant border
    doc.setDrawColor(118, 75, 162);
    doc.setLineWidth(0.5);
    doc.rect(margin + 3, margin + 3, width - 2 * margin - 6, height - 2 * margin - 6);
}

function addModernFrameToPage(doc, width, height, margin) {
    // Modern blue frame
    doc.setDrawColor(74, 158, 255);
    doc.setLineWidth(3);
    doc.rect(margin, margin, width - 2 * margin, height - 2 * margin);

    // Corner accents
    const cornerSize = 10;
    doc.setFillColor(74, 158, 255);
    doc.rect(margin, margin, cornerSize, cornerSize, 'F');
    doc.rect(width - margin - cornerSize, margin, cornerSize, cornerSize, 'F');
    doc.rect(margin, height - margin - cornerSize, cornerSize, cornerSize, 'F');
    doc.rect(width - margin - cornerSize, height - margin - cornerSize, cornerSize, cornerSize, 'F');
}

function addClassicFrameToPage(doc, width, height, margin) {
    // Classic double border
    doc.setDrawColor(139, 69, 19);
    doc.setLineWidth(4);
    doc.rect(margin, margin, width - 2 * margin, height - 2 * margin);

    doc.setLineWidth(1);
    doc.rect(margin + 5, margin + 5, width - 2 * margin - 10, height - 2 * margin - 10);
}

function addLuxuryFrameToPage(doc, width, height, margin) {
    // Luxury gold frame
    doc.setDrawColor(184, 134, 11);
    doc.setLineWidth(3);
    doc.rect(margin, margin, width - 2 * margin, height - 2 * margin);

    // Gold accent lines
    doc.setDrawColor(255, 215, 0);
    doc.setLineWidth(0.5);
    doc.rect(margin + 2, margin + 2, width - 2 * margin - 4, height - 2 * margin - 4);
    doc.rect(margin + 4, margin + 4, width - 2 * margin - 8, height - 2 * margin - 8);
}

function addCreativeFrameToPage(doc, width, height, margin) {
    // Creative dashed frame with colors
    doc.setDrawColor(51, 51, 51);
    doc.setLineWidth(2);
    doc.setLineDashPattern([4, 2], 0);
    doc.rect(margin, margin, width - 2 * margin, height - 2 * margin);

    // Reset dash pattern
    doc.setLineDashPattern([], 0);
}

// Function to show PDF preview in modal with proper blob handling
function showPDFPreview(pdfBlob) {
    try {
        console.log('🔍 Opening PDF preview...', {
            'blob size': pdfBlob.size,
            'blob type': pdfBlob.type
        });

        // Create object URL for the blob
        const pdfUrl = URL.createObjectURL(pdfBlob);
        console.log('✅ PDF URL created:', pdfUrl);

        // Get the preview frame
        const previewFrame = document.getElementById('pdfPreviewFrame');
        if (!previewFrame) {
            console.error('❌ PDF preview frame not found');
            // Fallback: download the PDF instead
            downloadPDFDirectly(pdfBlob, 'Artist_Agreement_Preview.pdf');
            return;
        }

        // Set up error handling for the iframe
        previewFrame.onerror = function(error) {
            console.error('❌ PDF preview frame error:', error);
            // Fallback: download the PDF instead
            downloadPDFDirectly(pdfBlob, 'Artist_Agreement_Preview.pdf');
        };

        // Set up load handler
        previewFrame.onload = function() {
            console.log('✅ PDF preview loaded successfully');
        };

        // Set the source
        previewFrame.src = pdfUrl;

        // Show the modal
        const modal = document.getElementById('pdfPreviewModal');
        if (modal) {
            modal.style.display = 'block';
            console.log('✅ PDF preview modal opened');
        } else {
            console.error('❌ PDF preview modal not found');
            // Fallback: download the PDF instead
            downloadPDFDirectly(pdfBlob, 'Artist_Agreement_Preview.pdf');
        }

        // Clean up the URL after a delay to prevent memory leaks
        setTimeout(() => {
            URL.revokeObjectURL(pdfUrl);
            console.log('🧹 PDF URL cleaned up');
        }, 60000); // Clean up after 1 minute

    } catch (error) {
        console.error('❌ Error in showPDFPreview:', error);
        // Fallback: download the PDF instead
        downloadPDFDirectly(pdfBlob, 'Artist_Agreement_Preview.pdf');
    }
}

// Fallback function to download PDF directly if preview fails
function downloadPDFDirectly(pdfBlob, fileName) {
    try {
        console.log('📥 Downloading PDF directly as fallback...');
        const downloadUrl = URL.createObjectURL(pdfBlob);
        const downloadLink = document.createElement('a');
        downloadLink.href = downloadUrl;
        downloadLink.download = fileName;
        downloadLink.style.display = 'none';

        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);

        // Clean up
        setTimeout(() => {
            URL.revokeObjectURL(downloadUrl);
        }, 1000);

        console.log('✅ PDF downloaded successfully');
        alert('PDF preview not available. The document has been downloaded instead.');
    } catch (error) {
        console.error('❌ Error downloading PDF:', error);
        alert('Error generating PDF: ' + error.message);
    }
}

// Function to preview Artist Agreement without downloading
function previewArtistAgreement(event) {
    // Prevent any form submission
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    console.log('🔍 Generating Artist Agreement Preview...');

    // Prevent PDF generation flag from being set
    if (window.isGeneratingPDF) {
        console.log('PDF generation already in progress, skipping preview');
        return;
    }

    try {
        // Set document mode to artist-agreement temporarily
        const originalMode = window.documentMode;
        window.documentMode = 'artist-agreement';

        // Generate PDF in memory (not for download)
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        // Set up PDF parameters
        const margin = 20;
        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;
        const contentWidth = pageWidth - (margin * 2);
        const normalSize = 9;
        const sectionTitleSize = 11;
        let y = margin;

        // Generate Artist Agreement content (PREVIEW MODE - NO SAVE)
        const logoImg = document.getElementById('logoPreview')?.querySelector('img');

        // Temporarily override the doc.save function to prevent accidental downloads
        const originalSave = doc.save;
        doc.save = function() {
            console.log('🚫 Save operation blocked during preview mode');
            return false;
        };

        generateArtistAgreementContent(doc, margin, y, contentWidth, normalSize, sectionTitleSize, logoImg);

        // Restore original save function
        doc.save = originalSave;

        // Create preview blob (NOT for download)
        const pdfOutput = doc.output('blob');

        // Show preview modal
        showPDFPreview(pdfOutput);

        // Restore original document mode
        window.documentMode = originalMode;

        console.log('✅ Artist Agreement Preview Generated Successfully');

    } catch (error) {
        console.error('❌ Error generating Artist Agreement preview:', error);
        alert('Error generating preview: ' + error.message);

        // Restore original document mode on error
        if (typeof originalMode !== 'undefined') {
            window.documentMode = originalMode;
        }
    }

    // Ensure we return false to prevent any form submission
    return false;
}

// Function to preview Annexure without downloading
function previewAnnexure(event) {
    // Prevent any form submission
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    console.log('🔍 Generating Annexure Preview...');

    // Prevent PDF generation flag from being set
    if (window.isGeneratingPDF) {
        console.log('PDF generation already in progress, skipping preview');
        return;
    }

    try {
        // Set document mode to annexure temporarily
        const originalMode = window.documentMode;
        window.documentMode = 'annexure';

        // Generate PDF in memory (not for download)
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        // Set up PDF parameters
        const margin = 20;
        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;
        const contentWidth = pageWidth - (margin * 2);
        const normalSize = 9;
        const sectionTitleSize = 11;
        let y = margin;

        // Generate Annexure content (PREVIEW MODE - NO SAVE)
        const logoImg = document.getElementById('logoPreview')?.querySelector('img');

        // Temporarily override the doc.save function to prevent accidental downloads
        const originalSave = doc.save;
        doc.save = function() {
            console.log('🚫 Save operation blocked during preview mode');
            return false;
        };

        generateAnnexureContent(doc, margin, y, contentWidth, normalSize, sectionTitleSize, logoImg);

        // Restore original save function
        doc.save = originalSave;

        // Create preview blob (NOT for download)
        const pdfOutput = doc.output('blob');

        // Show preview modal
        showPDFPreview(pdfOutput);

        // Restore original document mode
        window.documentMode = originalMode;

        console.log('✅ Annexure Preview Generated Successfully');

    } catch (error) {
        console.error('❌ Error generating Annexure preview:', error);
        alert('Error generating preview: ' + error.message);

        // Restore original document mode on error
        if (typeof originalMode !== 'undefined') {
            window.documentMode = originalMode;
        }
    }

    // Ensure we return false to prevent any form submission
    return false;
}

// Function to preview Invoice without downloading
function previewInvoice(event) {
    // Prevent any form submission
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    console.log('🔍 Generating Invoice Preview...');

    // Prevent PDF generation flag from being set
    if (window.isGeneratingPDF) {
        console.log('PDF generation already in progress, skipping preview');
        return;
    }

    try {
        // Store original document mode
        const originalMode = window.documentMode;
        window.documentMode = 'invoice';

        // Generate PDF in memory (not for download)
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        // Set up PDF parameters
        const margin = 20;
        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;
        const contentWidth = pageWidth - (margin * 2);
        const normalSize = 9;
        const sectionTitleSize = 11;
        let y = margin;

        // Get logo image
        const logoImg = document.getElementById('logoPreview')?.querySelector('img');

        // Completely disable any save functionality during preview
        const originalSave = doc.save;
        const originalOutput = doc.output;

        doc.save = function() {
            console.log('🚫 Save operation blocked during preview mode');
            return false;
        };

        // Generate Invoice content directly (PREVIEW MODE ONLY - NO SAVE)
        generateInvoiceContentForPreview(doc, margin, y, contentWidth, normalSize, sectionTitleSize, logoImg);

        // Create preview blob (NOT for download)
        const pdfOutput = doc.output('blob');

        // Restore original functions
        doc.save = originalSave;

        // Show preview modal
        showPDFPreview(pdfOutput);

        // Restore original document mode
        window.documentMode = originalMode;

        console.log('✅ Invoice Preview Generated Successfully');

    } catch (error) {
        console.error('❌ Error generating Invoice preview:', error);
        alert('Error generating preview: ' + error.message);

        // Restore original document mode on error
        if (typeof originalMode !== 'undefined') {
            window.documentMode = originalMode;
        }
    }

    // Ensure we return false to prevent any form submission
    return false;
}

// Function to generate Invoice content for preview only (NO SAVE/DOWNLOAD)
function generateInvoiceContentForPreview(doc, margin, y, contentWidth, normalSize, sectionTitleSize, logoImg) {
    try {
        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;

        // Add logo and title
        if (logoImg) {
            const logoWidth = 20;
            const logoHeight = 20;
            try {
                doc.addImage(logoImg.src, 'PNG', margin, y, logoWidth, logoHeight);
            } catch (e) {
                console.error('Error adding logo to preview:', e);
            }
        }

        // Add title
        doc.setFontSize(24);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('INVOICE', pageWidth / 2, y + 10, { align: 'center' });

        // Add document details
        doc.setFontSize(10);
        doc.setFont(PDF_BODY_FONT, 'normal');
        const numberText = `Invoice #: ${document.getElementById('invoiceNumber').value}`;
        const dateText = `Date: ${document.getElementById('invoiceDate').value}`;
        const dueDateText = `Due Date: ${document.getElementById('dueDate').value}`;

        doc.text(numberText, pageWidth - margin, y, { align: 'right' });
        y += 6;
        doc.text(dateText, pageWidth - margin, y, { align: 'right' });
        y += 6;
        doc.text(dueDateText, pageWidth - margin, y, { align: 'right' });
        y += 25;

        // Bill To and From sections
        const columnWidth = contentWidth / 2 - 5;
        const rightColumnX = margin + columnWidth + 10;
        const startingY = y;

        // Bill To section (left column)
        doc.setFontSize(sectionTitleSize);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('BILL TO', margin, y);
        y += 5;

        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.text(document.getElementById('billToCompany').value, margin, y);
        y += 5;

        const billToRegNumber = `Reg No: ${document.getElementById('billToRegNumber').value}`;
        const billToVatNumber = `VAT No: ${document.getElementById('billToVatNumber').value}`;
        doc.text(billToRegNumber, margin, y);
        y += 5;
        doc.text(billToVatNumber, margin, y);
        y += 5;

        const billToAddress = document.getElementById('billToAddress').value.split('\n');
        billToAddress.forEach(line => {
            doc.text(line, margin, y);
            y += 5;
        });

        const billToAttention = document.getElementById('billToAttention').value;
        const billToPhone = document.getElementById('billToPhone').value;

        if (billToAttention) {
            doc.text(`Attention: ${billToAttention}`, margin, y);
            y += 5;
        }
        if (billToPhone) {
            doc.text(`Tel: ${billToPhone}`, margin, y);
            y += 5;
        }

        const billToSectionHeight = y - startingY;
        y = startingY; // Reset Y for FROM section

        // From section (right column)
        doc.setFontSize(sectionTitleSize);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('FROM', rightColumnX, y);
        y += 5;

        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');

        // Get company details
        const companyName = document.getElementById('companyName')?.value || document.getElementById('fromCompany')?.value || 'Your Company';
        doc.text(companyName, rightColumnX, y);
        y += 5;

        const regNumber = document.getElementById('companyRegNumber')?.value || document.getElementById('fromRegNumber')?.value || '';
        const vatNumber = document.getElementById('companyVatNumber')?.value || document.getElementById('fromVatNumber')?.value || '';

        if (regNumber) {
            doc.text(`Reg No: ${regNumber}`, rightColumnX, y);
            y += 5;
        }

        if (vatNumber) {
            doc.text(`VAT No: ${vatNumber}`, rightColumnX, y);
            y += 5;
        }

        const companyEmail = document.getElementById('companyEmail')?.value || document.getElementById('fromEmail')?.value || '';
        const companyPhone = document.getElementById('companyPhone')?.value || document.getElementById('fromPhone')?.value || '';

        if (companyEmail) {
            doc.text(`Email: ${companyEmail}`, rightColumnX, y);
            y += 5;
        }

        if (companyPhone) {
            doc.text(`Phone: ${companyPhone}`, rightColumnX, y);
            y += 5;
        }

        const companyAddress = document.getElementById('companyAddress')?.value || document.getElementById('fromAddress')?.value || '';
        if (companyAddress) {
            const fromAddressLines = companyAddress.split('\n');
            fromAddressLines.forEach(line => {
                if (line.trim()) {
                    doc.text(line, rightColumnX, y);
                    y += 5;
                }
            });
        }

        const fromSectionHeight = y - startingY;
        y = startingY + Math.max(fromSectionHeight, billToSectionHeight) + 10;

        // Event details if provided
        const eventName = document.getElementById('eventName').value;
        if (eventName) {
            doc.setFontSize(sectionTitleSize);
            doc.setFont(PDF_HEADING_FONT, 'bold');
            doc.text('EVENT DETAILS', margin, y);
            y += 5;
            doc.setFontSize(normalSize);
            doc.setFont(PDF_BODY_FONT, 'normal');
            doc.text(`Event: ${eventName}`, margin, y); y += 5;

            const eventDate = document.getElementById('eventDate').value;
            if (eventDate) { doc.text(`Date: ${eventDate}`, margin, y); y += 5; }

            const eventTime = document.getElementById('eventTime').value;
            if (eventTime) { doc.text(`Time: ${eventTime}`, margin, y); y += 5; }

            const eventVenue = document.getElementById('eventVenue').value;
            if (eventVenue) { doc.text(`Venue: ${eventVenue}`, margin, y); y += 5; }

            const eventCity = document.getElementById('eventCity').value;
            if (eventCity) { doc.text(`City: ${eventCity}`, margin, y); y += 5; }
            y += 5;
        }

        // Items table
        const descriptionX = margin + 5;
        const qtyX = margin + contentWidth * 0.6;
        const priceX = margin + contentWidth * 0.75;
        const amountX = margin + contentWidth - 5;

        doc.setFontSize(sectionTitleSize);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('ITEMS / PARTICULARS:', margin, y);
        y += 10;

        doc.setFontSize(8);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('Description', descriptionX, y);
        doc.text('Qty', qtyX, y, { align: 'center' });
        doc.text('Price', priceX, y, { align: 'center' });
        doc.text('Amount', amountX, y, { align: 'right' });
        y += 8;

        doc.setDrawColor(230, 230, 230);
        doc.setLineWidth(0.2);
        doc.line(margin, y - 3, margin + contentWidth, y - 3);
        y += 5;
        doc.setFont(PDF_BODY_FONT, 'normal');

        // Add items
        const items = document.querySelectorAll('.item-row');
        items.forEach((item) => {
            const description = item.querySelector('.description').value;
            const quantity = item.querySelector('.quantity').value;
            const price = parseFloat(item.querySelector('.price').value);
            doc.text(description, descriptionX, y);
            doc.text(quantity, qtyX, y, { align: 'center' });
            doc.text(formatCurrency(price), priceX, y, { align: 'center' });
            doc.text(item.querySelector('.amount').value, amountX, y, { align: 'right' });
            y += 8;
        });

        // Totals section
        y += 5;
        doc.setDrawColor(230, 230, 230);
        doc.setLineWidth(0.2);
        doc.line(margin + contentWidth * 0.6, y - 2, margin + contentWidth, y - 2);
        y += 5;

        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.text('Subtotal:', margin + contentWidth * 0.75, y);
        doc.text(document.getElementById('subtotal').value, amountX, y, { align: 'right' });
        y += 7;

        const vatRate = document.getElementById('vatRate').value;
        doc.text(`VAT (${vatRate}%):`, margin + contentWidth * 0.75, y);
        doc.text(document.getElementById('vatAmount').value, amountX, y, { align: 'right' });
        y += 7;

        const discount = parseFloat(document.getElementById('discount').value);
        if (discount > 0) {
            doc.text('Discount:', margin + contentWidth * 0.75, y);
            doc.text(`-${formatCurrency(discount)}`, amountX, y, { align: 'right' });
            y += 7;
        }

        y += 3;
        doc.setFillColor(248, 248, 248);
        doc.rect(margin + contentWidth * 0.6, y - 5, contentWidth * 0.4, 10, 'F');
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('TOTAL:', margin + contentWidth * 0.75, y);
        doc.text(document.getElementById('grandTotal').value, amountX, y, { align: 'right' });
        y += 15;

        // Bank details
        const bankName = document.getElementById('bankName').value;
        if (bankName) {
            doc.setFontSize(sectionTitleSize);
            doc.setFont(PDF_HEADING_FONT, 'bold');
            doc.text('BANK DETAILS', margin, y);
            y += 5;

            doc.setFontSize(normalSize);
            doc.setFont(PDF_BODY_FONT, 'normal');
            doc.text(`Bank: ${bankName}`, margin, y);
            y += 5;

            const accountName = document.getElementById('accountName').value;
            if (accountName) {
                doc.text(`Account Name: ${accountName}`, margin, y);
                y += 5;
            }

            const accountNumber = document.getElementById('accountNumber').value;
            if (accountNumber) {
                doc.text(`Account Number: ${accountNumber}`, margin, y);
                y += 5;
            }

            const branchCode = document.getElementById('branchCode').value;
            if (branchCode) {
                doc.text(`Branch Code: ${branchCode}`, margin, y);
                y += 5;
            }
            y += 5;
        }

        // Notes
        const notes = document.getElementById('notes').value;
        if (notes) {
            if (y > pageHeight - margin - 20) {
                doc.addPage();
                y = margin;
            }
            doc.setFontSize(sectionTitleSize);
            doc.setFont(PDF_HEADING_FONT, 'bold');
            doc.text('NOTES', margin, y);
            y += 5;

            doc.setFontSize(8);
            doc.setFont(PDF_BODY_FONT, 'normal');
            const noteLines = notes.split('\n');
            noteLines.forEach(line => {
                doc.text(line, margin, y);
                y += 4;
            });
            y += 5;
        }

        // Signature if enabled
        if (document.getElementById('includeSignature').checked) {
            if (y > pageHeight - margin - 60) {
                doc.addPage();
                y = margin;
            }
            doc.setFontSize(sectionTitleSize);
            doc.setFont(PDF_HEADING_FONT, 'bold');
            doc.text('AUTHORIZED SIGNATURE', margin, y);
            y += 10;

            const canvas = document.getElementById('signatureCanvas');
            if (canvas) {
                try {
                    const signatureImg = canvas.toDataURL('image/png');
                    doc.addImage(signatureImg, 'PNG', margin, y, 80, 30);
                    y += 35;
                } catch (e) {
                    console.error('Error adding signature to preview:', e);
                    y += 20;
                }
            } else {
                y += 20;
            }

            const signatureName = document.getElementById('signatureName').value;
            if (signatureName) {
                doc.setFontSize(normalSize);
                doc.setFont(PDF_BODY_FONT, 'normal');
                doc.text(signatureName, margin, y);
                y += 10;
            }
        }

        // Add footer
        const totalPages = doc.internal.getNumberOfPages();
        const footerText = `Created by Anesu Adrian Mupemhi For Benjamin Music Initiatives (BMI)`;

        for (let i = 1; i <= totalPages; i++) {
            doc.setPage(i);
            doc.setFontSize(8);
            doc.setFont(PDF_BODY_FONT, 'normal');
            doc.setTextColor(100, 100, 100);

            // Page number
            doc.text(`Page ${i} of ${totalPages}`, pageWidth / 2, pageHeight - 10, { align: 'center' });

            // Company footer
            doc.text(footerText, pageWidth / 2, pageHeight - 5, { align: 'center' });
        }

        console.log('✅ Invoice preview content generated successfully (NO SAVE/DOWNLOAD)');
        return y;

    } catch (error) {
        console.error('❌ Error generating invoice preview content:', error);
        throw error;
    }
}

// Function to preview Receipt without downloading
function previewReceipt(event) {
    // Prevent any form submission
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    console.log('🔍 Generating Receipt Preview...');

    // Prevent PDF generation flag from being set
    if (window.isGeneratingPDF) {
        console.log('PDF generation already in progress, skipping preview');
        return;
    }

    try {
        // Store original document mode
        const originalMode = window.documentMode;
        window.documentMode = 'receipt';

        // Generate PDF in memory (not for download)
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        // Set up PDF parameters
        const margin = 20;
        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;
        const contentWidth = pageWidth - (margin * 2);
        const normalSize = 9;
        const sectionTitleSize = 11;
        let y = margin;

        // Get logo image
        const logoImg = document.getElementById('logoPreview')?.querySelector('img');

        // Completely disable any save functionality during preview
        const originalSave = doc.save;

        doc.save = function() {
            console.log('🚫 Save operation blocked during preview mode');
            return false;
        };

        // Generate Receipt content directly (PREVIEW MODE ONLY - NO SAVE)
        generateReceiptContentForPreview(doc, margin, y, contentWidth, normalSize, sectionTitleSize, logoImg);

        // Create preview blob (NOT for download)
        const pdfOutput = doc.output('blob');

        // Restore original functions
        doc.save = originalSave;

        // Show preview modal
        showPDFPreview(pdfOutput);

        // Restore original document mode
        window.documentMode = originalMode;

        console.log('✅ Receipt Preview Generated Successfully');

    } catch (error) {
        console.error('❌ Error generating Receipt preview:', error);
        alert('Error generating preview: ' + error.message);

        // Restore original document mode on error
        if (typeof originalMode !== 'undefined') {
            window.documentMode = originalMode;
        }
    }

    // Ensure we return false to prevent any form submission
    return false;
}

// Function to preview Quotation without downloading
function previewQuotation(event) {
    // Prevent any form submission
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    console.log('🔍 Generating Quotation Preview...');

    // Prevent PDF generation flag from being set
    if (window.isGeneratingPDF) {
        console.log('PDF generation already in progress, skipping preview');
        return;
    }

    try {
        // Store original document mode
        const originalMode = window.documentMode;
        window.documentMode = 'quotation';

        // Generate PDF in memory (not for download)
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        // Set up PDF parameters
        const margin = 20;
        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;
        const contentWidth = pageWidth - (margin * 2);
        const normalSize = 9;
        const sectionTitleSize = 11;
        let y = margin;

        // Get logo image
        const logoImg = document.getElementById('logoPreview')?.querySelector('img');

        // Completely disable any save functionality during preview
        const originalSave = doc.save;

        doc.save = function() {
            console.log('🚫 Save operation blocked during preview mode');
            return false;
        };

        // Generate Quotation content directly (PREVIEW MODE ONLY - NO SAVE)
        generateQuotationContentForPreview(doc, margin, y, contentWidth, normalSize, sectionTitleSize, logoImg);

        // Create preview blob (NOT for download)
        const pdfOutput = doc.output('blob');

        // Restore original functions
        doc.save = originalSave;

        // Show preview modal
        showPDFPreview(pdfOutput);

        // Restore original document mode
        window.documentMode = originalMode;

        console.log('✅ Quotation Preview Generated Successfully');

    } catch (error) {
        console.error('❌ Error generating Quotation preview:', error);
        alert('Error generating preview: ' + error.message);

        // Restore original document mode on error
        if (typeof originalMode !== 'undefined') {
            window.documentMode = originalMode;
        }
    }

    // Ensure we return false to prevent any form submission
    return false;
}

// Function to preview Contract without downloading
function previewContract(event) {
    // Prevent any form submission
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    console.log('🔍 Generating Contract Preview...');

    // Prevent PDF generation flag from being set
    if (window.isGeneratingPDF) {
        console.log('PDF generation already in progress, skipping preview');
        return;
    }

    try {
        // Store original document mode
        const originalMode = window.documentMode;
        window.documentMode = 'contract';

        // Generate PDF in memory (not for download)
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        // Set up PDF parameters
        const margin = 20;
        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;
        const contentWidth = pageWidth - (margin * 2);
        const normalSize = 9;
        const sectionTitleSize = 11;
        let y = margin;

        // Get logo image
        const logoImg = document.getElementById('logoPreview')?.querySelector('img');

        // Completely disable any save functionality during preview
        const originalSave = doc.save;

        doc.save = function() {
            console.log('🚫 Save operation blocked during preview mode');
            return false;
        };

        // Generate Contract content directly (PREVIEW MODE ONLY - NO SAVE)
        generateContractContentForPreview(doc, margin, y, contentWidth, normalSize, sectionTitleSize, logoImg);

        // Create preview blob (NOT for download)
        const pdfOutput = doc.output('blob');

        // Restore original functions
        doc.save = originalSave;

        // Show preview modal
        showPDFPreview(pdfOutput);

        // Restore original document mode
        window.documentMode = originalMode;

        console.log('✅ Contract Preview Generated Successfully');

    } catch (error) {
        console.error('❌ Error generating Contract preview:', error);
        alert('Error generating preview: ' + error.message);

        // Restore original document mode on error
        if (typeof originalMode !== 'undefined') {
            window.documentMode = originalMode;
        }
    }

    // Ensure we return false to prevent any form submission
    return false;
}

// Function to preview Technical Rider without downloading
function previewTechnicalRider(event) {
    // Prevent any form submission
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    console.log('🔍 Generating Technical Rider Preview...');

    // Prevent PDF generation flag from being set
    if (window.isGeneratingPDF) {
        console.log('PDF generation already in progress, skipping preview');
        return;
    }

    try {
        // Store original document mode
        const originalMode = window.documentMode;
        window.documentMode = 'rider';

        // Generate PDF in memory (not for download)
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        // Set up PDF parameters
        const margin = 20;
        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;
        const contentWidth = pageWidth - (margin * 2);
        const normalSize = 9;
        const sectionTitleSize = 11;
        let y = margin;

        // Get logo image
        const logoImg = document.getElementById('logoPreview')?.querySelector('img');

        // Completely disable any save functionality during preview
        const originalSave = doc.save;

        doc.save = function() {
            console.log('🚫 Save operation blocked during preview mode');
            return false;
        };

        // Generate Technical Rider content directly (PREVIEW MODE ONLY - NO SAVE)
        generateTechnicalRiderContentForPreview(doc, margin, y, contentWidth, normalSize, sectionTitleSize, logoImg);

        // Create preview blob (NOT for download)
        const pdfOutput = doc.output('blob');

        // Restore original functions
        doc.save = originalSave;

        // Show preview modal
        showPDFPreview(pdfOutput);

        // Restore original document mode
        window.documentMode = originalMode;

        console.log('✅ Technical Rider Preview Generated Successfully');

    } catch (error) {
        console.error('❌ Error generating Technical Rider preview:', error);
        alert('Error generating preview: ' + error.message);

        // Restore original document mode on error
        if (typeof originalMode !== 'undefined') {
            window.documentMode = originalMode;
        }
    }

    // Ensure we return false to prevent any form submission
    return false;
}

// Function to generate Receipt content for preview only (NO SAVE/DOWNLOAD)
function generateReceiptContentForPreview(doc, margin, y, contentWidth, normalSize, sectionTitleSize, logoImg) {
    try {
        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;

        // Add logo and title
        if (logoImg) {
            const logoWidth = 20;
            const logoHeight = 20;
            try {
                doc.addImage(logoImg.src, 'PNG', margin, y, logoWidth, logoHeight);
            } catch (e) {
                console.error('Error adding logo to receipt preview:', e);
            }
        }

        // Add title
        doc.setFontSize(24);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('RECEIPT', pageWidth / 2, y + 10, { align: 'center' });

        // Add document details
        doc.setFontSize(10);
        doc.setFont(PDF_BODY_FONT, 'normal');
        const numberText = `Receipt #: ${document.getElementById('invoiceNumber').value}`;
        const dateText = `Date: ${document.getElementById('invoiceDate').value}`;
        const paymentMethodText = `Payment Method: ${getPaymentMethod()}`;

        doc.text(numberText, pageWidth - margin, y, { align: 'right' });
        y += 6;
        doc.text(dateText, pageWidth - margin, y, { align: 'right' });
        y += 6;
        doc.text(paymentMethodText, pageWidth - margin, y, { align: 'right' });
        y += 25;

        // Bill To and From sections (similar to invoice but for receipt)
        const columnWidth = contentWidth / 2 - 5;
        const rightColumnX = margin + columnWidth + 10;
        const startingY = y;

        // Bill To section (left column)
        doc.setFontSize(sectionTitleSize);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('RECEIVED FROM', margin, y);
        y += 5;

        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.text(document.getElementById('billToCompany').value, margin, y);
        y += 5;

        const billToRegNumber = `Reg No: ${document.getElementById('billToRegNumber').value}`;
        const billToVatNumber = `VAT No: ${document.getElementById('billToVatNumber').value}`;
        doc.text(billToRegNumber, margin, y);
        y += 5;
        doc.text(billToVatNumber, margin, y);
        y += 5;

        const billToAddress = document.getElementById('billToAddress').value.split('\n');
        billToAddress.forEach(line => {
            doc.text(line, margin, y);
            y += 5;
        });

        const billToSectionHeight = y - startingY;
        y = startingY; // Reset Y for FROM section

        // From section (right column)
        doc.setFontSize(sectionTitleSize);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('RECEIVED BY', rightColumnX, y);
        y += 5;

        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');

        // Get company details
        const companyName = document.getElementById('companyName')?.value || document.getElementById('fromCompany')?.value || 'Your Company';
        doc.text(companyName, rightColumnX, y);
        y += 5;

        const regNumber = document.getElementById('companyRegNumber')?.value || document.getElementById('fromRegNumber')?.value || '';
        const vatNumber = document.getElementById('companyVatNumber')?.value || document.getElementById('fromVatNumber')?.value || '';

        if (regNumber) {
            doc.text(`Reg No: ${regNumber}`, rightColumnX, y);
            y += 5;
        }

        if (vatNumber) {
            doc.text(`VAT No: ${vatNumber}`, rightColumnX, y);
            y += 5;
        }

        const fromSectionHeight = y - startingY;
        y = startingY + Math.max(fromSectionHeight, billToSectionHeight) + 10;

        // Items table
        const descriptionX = margin + 5;
        const qtyX = margin + contentWidth * 0.6;
        const priceX = margin + contentWidth * 0.75;
        const amountX = margin + contentWidth - 5;

        doc.setFontSize(sectionTitleSize);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('PAYMENT RECEIVED FOR:', margin, y);
        y += 10;

        doc.setFontSize(8);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('Description', descriptionX, y);
        doc.text('Qty', qtyX, y, { align: 'center' });
        doc.text('Price', priceX, y, { align: 'center' });
        doc.text('Amount', amountX, y, { align: 'right' });
        y += 8;

        doc.setDrawColor(230, 230, 230);
        doc.setLineWidth(0.2);
        doc.line(margin, y - 3, margin + contentWidth, y - 3);
        y += 5;
        doc.setFont(PDF_BODY_FONT, 'normal');

        // Add items
        const items = document.querySelectorAll('.item-row');
        items.forEach((item) => {
            const description = item.querySelector('.description').value;
            const quantity = item.querySelector('.quantity').value;
            const price = parseFloat(item.querySelector('.price').value);
            doc.text(description, descriptionX, y);
            doc.text(quantity, qtyX, y, { align: 'center' });
            doc.text(formatCurrency(price), priceX, y, { align: 'center' });
            doc.text(item.querySelector('.amount').value, amountX, y, { align: 'right' });
            y += 8;
        });

        // Totals section
        y += 5;
        doc.setDrawColor(230, 230, 230);
        doc.setLineWidth(0.2);
        doc.line(margin + contentWidth * 0.6, y - 2, margin + contentWidth, y - 2);
        y += 5;

        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.text('Subtotal:', margin + contentWidth * 0.75, y);
        doc.text(document.getElementById('subtotal').value, amountX, y, { align: 'right' });
        y += 7;

        const vatRate = document.getElementById('vatRate').value;
        doc.text(`VAT (${vatRate}%):`, margin + contentWidth * 0.75, y);
        doc.text(document.getElementById('vatAmount').value, amountX, y, { align: 'right' });
        y += 7;

        y += 3;
        doc.setFillColor(50, 180, 50); // Green background for receipt
        doc.rect(margin + contentWidth * 0.6, y - 5, contentWidth * 0.4, 10, 'F');
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.setTextColor(255, 255, 255); // White text
        doc.text('TOTAL RECEIVED:', margin + contentWidth * 0.75, y);
        doc.text(document.getElementById('grandTotal').value, amountX, y, { align: 'right' });
        doc.setTextColor(0, 0, 0); // Reset to black
        y += 15;

        // Add "PAID" watermark
        doc.setTextColor(50, 180, 50);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.setFontSize(60);
        doc.saveGraphicsState();
        doc.setGState(new doc.GState({ opacity: 0.3 }));
        doc.text('PAID', pageWidth / 2, pageHeight / 2, {
            align: 'center',
            angle: 45
        });
        doc.restoreGraphicsState();
        doc.setTextColor(0, 0, 0);

        // Footer
        const footerText = `Created by Anesu Adrian Mupemhi For Benjamin Music Initiatives (BMI)`;
        doc.setFontSize(8);
        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.setTextColor(100, 100, 100);
        doc.text('Page 1 of 1', pageWidth / 2, pageHeight - 10, { align: 'center' });
        doc.text(footerText, pageWidth / 2, pageHeight - 5, { align: 'center' });

        console.log('✅ Receipt preview content generated successfully (NO SAVE/DOWNLOAD)');
        return y;

    } catch (error) {
        console.error('❌ Error generating receipt preview content:', error);
        throw error;
    }
}

// Function to generate Quotation content for preview only (NO SAVE/DOWNLOAD)
function generateQuotationContentForPreview(doc, margin, y, contentWidth, normalSize, sectionTitleSize, logoImg) {
    try {
        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;

        // Add logo and title
        if (logoImg) {
            const logoWidth = 20;
            const logoHeight = 20;
            try {
                doc.addImage(logoImg.src, 'PNG', margin, y, logoWidth, logoHeight);
            } catch (e) {
                console.error('Error adding logo to quotation preview:', e);
            }
        }

        // Add title
        doc.setFontSize(24);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('QUOTATION', pageWidth / 2, y + 10, { align: 'center' });

        // Add document details
        doc.setFontSize(10);
        doc.setFont(PDF_BODY_FONT, 'normal');
        const numberText = `Quotation #: ${document.getElementById('invoiceNumber').value}`;
        const dateText = `Date: ${document.getElementById('invoiceDate').value}`;
        const validUntilText = `Valid Until: ${getValidUntilDate()}`;

        doc.text(numberText, pageWidth - margin, y, { align: 'right' });
        y += 6;
        doc.text(dateText, pageWidth - margin, y, { align: 'right' });
        y += 6;
        doc.text(validUntilText, pageWidth - margin, y, { align: 'right' });
        y += 25;

        // Bill To and From sections
        const columnWidth = contentWidth / 2 - 5;
        const rightColumnX = margin + columnWidth + 10;
        const startingY = y;

        // Quotation To section (left column)
        doc.setFontSize(sectionTitleSize);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('QUOTATION TO', margin, y);
        y += 5;

        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.text(document.getElementById('billToCompany').value, margin, y);
        y += 5;

        const billToRegNumber = `Reg No: ${document.getElementById('billToRegNumber').value}`;
        const billToVatNumber = `VAT No: ${document.getElementById('billToVatNumber').value}`;
        doc.text(billToRegNumber, margin, y);
        y += 5;
        doc.text(billToVatNumber, margin, y);
        y += 5;

        const billToAddress = document.getElementById('billToAddress').value.split('\n');
        billToAddress.forEach(line => {
            doc.text(line, margin, y);
            y += 5;
        });

        const billToSectionHeight = y - startingY;
        y = startingY; // Reset Y for FROM section

        // From section (right column)
        doc.setFontSize(sectionTitleSize);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('FROM', rightColumnX, y);
        y += 5;

        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');

        // Get company details
        const companyName = document.getElementById('companyName')?.value || document.getElementById('fromCompany')?.value || 'Your Company';
        doc.text(companyName, rightColumnX, y);
        y += 5;

        const regNumber = document.getElementById('companyRegNumber')?.value || document.getElementById('fromRegNumber')?.value || '';
        const vatNumber = document.getElementById('companyVatNumber')?.value || document.getElementById('fromVatNumber')?.value || '';

        if (regNumber) {
            doc.text(`Reg No: ${regNumber}`, rightColumnX, y);
            y += 5;
        }

        if (vatNumber) {
            doc.text(`VAT No: ${vatNumber}`, rightColumnX, y);
            y += 5;
        }

        const fromSectionHeight = y - startingY;
        y = startingY + Math.max(fromSectionHeight, billToSectionHeight) + 10;

        // Items table
        const descriptionX = margin + 5;
        const qtyX = margin + contentWidth * 0.6;
        const priceX = margin + contentWidth * 0.75;
        const amountX = margin + contentWidth - 5;

        doc.setFontSize(sectionTitleSize);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('QUOTED ITEMS / SERVICES:', margin, y);
        y += 10;

        doc.setFontSize(8);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('Description', descriptionX, y);
        doc.text('Qty', qtyX, y, { align: 'center' });
        doc.text('Price', priceX, y, { align: 'center' });
        doc.text('Amount', amountX, y, { align: 'right' });
        y += 8;

        doc.setDrawColor(230, 230, 230);
        doc.setLineWidth(0.2);
        doc.line(margin, y - 3, margin + contentWidth, y - 3);
        y += 5;
        doc.setFont(PDF_BODY_FONT, 'normal');

        // Add items
        const items = document.querySelectorAll('.item-row');
        items.forEach((item) => {
            const description = item.querySelector('.description').value;
            const quantity = item.querySelector('.quantity').value;
            const price = parseFloat(item.querySelector('.price').value);
            doc.text(description, descriptionX, y);
            doc.text(quantity, qtyX, y, { align: 'center' });
            doc.text(formatCurrency(price), priceX, y, { align: 'center' });
            doc.text(item.querySelector('.amount').value, amountX, y, { align: 'right' });
            y += 8;
        });

        // Totals section
        y += 5;
        doc.setDrawColor(230, 230, 230);
        doc.setLineWidth(0.2);
        doc.line(margin + contentWidth * 0.6, y - 2, margin + contentWidth, y - 2);
        y += 5;

        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.text('Subtotal:', margin + contentWidth * 0.75, y);
        doc.text(document.getElementById('subtotal').value, amountX, y, { align: 'right' });
        y += 7;

        const vatRate = document.getElementById('vatRate').value;
        doc.text(`VAT (${vatRate}%):`, margin + contentWidth * 0.75, y);
        doc.text(document.getElementById('vatAmount').value, amountX, y, { align: 'right' });
        y += 7;

        y += 3;
        doc.setFillColor(248, 248, 248);
        doc.rect(margin + contentWidth * 0.6, y - 5, contentWidth * 0.4, 10, 'F');
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('TOTAL QUOTED:', margin + contentWidth * 0.75, y);
        doc.text(document.getElementById('grandTotal').value, amountX, y, { align: 'right' });
        y += 15;

        // Add validity stamp
        const watermarkText = document.getElementById('watermarkText').value || 'VALID FOR 30 DAYS';
        const stampWidth = 130;
        const stampHeight = 30;
        const stampX = margin + contentWidth - stampWidth;
        const stampY = y - 10;

        doc.setFillColor(245, 240, 255);
        doc.setDrawColor(100, 50, 200);
        doc.setLineWidth(0.75);
        doc.rect(stampX, stampY, stampWidth, stampHeight, 'FD');
        doc.setTextColor(100, 50, 200);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.setFontSize(14);
        doc.text(watermarkText, stampX + stampWidth/2, stampY + stampHeight/2 + 5, { align: 'center' });
        doc.setTextColor(0, 0, 0);

        // Footer
        const footerText = `Created by Anesu Adrian Mupemhi For Benjamin Music Initiatives (BMI)`;
        doc.setFontSize(8);
        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.setTextColor(100, 100, 100);
        doc.text('Page 1 of 1', pageWidth / 2, pageHeight - 10, { align: 'center' });
        doc.text(footerText, pageWidth / 2, pageHeight - 5, { align: 'center' });

        console.log('✅ Quotation preview content generated successfully (NO SAVE/DOWNLOAD)');
        return y;

    } catch (error) {
        console.error('❌ Error generating quotation preview content:', error);
        throw error;
    }
}

// Function to generate Contract content for preview only (NO SAVE/DOWNLOAD)
function generateContractContentForPreview(doc, margin, y, contentWidth, normalSize, sectionTitleSize, logoImg) {
    try {
        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;

        // Add logo and title
        if (logoImg) {
            const logoWidth = 20;
            const logoHeight = 20;
            try {
                doc.addImage(logoImg.src, 'PNG', margin, y, logoWidth, logoHeight);
            } catch (e) {
                console.error('Error adding logo to contract preview:', e);
            }
        }

        // Add title
        doc.setFontSize(24);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('SERVICE AGREEMENT', pageWidth / 2, y + 10, { align: 'center' });

        // Add document details
        doc.setFontSize(10);
        doc.setFont(PDF_BODY_FONT, 'normal');
        const numberText = `Contract #: ${document.getElementById('invoiceNumber').value}`;
        const dateText = `Effective Date: ${document.getElementById('invoiceDate').value}`;

        doc.text(numberText, pageWidth - margin, y, { align: 'right' });
        y += 6;
        doc.text(dateText, pageWidth - margin, y, { align: 'right' });
        y += 25;

        // Parties section
        doc.setFontSize(sectionTitleSize);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('PARTIES TO THIS AGREEMENT', margin, y);
        y += 10;

        // Party 1 (Client)
        doc.setFontSize(normalSize);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('CLIENT:', margin, y);
        y += 5;

        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.text(document.getElementById('billToCompany').value, margin, y);
        y += 5;

        const billToAddress = document.getElementById('billToAddress').value.split('\n');
        billToAddress.forEach(line => {
            doc.text(line, margin, y);
            y += 5;
        });
        y += 5;

        // Party 2 (Service Provider)
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('SERVICE PROVIDER:', margin, y);
        y += 5;

        doc.setFont(PDF_BODY_FONT, 'normal');
        const companyName = document.getElementById('companyName')?.value || document.getElementById('fromCompany')?.value || 'Your Company';
        doc.text(companyName, margin, y);
        y += 5;

        const companyAddress = document.getElementById('companyAddress')?.value || document.getElementById('fromAddress')?.value || '';
        if (companyAddress) {
            const fromAddressLines = companyAddress.split('\n');
            fromAddressLines.forEach(line => {
                if (line.trim()) {
                    doc.text(line, margin, y);
                    y += 5;
                }
            });
        }
        y += 10;

        // Contract terms
        doc.setFontSize(sectionTitleSize);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('TERMS AND CONDITIONS', margin, y);
        y += 10;

        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');

        // Sample contract clauses
        const contractClauses = [
            '1. SCOPE OF SERVICES',
            'The Service Provider agrees to provide the services as detailed in the attached schedule.',
            '',
            '2. PAYMENT TERMS',
            'Payment shall be made according to the schedule outlined in this agreement.',
            '',
            '3. DURATION',
            'This agreement shall commence on the effective date and continue as specified.',
            '',
            '4. TERMINATION',
            'Either party may terminate this agreement with written notice as specified herein.',
            '',
            '5. CONFIDENTIALITY',
            'Both parties agree to maintain confidentiality of proprietary information.',
            '',
            '6. GOVERNING LAW',
            'This agreement shall be governed by the laws of South Africa.'
        ];

        contractClauses.forEach(clause => {
            if (clause === '') {
                y += 3;
            } else if (clause.match(/^\d+\./)) {
                doc.setFont(PDF_HEADING_FONT, 'bold');
                doc.text(clause, margin, y);
                y += 6;
            } else {
                doc.setFont(PDF_BODY_FONT, 'normal');
                doc.text(clause, margin + 5, y);
                y += 5;
            }
        });

        y += 15;

        // Signature section
        doc.setFontSize(sectionTitleSize);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('SIGNATURES', margin, y);
        y += 15;

        // Client signature
        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.text('CLIENT:', margin, y);
        doc.text('SERVICE PROVIDER:', margin + contentWidth/2, y);
        y += 20;

        doc.text('_________________________', margin, y);
        doc.text('_________________________', margin + contentWidth/2, y);
        y += 5;

        doc.text('Signature', margin, y);
        doc.text('Signature', margin + contentWidth/2, y);
        y += 10;

        doc.text('_________________________', margin, y);
        doc.text('_________________________', margin + contentWidth/2, y);
        y += 5;

        doc.text('Date', margin, y);
        doc.text('Date', margin + contentWidth/2, y);

        // Footer
        const footerText = `Created by Anesu Adrian Mupemhi For Benjamin Music Initiatives (BMI)`;
        doc.setFontSize(8);
        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.setTextColor(100, 100, 100);
        doc.text('Page 1 of 1', pageWidth / 2, pageHeight - 10, { align: 'center' });
        doc.text(footerText, pageWidth / 2, pageHeight - 5, { align: 'center' });

        console.log('✅ Contract preview content generated successfully (NO SAVE/DOWNLOAD)');
        return y;

    } catch (error) {
        console.error('❌ Error generating contract preview content:', error);
        throw error;
    }
}

// Function to generate Technical Rider content for preview only (NO SAVE/DOWNLOAD)
function generateTechnicalRiderContentForPreview(doc, margin, y, contentWidth, normalSize, sectionTitleSize, logoImg) {
    try {
        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;

        // Add logo and title
        if (logoImg) {
            const logoWidth = 20;
            const logoHeight = 20;
            try {
                doc.addImage(logoImg.src, 'PNG', margin, y, logoWidth, logoHeight);
            } catch (e) {
                console.error('Error adding logo to technical rider preview:', e);
            }
        }

        // Add title
        setProfessionalHeadingFont(doc, 'bold', 24);
        doc.text('TECHNICAL RIDER', pageWidth / 2, y + 10, { align: 'center' });

        // Add document details
        setProfessionalBodyFont(doc, 'normal', 10);
        const numberText = `Rider ID: ${document.getElementById('invoiceNumber').value}`;
        const dateText = `Date: ${document.getElementById('invoiceDate').value}`;

        doc.text(numberText, pageWidth - margin, y, { align: 'right' });
        y += 6;
        doc.text(dateText, pageWidth - margin, y, { align: 'right' });
        y += 25;

        // Artist/Band information
        doc.setFontSize(sectionTitleSize);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('ARTIST/BAND INFORMATION', margin, y);
        y += 10;

        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');
        const companyName = document.getElementById('companyName')?.value || document.getElementById('fromCompany')?.value || 'Your Company';
        doc.text(`Artist/Band: ${companyName}`, margin, y);
        y += 5;

        const eventName = document.getElementById('eventName').value;
        if (eventName) {
            doc.text(`Event: ${eventName}`, margin, y);
            y += 5;
        }

        const eventDate = document.getElementById('eventDate').value;
        if (eventDate) {
            doc.text(`Performance Date: ${eventDate}`, margin, y);
            y += 5;
        }

        const eventVenue = document.getElementById('eventVenue').value;
        if (eventVenue) {
            doc.text(`Venue: ${eventVenue}`, margin, y);
            y += 5;
        }
        y += 10;

        // Technical Requirements sections
        const sections = [
            {
                title: '🔊 AUDIO REQUIREMENTS',
                items: [
                    'PA System: 15kW Line Array (Standard)',
                    'Mixing Console: Yamaha CL5 (32+ channels)',
                    'In-Ear Monitor System: 6x IEM Transmitters',
                    'Microphones: 8x Dynamic, 4x Condenser',
                    'DI Boxes: 6x Active DI'
                ]
            },
            {
                title: '🏗️ STAGE REQUIREMENTS',
                items: [
                    'Stage Dimensions: 10m x 8m (minimum)',
                    'Drum Riser: 3m x 3m x 0.4m',
                    'Power Supply: 63A 3-Phase CEE',
                    'Stage Lighting: Basic wash + spot',
                    'Backdrop: Black drape preferred'
                ]
            },
            {
                title: '🎸 BACKLINE REQUIREMENTS',
                items: [
                    'Guitar Amplification: 2x Marshall JCM800 + 4x12 Cabs',
                    'Bass Amplification: Ampeg SVT-CL + 8x10 Cab',
                    'Drum Kit: 5-piece kit with hardware',
                    'Keyboards: 88-key weighted action',
                    'Monitors: 4x wedge monitors minimum'
                ]
            },
            {
                title: '👥 PERSONNEL REQUIREMENTS',
                items: [
                    'FOH Engineer (experienced with digital consoles)',
                    'Monitor Engineer',
                    'Lighting Technician',
                    'Stage Manager',
                    '4x Stage Hands for load-in/out'
                ]
            }
        ];

        sections.forEach(section => {
            // Check if we need a new page
            if (y > pageHeight - 60) {
                doc.addPage();
                y = margin;
            }

            doc.setFontSize(sectionTitleSize);
            doc.setFont(PDF_HEADING_FONT, 'bold');
            doc.text(section.title, margin, y);
            y += 8;

            doc.setFontSize(normalSize);
            doc.setFont(PDF_BODY_FONT, 'normal');

            section.items.forEach(item => {
                doc.text(`• ${item}`, margin + 5, y);
                y += 5;
            });
            y += 8;
        });

        // Additional requirements
        doc.setFontSize(sectionTitleSize);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('📝 ADDITIONAL REQUIREMENTS', margin, y);
        y += 8;

        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');
        const additionalReqs = [
            '• Load-in time: 4 hours before show',
            '• Soundcheck: 2 hours before doors',
            '• Dressing room with refreshments',
            '• Secure parking for equipment truck',
            '• 24-hour advance contact required'
        ];

        additionalReqs.forEach(req => {
            doc.text(req, margin, y);
            y += 5;
        });

        y += 10;

        // Contact information
        doc.setFontSize(sectionTitleSize);
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('📞 TECHNICAL CONTACT', margin, y);
        y += 8;

        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');
        const companyPhone = document.getElementById('companyPhone')?.value || document.getElementById('fromPhone')?.value || '';
        const companyEmail = document.getElementById('companyEmail')?.value || document.getElementById('fromEmail')?.value || '';

        if (companyPhone) {
            doc.text(`Phone: ${companyPhone}`, margin, y);
            y += 5;
        }
        if (companyEmail) {
            doc.text(`Email: ${companyEmail}`, margin, y);
            y += 5;
        }

        // Footer
        const totalPages = doc.internal.getNumberOfPages();
        const footerText = `Created by Anesu Adrian Mupemhi For Benjamin Music Initiatives (BMI)`;

        for (let i = 1; i <= totalPages; i++) {
            doc.setPage(i);
            doc.setFontSize(8);
            doc.setFont(PDF_BODY_FONT, 'normal');
            doc.setTextColor(100, 100, 100);
            doc.text(`Page ${i} of ${totalPages}`, pageWidth / 2, pageHeight - 10, { align: 'center' });
            doc.text(footerText, pageWidth / 2, pageHeight - 5, { align: 'center' });
        }

        console.log('✅ Technical Rider preview content generated successfully (NO SAVE/DOWNLOAD)');
        return y;

    } catch (error) {
        console.error('❌ Error generating technical rider preview content:', error);
        throw error;
    }
}

// Add form submission handler
document.getElementById('invoiceForm').addEventListener('submit', function(e) {
    e.preventDefault();
    if (isGeneratingPDF) { console.log('PDF generation already in progress'); return; }
    isGeneratingPDF = true;
    const submitButton = document.getElementById('generatePDF');
    const originalButtonText = submitButton.innerHTML;
    submitButton.innerHTML = '<span>Generating...</span>';
    submitButton.disabled = true;
    try {
        createInvoicePDF();
    } catch (error) {
        console.error('Error generating invoice:', error);
        alert('An error occurred while generating the invoice. Please try again.');
    } finally {
        submitButton.disabled = false;
        submitButton.innerHTML = originalButtonText;
        isGeneratingPDF = false;
    }
});

function formatCurrency(number) { return `${currencySymbol}${number.toFixed(2)}`; }
function getPaymentMethod() { return "Electronic Transfer"; }
function getValidUntilDate() {
    const invoiceDate = new Date(document.getElementById('invoiceDate').value);
    const validUntil = new Date(invoiceDate);
    validUntil.setDate(validUntil.getDate() + 30);
    return validUntil.toISOString().split('T')[0];
}
function calculateAmount(quantity, price) { return quantity * price; }

function calculateTotals() {
    const items = document.querySelectorAll('.item-row');
    let subtotal = 0;
    items.forEach(item => {
        const quantity = parseFloat(item.querySelector('.quantity').value) || 0;
        const price = parseFloat(item.querySelector('.price').value) || 0;
        const amount = calculateAmount(quantity, price);
        item.querySelector('.amount').value = formatCurrency(amount);
        subtotal += amount;
    });
    const vatRate = parseFloat(document.getElementById('vatRate').value) || 0;
    const discount = parseFloat(document.getElementById('discount').value) || 0;
    const vatAmount = (subtotal * vatRate) / 100;
    const grandTotal = subtotal + vatAmount - discount;
    document.getElementById('subtotal').value = formatCurrency(subtotal);
    document.getElementById('vatAmount').value = formatCurrency(vatAmount);
    document.getElementById('grandTotal').value = formatCurrency(grandTotal);
}

document.getElementById('addItem').addEventListener('click', () => {
    const container = document.getElementById('itemsContainer');
    const index = container.children.length;
    const itemRow = document.createElement('div');
    itemRow.className = 'item-row';
    itemRow.innerHTML = `
        <div class="form-group">
            <label for="description${index}">Description:</label>
            <input type="text" class="description" id="description${index}" required>
        </div>
        <div class="form-group">
            <label for="quantity${index}">Quantity:</label>
            <input type="number" class="quantity" id="quantity${index}" min="1" value="1" required>
        </div>
        <div class="form-group">
            <label for="price${index}">Price:</label>
            <input type="number" class="price" id="price${index}" step="0.01" required>
        </div>
        <div class="form-group">
            <label for="amount${index}">Amount:</label>
            <input type="text" class="amount" id="amount${index}" readonly>
        </div>
        <div class="form-group">
            <button type="button" class="delete-item-btn" onclick="deleteItem(this)" title="Delete this item">
                <span>🗑️</span>
            </button>
        </div>
    `;
    container.appendChild(itemRow);
    const quantityInput = itemRow.querySelector('.quantity');
    const priceInput = itemRow.querySelector('.price');
    [quantityInput, priceInput].forEach(input => {
        input.addEventListener('input', calculateTotals);
    });
    calculateTotals();
});

// Function to delete an item row
function deleteItem(button) {
    const itemRow = button.closest('.item-row');
    const container = document.getElementById('itemsContainer');

    // Don't allow deletion if it's the last item
    if (container.children.length <= 1) {
        alert('You must have at least one item. Clear the fields instead of deleting.');
        return;
    }

    // Add fade out animation
    itemRow.style.transition = 'all 0.3s ease';
    itemRow.style.opacity = '0';
    itemRow.style.transform = 'translateX(-100%)';

    setTimeout(() => {
        itemRow.remove();
        calculateTotals();
        updateItemIndices();
    }, 300);
}

// Function to update item indices after deletion
function updateItemIndices() {
    const container = document.getElementById('itemsContainer');
    const itemRows = container.querySelectorAll('.item-row');

    itemRows.forEach((row, index) => {
        const inputs = row.querySelectorAll('input');
        const labels = row.querySelectorAll('label');

        inputs.forEach(input => {
            const oldId = input.id;
            const fieldType = oldId.replace(/\d+$/, ''); // Remove number at end
            input.id = fieldType + index;
        });

        labels.forEach(label => {
            const oldFor = label.getAttribute('for');
            if (oldFor) {
                const fieldType = oldFor.replace(/\d+$/, ''); // Remove number at end
                label.setAttribute('for', fieldType + index);
            }
        });
    });
}

// Function to add delete buttons to existing items that don't have them
function addDeleteButtonsToExistingItems() {
    const container = document.getElementById('itemsContainer');
    const itemRows = container.querySelectorAll('.item-row');

    itemRows.forEach(row => {
        // Check if this row already has a delete button
        if (!row.querySelector('.delete-item-btn')) {
            const deleteButtonGroup = document.createElement('div');
            deleteButtonGroup.className = 'form-group';
            deleteButtonGroup.innerHTML = `
                <button type="button" class="delete-item-btn" onclick="deleteItem(this)" title="Delete this item">
                    <span>🗑️</span>
                </button>
            `;
            row.appendChild(deleteButtonGroup);
        }
    });
}

function updateDueDate() {
    const invoiceDate = new Date(document.getElementById('invoiceDate').value);
    const paymentTermsSelect = document.getElementById('paymentTerms');
    const customPaymentTermsGroup = document.getElementById('customPaymentTermsGroup');
    const customPaymentTermsInput = document.getElementById('customPaymentTerms');

    let terms;

    // Handle custom payment terms
    if (paymentTermsSelect.value === 'custom') {
        customPaymentTermsGroup.style.display = 'block';
        terms = parseInt(customPaymentTermsInput.value) || 0;
    } else {
        customPaymentTermsGroup.style.display = 'none';
        terms = parseInt(paymentTermsSelect.value) || 0;
    }

    if (terms > 0) {
        const dueDate = new Date(invoiceDate);
        dueDate.setDate(dueDate.getDate() + terms);
        document.getElementById('dueDate').value = dueDate.toISOString().split('T')[0];
    } else if (paymentTermsSelect.value === 'immediate') {
        document.getElementById('dueDate').value = invoiceDate.toISOString().split('T')[0];
    } else {
        document.getElementById('dueDate').value = invoiceDate.toISOString().split('T')[0];
    }
}

// Initialize payment terms handlers
function initializePaymentTermsHandler() {
    const paymentTermsSelect = document.getElementById('paymentTerms');
    const customPaymentTermsInput = document.getElementById('customPaymentTerms');

    if (paymentTermsSelect) {
        paymentTermsSelect.addEventListener('change', updateDueDate);
    }

    if (customPaymentTermsInput) {
        customPaymentTermsInput.addEventListener('input', updateDueDate);
    }

    document.getElementById('invoiceDate').addEventListener('change', updateDueDate);
}

// Function to get human-readable payment terms text
function getPaymentTermsText() {
    const paymentTermsSelect = document.getElementById('paymentTerms');
    const customPaymentTermsInput = document.getElementById('customPaymentTerms');

    if (paymentTermsSelect.value === 'immediate') {
        return 'Payment Due Immediately';
    } else if (paymentTermsSelect.value === 'custom') {
        const customDays = parseInt(customPaymentTermsInput.value) || 0;
        if (customDays === 1) {
            return '1 Day Payment Terms';
        } else {
            return `${customDays} Days Payment Terms`;
        }
    } else {
        const days = parseInt(paymentTermsSelect.value) || 0;
        if (days === 1) {
            return '1 Day Payment Terms';
        } else {
            return `${days} Days Payment Terms`;
        }
    }
}

document.querySelectorAll('.quantity, .price, #vatRate, #discount').forEach(input => {
    input.addEventListener('input', calculateTotals);
});

document.getElementById('logoUpload').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(event) {
            const img = new Image();
            img.onload = function() {
                logoImage = img;
                const preview = document.getElementById('logoPreview');
                preview.innerHTML = '';
                const previewImg = document.createElement('img');
                previewImg.src = event.target.result;
                preview.appendChild(previewImg);
                const shape = document.getElementById('logoShape').value;
                preview.className = 'logo-preview ' + shape;
            };
            img.src = event.target.result;
        };
        reader.readAsDataURL(file);
    }
});

document.getElementById('logoShape').addEventListener('change', function(e) {
    const preview = document.getElementById('logoPreview');
    preview.className = 'logo-preview ' + e.target.value;
});

function saveCompanyProfile() {
    const companyProfile = {
        company: document.getElementById('fromCompany').value,
        regNumber: document.getElementById('fromRegNumber').value,
        vatNumber: document.getElementById('fromVatNumber').value,
        email: document.getElementById('fromEmail').value,
        address: document.getElementById('fromAddress').value,
        contact: document.getElementById('fromContact').value,
        phone: document.getElementById('fromPhone').value
    };
    localStorage.setItem('companyProfile', JSON.stringify(companyProfile));
    alert('Company profile saved successfully!');
}

function loadCompanyProfile() {
    const savedProfile = localStorage.getItem('companyProfile');
    if (savedProfile) {
        const profile = JSON.parse(savedProfile);
        document.getElementById('fromCompany').value = profile.company || '';
        document.getElementById('fromRegNumber').value = profile.regNumber || '';
        document.getElementById('fromVatNumber').value = profile.vatNumber || '';
        document.getElementById('fromEmail').value = profile.email || '';
        document.getElementById('fromAddress').value = profile.address || '';
        document.getElementById('fromContact').value = profile.contact || '';
        document.getElementById('fromPhone').value = profile.phone || '';
    }
}

function saveClientProfile() {
    const clientName = document.getElementById('clientProfileName').value;
    if (!clientName) { alert('Please enter a name for this client profile'); return; }
    const clientProfile = {
        company: document.getElementById('billToCompany').value,
        regNumber: document.getElementById('billToRegNumber').value,
        vatNumber: document.getElementById('billToVatNumber').value,
        address: document.getElementById('billToAddress').value,
        attention: document.getElementById('billToAttention').value,
        phone: document.getElementById('billToPhone').value
    };
    let clientProfiles = JSON.parse(localStorage.getItem('clientProfiles') || '{}');
    clientProfiles[clientName] = clientProfile;
    localStorage.setItem('clientProfiles', JSON.stringify(clientProfiles));
    alert(`Client profile "${clientName}" saved successfully!`);
    updateClientProfileDropdown();
}

function loadClientProfile(clientName) {
    console.log('📋 Loading client profile:', clientName);
    const savedProfiles = localStorage.getItem('clientProfiles');
    if (savedProfiles) {
        const profiles = JSON.parse(savedProfiles);
        const profile = profiles[clientName];
        if (profile) {
            console.log('✅ Profile found:', profile);

            // Map to the correct "Bill To" section field IDs from the HTML
            const billToCompanyField = document.getElementById('billToCompany');
            const billToRegNumberField = document.getElementById('billToRegNumber');
            const billToVatNumberField = document.getElementById('billToVatNumber');
            const billToAddressField = document.getElementById('billToAddress');
            const billToAttentionField = document.getElementById('billToAttention');
            const billToPhoneField = document.getElementById('billToPhone');
            const billToEmailField = document.getElementById('billToEmail');

            // Populate all the Bill To fields
            if (billToCompanyField) {
                billToCompanyField.value = profile.company || '';
                console.log('✅ Company field populated:', profile.company);
            }

            if (billToRegNumberField) {
                billToRegNumberField.value = profile.regNumber || '';
                console.log('✅ Reg number field populated:', profile.regNumber);
            }

            if (billToVatNumberField) {
                billToVatNumberField.value = profile.vatNumber || '';
                console.log('✅ VAT number field populated:', profile.vatNumber);
            }

            if (billToAddressField) {
                billToAddressField.value = profile.address || '';
                console.log('✅ Address field populated:', profile.address);
            }

            if (billToAttentionField) {
                billToAttentionField.value = profile.attention || '';
                console.log('✅ Attention field populated:', profile.attention);
            }

            if (billToPhoneField) {
                billToPhoneField.value = profile.phone || '';
                console.log('✅ Phone field populated:', profile.phone);
            }

            if (billToEmailField) {
                billToEmailField.value = profile.email || '';
                console.log('✅ Email field populated:', profile.email);
            }

            // Update profile name field for saving reference
            const profileNameField = document.getElementById('clientProfileName');
            if (profileNameField) {
                profileNameField.value = clientName;
                console.log('✅ Profile name field updated:', clientName);
            }

            // Also populate Artist Agreement client fields if they exist
            populateArtistAgreementClientFields(profile);

            console.log('✅ All client profile fields loaded successfully');
        } else {
            console.log('❌ Profile not found for:', clientName);
        }
    } else {
        console.log('❌ No saved profiles found');
    }
}

// Function to populate Artist Agreement client fields
function populateArtistAgreementClientFields(profile) {
    // Artist Agreement specific client fields
    const clientDetailsField = document.getElementById('clientDetails1412');
    const clientPhysicalAddressField = document.getElementById('clientPhysicalAddress');
    const clientEmailField = document.getElementById('clientEmail');

    if (clientDetailsField && profile.company) {
        // Extract attention name for client details
        const attentionName = profile.attention || 'Contact Person';
        clientDetailsField.value = `Client: ${attentionName}    Company: ${profile.company}`;
        console.log('✅ Artist Agreement client details populated');
    }

    if (clientPhysicalAddressField && profile.address) {
        clientPhysicalAddressField.value = profile.address;
        console.log('✅ Artist Agreement client address populated');
    }

    if (clientEmailField && profile.email) {
        clientEmailField.value = profile.email;
        console.log('✅ Artist Agreement client email populated');
    }
}

function updateClientProfileDropdown() {
    console.log('🔄 Updating client profile dropdown...');
    const dropdown = document.getElementById('clientProfileDropdown');
    const artistAgreementDropdown = document.getElementById('artistAgreementClientDropdown');

    if (!dropdown && !artistAgreementDropdown) {
        console.log('❌ No client dropdowns found');
        return;
    }

    const savedProfiles = localStorage.getItem('clientProfiles');
    console.log('📋 Saved profiles:', savedProfiles);

    if (savedProfiles) {
        const profiles = JSON.parse(savedProfiles);
        const clientNames = Object.keys(profiles);
        console.log('👥 Client names:', clientNames);

        // Update main client dropdown
        if (dropdown) {
            dropdown.innerHTML = '<option value="">Select a client...</option>';
            clientNames.forEach(clientName => {
                const option = document.createElement('option');
                option.value = clientName;
                option.textContent = clientName;
                dropdown.appendChild(option);
            });
        }

        // Update Artist Agreement client dropdown
        if (artistAgreementDropdown) {
            artistAgreementDropdown.innerHTML = '<option value="">Select a client...</option>';
            clientNames.forEach(clientName => {
                const option = document.createElement('option');
                option.value = clientName;
                option.textContent = clientName;
                artistAgreementDropdown.appendChild(option);
            });
        }

        console.log(`✅ Added ${clientNames.length} clients to dropdowns`);
    } else {
        console.log('⚠️ No saved profiles found');
    }
}

// Function to load client for Artist Agreement
function loadClientForArtistAgreement(clientName) {
    if (!clientName) return;

    console.log('🎤 Loading client for Artist Agreement:', clientName);

    // Load the client profile data
    loadClientProfile(clientName);

    console.log('✅ Client loaded for Artist Agreement');
}

function addMockClientProfiles() {
    console.log('🔍 Adding mock client profiles...');
    const savedProfiles = localStorage.getItem('clientProfiles');
    console.log('📄 Existing profiles:', savedProfiles);

    // Always add mock profiles for demo purposes (remove the return condition)
    const mockProfiles = {
        "Stellar Events & Entertainment": {
            company: "Stellar Events & Entertainment",
            regNumber: "2023/789012/07",
            vatNumber: "**********",
            address: "789 Festival Boulevard\nCreative Quarter\nJohannesburg, 2196",
            attention: "Michael Thompson",
            phone: "+27 11 555 0456",
            email: "<EMAIL>"
        },
        "Rhythm Records": {
            company: "Rhythm Records",
            regNumber: "2018/987654/07",
            vatNumber: "4982761530",
            address: "456 Beat Avenue\nHarmony Heights\nCape Town, 8001",
            attention: "Sarah Smith",
            phone: "+27 21 555 7890",
            email: "<EMAIL>"
        },
        "Melody Productions": {
            company: "Melody Productions",
            regNumber: "2020/456789/07",
            vatNumber: "4123658709",
            address: "789 Tune Boulevard\nSymphony Square\nDurban, 4001",
            attention: "Michael Moyo",
            phone: "+27 31 222 3456",
            email: "<EMAIL>"
        },
        "Soundwave Studios": {
            company: "Soundwave Studios",
            regNumber: "2019/345678/07",
            vatNumber: "4765098231",
            address: "321 Audio Lane\nEcho Park\nPretoria, 0002",
            attention: "David Dlamini",
            phone: "+27 12 987 6543",
            email: "<EMAIL>"
        },
        "Sunset Music Festival": {
            company: "Sunset Music Festival",
            regNumber: "2022/654321/07",
            vatNumber: "4876543210",
            address: "123 Festival Grounds\nMusic Valley\nCape Town, 8005",
            attention: "Lisa Johnson",
            phone: "+27 21 444 5555",
            email: "<EMAIL>"
        },
        "Urban Beats Entertainment": {
            company: "Urban Beats Entertainment",
            regNumber: "2021/111222/07",
            vatNumber: "4333444555",
            address: "567 Hip Hop Street\nUrban District\nJohannesburg, 2001",
            attention: "Thabo Mthembu",
            phone: "+27 11 333 4444",
            email: "<EMAIL>"
        }
    };

    localStorage.setItem('clientProfiles', JSON.stringify(mockProfiles));
    console.log('✅ Mock client profiles added:', Object.keys(mockProfiles));
    updateClientProfileDropdown();
}

function addMockEventDetails() {
    if (!document.getElementById('eventName').value) {
        document.getElementById('eventName').value = 'Live Music Performance';
        document.getElementById('eventDate').value = '2025-06-15';
        document.getElementById('eventTime').value = '19:30';
        document.getElementById('eventVenue').value = 'Rhythm Lounge';
        document.getElementById('eventCity').value = 'Cape Town';
    }
}

function addMockInvoiceItems() {
    const items = document.querySelectorAll('.item-row');
    let hasItems = false;
    items.forEach(item => { if (item.querySelector('.description').value && parseFloat(item.querySelector('.price').value) > 0) hasItems = true; });
    if (!hasItems) {
        const container = document.getElementById('itemsContainer');
        container.innerHTML = '';
        const mockItemsData = [
            { description: 'Professional Audio Services', quantity: 1, price: 3000 },
            { description: 'Equipment Rental', quantity: 1, price: 1500 },
            { description: 'Sound Engineer (8 hours)', quantity: 1, price: 2400 }
        ];
        mockItemsData.forEach((itemData, index) => {
            const itemRow = document.createElement('div');
            itemRow.className = 'item-row';
            itemRow.innerHTML = `
                <div class="form-group"><label for="description${index}">Description:</label><input type="text" class="description" id="description${index}" value="${itemData.description}" required></div>
                <div class="form-group"><label for="quantity${index}">Quantity:</label><input type="number" class="quantity" id="quantity${index}" min="1" value="${itemData.quantity}" required></div>
                <div class="form-group"><label for="price${index}">Price:</label><input type="number" class="price" id="price${index}" step="0.01" value="${itemData.price}" required></div>
                <div class="form-group"><label for="amount${index}">Amount:</label><input type="text" class="amount" id="amount${index}" readonly></div>
                <div class="form-group">
                    <button type="button" class="delete-item-btn" onclick="deleteItem(this)" title="Delete this item">
                        <span>🗑️</span>
                    </button>
                </div>
            `;
            container.appendChild(itemRow);
            const quantityInput = itemRow.querySelector('.quantity');
            const priceInput = itemRow.querySelector('.price');
            [quantityInput, priceInput].forEach(input => input.addEventListener('input', calculateTotals));
        });
        calculateTotals();
    }
}

function addMockBankDetails() {
    if (!document.getElementById('bankName').value) {
        document.getElementById('bankName').value = 'First National Bank';
        document.getElementById('accountName').value = 'Benjamin Music Initiatives (Pty) Ltd';
        document.getElementById('accountNumber').value = '***********';
        document.getElementById('branchCode').value = '250655';
    }
}

function initializeSignatureCanvas() {
    const canvas = document.getElementById('signatureCanvas');
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    let isDrawing = false; let lastX = 0; let lastY = 0; let lastVelocity = 0; let lastTime = 0; let points = [];
    ctx.fillStyle = '#ffffff'; ctx.fillRect(0, 0, canvas.width, canvas.height);
    ctx.beginPath(); ctx.moveTo(50, canvas.height - 30); ctx.lineTo(canvas.width - 50, canvas.height - 30); ctx.strokeStyle = '#cccccc'; ctx.stroke();
    function drawCurve() {
        if (points.length < 3) return;
        ctx.beginPath(); ctx.moveTo(points[0].x, points[0].y);
        for (let i = 1; i < points.length - 2; i++) {
            const xc = (points[i].x + points[i + 1].x) / 2; const yc = (points[i].y + points[i + 1].y) / 2;
            const lineWidth = Math.min(3, Math.max(1, points[i].v * 1.5)); ctx.lineWidth = lineWidth;
            ctx.quadraticCurveTo(points[i].x, points[i].y, xc, yc);
        }
        if (points.length > 2) {
            const lastPoint = points[points.length - 1]; const secondLastPoint = points[points.length - 2];
            ctx.quadraticCurveTo(secondLastPoint.x, secondLastPoint.y, lastPoint.x, lastPoint.y);
        }
        ctx.strokeStyle = '#000000'; ctx.stroke();
    }
    const addExampleSignature = () => {
        points = []; const totalFrames = 60; let frame = 0;
        function animateSignature() {
            if (frame >= totalFrames) return;
            const progress = frame / totalFrames;
            if (frame === 0) {
                ctx.fillStyle = '#ffffff'; ctx.fillRect(0, 0, canvas.width, canvas.height);
                ctx.beginPath(); ctx.moveTo(50, canvas.height - 30); ctx.lineTo(canvas.width - 50, canvas.height - 30); ctx.strokeStyle = '#cccccc'; ctx.stroke();
            }
            const x = 100 + progress * 180; const y = canvas.height - 50 + Math.sin(progress * Math.PI * 3) * 20;
            const velocity = Math.abs(Math.cos(progress * Math.PI * 3)) + 0.5;
            points.push({ x, y, v: velocity });
            drawCurve();
            frame++; requestAnimationFrame(animateSignature);
        }
        animateSignature();
        if (!document.getElementById('signatureName').value) document.getElementById('signatureName').value = 'Benjamin Johnson, CEO';
    };
    setTimeout(addExampleSignature, 500);
    canvas.addEventListener('mousedown', (e) => { isDrawing = true; points = []; lastTime = Date.now(); [lastX, lastY] = [e.offsetX, e.offsetY]; points.push({ x: lastX, y: lastY, v: 0 }); });
    canvas.addEventListener('mousemove', (e) => {
        if (!isDrawing) return;
        const currentTime = Date.now(); const dt = (currentTime - lastTime) || 1;
        const dx = e.offsetX - lastX; const dy = e.offsetY - lastY;
        const distance = Math.sqrt(dx * dx + dy * dy);
        const velocity = Math.min(3, Math.max(0.5, distance / dt * 10));
        lastVelocity = lastVelocity * 0.6 + velocity * 0.4;
        points.push({ x: e.offsetX, y: e.offsetY, v: lastVelocity });
        drawCurve();
        [lastX, lastY] = [e.offsetX, e.offsetY]; lastTime = currentTime;
    });
    const touchHandler = (mouseEventName) => (e) => {
        e.preventDefault(); const touch = e.touches[0];
        const mouseEvent = new MouseEvent(mouseEventName, { clientX: touch.clientX, clientY: touch.clientY });
        canvas.dispatchEvent(mouseEvent);
    };
    canvas.addEventListener('touchstart', touchHandler('mousedown'));
    canvas.addEventListener('touchmove', touchHandler('mousemove'));
    canvas.addEventListener('touchend', (e) => { e.preventDefault(); canvas.dispatchEvent(new MouseEvent('mouseup', {})); });
    canvas.addEventListener('mouseup', () => { isDrawing = false; });
    canvas.addEventListener('mouseout', () => { isDrawing = false; });
    const clearBtn = document.getElementById('clearSignature');
    if (clearBtn) {
        clearBtn.addEventListener('click', () => {
            let opacity = 1;
            function fadeOut() {
                opacity -= 0.1;
                if (opacity <= 0) {
                    ctx.fillStyle = '#ffffff'; ctx.fillRect(0, 0, canvas.width, canvas.height);
                    ctx.beginPath(); ctx.moveTo(50, canvas.height - 30); ctx.lineTo(canvas.width - 50, canvas.height - 30); ctx.strokeStyle = '#cccccc'; ctx.stroke();
                    return;
                }
                ctx.fillStyle = 'rgba(255, 255, 255, 0.3)'; ctx.fillRect(0, 0, canvas.width, canvas.height);
                requestAnimationFrame(fadeOut);
            }
            fadeOut();
        });
    }
}

function createDefaultLogo() {
    const canvas = document.createElement('canvas'); canvas.width = 200; canvas.height = 200; const ctx = canvas.getContext('2d');
    ctx.fillStyle = '#3498db'; ctx.fillRect(0, 0, 200, 200);
    ctx.beginPath(); ctx.fillStyle = 'white'; ctx.font = 'bold 120px Arial'; ctx.textAlign = 'center'; ctx.textBaseline = 'middle'; ctx.fillText('♫', 100, 100); ctx.fill();
    ctx.font = 'bold 20px Arial'; ctx.fillText('MUSIC', 100, 160);
    const img = new Image(); img.src = canvas.toDataURL('image/png');
    const preview = document.getElementById('logoPreview');
    if (preview && !preview.querySelector('img')) {
        preview.innerHTML = ''; preview.appendChild(img); logoImage = img;
        const shape = document.getElementById('logoShape').value; preview.className = 'logo-preview ' + shape;
    }
    return img;
}

// Enhanced Tab-Based Document Switching
function switchDocumentTab(documentType) {
    console.log('🔄 Switching to document tab:', documentType);

    // Update active tab
    const allTabs = document.querySelectorAll('.tab-btn');
    const activeTab = document.querySelector(`[data-document="${documentType}"]`);

    allTabs.forEach(tab => tab.classList.remove('active'));
    if (activeTab) {
        activeTab.classList.add('active');
    }

    // Update active document info
    const currentDocIcon = document.querySelector('.current-doc-icon');
    const currentDocName = document.querySelector('.current-doc-name');

    const documentConfig = {
        invoice: { icon: '📄', name: 'Invoice Generator', button: 'Generate Invoice' },
        receipt: { icon: '🧾', name: 'Receipt Generator', button: 'Generate Receipt' },
        quotation: { icon: '💰', name: 'Quotation Generator', button: 'Generate Quotation' },
        contract: { icon: '📋', name: 'Contract Generator', button: 'Generate Contract' },
        rider: { icon: '🎸', name: 'Technical Rider Generator', button: 'Generate Technical Rider' },
        annexure: { icon: '📑', name: 'Annexure Generator', button: 'Generate Annexure' },
        'artist-agreement': { icon: '🎤', name: 'Artist Agreement Generator', button: 'Generate Artist Agreement' }
    };

    const config = documentConfig[documentType];
    if (config) {
        if (currentDocIcon) currentDocIcon.textContent = config.icon;
        if (currentDocName) currentDocName.textContent = config.name;

        // Update generate button
        const generateButton = document.getElementById('generatePDF');
        const generateButtonSpan = generateButton?.querySelector('span');
        if (generateButtonSpan) generateButtonSpan.textContent = config.button;
    }

    // Handle quick sharing section visibility
    const quickSharingSection = document.getElementById('quickSharingOptions');
    if (quickSharingSection) {
        quickSharingSection.style.display = 'none';
        quickSharingSection.classList.remove('visible');
        quickSharingSection.classList.add('hidden');
    }

    // Show/hide document-specific sections
    const contractPage1Section = document.getElementById('contractPage1Section');
    const technicalRiderSection = document.getElementById('technicalRiderSection');
    const artistAgreementSection = document.getElementById('artistAgreementSection');
    const previewButton = document.getElementById('previewAgreementPDF');

    // Hide all special sections first
    if (contractPage1Section) contractPage1Section.style.display = 'none';
    if (technicalRiderSection) technicalRiderSection.style.display = 'none';
    if (artistAgreementSection) artistAgreementSection.style.display = 'none';
    if (previewButton) previewButton.style.display = 'none';

    // Hide invoice preview button
    const invoicePreviewButton = document.getElementById('previewInvoicePDF');
    if (invoicePreviewButton) invoicePreviewButton.style.display = 'none';

    // Show relevant sections based on document type
    switch(documentType) {
        case 'invoice':
            // Show preview button for Invoice
            const invoicePreviewButton = document.getElementById('previewInvoicePDF');
            if (invoicePreviewButton) {
                invoicePreviewButton.style.display = 'block';
                const previewSpan = invoicePreviewButton.querySelector('span');
                if (previewSpan) previewSpan.textContent = '👁️ Preview Invoice';
                invoicePreviewButton.onclick = function(event) { previewInvoice(event); return false; };
            }

            // Auto-populate invoice fields with active company data
            setTimeout(() => {
                autoPopulateInvoiceFields();
            }, 100);
            break;
        case 'receipt':
            // Show preview button for Receipt
            const receiptPreviewButton = document.getElementById('previewInvoicePDF');
            if (receiptPreviewButton) {
                receiptPreviewButton.style.display = 'block';
                const previewSpan = receiptPreviewButton.querySelector('span');
                if (previewSpan) previewSpan.textContent = '👁️ Preview Receipt';
                receiptPreviewButton.onclick = function(event) { previewReceipt(event); return false; };
            }
            break;
        case 'quotation':
            // Show preview button for Quotation
            const quotationPreviewButton = document.getElementById('previewInvoicePDF');
            if (quotationPreviewButton) {
                quotationPreviewButton.style.display = 'block';
                const previewSpan = quotationPreviewButton.querySelector('span');
                if (previewSpan) previewSpan.textContent = '👁️ Preview Quotation';
                quotationPreviewButton.onclick = function(event) { previewQuotation(event); return false; };
            }
            break;
        case 'contract':
            if (contractPage1Section) contractPage1Section.style.display = 'block';
            // Show preview button for Contract
            const contractPreviewButton = document.getElementById('previewInvoicePDF');
            if (contractPreviewButton) {
                contractPreviewButton.style.display = 'block';
                const previewSpan = contractPreviewButton.querySelector('span');
                if (previewSpan) previewSpan.textContent = '👁️ Preview Contract';
                contractPreviewButton.onclick = function(event) { previewContract(event); return false; };
            }
            break;
        case 'rider':
            if (technicalRiderSection) technicalRiderSection.style.display = 'block';
            // Show preview button for Technical Rider
            const riderPreviewButton = document.getElementById('previewInvoicePDF');
            if (riderPreviewButton) {
                riderPreviewButton.style.display = 'block';
                const previewSpan = riderPreviewButton.querySelector('span');
                if (previewSpan) previewSpan.textContent = '👁️ Preview Rider';
                riderPreviewButton.onclick = function(event) { previewTechnicalRider(event); return false; };
            }
            break;
        case 'annexure':
            if (previewButton) {
                previewButton.style.display = 'block';
                // Update preview button for Annexure
                const previewSpan = previewButton.querySelector('span');
                if (previewSpan) previewSpan.textContent = '👁️ Preview Annexure';
                previewButton.onclick = function(event) { previewAnnexure(event); return false; };
            }
            break;
        case 'artist-agreement':
            if (artistAgreementSection) artistAgreementSection.style.display = 'block';
            if (previewButton) {
                previewButton.style.display = 'block';
                // Update preview button for Artist Agreement
                const previewSpan = previewButton.querySelector('span');
                if (previewSpan) previewSpan.textContent = '👁️ Preview Agreement';
                previewButton.onclick = function(event) { previewArtistAgreement(event); return false; };
            }

            // Auto-populate artist agreement fields with active company data
            setTimeout(() => {
                autoPopulateArtistAgreementFields();
            }, 100);
            break;
    }

    // Store the current document mode globally
    window.documentMode = documentType;
    documentMode = documentType; // For backward compatibility

    // Update hidden document type field for backward compatibility
    const documentTypeSelect = document.getElementById('documentType');
    if (documentTypeSelect) {
        documentTypeSelect.value = documentType;
    }

    console.log('✅ Document tab switched to:', window.documentMode);

    // Call the existing changeDocumentType function for additional processing
    changeDocumentType();
}

function changeDocumentType() {
    // Use the global documentMode if documentType element doesn't exist (tab-based navigation)
    const documentTypeElement = document.getElementById('documentType');
    if (documentTypeElement) {
        documentMode = documentTypeElement.value;
        documentTypeElement.className = 'document-type-select'; // Reset classes
        documentTypeElement.classList.add(`${documentMode}-mode`); // Add specific mode class
    } else {
        // Use the global documentMode set by tab switching
        documentMode = window.documentMode || documentMode || 'invoice';
    }

    const titleElement = document.querySelector('h1');
    const invoiceDetailsTitle = document.querySelector('.section:nth-child(3) h2'); // Assuming Invoice Details is the 3rd section
    const invoiceNumberLabel = document.querySelector('label[for="invoiceNumber"]');
    const billToTitle = document.querySelector('.section:nth-child(4) h2'); // Assuming Bill To is the 4th section
    const watermarkInput = document.getElementById('watermarkText');
    const templateLabel = document.querySelector('label[for="invoiceTemplate"]');
    const generateButton = document.getElementById('generatePDF');
    const bankDetailsSection = document.querySelector('.section:nth-child(9)'); // Assuming Bank Details is the 9th section
    const contractPage1Section = document.getElementById('contractPage1Section');
    const allFormSections = document.querySelectorAll('#invoiceForm > .section');

    if (titleElement) {
        switch(documentMode) {
            case 'receipt': titleElement.textContent = 'Receipt Generator'; break;
            case 'quotation': titleElement.textContent = 'Quotation Generator'; break;
            case 'contract': titleElement.textContent = 'Contract Agreement Generator'; break;
            case 'rider': titleElement.textContent = 'Technical Rider Generator'; break;
            case 'annexure': titleElement.textContent = 'Annexure Generator'; break;
            case 'artist-agreement': titleElement.textContent = 'Artist Agreement Generator'; break;
            default: titleElement.textContent = 'Invoice Generator';
        }
    }

    if (invoiceDetailsTitle) {
        switch(documentMode) {
            case 'receipt': invoiceDetailsTitle.textContent = 'Receipt Details'; break;
            case 'quotation': invoiceDetailsTitle.textContent = 'Quotation Details'; break;
            case 'contract': invoiceDetailsTitle.textContent = 'Contract Details'; break;
            case 'rider': invoiceDetailsTitle.textContent = 'Rider Details'; break;
            case 'annexure': invoiceDetailsTitle.textContent = 'Annexure Details'; break;
            default: invoiceDetailsTitle.textContent = 'Invoice Details';
        }
    }

    if (invoiceNumberLabel) {
        switch(documentMode) {
            case 'receipt': invoiceNumberLabel.textContent = 'Receipt Number:'; break;
            case 'quotation': invoiceNumberLabel.textContent = 'Quotation Number:'; break;
            case 'contract': invoiceNumberLabel.textContent = 'Contract Number:'; break;
            case 'rider': invoiceNumberLabel.textContent = 'Rider ID:'; break;
            case 'annexure': invoiceNumberLabel.textContent = 'Annexure ID:'; break;
            default: invoiceNumberLabel.textContent = 'Invoice Number:';
        }
    }

    if (billToTitle) {
        switch(documentMode) {
            case 'quotation': billToTitle.textContent = 'Quotation To'; break;
            case 'contract': billToTitle.textContent = 'Parties To'; break;
            case 'rider': case 'annexure': billToTitle.textContent = 'Details For'; break;
            default: billToTitle.textContent = 'Bill To';
        }
    }

    if (watermarkInput) {
        switch(documentMode) {
            case 'receipt': watermarkInput.placeholder = 'e.g., PAID, COPY, DUPLICATE'; watermarkInput.value = ''; break;
            case 'quotation': watermarkInput.placeholder = 'e.g., VALID FOR 30 DAYS, ESTIMATE ONLY'; watermarkInput.value = 'VALID FOR 30 DAYS'; break;
            case 'contract': watermarkInput.placeholder = 'e.g., DRAFT, CONFIDENTIAL'; watermarkInput.value = ''; break;
            case 'rider': watermarkInput.placeholder = 'e.g., DRAFT, APPENDIX A'; watermarkInput.value = ''; break;
            case 'annexure': watermarkInput.placeholder = 'e.g., EXHIBIT 1, SCHEDULE B'; watermarkInput.value = ''; break;
            default: watermarkInput.placeholder = 'e.g., PAID, COPY, DRAFT'; watermarkInput.value = '';
        }
    }

    if (templateLabel) {
        switch(documentMode) {
            case 'receipt': templateLabel.textContent = 'Receipt Template:'; break;
            case 'quotation': templateLabel.textContent = 'Quotation Template:'; break;
            case 'contract': templateLabel.textContent = 'Contract Template:'; break;
            case 'rider': templateLabel.textContent = 'Rider Template:'; break;
            case 'annexure': templateLabel.textContent = 'Annexure Template:'; break;
            default: templateLabel.textContent = 'Invoice Template:';
        }
    }

    if (generateButton && generateButton.querySelector('span')) {
        switch(documentMode) {
            case 'receipt': generateButton.querySelector('span').textContent = 'Generate Receipt'; break;
            case 'quotation': generateButton.querySelector('span').textContent = 'Generate Quotation'; break;
            case 'contract': generateButton.querySelector('span').textContent = 'Generate Contract'; break;
            case 'rider': generateButton.querySelector('span').textContent = 'Generate Rider'; break;
            case 'annexure': generateButton.querySelector('span').textContent = 'Generate Annexure'; break;
            case 'artist-agreement': generateButton.querySelector('span').textContent = 'Generate Artist Agreement'; break;
            default: generateButton.querySelector('span').textContent = 'Generate Invoice';
        }
    }

    if (bankDetailsSection) {
        if (documentMode === 'quotation' || documentMode === 'contract' || documentMode === 'rider' || documentMode === 'annexure' || documentMode === 'artist-agreement') {
            bankDetailsSection.style.display = 'none';
        } else {
            bankDetailsSection.style.display = 'block';
        }
    }

    // Show/hide sections based on document type
    allFormSections.forEach(section => {
        if (section.id === 'contractPage1Section') {
            section.style.display = documentMode === 'contract' ? 'block' : 'none';
        } else if (section.id === 'technicalRiderSection') {
            section.style.display = documentMode === 'rider' ? 'block' : 'none';
        } else if (section.id === 'annexureSection') {
            section.style.display = documentMode === 'annexure' ? 'block' : 'none';
        } else if (section.id === 'artistAgreementSection') {
            section.style.display = documentMode === 'artist-agreement' ? 'block' : 'none';
        } else {
            // For other sections, hide them if contract, rider, annexure, or artist-agreement mode is active, otherwise show
            if (documentMode === 'contract' || documentMode === 'rider' || documentMode === 'annexure' || documentMode === 'artist-agreement') {
                section.style.display = 'none';
                // Remove required attributes from hidden fields to prevent validation errors
                const requiredFields = section.querySelectorAll('[required]');
                requiredFields.forEach(field => {
                    field.removeAttribute('required');
                    field.setAttribute('data-was-required', 'true');
                });
            } else {
                // Restore display for non-contract/rider/annexure/artist-agreement sections if they aren't bank details in a non-invoice/receipt mode
                if (section === bankDetailsSection && (documentMode === 'quotation')) {
                    // Already handled
                } else {
                     section.style.display = 'block';
                     // Restore required attributes when showing sections
                     const wasRequiredFields = section.querySelectorAll('[data-was-required="true"]');
                     wasRequiredFields.forEach(field => {
                         field.setAttribute('required', 'true');
                         field.removeAttribute('data-was-required');
                     });
                }
            }
        }
    });

    // Handle quick sharing section visibility
    const quickSharingSection = document.getElementById('quickSharingOptions');
    if (quickSharingSection) {
        // Hide quick sharing when switching document types (will show after PDF generation)
        quickSharingSection.style.display = 'none';
    }

    // Create technical rider section if it doesn't exist
    if (documentMode === 'rider') {
        createTechnicalRiderSection();
    }

    // Create annexure section if it doesn't exist
    if (documentMode === 'annexure') {
        createAnnexureSection();
    }
    
    // Ensure the main styling section and document type selector are always visible
    const stylingSection = document.querySelector('.styling-section');
    if(stylingSection) stylingSection.style.display = 'block';


    document.querySelectorAll('#invoiceForm > .section').forEach(section => { // Only animate visible sections
        if (section.style.display !== 'none') {
            section.style.transition = 'all 0.3s ease'; section.style.transform = 'scale(0.98)'; section.style.opacity = '0.8';
            setTimeout(() => { section.style.transform = 'scale(1)'; section.style.opacity = '1'; }, 300);
        }
    });
    console.log(`Switched to ${documentMode} mode`);

    // Auto-populate company fields after switching document types
    autoPopulateCompanyFields();
}

// Create technical rider specific form section
function createTechnicalRiderSection() {
    // Check if technical rider section already exists
    let technicalRiderSection = document.getElementById('technicalRiderSection');

    if (!technicalRiderSection) {
        // Create the technical rider section
        technicalRiderSection = document.createElement('div');
        technicalRiderSection.id = 'technicalRiderSection';
        technicalRiderSection.className = 'section';

        technicalRiderSection.innerHTML = `
            <h2>🎸 Technical Rider Specifications</h2>
            <p class="section-description">Configure your technical requirements for professional events and performances.</p>

            <div class="rider-form-grid">
                <div class="rider-section">
                    <h3>🔊 Audio Requirements</h3>
                    <div class="form-group">
                        <label for="riderPASystem">PA System Requirements:</label>
                        <select id="riderPASystem" onchange="handleCustomSelection(this, 'riderPASystemCustom')">
                            <option value="15kW">15kW Line Array (Standard)</option>
                            <option value="20kW">20kW Line Array (Large Venue)</option>
                            <option value="25kW">25kW Line Array (Arena)</option>
                            <option value="custom">➕ Custom Requirements</option>
                        </select>
                        <div id="riderPASystemCustom" class="custom-input-container" style="display: none;">
                            <input type="text" class="custom-input" placeholder="Enter custom PA system requirements...">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="riderMixingConsole">Mixing Console:</label>
                        <select id="riderMixingConsole" onchange="handleCustomSelection(this, 'riderMixingConsoleCustom')">
                            <option value="yamaha-cl5">Yamaha CL5 (32+ channels)</option>
                            <option value="avid-s6l">Avid S6L (64+ channels)</option>
                            <option value="digico-sd12">DiGiCo SD12 (72+ channels)</option>
                            <option value="custom">➕ Custom Console</option>
                        </select>
                        <div id="riderMixingConsoleCustom" class="custom-input-container" style="display: none;">
                            <input type="text" class="custom-input" placeholder="Enter custom mixing console requirements...">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="riderIEMSystem">In-Ear Monitor System:</label>
                        <select id="riderIEMSystem" onchange="handleCustomSelection(this, 'riderIEMSystemCustom')">
                            <option value="6-transmitters">6x IEM Transmitters (Standard)</option>
                            <option value="8-transmitters">8x IEM Transmitters (Extended)</option>
                            <option value="12-transmitters">12x IEM Transmitters (Full Band)</option>
                            <option value="band-provides">Band Provides Own System</option>
                            <option value="custom">➕ Custom IEM System</option>
                        </select>
                        <div id="riderIEMSystemCustom" class="custom-input-container" style="display: none;">
                            <input type="text" class="custom-input" placeholder="Enter custom IEM system requirements...">
                        </div>
                    </div>

                    <div class="custom-requirements-section">
                        <h4>📝 Custom Audio Requirements</h4>
                        <div id="audioCustomRequirements" class="custom-requirements-container">
                            <!-- Custom audio requirements will be added here -->
                        </div>
                        <button type="button" class="add-requirement-btn" onclick="addCustomRequirement('audio')">
                            ➕ Add Custom Audio Requirement
                        </button>
                    </div>
                </div>

                <div class="rider-section">
                    <h3>🏗️ Stage Requirements</h3>
                    <div class="form-group">
                        <label for="riderStageSize">Stage Dimensions:</label>
                        <select id="riderStageSize" onchange="handleCustomSelection(this, 'riderStageSizeCustom')">
                            <option value="10x8">10m x 8m (Standard)</option>
                            <option value="12x10">12m x 10m (Large)</option>
                            <option value="15x12">15m x 12m (Arena)</option>
                            <option value="custom">➕ Custom Dimensions</option>
                        </select>
                        <div id="riderStageSizeCustom" class="custom-input-container" style="display: none;">
                            <input type="text" class="custom-input" placeholder="Enter custom stage dimensions...">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="riderDrumRiser">Drum Riser:</label>
                        <select id="riderDrumRiser" onchange="handleCustomSelection(this, 'riderDrumRiserCustom')">
                            <option value="3x3">3m x 3m x 0.4m (Standard)</option>
                            <option value="4x4">4m x 4m x 0.5m (Large)</option>
                            <option value="none">No Riser Required</option>
                            <option value="custom">➕ Custom Riser</option>
                        </select>
                        <div id="riderDrumRiserCustom" class="custom-input-container" style="display: none;">
                            <input type="text" class="custom-input" placeholder="Enter custom drum riser requirements...">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="riderPowerSupply">Power Requirements:</label>
                        <select id="riderPowerSupply" onchange="handleCustomSelection(this, 'riderPowerSupplyCustom')">
                            <option value="63A-3phase">63A 3-Phase CEE (Standard)</option>
                            <option value="125A-3phase">125A 3-Phase CEE (High Power)</option>
                            <option value="custom">➕ Custom Power Requirements</option>
                        </select>
                        <div id="riderPowerSupplyCustom" class="custom-input-container" style="display: none;">
                            <input type="text" class="custom-input" placeholder="Enter custom power requirements...">
                        </div>
                    </div>

                    <div class="custom-requirements-section">
                        <h4>📝 Custom Stage Requirements</h4>
                        <div id="stageCustomRequirements" class="custom-requirements-container">
                            <!-- Custom stage requirements will be added here -->
                        </div>
                        <button type="button" class="add-requirement-btn" onclick="addCustomRequirement('stage')">
                            ➕ Add Custom Stage Requirement
                        </button>
                    </div>
                </div>

                <div class="rider-section">
                    <h3>🎸 Backline Requirements</h3>
                    <div class="form-group">
                        <label for="riderGuitarAmps">Guitar Amplification:</label>
                        <select id="riderGuitarAmps" onchange="handleCustomSelection(this, 'riderGuitarAmpsCustom')">
                            <option value="marshall-jcm800">2x Marshall JCM800 + 4x12 Cabs</option>
                            <option value="fender-twin">2x Fender Twin Reverb</option>
                            <option value="vox-ac30">2x Vox AC30</option>
                            <option value="band-provides">Band Provides Own</option>
                            <option value="custom">➕ Custom Guitar Amps</option>
                        </select>
                        <div id="riderGuitarAmpsCustom" class="custom-input-container" style="display: none;">
                            <input type="text" class="custom-input" placeholder="Enter custom guitar amplification requirements...">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="riderBassAmp">Bass Amplification:</label>
                        <select id="riderBassAmp" onchange="handleCustomSelection(this, 'riderBassAmpCustom')">
                            <option value="ampeg-svt">Ampeg SVT-CL + 8x10 Cab</option>
                            <option value="markbass">Markbass Little Mark + 4x10 Cab</option>
                            <option value="di-only">DI Only (No Amp)</option>
                            <option value="band-provides">Band Provides Own</option>
                            <option value="custom">➕ Custom Bass Amp</option>
                        </select>
                        <div id="riderBassAmpCustom" class="custom-input-container" style="display: none;">
                            <input type="text" class="custom-input" placeholder="Enter custom bass amplification requirements...">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="riderDrumKit">Drum Kit:</label>
                        <select id="riderDrumKit" onchange="handleCustomSelection(this, 'riderDrumKitCustom')">
                            <option value="dw-collectors">DW Collectors Series (5-piece)</option>
                            <option value="pearl-masters">Pearl Masters (5-piece)</option>
                            <option value="yamaha-recording">Yamaha Recording Custom (5-piece)</option>
                            <option value="band-provides">Band Provides Own</option>
                            <option value="custom">➕ Custom Drum Kit</option>
                        </select>
                        <div id="riderDrumKitCustom" class="custom-input-container" style="display: none;">
                            <input type="text" class="custom-input" placeholder="Enter custom drum kit requirements...">
                        </div>
                    </div>

                    <div class="custom-requirements-section">
                        <h4>📝 Custom Backline Requirements</h4>
                        <div id="backlineCustomRequirements" class="custom-requirements-container">
                            <!-- Custom backline requirements will be added here -->
                        </div>
                        <button type="button" class="add-requirement-btn" onclick="addCustomRequirement('backline')">
                            ➕ Add Custom Backline Requirement
                        </button>
                    </div>
                </div>

                <div class="rider-section">
                    <h3>💡 Lighting Requirements</h3>
                    <div class="form-group">
                        <label for="riderLightingConsole">Lighting Console:</label>
                        <select id="riderLightingConsole" onchange="handleCustomSelection(this, 'riderLightingConsoleCustom')">
                            <option value="ma-grandma3">MA Lighting GrandMA3</option>
                            <option value="chamsys-magicq">ChamSys MagicQ</option>
                            <option value="etc-eos">ETC Eos Family</option>
                            <option value="basic">Basic Lighting Only</option>
                            <option value="custom">➕ Custom Lighting Console</option>
                        </select>
                        <div id="riderLightingConsoleCustom" class="custom-input-container" style="display: none;">
                            <input type="text" class="custom-input" placeholder="Enter custom lighting console requirements...">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="riderLightingFixtures">Lighting Fixtures:</label>
                        <select id="riderLightingFixtures" onchange="handleCustomSelection(this, 'riderLightingFixturesCustom')">
                            <option value="full-rig">Full LED Rig (24x Wash + 12x Spot)</option>
                            <option value="standard">Standard Rig (16x Wash + 8x Spot)</option>
                            <option value="basic">Basic Wash Only</option>
                            <option value="band-provides">Band Provides Own</option>
                            <option value="custom">➕ Custom Lighting Fixtures</option>
                        </select>
                        <div id="riderLightingFixturesCustom" class="custom-input-container" style="display: none;">
                            <input type="text" class="custom-input" placeholder="Enter custom lighting fixtures requirements...">
                        </div>
                    </div>

                    <div class="custom-requirements-section">
                        <h4>📝 Custom Lighting Requirements</h4>
                        <div id="lightingCustomRequirements" class="custom-requirements-container">
                            <!-- Custom lighting requirements will be added here -->
                        </div>
                        <button type="button" class="add-requirement-btn" onclick="addCustomRequirement('lighting')">
                            ➕ Add Custom Lighting Requirement
                        </button>
                    </div>
                </div>

                <div class="rider-section">
                    <h3>👥 Personnel Requirements</h3>
                    <div class="form-group">
                        <label for="riderPersonnel">Required Personnel:</label>
                        <select id="riderPersonnel" multiple>
                            <option value="foh-engineer">FOH Engineer</option>
                            <option value="monitor-engineer">Monitor Engineer</option>
                            <option value="lighting-tech">Lighting Technician</option>
                            <option value="stage-manager">Stage Manager</option>
                            <option value="stage-hands">4x Stage Hands</option>
                            <option value="security">Security Personnel</option>
                        </select>
                        <small>Hold Ctrl/Cmd to select multiple options</small>
                    </div>

                    <div class="custom-requirements-section">
                        <h4>📝 Custom Personnel Requirements</h4>
                        <div id="personnelCustomRequirements" class="custom-requirements-container">
                            <!-- Custom personnel requirements will be added here -->
                        </div>
                        <button type="button" class="add-requirement-btn" onclick="addCustomRequirement('personnel')">
                            ➕ Add Custom Personnel Requirement
                        </button>
                    </div>
                </div>

                <div class="rider-section">
                    <h3>📝 Additional Requirements</h3>
                    <div class="form-group">
                        <label for="riderSpecialRequests">Special Technical Requests:</label>
                        <textarea id="riderSpecialRequests" rows="4" placeholder="Enter any additional technical requirements, special equipment needs, or specific setup instructions..."></textarea>
                    </div>
                    <div class="form-group">
                        <label for="riderContactInfo">Technical Contact Information:</label>
                        <textarea id="riderContactInfo" rows="3" placeholder="Production Manager contact details, advance coordinator, etc..."></textarea>
                    </div>
                </div>
            </div>

            <div class="rider-preview-section">
                <h3>📋 Technical Rider Preview</h3>
                <p>Your technical rider will include 9 comprehensive sections:</p>
                <ul class="rider-features-list">
                    <li>🔊 Audio Requirements (PA, Mixing, IEM Systems)</li>
                    <li>🏗️ Stage Requirements (Dimensions, Power, Rigging)</li>
                    <li>🎸 Backline Requirements (Amps, Drums, Keyboards)</li>
                    <li>💡 Lighting Requirements (Console, Fixtures, DMX)</li>
                    <li>👥 Personnel Requirements (Engineers, Technicians, Crew)</li>
                    <li>⏰ Schedule Requirements (Load-in, Soundcheck, Show)</li>
                    <li>🍽️ Hospitality Requirements (Dressing Rooms, Catering)</li>
                    <li>🛡️ Safety & Compliance (Insurance, Certifications)</li>
                    <li>📞 Technical Contacts (Production, Advance, Emergency)</li>
                </ul>
            </div>
        `;

        // Insert the technical rider section after the styling section
        const stylingSection = document.querySelector('.styling-section');
        if (stylingSection && stylingSection.parentNode) {
            stylingSection.parentNode.insertBefore(technicalRiderSection, stylingSection.nextSibling);
        } else {
            // Fallback: append to form
            const form = document.getElementById('invoiceForm');
            if (form) {
                form.appendChild(technicalRiderSection);
            }
        }

        console.log('✅ Technical rider section created');

        // Add mock data for technical rider
        setTimeout(() => {
            populateTechnicalRiderMockData();
            addMockCustomRequirements();
        }, 100);
    }
}

// Create annexure specific form section
function createAnnexureSection() {
    // Check if annexure section already exists
    let annexureSection = document.getElementById('annexureSection');

    if (!annexureSection) {
        // Create the annexure section
        annexureSection = document.createElement('div');
        annexureSection.id = 'annexureSection';
        annexureSection.className = 'section';

        annexureSection.innerHTML = `
            <h2>📑 Annexure Specifications</h2>
            <p class="section-description">Configure your entertainment industry annexure with professional terms and conditions.</p>

            <div class="annexure-form-grid">
                <div class="annexure-section">
                    <h3>📢 Marketing and Promotional Materials</h3>
                    <div class="form-group">
                        <label for="annexureMarketingDeadline">Material Submission Deadline:</label>
                        <select id="annexureMarketingDeadline">
                            <option value="4-weeks">4 weeks before event</option>
                            <option value="3-weeks">3 weeks before event</option>
                            <option value="2-weeks">2 weeks before event</option>
                            <option value="custom">Custom deadline</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="annexurePhotoRequirements">Photo Requirements:</label>
                        <select id="annexurePhotoRequirements">
                            <option value="300dpi">300 DPI minimum (Standard)</option>
                            <option value="600dpi">600 DPI minimum (High Quality)</option>
                            <option value="both-formats">RGB and CMYK versions</option>
                            <option value="custom">Custom requirements</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="annexureSocialMedia">Social Media Guidelines:</label>
                        <textarea id="annexureSocialMedia" rows="3" placeholder="Hashtags, tagging requirements, brand guidelines...">Official hashtags: #BMILive #BenjaminMusicInitiatives
Tagging: @benjaminmusicinitiatives
Brand colors: #1a1a1a, #ff6b35, #ffffff</textarea>
                    </div>
                </div>

                <div class="annexure-section">
                    <h3>🛍️ Merchandise Agreement</h3>
                    <div class="form-group">
                        <label for="annexureMerchSplit">Revenue Split (Artist/Venue):</label>
                        <select id="annexureMerchSplit">
                            <option value="85-15">85% Artist / 15% Venue</option>
                            <option value="80-20">80% Artist / 20% Venue</option>
                            <option value="90-10">90% Artist / 10% Venue</option>
                            <option value="custom">Custom split</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="annexureMerchTypes">Merchandise Types:</label>
                        <select id="annexureMerchTypes" multiple>
                            <option value="apparel">T-shirts & Apparel</option>
                            <option value="music">CDs & Vinyl</option>
                            <option value="posters">Posters & Prints</option>
                            <option value="accessories">Accessories</option>
                            <option value="limited">Limited Editions</option>
                        </select>
                        <small>Hold Ctrl/Cmd to select multiple types</small>
                    </div>
                    <div class="form-group">
                        <label for="annexureBoothSize">Booth Requirements:</label>
                        <select id="annexureBoothSize">
                            <option value="8x6">8ft x 6ft (Standard)</option>
                            <option value="10x8">10ft x 8ft (Large)</option>
                            <option value="12x10">12ft x 10ft (Premium)</option>
                            <option value="custom">Custom size</option>
                        </select>
                    </div>
                </div>

                <div class="annexure-section">
                    <h3>🎫 Guest List & Travel</h3>
                    <div class="form-group">
                        <label for="annexureGuestTickets">Complimentary Tickets:</label>
                        <select id="annexureGuestTickets">
                            <option value="10">10 tickets (Standard)</option>
                            <option value="15">15 tickets (Extended)</option>
                            <option value="20">20 tickets (Premium)</option>
                            <option value="custom">Custom amount</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="annexureBackstagePasses">Backstage Passes:</label>
                        <select id="annexureBackstagePasses">
                            <option value="6">6 all-access passes</option>
                            <option value="8">8 all-access passes</option>
                            <option value="10">10 all-access passes</option>
                            <option value="custom">Custom amount</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="annexurePerDiem">Per Diem Rates:</label>
                        <select id="annexurePerDiem">
                            <option value="standard">Artist: $150, Crew: $75</option>
                            <option value="premium">Artist: $200, Crew: $100</option>
                            <option value="budget">Artist: $100, Crew: $50</option>
                            <option value="custom">Custom rates</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="annexureTravelClass">Travel Requirements:</label>
                        <select id="annexureTravelClass">
                            <option value="business">Business class (3+ hours)</option>
                            <option value="economy-plus">Economy Plus</option>
                            <option value="economy">Economy class</option>
                            <option value="custom">Custom requirements</option>
                        </select>
                    </div>
                </div>

                <div class="annexure-section">
                    <h3>⚖️ Force Majeure & Cancellation</h3>
                    <div class="form-group">
                        <label for="annexureNotificationTime">Cancellation Notice:</label>
                        <select id="annexureNotificationTime">
                            <option value="immediate">Immediate notification required</option>
                            <option value="24-hours">24 hours written confirmation</option>
                            <option value="48-hours">48 hours written confirmation</option>
                            <option value="custom">Custom timeline</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="annexureRescheduleWindow">Rescheduling Window:</label>
                        <select id="annexureRescheduleWindow">
                            <option value="90-days">90 days (Standard)</option>
                            <option value="120-days">120 days (Extended)</option>
                            <option value="180-days">180 days (Flexible)</option>
                            <option value="custom">Custom window</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="annexureDispute">Dispute Resolution:</label>
                        <select id="annexureDispute">
                            <option value="mediation-arbitration">Mediation then Arbitration</option>
                            <option value="arbitration-only">Arbitration only</option>
                            <option value="court">Court jurisdiction</option>
                            <option value="custom">Custom process</option>
                        </select>
                    </div>
                </div>

                <div class="annexure-section">
                    <h3>📝 Additional Terms</h3>
                    <div class="form-group">
                        <label for="annexureSpecialClauses">Special Clauses:</label>
                        <textarea id="annexureSpecialClauses" rows="4" placeholder="Enter any additional terms, special conditions, or specific requirements...">All promotional materials subject to artist approval.
Venue must provide secure storage for merchandise.
Artist retains all intellectual property rights.
Professional conduct expected from all parties.</textarea>
                    </div>
                    <div class="form-group">
                        <label for="annexureInsurance">Insurance Requirements:</label>
                        <textarea id="annexureInsurance" rows="3" placeholder="Insurance coverage details, liability limits, etc...">Venue public liability: €2M minimum
Equipment insurance: Full replacement value
Artist personal insurance: As per rider requirements</textarea>
                    </div>
                </div>
            </div>

            <div class="annexure-preview-section">
                <h3>📋 Annexure Preview</h3>
                <p>Your annexure will include 4 comprehensive sections:</p>
                <ul class="annexure-features-list">
                    <li>📢 Marketing and Promotional Materials (Photos, Social Media, Deadlines)</li>
                    <li>🛍️ Merchandise Agreement Details (Revenue Split, Types, Booth Setup)</li>
                    <li>🎫 Guest List and Travel Arrangements (Tickets, Passes, Per Diems)</li>
                    <li>⚖️ Force Majeure and Cancellation Clauses (Notice, Rescheduling, Disputes)</li>
                </ul>
            </div>
        `;

        // Insert the annexure section after the styling section
        const stylingSection = document.querySelector('.styling-section');
        if (stylingSection && stylingSection.parentNode) {
            stylingSection.parentNode.insertBefore(annexureSection, stylingSection.nextSibling);
        } else {
            // Fallback: append to form
            const form = document.getElementById('invoiceForm');
            if (form) {
                form.appendChild(annexureSection);
            }
        }

        console.log('✅ Annexure section created');

        // Add mock data for annexure
        setTimeout(() => {
            populateAnnexureMockData();
        }, 100);
    }
}

// Populate annexure with mock data
function populateAnnexureMockData() {
    const mockData = {
        annexureMarketingDeadline: '4-weeks',
        annexurePhotoRequirements: 'both-formats',
        annexureMerchSplit: '85-15',
        annexureBoothSize: '8x6',
        annexureGuestTickets: '10',
        annexureBackstagePasses: '6',
        annexurePerDiem: 'standard',
        annexureTravelClass: 'business',
        annexureNotificationTime: 'immediate',
        annexureRescheduleWindow: '90-days',
        annexureDispute: 'mediation-arbitration'
    };

    // Populate select fields
    Object.keys(mockData).forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            if (fieldId === 'annexureMerchTypes') {
                // Handle multiple select
                const options = ['apparel', 'music', 'posters', 'accessories'];
                options.forEach(option => {
                    const optionElement = field.querySelector(`option[value="${option}"]`);
                    if (optionElement) optionElement.selected = true;
                });
            } else {
                field.value = mockData[fieldId];
            }
        }
    });

    console.log('📑 Annexure mock data populated');
}

// Populate technical rider with mock data
function populateTechnicalRiderMockData() {
    const mockData = {
        riderPASystem: 'custom', // This will trigger custom input
        riderMixingConsole: 'yamaha-cl5',
        riderIEMSystem: '6-transmitters',
        riderStageSize: '10x8',
        riderDrumRiser: 'custom', // This will trigger custom input
        riderPowerSupply: '63A-3phase',
        riderGuitarAmps: 'marshall-jcm800',
        riderBassAmp: 'custom', // This will trigger custom input
        riderDrumKit: 'dw-collectors',
        riderLightingConsole: 'ma-grandma3',
        riderLightingFixtures: 'full-rig',
        riderSpecialRequests: 'Please ensure all equipment is PAT tested and certified. Band requires 2 hours minimum for soundcheck. Stage must be clear of all other equipment during load-in.',
        riderContactInfo: 'Production Manager: Sarah Johnson (+27 82 123 4567)\nTechnical Advance: Mike Stevens (<EMAIL>)\nEmergency Contact: Tour Manager (+27 83 987 6543)'
    };

    // Custom input values for demonstration
    const customInputValues = {
        riderPASystemCustom: '25kW L-Acoustics K2 line array with KS28 subwoofers',
        riderDrumRiserCustom: '4m x 3m x 0.6m high riser with black carpet and LED strip lighting',
        riderBassAmpCustom: 'Vintage Ampeg SVT-VR tube head with custom 6x10 cabinet'
    };

    // Populate select fields
    Object.keys(mockData).forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            if (fieldId === 'riderPersonnel') {
                // Handle multiple select
                const options = ['foh-engineer', 'monitor-engineer', 'lighting-tech', 'stage-manager', 'stage-hands'];
                options.forEach(option => {
                    const optionElement = field.querySelector(`option[value="${option}"]`);
                    if (optionElement) optionElement.selected = true;
                });
            } else {
                field.value = mockData[fieldId];

                // If this is a custom selection, trigger the custom input
                if (mockData[fieldId] === 'custom') {
                    const customContainerId = fieldId + 'Custom';
                    handleCustomSelection(field, customContainerId);

                    // Populate the custom input with demo value
                    setTimeout(() => {
                        const customContainer = document.getElementById(customContainerId);
                        if (customContainer && customInputValues[customContainerId]) {
                            const customInput = customContainer.querySelector('.custom-input');
                            if (customInput) {
                                customInput.value = customInputValues[customContainerId];
                            }
                        }
                    }, 200);
                }
            }
        }
    });

    console.log('🎸 Technical rider mock data populated');
}

// Add custom requirement row
function addCustomRequirement(section) {
    const container = document.getElementById(`${section}CustomRequirements`);
    if (!container) return;

    const requirementId = `${section}_custom_${Date.now()}`;
    const requirementRow = document.createElement('div');
    requirementRow.className = 'custom-requirement-row';
    requirementRow.id = requirementId;

    requirementRow.innerHTML = `
        <div class="custom-requirement-input-group">
            <input type="text"
                   class="custom-requirement-input"
                   placeholder="Enter custom ${section} requirement..."
                   id="${requirementId}_input">
            <button type="button"
                    class="remove-requirement-btn"
                    onclick="removeCustomRequirement('${requirementId}')"
                    title="Remove this requirement">
                ❌
            </button>
        </div>
    `;

    container.appendChild(requirementRow);

    // Focus on the new input
    const newInput = document.getElementById(`${requirementId}_input`);
    if (newInput) {
        newInput.focus();
    }

    console.log(`✅ Added custom ${section} requirement`);
}

// Remove custom requirement row
function removeCustomRequirement(requirementId) {
    const requirementRow = document.getElementById(requirementId);
    if (requirementRow) {
        // Add fade out animation
        requirementRow.style.transition = 'all 0.3s ease';
        requirementRow.style.opacity = '0';
        requirementRow.style.transform = 'translateX(-100%)';

        setTimeout(() => {
            requirementRow.remove();
        }, 300);

        console.log(`🗑️ Removed custom requirement: ${requirementId}`);
    }
}

// Get all custom requirements for a section
function getCustomRequirements(section) {
    const container = document.getElementById(`${section}CustomRequirements`);
    if (!container) return [];

    const requirements = [];
    const inputs = container.querySelectorAll('.custom-requirement-input');

    inputs.forEach(input => {
        const value = input.value.trim();
        if (value) {
            requirements.push(value);
        }
    });

    return requirements;
}

// Get all custom requirements for PDF generation
function getAllCustomRequirements() {
    const sections = ['audio', 'stage', 'backline', 'lighting', 'personnel'];
    const allRequirements = {};

    sections.forEach(section => {
        allRequirements[section] = getCustomRequirements(section);
    });

    return allRequirements;
}

// Add mock custom requirements for demonstration
function addMockCustomRequirements() {
    const mockRequirements = {
        audio: [
            'Wireless microphone system with 4 channels',
            'Subwoofer array for low-end reinforcement'
        ],
        stage: [
            'Black stage draping around drum riser',
            'Additional power outlet stage center for keyboards'
        ],
        backline: [
            'Vintage Fender Rhodes electric piano',
            'Additional guitar cabinet for stereo setup'
        ],
        lighting: [
            'UV blacklight for special effects',
            'Follow spot for lead vocalist'
        ],
        personnel: [
            'Video technician for live streaming',
            'Merchandise coordinator for sales area'
        ]
    };

    Object.keys(mockRequirements).forEach(section => {
        mockRequirements[section].forEach(requirement => {
            addCustomRequirement(section);
            // Find the last added input and populate it
            const container = document.getElementById(`${section}CustomRequirements`);
            if (container) {
                const inputs = container.querySelectorAll('.custom-requirement-input');
                const lastInput = inputs[inputs.length - 1];
                if (lastInput) {
                    lastInput.value = requirement;
                }
            }
        });
    });

    console.log('🎸 Mock custom requirements added');
}

// Handle custom selection in dropdowns
function handleCustomSelection(selectElement, customContainerId) {
    const customContainer = document.getElementById(customContainerId);
    if (!customContainer) return;

    if (selectElement.value === 'custom') {
        // Show custom input
        customContainer.style.display = 'block';
        customContainer.style.animation = 'fadeIn 0.3s ease';

        // Focus on the input
        const customInput = customContainer.querySelector('.custom-input');
        if (customInput) {
            setTimeout(() => {
                customInput.focus();
            }, 100);
        }

        console.log(`✅ Custom input activated for ${selectElement.id}`);
    } else {
        // Hide custom input
        customContainer.style.display = 'none';

        // Clear the custom input value
        const customInput = customContainer.querySelector('.custom-input');
        if (customInput) {
            customInput.value = '';
        }
    }
}

// Get value from dropdown or custom input
function getDropdownOrCustomValue(selectId, customContainerId) {
    const selectElement = document.getElementById(selectId);
    const customContainer = document.getElementById(customContainerId);

    if (!selectElement) return '';

    if (selectElement.value === 'custom' && customContainer) {
        const customInput = customContainer.querySelector('.custom-input');
        return customInput ? customInput.value.trim() : '';
    }

    return selectElement.value;
}

// Get all dropdown and custom values for PDF generation
function getAllDropdownValues() {
    const dropdownMappings = {
        riderPASystem: 'riderPASystemCustom',
        riderMixingConsole: 'riderMixingConsoleCustom',
        riderIEMSystem: 'riderIEMSystemCustom',
        riderStageSize: 'riderStageSizeCustom',
        riderDrumRiser: 'riderDrumRiserCustom',
        riderPowerSupply: 'riderPowerSupplyCustom',
        riderGuitarAmps: 'riderGuitarAmpsCustom',
        riderBassAmp: 'riderBassAmpCustom',
        riderDrumKit: 'riderDrumKitCustom',
        riderLightingConsole: 'riderLightingConsoleCustom',
        riderLightingFixtures: 'riderLightingFixturesCustom'
    };

    const values = {};

    Object.keys(dropdownMappings).forEach(selectId => {
        const customContainerId = dropdownMappings[selectId];
        values[selectId] = getDropdownOrCustomValue(selectId, customContainerId);
    });

    return values;
}

// Initialize app with user session and document type handling
function initializeApp() {
    console.log('Initializing DocuGen Pro application...');

    // Check user session
    const currentUser = localStorage.getItem('currentUser');
    if (currentUser) {
        const user = JSON.parse(currentUser);
        console.log('User logged in:', user.name);

        // Show user info and back button
        const userProfile = document.getElementById('userProfile');
        const currentUserName = document.getElementById('currentUserName');
        const backToDashboard = document.getElementById('backToDashboard');

        if (userProfile && currentUserName && backToDashboard) {
            userProfile.style.display = 'block';
            backToDashboard.style.display = 'block';
            currentUserName.textContent = user.name;

            // Add event listeners
            backToDashboard.addEventListener('click', function() {
                window.location.href = 'dashboard2.html';
            });

            userProfile.addEventListener('click', function() {
                alert(`Logged in as: ${user.name}\nCompany: ${user.company}\nPlan: ${user.plan}`);
            });
        }
    }

    // Check for selected document type from dashboard
    const selectedDocumentType = localStorage.getItem('selectedDocumentType');
    if (selectedDocumentType) {
        console.log('Setting document type to:', selectedDocumentType);
        documentMode = selectedDocumentType;

        // Update the document type selector
        const documentTypeSelect = document.getElementById('documentType');
        if (documentTypeSelect) {
            documentTypeSelect.value = selectedDocumentType;
        }

        // Update app title
        const appTitle = document.getElementById('appTitle');
        if (appTitle) {
            const titles = {
                'invoice': 'Invoice Generator',
                'receipt': 'Receipt Generator',
                'quotation': 'Quotation Generator',
                'contract': 'Contract Generator',
                'rider': 'Technical Rider Generator',
                'annexure': 'Annexure Generator',
                'artist-agreement': 'Artist Agreement Generator'
            };
            appTitle.textContent = titles[selectedDocumentType] || 'Document Generator';
        }

        // Store the selected document type for tab initialization
        window.initialDocumentType = selectedDocumentType;
    }

    // Check for special actions from dashboard
    if (localStorage.getItem('openTemplateManager') === 'true') {
        localStorage.removeItem('openTemplateManager');
        setTimeout(() => {
            const manageTemplatesBtn = document.getElementById('manageTemplates');
            if (manageTemplatesBtn) manageTemplatesBtn.click();
        }, 1000);
    }

    if (localStorage.getItem('openClientManager') === 'true') {
        localStorage.removeItem('openClientManager');
        setTimeout(() => {
            const saveClientBtn = document.getElementById('saveClientProfile');
            if (saveClientBtn) saveClientBtn.click();
        }, 1000);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    try {
        if (typeof window.jspdf !== 'undefined') window.jsPDF = window.jspdf.jsPDF;
        else if (typeof jsPDF === 'undefined') { console.error('jsPDF library not found'); alert('Error: PDF generation library not loaded.'); }
    } catch (e) { console.error('Error initializing jsPDF:', e); }

    // Auto-populate form fields with active company data
    setTimeout(() => {
        autoPopulateArtistAgreementFields();
        autoPopulateInvoiceFields();
    }, 1000); // Delay to ensure all data is loaded

    // Initialize app with user session and document type
    initializeApp();

    // Load stored company information and active company data
    setTimeout(() => {
        loadActiveCompanyData();
        loadStoredCompanyInfo();
        displayActiveCompanyInfo();

        // Auto-populate invoice fields with active company data
        autoPopulateInvoiceFields();

        // Auto-populate artist agreement fields with active company data
        autoPopulateArtistAgreementFields();

        // Update company display fields
        const companyInfo = getStoredCompanyInfo();
        updateCompanyDisplayFields(companyInfo);

        // Initialize digital signature integration
        initializeSignatureIntegration();

        // Ensure logo is loaded for document generation
        const logoPreview = document.getElementById('logoPreview');
        if (logoPreview && !logoPreview.querySelector('img')) {
            loadActiveCompanyGenericLogo();
        }

        console.log('Company information and logo loaded on page load');
    }, 500);

    // Initialize tab-based navigation (new approach)
    // Check for URL parameters or stored document type
    const urlParams = new URLSearchParams(window.location.search);
    const urlDocType = urlParams.get('type');
    const storedDocType = localStorage.getItem('selectedDocumentType');
    const initialType = urlDocType || storedDocType || window.initialDocumentType || 'invoice';

    // Clear stored document type after use
    if (storedDocType) {
        localStorage.removeItem('selectedDocumentType');
    }

    switchDocumentTab(initialType); // Initialize with selected document type

    // Keep backward compatibility with dropdown
    const documentTypeSelect = document.getElementById('documentType');
    if (documentTypeSelect) {
        documentTypeSelect.addEventListener('change', changeDocumentType);
        documentTypeSelect.style.display = 'none'; // Hide the old dropdown
    }

    // Force add mock client profiles and update dropdown
    console.log('🔄 Initializing client profiles...');
    setTimeout(() => {
        addMockClientProfiles();
        updateClientProfileDropdown();
    }, 500);

    // Initialize company profile
    console.log('🏢 Initializing company profile...');
    setTimeout(() => {
        // First try to sync from dashboard if available
        syncCompanyProfileFromDashboard();
        // Then load the profile
        loadCompanyProfile();
    }, 300);

    // Set up client profile dropdown listener
    const clientDropdown = document.getElementById('clientProfileDropdown');
    if (clientDropdown) {
        clientDropdown.addEventListener('change', function() {
            if (this.value) {
                loadClientProfile(this.value);
            }
        });
        console.log('✅ Client dropdown listener added');
    } else {
        console.log('❌ Client dropdown not found');
    }

    // Initialize quick sharing section (hidden by default)
    const quickSharingSection = document.getElementById('quickSharingOptions');
    if (quickSharingSection) {
        quickSharingSection.style.display = 'none';
    }

    updateDueDate(); calculateTotals();
    const savedProfile = localStorage.getItem('companyProfile');
    if (!savedProfile) saveCompanyProfile(); else loadCompanyProfile();
    addMockClientProfiles(); addMockEventDetails(); addMockInvoiceItems(); addMockBankDetails();
    addDeleteButtonsToExistingItems(); clearContractBranding();
    initializeSignatureCanvas(); createDefaultLogo(); updateClientProfileDropdown();
    initializePaymentTermsHandler(); initializeContractLogoUpload();

    const saveCompanyBtn = document.getElementById('saveCompanyProfile');
    if (saveCompanyBtn) saveCompanyBtn.addEventListener('click', saveCompanyProfile);
    const saveClientBtn = document.getElementById('saveClientProfile');
    if (saveClientBtn) saveClientBtn.addEventListener('click', saveClientProfile);
    // Client dropdown listener already added in main initialization

// Company Profile Management Functions
function loadCompanyProfile() {
    console.log('🏢 Loading company profile...');

    // Check both old and new profile storage formats
    let savedProfile = localStorage.getItem('companyProfile');
    let profile = null;

    if (savedProfile) {
        profile = JSON.parse(savedProfile);
        console.log('✅ Company profile found:', profile);
    } else {
        // Check for old format profile
        const oldProfile = localStorage.getItem('companyInfo');
        if (oldProfile) {
            const oldData = JSON.parse(oldProfile);
            console.log('📄 Converting old profile format:', oldData);

            // Convert old format to new format
            profile = {
                companyName: oldData.name || '',
                companyEmail: oldData.email || '',
                companyPhone: oldData.phone || '',
                companyAddress: oldData.address || '',
                representativeName: oldData.representative?.name || '',
                companyRegNumber: oldData.regNumber || '',
                companyVatNumber: oldData.vatNumber || '',
                bankName: oldData.bankName || '',
                accountName: oldData.accountHolder || '',
                accountNumber: oldData.accountNumber || '',
                branchCode: oldData.branchCode || ''
            };

            // Save in new format
            localStorage.setItem('companyProfile', JSON.stringify(profile));
            console.log('✅ Profile converted and saved in new format');
        }
    }

    if (profile) {
        // Populate main form fields
        const companyName = document.getElementById('companyName');
        const companyEmail = document.getElementById('companyEmail');
        const companyPhone = document.getElementById('companyPhone');
        const companyAddress = document.getElementById('companyAddress');
        const representativeName = document.getElementById('representativeName');
        const companyRegNumber = document.getElementById('companyRegNumber');
        const companyVatNumber = document.getElementById('companyVatNumber');

        if (companyName) {
            companyName.value = profile.companyName || '';
            console.log('✅ Company name loaded:', profile.companyName);
        }
        if (companyEmail) {
            companyEmail.value = profile.companyEmail || '';
            console.log('✅ Company email loaded:', profile.companyEmail);
        }
        if (companyPhone) {
            companyPhone.value = profile.companyPhone || '';
            console.log('✅ Company phone loaded:', profile.companyPhone);
        }
        if (companyAddress) {
            companyAddress.value = profile.companyAddress || '';
            console.log('✅ Company address loaded:', profile.companyAddress);
        }
        if (representativeName) {
            representativeName.value = profile.representativeName || '';
            console.log('✅ Representative name loaded:', profile.representativeName);
        }
        if (companyRegNumber) {
            companyRegNumber.value = profile.companyRegNumber || '';
            console.log('✅ Registration number loaded:', profile.companyRegNumber);
        }
        if (companyVatNumber) {
            companyVatNumber.value = profile.companyVatNumber || '';
            console.log('✅ VAT number loaded:', profile.companyVatNumber);
        }

        // Also populate bank details if they exist
        const bankName = document.getElementById('bankName');
        const accountName = document.getElementById('accountName');
        const accountNumber = document.getElementById('accountNumber');
        const branchCode = document.getElementById('branchCode');

        if (bankName && profile.bankName) {
            bankName.value = profile.bankName;
            console.log('✅ Bank name loaded:', profile.bankName);
        }
        if (accountName && profile.accountName) {
            accountName.value = profile.accountName;
            console.log('✅ Account name loaded:', profile.accountName);
        }
        if (accountNumber && profile.accountNumber) {
            accountNumber.value = profile.accountNumber;
            console.log('✅ Account number loaded:', profile.accountNumber);
        }
        if (branchCode && profile.branchCode) {
            branchCode.value = profile.branchCode;
            console.log('✅ Branch code loaded:', profile.branchCode);
        }

        console.log('✅ Company profile loaded into form successfully');
    } else {
        console.log('⚠️ No company profile found, using default values');
        setDefaultCompanyProfile();
    }
}

function setDefaultCompanyProfile() {
    console.log('🏢 Setting default company profile...');
    const defaultProfile = {
        companyName: 'DocuGen Pro Solutions',
        companyEmail: '<EMAIL>',
        companyPhone: '+27 21 555 0123',
        companyAddress: '123 Business Park Drive\nSuite 456\nCape Town, 8001',
        representativeName: 'Sarah Johnson',
        companyRegNumber: '2023/789012/07',
        companyVatNumber: '**********',
        bankName: 'First National Bank (FNB)',
        accountName: 'DocuGen Pro Solutions (Pty) Ltd',
        accountNumber: '***********',
        branchCode: '250655'
    };

    // Save default profile
    localStorage.setItem('companyProfile', JSON.stringify(defaultProfile));

    // Load it into the form
    loadCompanyProfile();
}

function openCompanyProfileModal() {
    console.log('⚙️ Opening company profile modal...');
    const modal = document.getElementById('companyProfileModal');
    if (modal) {
        // Load current values into modal
        const savedProfile = localStorage.getItem('companyProfile');
        if (savedProfile) {
            const profile = JSON.parse(savedProfile);

            const modalFields = {
                modalCompanyName: profile.companyName,
                modalCompanyEmail: profile.companyEmail,
                modalCompanyPhone: profile.companyPhone,
                modalCompanyAddress: profile.companyAddress,
                modalRepresentativeName: profile.representativeName,
                modalCompanyRegNumber: profile.companyRegNumber,
                modalCompanyVatNumber: profile.companyVatNumber,
                modalBankName: profile.bankName,
                modalAccountName: profile.accountName,
                modalAccountNumber: profile.accountNumber,
                modalBranchCode: profile.branchCode
            };

            Object.keys(modalFields).forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field && modalFields[fieldId]) {
                    field.value = modalFields[fieldId];
                }
            });
        }

        modal.style.display = 'block';
    }
}

function closeCompanyProfileModal() {
    const modal = document.getElementById('companyProfileModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

function saveCompanyProfile() {
    console.log('💾 Saving company profile...');

    const profile = {
        companyName: document.getElementById('modalCompanyName').value,
        companyEmail: document.getElementById('modalCompanyEmail').value,
        companyPhone: document.getElementById('modalCompanyPhone').value,
        companyAddress: document.getElementById('modalCompanyAddress').value,
        representativeName: document.getElementById('modalRepresentativeName').value,
        companyRegNumber: document.getElementById('modalCompanyRegNumber').value,
        companyVatNumber: document.getElementById('modalCompanyVatNumber').value,
        bankName: document.getElementById('modalBankName').value,
        accountName: document.getElementById('modalAccountName').value,
        accountNumber: document.getElementById('modalAccountNumber').value,
        branchCode: document.getElementById('modalBranchCode').value
    };

    // Save to localStorage
    localStorage.setItem('companyProfile', JSON.stringify(profile));
    console.log('✅ Company profile saved:', profile);

    // Reload profile into main form
    loadCompanyProfile();

    // Close modal
    closeCompanyProfileModal();

    // Show success message
    alert('✅ Company profile saved successfully!');
}

// Function to sync company profile from dashboard if available
function syncCompanyProfileFromDashboard() {
    console.log('🔄 Checking for dashboard company profile...');

    // Check if there's company info from dashboard
    const dashboardCompanyInfo = localStorage.getItem('companyInfo');
    const dashboardBankDetails = localStorage.getItem('bankDetails');

    if (dashboardCompanyInfo) {
        const companyData = JSON.parse(dashboardCompanyInfo);
        console.log('📋 Found dashboard company info:', companyData);

        let bankData = null;
        if (dashboardBankDetails) {
            bankData = JSON.parse(dashboardBankDetails);
            console.log('🏦 Found dashboard bank details:', bankData);
        }

        // Convert to new profile format
        const syncedProfile = {
            companyName: companyData.name || '',
            companyEmail: companyData.email || '',
            companyPhone: companyData.phone || '',
            companyAddress: companyData.address || '',
            representativeName: companyData.representative?.name || '',
            companyRegNumber: companyData.regNumber || '',
            companyVatNumber: companyData.vatNumber || '',
            bankName: bankData?.bankName || '',
            accountName: bankData?.accountHolder || '',
            accountNumber: bankData?.accountNumber || '',
            branchCode: bankData?.branchCode || ''
        };

        // Save synced profile
        localStorage.setItem('companyProfile', JSON.stringify(syncedProfile));
        console.log('✅ Dashboard profile synced to company profile');

        return true;
    }

    console.log('ℹ️ No dashboard company profile found');
    return false;
}

    initializePDFPreviewModal();
});

// Initialize contract logo upload functionality
function initializeContractLogoUpload() {
    const contractLogoUpload = document.getElementById('contractLogoUpload');
    const contractLogoShape = document.getElementById('contractLogoShape');
    const contractLogoPreview = document.getElementById('contractLogoPreview');
    const contractLogoDisplay = document.getElementById('contractLogoDisplay');

    if (contractLogoUpload) {
        contractLogoUpload.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const imageUrl = e.target.result;
                    updateContractLogoDisplay(imageUrl);
                };
                reader.readAsDataURL(file);
            }
        });
    }

    if (contractLogoShape) {
        contractLogoShape.addEventListener('change', function() {
            const currentImage = contractLogoDisplay.querySelector('img');
            if (currentImage) {
                updateContractLogoDisplay(currentImage.src);
            }
        });
    }
}

function updateContractLogoDisplay(imageUrl) {
    const contractLogoPreview = document.getElementById('contractLogoPreview');
    const contractLogoDisplay = document.getElementById('contractLogoDisplay');
    const contractLogoShape = document.getElementById('contractLogoShape');
    const shape = contractLogoShape ? contractLogoShape.value : 'square';

    // Only update the contract header display (remove duplicate preview)
    if (contractLogoDisplay) {
        contractLogoDisplay.innerHTML = `
            <img src="${imageUrl}" alt="Contract Logo" class="contract-logo-display-img ${shape}" style="max-width: 200px; max-height: 150px;">
        `;
    }

    // Update preview area to show upload status instead of duplicate logo
    if (contractLogoPreview) {
        contractLogoPreview.innerHTML = `
            <div class="logo-upload-success">
                <span style="color: #28a745; font-weight: bold;">✅ Logo uploaded successfully!</span>
                <br>
                <small style="color: #666;">Logo appears in contract header above</small>
            </div>
        `;
    }
}

// Function to clear Brother Collective branding and make contract fields generic
function clearContractBranding() {
    // Replace hardcoded company details with editable form fields
    const contractParties = document.querySelectorAll('.contract-party');

    contractParties.forEach((party, index) => {
        if (index === 0) { // First party (Purchaser)
            // Check if it contains hardcoded Brother Collective data
            if (party.innerHTML.includes('BROTHER COLLECTIVE')) {
                party.innerHTML = `
                    <h4>Purchaser Details</h4>
                    <div class="form-group">
                        <label for="contractPurchaserCompany">Company Name:</label>
                        <input type="text" id="contractPurchaserCompany" placeholder="Enter company name" style="font-weight: bold;">
                        <small>(Hereinafter referred to as the "PURCHASER")</small>
                    </div>
                    <div class="form-group">
                        <label for="contractPurchaserAddress">Domiciled At (Address):</label>
                        <textarea id="contractPurchaserAddress" rows="2" placeholder="Enter company address"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="contractPurchaserRegNumber">Company Registration Number:</label>
                        <input type="text" id="contractPurchaserRegNumber" placeholder="e.g., 2024/123456/07">
                    </div>
                    <div class="form-group">
                        <label for="contractPurchaserRepresentative">Represented By:</label>
                        <input type="text" id="contractPurchaserRepresentative" placeholder="Enter representative name">
                    </div>
                `;

                // Auto-populate with stored company information after creating fields
                setTimeout(() => {
                    loadStoredCompanyInfo();
                }, 100);
            }
        } else if (index === 1) { // Second party (Agent/Artist)
            // Check if it needs to be updated to proper form fields
            if (!party.querySelector('#contractAgentCompany')) {
                party.innerHTML = `
                    <h4>Agent/Artist Details</h4>
                    <div class="form-group">
                        <label for="contractAgentCompany">Agent/Company Name:</label>
                        <input type="text" id="contractAgentCompany" placeholder="Enter agent or company name">
                        <small>(Hereinafter referred to as the "AGENT")</small>
                    </div>
                    <div class="form-group">
                        <label for="contractAgentDomiciledAt">Domiciled At (Address):</label>
                        <textarea id="contractAgentDomiciledAt" rows="2" placeholder="Enter agent's address"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="contractAgentRepresenting">Representing (Artist/Entity):</label>
                        <input type="text" id="contractAgentRepresenting" placeholder="Enter artist or entity name">
                        <small>(Hereinafter referred to as the "ARTIST")</small>
                    </div>
                `;
            }
        }
    });
}

function initializePDFPreviewModal() {
    const closePdfPreview = document.querySelector('.close-pdf-preview');
    if (closePdfPreview) {
        closePdfPreview.addEventListener('click', function() {
            document.getElementById('pdfPreviewModal').style.display = 'none';
            if (document.getElementById('pdfPreviewFrame').src) URL.revokeObjectURL(document.getElementById('pdfPreviewFrame').src);
        });
    }
    const downloadPdfBtn = document.getElementById('downloadPdf');
    if (downloadPdfBtn) {
        downloadPdfBtn.addEventListener('click', function() {
            if (window.generatedPDF) {
                const downloadLink = document.createElement('a');
                downloadLink.href = URL.createObjectURL(window.generatedPDF.blob);
                downloadLink.download = window.generatedPDF.fileName;
                document.body.appendChild(downloadLink); downloadLink.click(); document.body.removeChild(downloadLink);
                document.getElementById('pdfPreviewModal').style.display = 'none';
            }
        });
    }
    const sharePdfBtn = document.getElementById('sharePdf');
    if (sharePdfBtn) {
        sharePdfBtn.addEventListener('click', function() {
            showDocumentSharing();
        });
    }
    window.addEventListener('click', function(event) {
        const modal = document.getElementById('pdfPreviewModal');
        if (event.target === modal) {
            modal.style.display = 'none';
            if (document.getElementById('pdfPreviewFrame').src) URL.revokeObjectURL(document.getElementById('pdfPreviewFrame').src);
        }
    });
}

// Function to load stored company information into forms
function loadStoredCompanyInfo() {
    const companyInfo = getStoredCompanyInfo();
    const bankDetails = getStoredBankDetails();

    if (companyInfo) {
        // Populate "From" section (Invoice company details)
        const fromCompanyField = document.getElementById('fromCompany');
        const fromRegNumberField = document.getElementById('fromRegNumber');
        const fromVatNumberField = document.getElementById('fromVatNumber');
        const fromEmailField = document.getElementById('fromEmail');
        const fromAddressField = document.getElementById('fromAddress');
        const fromContactField = document.getElementById('fromContact');
        const fromPhoneField = document.getElementById('fromPhone');

        if (fromCompanyField) fromCompanyField.value = companyInfo.name || '';
        if (fromRegNumberField) fromRegNumberField.value = companyInfo.regNumber || '';
        if (fromVatNumberField) fromVatNumberField.value = companyInfo.vatNumber || '';
        if (fromEmailField) fromEmailField.value = companyInfo.email || '';
        if (fromAddressField) fromAddressField.value = companyInfo.address || '';
        if (fromContactField) fromContactField.value = companyInfo.representative?.name || '';
        if (fromPhoneField) fromPhoneField.value = companyInfo.phone || '';

        // Also populate the new invoice form field names
        const companyNameField = document.getElementById('companyName');
        const companyEmailField = document.getElementById('companyEmail');
        const companyPhoneField = document.getElementById('companyPhone');
        const companyAddressField = document.getElementById('companyAddress');
        const representativeNameField = document.getElementById('representativeName');
        const companyRegNumberField = document.getElementById('companyRegNumber');
        const companyVatNumberField = document.getElementById('companyVatNumber');

        if (companyNameField) companyNameField.value = companyInfo.companyName || companyInfo.name || '';
        if (companyEmailField) companyEmailField.value = companyInfo.companyEmail || companyInfo.email || '';
        if (companyPhoneField) companyPhoneField.value = companyInfo.companyPhone || companyInfo.phone || '';
        if (companyAddressField) companyAddressField.value = companyInfo.companyAddress || companyInfo.address || '';
        if (representativeNameField) representativeNameField.value = companyInfo.representativeName || companyInfo.representative?.name || '';
        if (companyRegNumberField) companyRegNumberField.value = companyInfo.registrationNumber || companyInfo.regNumber || '';
        if (companyVatNumberField) companyVatNumberField.value = companyInfo.vatNumber || '';

        // Populate signature name
        const signatureNameField = document.getElementById('signatureName');
        if (signatureNameField) {
            const repName = companyInfo.representative?.name || '';
            const repTitle = companyInfo.representative?.title || '';
            const signatureName = repTitle ? `${repName}, ${repTitle}` : repName;
            signatureNameField.value = signatureName;
        }

        // Populate contract fields if they exist
        const contractPurchaserCompany = document.getElementById('contractPurchaserCompany');
        const contractPurchaserAddress = document.getElementById('contractPurchaserAddress');
        const contractPurchaserRegNumber = document.getElementById('contractPurchaserRegNumber');
        const contractPurchaserRepresentative = document.getElementById('contractPurchaserRepresentative');

        if (contractPurchaserCompany) contractPurchaserCompany.value = companyInfo.name || '';
        if (contractPurchaserAddress) contractPurchaserAddress.value = companyInfo.address || '';
        if (contractPurchaserRegNumber) contractPurchaserRegNumber.value = companyInfo.regNumber || '';
        if (contractPurchaserRepresentative) contractPurchaserRepresentative.value = companyInfo.representative?.name || '';
    }

    if (bankDetails) {
        // Populate banking fields
        const bankNameField = document.getElementById('bankName');
        const accountNumberField = document.getElementById('accountNumber');
        const branchCodeField = document.getElementById('branchCode');
        const accountNameField = document.getElementById('accountName');

        if (bankNameField) bankNameField.value = bankDetails.bankName || '';
        if (accountNumberField) accountNumberField.value = bankDetails.accountNumber || '';
        if (branchCodeField) branchCodeField.value = bankDetails.branchCode || '';
        if (accountNameField) accountNameField.value = bankDetails.accountHolder || bankDetails.accountName || '';
    }

    // Load company logo if available
    const storedLogo = getStoredCompanyLogo();
    if (storedLogo) {
        // Set the main logo upload preview
        const logoPreview = document.getElementById('logoPreview');
        const logoShape = document.getElementById('logoShape');

        if (logoPreview) {
            logoPreview.innerHTML = '';
            const img = document.createElement('img');

            // Handle both old format (object with data property) and new format (base64 string)
            const logoSrc = typeof storedLogo === 'string' ? storedLogo : storedLogo.data;
            const logoShapeValue = typeof storedLogo === 'string' ? 'square' : (storedLogo.shape || 'square');

            img.src = logoSrc;
            logoPreview.appendChild(img);
            logoPreview.className = `logo-preview ${logoShapeValue}`;

            // Set the global logoImage for PDF generation
            const logoImg = new Image();
            logoImg.onload = function() {
                window.logoImage = logoImg;
                console.log('✅ Company logo loaded for PDF generation:', logoSrc.substring(0, 50) + '...');
            };
            logoImg.src = logoSrc;
        }

        if (logoShape) {
            const logoShapeValue = typeof storedLogo === 'string' ? 'square' : (storedLogo.shape || 'square');
            logoShape.value = logoShapeValue;
        }

        // Also set contract logo if in contract mode
        const contractLogoDisplay = document.getElementById('contractLogoDisplay');
        if (contractLogoDisplay) {
            contractLogoDisplay.innerHTML = '';
            const contractImg = document.createElement('img');

            const logoSrc = typeof storedLogo === 'string' ? storedLogo : storedLogo.data;
            const logoShapeValue = typeof storedLogo === 'string' ? 'square' : (storedLogo.shape || 'square');

            contractImg.src = logoSrc;
            contractImg.style.width = '120px';
            contractImg.style.height = '120px';
            contractImg.style.objectFit = 'cover';
            contractImg.style.borderRadius = logoShapeValue === 'round' ? '50%' : '12px';
            contractImg.style.border = '2px solid #ddd';
            contractImg.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
            contractImg.style.imageRendering = '-webkit-optimize-contrast';
            contractImg.style.filter = 'contrast(1.1) brightness(1.05)';
            contractLogoDisplay.appendChild(contractImg);
        }

        console.log('✅ Company logo loaded into document generator');
    } else {
        // If no stored logo, try to load the generic logo for the active company
        loadActiveCompanyGenericLogo();
    }

    console.log('Company information loaded into forms');
}

// Test companies data (same as in dashboard)
const testCompanies = {
    'docugen-pro': {
        name: 'DocuGen Pro Solutions Ltd',
        regNumber: '2024/123456/07',
        vatNumber: '4123456789',
        address: '123 Business Street\nBusiness District\nJohannesburg, 2000\nSouth Africa',
        phone: '+27 11 123 4567',
        email: '<EMAIL>',
        website: 'https://www.docugenpro.com',
        representative: {
            name: 'John Smith',
            title: 'Managing Director',
            email: '<EMAIL>'
        }
    },
    'bongomaffin': {
        name: 'Bongomaffin',
        regNumber: '2023/987654/07',
        vatNumber: '4987654321',
        address: '456 Music Avenue\nMelody Park\nCape Town, 8001\nSouth Africa',
        phone: '+27 21 987 6543',
        email: '<EMAIL>',
        website: 'https://www.bongomaffin.co.za',
        representative: {
            name: 'Stoan Seate',
            title: 'Creative Director',
            email: '<EMAIL>'
        }
    },
    'benjamin-music': {
        name: 'Benjamin Music Initiatives',
        regNumber: '2022/555666/07',
        vatNumber: '4555666777',
        address: '789 Harmony Street\nRhythm District\nDurban, 4001\nSouth Africa',
        phone: '+27 31 555 7777',
        email: '<EMAIL>',
        website: 'https://www.benjaminmusic.co.za',
        representative: {
            name: 'Benjamin Mthembu',
            title: 'Founder & CEO',
            email: '<EMAIL>'
        }
    }
};

// Load active company data from test companies
function loadActiveCompanyData() {
    console.log('🔄 Loading active company data...');

    // Get active company key from dashboard
    const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';
    const activeCompany = testCompanies[activeCompanyKey];

    if (!activeCompany) {
        console.log('Active company not found in test companies data');
        return;
    }

    console.log(`📋 Loading data for active company: ${activeCompany.name}`);

    // Update document headers/titles to reflect active company
    updateDocumentHeadersForActiveCompany(activeCompany);

    // Display active company info in the UI
    displayActiveCompanyInfo();
}

// Display active company information in the document generator
function displayActiveCompanyInfo() {
    const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';
    const activeCompany = testCompanies[activeCompanyKey];

    if (!activeCompany) return;

    // Create or update active company indicator
    let indicator = document.getElementById('activeCompanyIndicator');
    if (!indicator) {
        indicator = document.createElement('div');
        indicator.id = 'activeCompanyIndicator';
        indicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, #4a9eff 0%, #667eea 100%);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        `;

        // Add click handler to go back to dashboard
        indicator.onclick = () => {
            if (confirm('Return to dashboard to switch companies?')) {
                window.open('dashboard2.html', '_blank');
            }
        };

        document.body.appendChild(indicator);
    }

    // Get company icon
    const icons = {
        'docugen-pro': '📄',
        'bongomaffin': '🎵',
        'benjamin-music': '🎼'
    };

    const icon = icons[activeCompanyKey] || '🏢';
    indicator.innerHTML = `${icon} ${activeCompany.name}`;

    console.log(`✅ Active company indicator updated: ${activeCompany.name}`);
}

// Update document headers to show active company
function updateDocumentHeadersForActiveCompany(activeCompany) {
    // Get company icon based on company key
    const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';
    const icons = {
        'docugen-pro': '📄',
        'bongomaffin': '🎵',
        'benjamin-music': '🎼'
    };

    const icon = icons[activeCompanyKey] || '🏢';

    // Update page title if exists
    const pageTitle = document.querySelector('title');
    if (pageTitle) {
        pageTitle.textContent = `${icon} ${activeCompany.name} - DocuGen Pro`;
    }

    // Add active company indicator to header if exists
    const header = document.querySelector('.header, .app-header, h1');
    if (header) {
        // Remove existing company indicator
        const existingIndicator = header.querySelector('.active-company-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        // Add new company indicator
        const indicator = document.createElement('span');
        indicator.className = 'active-company-indicator';
        indicator.style.cssText = `
            font-size: 12px;
            color: #666;
            margin-left: 10px;
            padding: 4px 8px;
            background: #f8f9fa;
            border-radius: 12px;
            border: 1px solid #dee2e6;
        `;
        indicator.textContent = `${icon} ${activeCompany.name}`;
        header.appendChild(indicator);
    }

    console.log(`🎯 Document headers updated for: ${activeCompany.name}`);

    // Load company logo if available
    loadDevelopmentLogo(activeCompany);

    // Auto-populate form fields with new company data
    setTimeout(() => {
        autoPopulateArtistAgreementFields();
        autoPopulateInvoiceFields();
    }, 200); // Small delay to ensure data is loaded
}

// Load development logos for companies
function loadDevelopmentLogo(company) {
    if (!company) return;

    // Get development logos
    const developmentLogos = localStorage.getItem('developmentLogos');
    if (!developmentLogos) return;

    const logos = JSON.parse(developmentLogos);
    const companyLogo = logos[company.name];

    if (companyLogo) {
        // Update logo preview in the main application
        const logoPreview = document.getElementById('logoPreview');
        if (logoPreview) {
            logoPreview.innerHTML = `<img src="${companyLogo}" alt="${company.name} Logo" style="max-width: 100%; max-height: 100px;">`;
            console.log(`🎨 Loaded development logo for: ${company.name}`);
        }

        // Store logo for document generation
        localStorage.setItem('currentCompanyLogo', companyLogo);
    }
}

// Auto-populate Artist Agreement form fields with active company data
function autoPopulateArtistAgreementFields() {
    console.log('🔄 Auto-populating Artist Agreement fields with active company data...');

    const storedCompanyInfo = getStoredCompanyInfo();

    if (storedCompanyInfo) {
        console.log('📋 Found stored company info for Artist Agreement:', storedCompanyInfo);

        // Update company name field - be aggressive in replacing hardcoded values
        const contractCompanyNameField = document.getElementById('contractCompanyName');
        if (contractCompanyNameField) {
            const currentValue = contractCompanyNameField.value;
            const isHardcoded = currentValue === 'BROTHER COLLECTIVE PTY (LTD)' ||
                               currentValue === '' ||
                               currentValue.includes('BROTHER COLLECTIVE');

            if (isHardcoded || !currentValue) {
                contractCompanyNameField.value = storedCompanyInfo.name || storedCompanyInfo.companyName || '';
                console.log('✅ Updated contract company name:', contractCompanyNameField.value);
            }
        }

        // Update purchaser address field
        const purchaserAddressField = document.getElementById('purchaserAddress');
        if (purchaserAddressField) {
            const currentValue = purchaserAddressField.value;
            const isHardcoded = currentValue.includes('181 Bryanston Drive') ||
                               currentValue.includes('Bryanston') ||
                               currentValue === '';

            if (isHardcoded) {
                purchaserAddressField.value = storedCompanyInfo.address || storedCompanyInfo.companyAddress || '';
                console.log('✅ Updated purchaser address:', purchaserAddressField.value);
            }
        }

        // Update registration number field
        const purchaserRegNumberField = document.getElementById('purchaserRegNumber');
        if (purchaserRegNumberField) {
            const currentValue = purchaserRegNumberField.value;
            const isHardcoded = currentValue === '2024/772455/07' ||
                               currentValue === '2023/123456/07' ||
                               currentValue === '';

            if (isHardcoded) {
                purchaserRegNumberField.value = storedCompanyInfo.regNumber || storedCompanyInfo.registrationNumber || '';
                console.log('✅ Updated purchaser registration number:', purchaserRegNumberField.value);
            }
        }

        // Update representative field
        const purchaserRepresentativeField = document.getElementById('purchaserRepresentative');
        if (purchaserRepresentativeField) {
            const currentValue = purchaserRepresentativeField.value;
            const isHardcoded = currentValue === 'LIZIWE KWANINI' ||
                               currentValue === '' ||
                               currentValue.includes('LIZIWE');

            if (isHardcoded) {
                const repName = storedCompanyInfo.representative?.name || storedCompanyInfo.representativeName || '';
                purchaserRepresentativeField.value = repName;
                console.log('✅ Updated purchaser representative:', repName);
            }
        }

        // Also try the other representative field ID
        const contractPurchaserRepresentativeField = document.getElementById('contractPurchaserRepresentative');
        if (contractPurchaserRepresentativeField) {
            const currentValue = contractPurchaserRepresentativeField.value;
            const isHardcoded = currentValue === 'LIZIWE KWANINI' ||
                               currentValue === '' ||
                               currentValue.includes('LIZIWE');

            if (isHardcoded) {
                const repName = storedCompanyInfo.representative?.name || storedCompanyInfo.representativeName || '';
                contractPurchaserRepresentativeField.value = repName;
                console.log('✅ Updated contract purchaser representative:', repName);
            }
        }

        // Update invoice address field
        const invoiceAddressField = document.getElementById('invoiceAddress');
        if (invoiceAddressField) {
            const currentValue = invoiceAddressField.value;
            const isHardcoded = currentValue.includes('181 Bryanston Drive') ||
                               currentValue.includes('Brother Collective') ||
                               currentValue.includes('2024/772455/07') ||
                               currentValue === '';

            if (isHardcoded) {
                const companyName = storedCompanyInfo.name || storedCompanyInfo.companyName || '';
                const companyAddress = storedCompanyInfo.address || storedCompanyInfo.companyAddress || '';
                const regNumber = storedCompanyInfo.regNumber || storedCompanyInfo.registrationNumber || '';
                const newInvoiceAddress = `${companyName}\n${companyAddress}\n${regNumber}`;
                invoiceAddressField.value = newInvoiceAddress;
                console.log('✅ Updated invoice address:', newInvoiceAddress);
            }
        }

        console.log(`✅ Auto-populated Artist Agreement fields with: ${storedCompanyInfo.name || storedCompanyInfo.companyName}`);
    } else {
        console.log('⚠️ No stored company info found for Artist Agreement auto-population');
    }
}

// Auto-populate Invoice form fields with active company data
function autoPopulateInvoiceFields() {
    console.log('🔄 Auto-populating invoice fields with active company data...');

    const storedCompanyInfo = getStoredCompanyInfo();

    if (storedCompanyInfo) {
        console.log('📋 Found stored company info:', storedCompanyInfo);

        // Update company name field - be more aggressive in replacing placeholder values
        const companyNameField = document.getElementById('companyName');
        if (companyNameField) {
            const currentValue = companyNameField.value;
            const isPlaceholder = currentValue === 'Your Company Name' || currentValue === '' ||
                                 companyNameField.placeholder === 'Your Company Name' ||
                                 currentValue === companyNameField.placeholder;

            if (isPlaceholder || !currentValue) {
                companyNameField.value = storedCompanyInfo.name || storedCompanyInfo.companyName || '';
                console.log('✅ Updated company name:', companyNameField.value);
            }
        }

        // Update company email field
        const companyEmailField = document.getElementById('companyEmail');
        if (companyEmailField) {
            const currentValue = companyEmailField.value;
            const isPlaceholder = currentValue === '<EMAIL>' || currentValue === '' ||
                                 companyEmailField.placeholder === '<EMAIL>' ||
                                 currentValue === companyEmailField.placeholder;

            if (isPlaceholder || !currentValue) {
                companyEmailField.value = storedCompanyInfo.email || storedCompanyInfo.companyEmail || '';
                console.log('✅ Updated company email:', companyEmailField.value);
            }
        }

        // Update company phone field
        const companyPhoneField = document.getElementById('companyPhone');
        if (companyPhoneField) {
            const currentValue = companyPhoneField.value;
            const isPlaceholder = currentValue === '+27 21 555 0123' || currentValue === '' ||
                                 companyPhoneField.placeholder === '+27 21 555 0123' ||
                                 currentValue === companyPhoneField.placeholder;

            if (isPlaceholder || !currentValue) {
                companyPhoneField.value = storedCompanyInfo.phone || storedCompanyInfo.companyPhone || '';
                console.log('✅ Updated company phone:', companyPhoneField.value);
            }
        }

        // Update company address field
        const companyAddressField = document.getElementById('companyAddress');
        if (companyAddressField) {
            const currentValue = companyAddressField.value;
            const isPlaceholder = currentValue.includes('123 Business Street') ||
                                 currentValue.includes('Suite 456') ||
                                 currentValue === '' ||
                                 currentValue === companyAddressField.placeholder;

            if (isPlaceholder || !currentValue) {
                companyAddressField.value = storedCompanyInfo.address || storedCompanyInfo.companyAddress || '';
                console.log('✅ Updated company address:', companyAddressField.value);
            }
        }

        // Update representative name field
        const representativeNameField = document.getElementById('representativeName');
        if (representativeNameField) {
            const currentValue = representativeNameField.value;
            const isPlaceholder = currentValue === 'Your Name' || currentValue === '' ||
                                 representativeNameField.placeholder === 'Your Name' ||
                                 currentValue === representativeNameField.placeholder;

            if (isPlaceholder || !currentValue) {
                const repName = storedCompanyInfo.representative?.name || storedCompanyInfo.representativeName || '';
                representativeNameField.value = repName;
                console.log('✅ Updated representative name:', repName);
            }
        }

        // Update registration number field
        const regNumberField = document.getElementById('regNumber');
        if (regNumberField) {
            const currentValue = regNumberField.value;
            const isPlaceholder = currentValue === '2023/123456/07' || currentValue === '' ||
                                 regNumberField.placeholder === '2023/123456/07' ||
                                 currentValue === regNumberField.placeholder;

            if (isPlaceholder || !currentValue) {
                regNumberField.value = storedCompanyInfo.regNumber || storedCompanyInfo.registrationNumber || '';
                console.log('✅ Updated registration number:', regNumberField.value);
            }
        }

        // Update VAT number field
        const vatNumberField = document.getElementById('vatNumber');
        if (vatNumberField) {
            const currentValue = vatNumberField.value;
            const isPlaceholder = currentValue === '4123456789' || currentValue === '' ||
                                 vatNumberField.placeholder === '4123456789' ||
                                 currentValue === vatNumberField.placeholder;

            if (isPlaceholder || !currentValue) {
                vatNumberField.value = storedCompanyInfo.vatNumber || '';
                console.log('✅ Updated VAT number:', vatNumberField.value);
            }
        }

        console.log(`✅ Auto-populated Invoice fields with: ${storedCompanyInfo.name || storedCompanyInfo.companyName}`);

        // Also update the display fields
        updateCompanyDisplayFields(storedCompanyInfo);
    } else {
        console.log('⚠️ No stored company info found for auto-population');

        // Show "No data" in display fields
        updateCompanyDisplayFields(null);
    }

    // Also auto-populate bank details
    const storedBankDetails = getStoredBankDetails();
    if (storedBankDetails) {
        const bankNameField = document.getElementById('bankName');
        const accountNameField = document.getElementById('accountName');
        const accountNumberField = document.getElementById('accountNumber');
        const branchCodeField = document.getElementById('branchCode');

        if (bankNameField && !bankNameField.value) {
            bankNameField.value = storedBankDetails.bankName || '';
        }
        if (accountNameField && !accountNameField.value) {
            accountNameField.value = storedBankDetails.accountName || storedBankDetails.accountHolder || '';
        }
        if (accountNumberField && !accountNumberField.value) {
            accountNumberField.value = storedBankDetails.accountNumber || '';
        }
        if (branchCodeField && !branchCodeField.value) {
            branchCodeField.value = storedBankDetails.branchCode || '';
        }

        console.log(`🏦 Auto-populated Bank details for: ${storedBankDetails.bankName}`);
    }
}

// Update the company display fields (read-only display)
function updateCompanyDisplayFields(companyInfo) {
    console.log('🔄 Updating company display fields...');

    const displayFields = {
        'displayCompanyName': companyInfo?.name || companyInfo?.companyName || 'Not set',
        'displayCompanyEmail': companyInfo?.email || companyInfo?.companyEmail || 'Not set',
        'displayCompanyPhone': companyInfo?.phone || companyInfo?.companyPhone || 'Not set',
        'displayCompanyRegNumber': companyInfo?.regNumber || companyInfo?.registrationNumber || 'Not set',
        'displayCompanyVatNumber': companyInfo?.vatNumber || 'Not set',
        'displayCompanyAddress': companyInfo?.address || companyInfo?.companyAddress || 'Not set',
        'displayRepresentativeName': companyInfo?.representative?.name || companyInfo?.representativeName || 'Not set'
    };

    // Update each display field
    Object.entries(displayFields).forEach(([fieldId, value]) => {
        const element = document.getElementById(fieldId);
        if (element) {
            element.textContent = value;

            // Add visual styling based on whether data is available
            if (value === 'Not set') {
                element.style.color = '#6c757d';
                element.style.fontStyle = 'italic';
            } else {
                element.style.color = '#333';
                element.style.fontStyle = 'normal';
            }
        }
    });

    console.log('✅ Company display fields updated');
}

// ========================================
// DIGITAL SIGNATURE INTEGRATION SYSTEM
// ========================================

// Global signature integration variables
let selectedSignatures = {
    artist: null,
    client: null,
    purchaser: null,
    general: null
};

// Initialize signature integration system
function initializeSignatureIntegration() {
    console.log('🔄 Initializing signature integration system...');

    // Load saved signatures and populate dropdowns
    loadSignatureDropdowns();

    // Set up signature block detection and auto-population
    setupSignatureBlockDetection();

    // Initialize signature preview functionality
    initializeSignaturePreviews();

    console.log('✅ Signature integration system initialized');
}

// Load saved signatures into dropdown menus
function loadSignatureDropdowns() {
    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
    console.log('📋 Loading', signatures.length, 'saved signatures into dropdowns');

    // Find all signature select elements
    const signatureSelects = [
        'artistSignatureSelect',
        'clientSignatureSelect',
        'purchaserSignatureSelect',
        'generalSignatureSelect'
    ];

    signatureSelects.forEach(selectId => {
        const selectElement = document.getElementById(selectId);
        if (selectElement) {
            // Clear existing options except the first one
            selectElement.innerHTML = '<option value="">Select a saved signature...</option>';

            // Add signature options
            signatures.forEach(signature => {
                const option = document.createElement('option');
                option.value = signature.id;
                option.textContent = `${signature.name} (${signature.type})`;
                option.dataset.signatureData = signature.data;
                selectElement.appendChild(option);
            });

            console.log(`✅ Loaded signatures into ${selectId}`);
        }
    });
}

// Set up automatic signature block detection and population
function setupSignatureBlockDetection() {
    console.log('🔍 Setting up signature block detection...');

    // Find all signature-related input fields and text areas
    const signatureFields = document.querySelectorAll(`
        input[id*="signature" i],
        input[id*="Signature" i],
        textarea[id*="signature" i],
        textarea[id*="Signature" i],
        input[placeholder*="signature" i],
        input[placeholder*="Signature" i],
        textarea[placeholder*="signature" i],
        textarea[placeholder*="Signature" i]
    `);

    console.log(`📝 Found ${signatureFields.length} signature fields for auto-population`);

    // Add click listeners to signature fields
    signatureFields.forEach(field => {
        field.addEventListener('click', () => handleSignatureFieldClick(field));
        field.addEventListener('focus', () => handleSignatureFieldClick(field));

        // Add visual indicator that this field supports signatures
        field.style.borderLeft = '3px solid #007bff';
        field.title = 'Click to select a digital signature';
    });
}

// Handle signature field click/focus
function handleSignatureFieldClick(field) {
    console.log('🖊️ Signature field clicked:', field.id || field.placeholder);

    // Show signature selection modal
    showSignatureSelectionModal(field);
}

// Show signature selection modal
function showSignatureSelectionModal(targetField) {
    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');

    if (signatures.length === 0) {
        alert('No saved signatures found. Please create signatures in the Signature Tools first.');
        return;
    }

    // Create modal HTML
    const modalHTML = `
        <div id="signatureSelectionModal" class="signature-modal">
            <div class="signature-modal-content">
                <div class="signature-modal-header">
                    <h3>🖊️ Select Digital Signature</h3>
                    <span class="signature-modal-close" onclick="closeSignatureSelectionModal()">&times;</span>
                </div>
                <div class="signature-modal-body">
                    <p>Choose a signature to insert into the field:</p>
                    <div class="signature-grid">
                        ${signatures.map(signature => `
                            <div class="signature-option" onclick="selectSignatureForField('${signature.id}', '${targetField.id}')">
                                <img src="${signature.data}" alt="${signature.name}" class="signature-preview">
                                <div class="signature-info">
                                    <strong>${signature.name}</strong>
                                    <small>${signature.type}</small>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                    <div class="signature-modal-actions">
                        <button type="button" class="btn-secondary" onclick="closeSignatureSelectionModal()">Cancel</button>
                        <button type="button" class="btn-primary" onclick="openSignatureTools()">Create New Signature</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if present
    const existingModal = document.getElementById('signatureSelectionModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Store reference to target field
    window.currentSignatureTargetField = targetField;

    console.log('✅ Signature selection modal opened');
}

// Close signature selection modal
function closeSignatureSelectionModal() {
    const modal = document.getElementById('signatureSelectionModal');
    if (modal) {
        modal.remove();
    }
    window.currentSignatureTargetField = null;
}

// Select signature for field
function selectSignatureForField(signatureId, fieldId) {
    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
    const signature = signatures.find(s => s.id == signatureId);
    const targetField = document.getElementById(fieldId) || window.currentSignatureTargetField;

    if (!signature || !targetField) {
        console.error('❌ Signature or target field not found');
        return;
    }

    console.log(`🖊️ Applying signature "${signature.name}" to field "${fieldId}"`);

    // Determine how to populate the field based on its type and context
    if (targetField.tagName.toLowerCase() === 'textarea') {
        // For textarea fields, replace signature lines with signature name
        let currentValue = targetField.value;

        // Replace signature lines with actual signature name
        currentValue = currentValue.replace(/_+\s*Signature\s*/gi, signature.name);
        currentValue = currentValue.replace(/_+\s*\n\s*Signature/gi, signature.name);
        currentValue = currentValue.replace(/Signature\s*_+/gi, `Signature: ${signature.name}`);

        targetField.value = currentValue;
    } else {
        // For input fields, just set the signature name
        targetField.value = signature.name;
    }

    // Store signature data for PDF generation
    targetField.dataset.signatureData = signature.data;
    targetField.dataset.signatureId = signature.id;
    targetField.dataset.signatureName = signature.name;

    // Add visual feedback
    targetField.style.borderLeft = '3px solid #28a745';
    targetField.style.backgroundColor = '#f8fff8';

    // Close modal
    closeSignatureSelectionModal();

    // Update any open document previews to show the signature
    updateDocumentPreviewWithSignatures();

    console.log(`✅ Signature "${signature.name}" applied to field`);
}

// Initialize signature previews for existing dropdowns
function initializeSignaturePreviews() {
    console.log('🔄 Initializing signature previews...');

    // Set up preview functionality for existing signature selects
    const previewConfigs = [
        { selectId: 'artistSignatureSelect', previewId: 'artistSignaturePreview', imgId: 'artistSignatureImg' },
        { selectId: 'clientSignatureSelect', previewId: 'clientSignaturePreview', imgId: 'clientSignatureImg' }
    ];

    previewConfigs.forEach(config => {
        const selectElement = document.getElementById(config.selectId);
        if (selectElement) {
            selectElement.addEventListener('change', () => {
                previewSelectedSignature(config.selectId.replace('SignatureSelect', ''));
            });
        }
    });

    console.log('✅ Signature previews initialized');
}

// Preview selected signature (enhanced version)
function previewSelectedSignature(type) {
    const selectElement = document.getElementById(`${type}SignatureSelect`);
    const previewContainer = document.getElementById(`${type}SignaturePreview`);
    const previewImg = document.getElementById(`${type}SignatureImg`);

    if (!selectElement || !previewContainer || !previewImg) {
        console.log(`⚠️ Preview elements not found for type: ${type}`);
        return;
    }

    const selectedOption = selectElement.options[selectElement.selectedIndex];

    if (selectedOption.value && selectedOption.dataset.signatureData) {
        // Show preview
        previewImg.src = selectedOption.dataset.signatureData;
        previewContainer.style.display = 'block';

        // Store selected signature
        selectedSignatures[type] = {
            id: selectedOption.value,
            name: selectedOption.textContent,
            data: selectedOption.dataset.signatureData
        };

        console.log(`✅ Preview updated for ${type} signature:`, selectedOption.textContent);

        // Auto-populate related signature fields
        autoPopulateSignatureFields(type, selectedSignatures[type]);

        // Update any open document previews to show the signature
        updateDocumentPreviewWithSignatures();
    } else {
        // Hide preview
        previewContainer.style.display = 'none';
        selectedSignatures[type] = null;

        console.log(`🔄 Preview cleared for ${type} signature`);

        // Update any open document previews to remove the signature
        updateDocumentPreviewWithSignatures();
    }
}

// Update document preview with current signatures
function updateDocumentPreviewWithSignatures() {
    // Check if preview modal is currently open
    const previewModal = document.getElementById('pdfPreviewModal');
    if (previewModal && previewModal.style.display === 'block') {
        console.log('🔄 Updating document preview with current signatures...');

        // Determine current document type and regenerate preview
        const currentDocumentMode = window.documentMode || 'invoice';

        // Regenerate preview based on document type
        switch (currentDocumentMode) {
            case 'artist-agreement':
                setTimeout(() => previewArtistAgreement(), 100);
                break;
            case 'invoice':
                setTimeout(() => previewInvoice(), 100);
                break;
            case 'quotation':
                setTimeout(() => previewQuotation(), 100);
                break;
            case 'contract':
                setTimeout(() => previewContract(), 100);
                break;
            case 'annexure':
                setTimeout(() => previewAnnexure(), 100);
                break;
            default:
                console.log('No preview update needed for document type:', currentDocumentMode);
        }
    }
}

// Auto-populate signature fields based on type
function autoPopulateSignatureFields(type, signature) {
    console.log(`🔄 Auto-populating ${type} signature fields...`);

    // Define field mappings for different signature types
    const fieldMappings = {
        artist: [
            'artistSignature',
            'contractForArtist',
            'artistSignatureBlock'
        ],
        client: [
            'purchaserSignature',
            'contractForPurchaser',
            'clientSignatureBlock'
        ],
        purchaser: [
            'purchaserSignature',
            'contractForPurchaser'
        ]
    };

    const fieldsToUpdate = fieldMappings[type] || [];

    fieldsToUpdate.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field && signature) {
            if (field.tagName.toLowerCase() === 'textarea') {
                // For textarea fields, replace signature placeholders
                let currentValue = field.value;
                currentValue = currentValue.replace(/_+\s*Signature\s*/gi, signature.name);
                currentValue = currentValue.replace(/Signature\s*_+/gi, `Signature: ${signature.name}`);
                field.value = currentValue;
            } else {
                // For input fields, set the signature name
                field.value = signature.name;
            }

            // Store signature data for PDF generation
            field.dataset.signatureData = signature.data;
            field.dataset.signatureId = signature.id;
            field.dataset.signatureName = signature.name;

            // Visual feedback
            field.style.borderLeft = '3px solid #28a745';
            field.style.backgroundColor = '#f8fff8';

            console.log(`✅ Auto-populated field: ${fieldId}`);
        }
    });

    // Update any open document previews to show the signature
    updateDocumentPreviewWithSignatures();
}

// Refresh signature integration (call when new signatures are saved)
function refreshSignatureIntegration() {
    console.log('🔄 Refreshing signature integration...');

    // Reload signature dropdowns
    loadSignatureDropdowns();

    // Re-initialize signature block detection for any new fields
    setupSignatureBlockDetection();

    console.log('✅ Signature integration refreshed');
}

// Open signature tools (placeholder - should open signature tools modal/page)
function openSignatureTools() {
    console.log('🔧 Opening signature tools...');

    // Check if we're in dashboard context
    if (window.location.href.includes('dashboard')) {
        // If in dashboard, show signature tools section
        const signatureToolsSection = document.getElementById('signatureToolsSection');
        if (signatureToolsSection) {
            signatureToolsSection.style.display = 'block';
            signatureToolsSection.scrollIntoView({ behavior: 'smooth' });
        }
    } else {
        // If in document generator, open dashboard in new tab
        window.open('dashboard2.html#signature-tools', '_blank');
    }

    // Close signature selection modal if open
    closeSignatureSelectionModal();
}

// Helper functions to get stored data from active company
function getStoredCompanyInfo() {
    // Get active company key from dashboard
    const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';

    // Try to get company-specific data first
    let stored = localStorage.getItem(`companyInfo_${activeCompanyKey}`);
    if (stored) {
        console.log(`📋 Loading company info for active company: ${activeCompanyKey}`);
        return JSON.parse(stored);
    }

    // Fallback to old format for backward compatibility
    stored = localStorage.getItem('companyInfo');
    if (stored) {
        console.log('📋 Loading company info from legacy format');
        return JSON.parse(stored);
    }

    return null;
}

function getStoredBankDetails() {
    // Get active company key from dashboard
    const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';

    // Try to get company-specific data first
    let stored = localStorage.getItem(`bankDetails_${activeCompanyKey}`);
    if (stored) {
        console.log(`🏦 Loading bank details for active company: ${activeCompanyKey}`);
        return JSON.parse(stored);
    }

    // Fallback to old format for backward compatibility
    stored = localStorage.getItem('bankDetails');
    if (stored) {
        console.log('🏦 Loading bank details from legacy format');
        return JSON.parse(stored);
    }

    return null;
}

function getStoredCompanyLogo() {
    // Get active company key from dashboard
    const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';

    // Try to get company-specific logo first
    let stored = localStorage.getItem(`companyLogo_${activeCompanyKey}`);
    if (stored) {
        console.log(`🎨 Loading logo for active company: ${activeCompanyKey}`);
        return stored; // Logo is stored as base64 string, not JSON
    }

    // Fallback to old format for backward compatibility
    stored = localStorage.getItem('companyLogo');
    if (stored) {
        console.log('🎨 Loading logo from legacy format');
        try {
            return JSON.parse(stored);
        } catch (e) {
            // If it's not JSON, return as is (base64 string)
            return stored;
        }
    }

    return null;
}

// Load generic logo for active company if no custom logo is available
function loadActiveCompanyGenericLogo() {
    const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';

    // Try to get the generic logo from localStorage
    const genericLogo = localStorage.getItem(`companyLogo_${activeCompanyKey}`);

    if (genericLogo) {
        // Set the main logo upload preview
        const logoPreview = document.getElementById('logoPreview');

        if (logoPreview) {
            logoPreview.innerHTML = '';
            const img = document.createElement('img');
            img.src = genericLogo;
            logoPreview.appendChild(img);
            logoPreview.className = 'logo-preview square';

            // Set the global logoImage for PDF generation
            const logoImg = new Image();
            logoImg.onload = function() {
                window.logoImage = logoImg;
                console.log('✅ Generic company logo loaded for PDF generation');
            };
            logoImg.src = genericLogo;
        }

        // Also set contract logo if in contract mode
        const contractLogoDisplay = document.getElementById('contractLogoDisplay');
        if (contractLogoDisplay) {
            contractLogoDisplay.innerHTML = '';
            const contractImg = document.createElement('img');
            contractImg.src = genericLogo;
            contractImg.style.width = '120px';
            contractImg.style.height = '120px';
            contractImg.style.objectFit = 'cover';
            contractImg.style.borderRadius = '12px';
            contractImg.style.border = '2px solid #ddd';
            contractImg.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
            contractImg.style.imageRendering = '-webkit-optimize-contrast';
            contractImg.style.filter = 'contrast(1.1) brightness(1.05)';
            contractLogoDisplay.appendChild(contractImg);
        }

        console.log(`✅ Generic logo loaded for active company: ${activeCompanyKey}`);
    } else {
        // Generate the generic logo if it doesn't exist
        generateAndLoadGenericLogo(activeCompanyKey);
    }
}

// Generate generic logo for company and load it into the document
function generateAndLoadGenericLogo(companyKey) {
    const testCompanies = {
        'docugen-pro': {
            name: 'DocuGen Pro Solutions Ltd',
            bgColor: '#4a9eff',
            textColor: '#ffffff',
            text: 'DGP',
            icon: '📄'
        },
        'bongomaffin': {
            name: 'Bongomaffin',
            bgColor: '#ff6b35',
            textColor: '#ffffff',
            text: 'BM',
            icon: '🎵'
        },
        'benjamin-music': {
            name: 'Benjamin Music Initiatives',
            bgColor: '#28a745',
            textColor: '#ffffff',
            text: 'BMI',
            icon: '🎼'
        }
    };

    const config = testCompanies[companyKey];
    if (!config) return;

    const canvas = document.createElement('canvas');
    canvas.width = 100;
    canvas.height = 100;
    const ctx = canvas.getContext('2d');

    // Create gradient background
    const gradient = ctx.createLinearGradient(0, 0, 100, 100);
    gradient.addColorStop(0, config.bgColor);
    gradient.addColorStop(1, adjustBrightness(config.bgColor, -20));

    // Draw background
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 100, 100);

    // Add border
    ctx.strokeStyle = adjustBrightness(config.bgColor, -30);
    ctx.lineWidth = 2;
    ctx.strokeRect(1, 1, 98, 98);

    // Add icon
    ctx.font = '30px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillStyle = config.textColor;
    ctx.fillText(config.icon, 50, 35);

    // Add text
    ctx.font = 'bold 16px Arial';
    ctx.fillText(config.text, 50, 70);

    const logoData = canvas.toDataURL();

    // Save the generated logo
    localStorage.setItem(`companyLogo_${companyKey}`, logoData);

    // Load it into the document
    loadActiveCompanyGenericLogo();
}

// Helper function to adjust brightness
function adjustBrightness(hex, percent) {
    // Remove # if present
    hex = hex.replace('#', '');

    // Parse RGB
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    // Adjust brightness
    const newR = Math.max(0, Math.min(255, r + (r * percent / 100)));
    const newG = Math.max(0, Math.min(255, g + (g * percent / 100)));
    const newB = Math.max(0, Math.min(255, b + (b * percent / 100)));

    // Convert back to hex
    return '#' +
        Math.round(newR).toString(16).padStart(2, '0') +
        Math.round(newG).toString(16).padStart(2, '0') +
        Math.round(newB).toString(16).padStart(2, '0');
}

// Function to auto-populate forms when document type changes
function autoPopulateCompanyFields() {
    // Call this whenever switching document types
    setTimeout(() => {
        loadStoredCompanyInfo();

        // Ensure logo is loaded for the current document type
        const logoPreview = document.getElementById('logoPreview');
        if (logoPreview && !logoPreview.querySelector('img')) {
            loadActiveCompanyGenericLogo();
        }

        // Also clear any hardcoded values and replace with stored data
        const companyInfo = getStoredCompanyInfo();
        if (companyInfo) {
            // Remove hardcoded values from "From" section
            const fromCompanyField = document.getElementById('fromCompany');
            if (fromCompanyField && (fromCompanyField.value === 'Benjamin Music Initiatives' || fromCompanyField.value === '')) {
                fromCompanyField.value = companyInfo.name || '';
            }

            const fromRegNumberField = document.getElementById('fromRegNumber');
            if (fromRegNumberField && (fromRegNumberField.value === '2023/123456/07' || fromRegNumberField.value === '')) {
                fromRegNumberField.value = companyInfo.regNumber || '';
            }

            const fromVatNumberField = document.getElementById('fromVatNumber');
            if (fromVatNumberField && (fromVatNumberField.value === '4530275839' || fromVatNumberField.value === '')) {
                fromVatNumberField.value = companyInfo.vatNumber || '';
            }

            const fromEmailField = document.getElementById('fromEmail');
            if (fromEmailField && (fromEmailField.value === '<EMAIL>' || fromEmailField.value === '')) {
                fromEmailField.value = companyInfo.email || '';
            }

            const fromAddressField = document.getElementById('fromAddress');
            if (fromAddressField && (fromAddressField.value.includes('123 Harmony Street') || fromAddressField.value === '')) {
                fromAddressField.value = companyInfo.address || '';
            }

            const fromContactField = document.getElementById('fromContact');
            if (fromContactField && (fromContactField.value === 'Benjamin Johnson' || fromContactField.value === '')) {
                fromContactField.value = companyInfo.representative?.name || '';
            }

            const fromPhoneField = document.getElementById('fromPhone');
            if (fromPhoneField && (fromPhoneField.value === '+27 12 345 6789' || fromPhoneField.value === '')) {
                fromPhoneField.value = companyInfo.phone || '';
            }
        }

        // Also call specific auto-population functions based on document mode
        const currentDocumentMode = window.documentMode || documentMode || 'invoice';
        if (currentDocumentMode === 'invoice') {
            autoPopulateInvoiceFields();
        } else if (currentDocumentMode === 'artist-agreement') {
            autoPopulateArtistAgreementFields();
        }

        console.log('Company fields and logo auto-populated');
    }, 100); // Small delay to ensure DOM is updated
}

// Manual function to force reload company information (for testing)
function forceLoadCompanyInfo() {
    loadStoredCompanyInfo();
    autoPopulateCompanyFields();

    // Force reload logo
    const logoPreview = document.getElementById('logoPreview');
    if (logoPreview) {
        logoPreview.innerHTML = ''; // Clear existing logo
        loadActiveCompanyGenericLogo(); // Reload logo
    }

    console.log('Company information and logo force loaded');
}

// Function to refresh logo when company changes
function refreshCompanyLogo() {
    const activeCompanyKey = localStorage.getItem('activeTestCompany') || 'docugen-pro';
    const activeCompany = testCompanies[activeCompanyKey];

    if (!activeCompany) return;

    console.log(`🔄 Refreshing logo for: ${activeCompany.name}`);

    // Clear existing logo
    const logoPreview = document.getElementById('logoPreview');
    if (logoPreview) {
        logoPreview.innerHTML = '';
    }

    // Clear global logo image
    window.logoImage = null;

    // Reload logo
    loadActiveCompanyGenericLogo();

    // Update active company indicator
    displayActiveCompanyInfo();

    console.log(`✅ Logo refreshed for: ${activeCompany.name}`);
}

// Generate comprehensive technical rider content
function generateTechnicalRiderContent(doc, margin, startY, contentWidth, normalSize, sectionTitleSize, customRequirements = {}, dropdownValues = {}) {
    let y = startY;
    const lineHeight = 6;
    const sectionSpacing = 12;
    const pageHeight = doc.internal.pageSize.height;
    const bottomMargin = 20;

    // Function to check if we need a new page
    function checkPageBreak(requiredSpace = 20) {
        if (y + requiredSpace > pageHeight - bottomMargin) {
            doc.addPage();
            y = margin;
            return true;
        }
        return false;
    }

    // Audio Requirements Section
    checkPageBreak(100); // Check if we need space for the section
    setProfessionalHeadingFont(doc, 'bold', sectionTitleSize);
    doc.text('1. AUDIO REQUIREMENTS', margin, y);
    y += 8;

    setProfessionalBodyFont(doc, 'normal', normalSize);

    const audioRequirements = [
        '• PA System: Professional line array system, minimum 15kW total power',
        '• Mixing Console: 32+ channel digital console (Yamaha CL5, Avid S6L, or equivalent)',
        '• FOH Position: Central audience position with clear sightlines to stage',
        '• Monitor Console: 24+ channel digital monitor console with IEM capabilities',
        '• Microphones: 8x Shure SM58, 4x Shure SM57, 1x AKG D112, drum mic package',
        '• Wireless Systems: 6x professional wireless microphone systems (Shure ULX-D or equivalent)',
        '• In-Ear Monitors: 6x IEM transmitters with frequency coordination',
        '  - Band provides own earphones OR venue provides complete IEM kit',
        '  - Frequencies: 470-608 MHz (TV white space compliant)',
        '• Monitor Wedges: 8x active wedge monitors (d&b M4, L-Acoustics X8, or equivalent)',
        '• DI Boxes: 12x active DI boxes (Radial JDI, BSS AR-133, or equivalent)',
        '• Cables: Professional XLR and jack cables, minimum 50m multicore'
    ];

    audioRequirements.forEach(req => {
        checkPageBreak(lineHeight + 5); // Check before each line
        doc.text(req, margin + 5, y);
        y += lineHeight;
    });

    // Add custom audio requirements
    if (customRequirements.audio && customRequirements.audio.length > 0) {
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('Custom Audio Requirements:', margin + 5, y);
        y += lineHeight;
        doc.setFont(PDF_BODY_FONT, 'normal');

        customRequirements.audio.forEach(req => {
            doc.text(`• ${req}`, margin + 10, y);
            y += lineHeight;
        });
    }

    y += sectionSpacing;

    // Stage Requirements Section
    checkPageBreak(80); // Check if we need space for the section
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('2. STAGE REQUIREMENTS', margin, y);
    y += 8;

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const stageRequirements = [
        '• Stage Dimensions: Minimum 10m wide x 8m deep x 1.2m high',
        '• Drum Riser: 3m x 3m x 0.4m high, center-back position',
        '• Keyboard Riser: 2m x 1.5m x 0.2m high, stage left',
        '• Power Supply: 63A 3-phase CEE form supply, stage left and right',
        '• Power Distribution: 12x Schuko outlets per side, RCD protected',
        '• Voltage: 230V/400V 50Hz (EU standard) with earth bonding',
        '• Load-in Access: 4m wide minimum, level access for flight cases',
        '• Rigging Points: SWL 500kg minimum, certified inspection required'
    ];

    stageRequirements.forEach(req => {
        checkPageBreak(lineHeight + 5); // Check before each line
        doc.text(req, margin + 5, y);
        y += lineHeight;
    });

    // Add custom stage requirements
    if (customRequirements.stage && customRequirements.stage.length > 0) {
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('Custom Stage Requirements:', margin + 5, y);
        y += lineHeight;
        doc.setFont(PDF_BODY_FONT, 'normal');

        customRequirements.stage.forEach(req => {
            doc.text(`• ${req}`, margin + 10, y);
            y += lineHeight;
        });
    }

    y += sectionSpacing;

    // Backline Requirements Section
    checkPageBreak(90); // Check if we need space for the section
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('3. BACKLINE REQUIREMENTS', margin, y);
    y += 8;

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const backlineRequirements = [
        '• Drum Kit: Professional 5-piece kit (DW, Pearl, Yamaha, or equivalent)',
        '  - Kick: 22", Snare: 14"x5.5", Toms: 10", 12", 16"',
        '  - Hardware: Hi-hat, 2x cymbal stands, snare stand, kick pedal',
        '  - Cymbals: 14" hi-hats, 16" crash, 18" crash, 20" ride (Zildjian/Sabian)',
        '• Guitar Amplification: 2x Marshall JCM800 100W heads + 4x12 cabinets',
        '• Bass Amplification: Ampeg SVT-CL head + 8x10 cabinet OR DI only',
        '• Keyboards: Yamaha CP88 stage piano + Nord Lead A1 synthesizer',
        '• Guitar Stands: 6x professional guitar stands',
        '• Spare Strings: Full sets for all instruments',
        '• Cables: 10x instrument cables, 6x speaker cables'
    ];

    backlineRequirements.forEach(req => {
        checkPageBreak(lineHeight + 5); // Check before each line
        doc.text(req, margin + 5, y);
        y += lineHeight;
    });

    // Add custom backline requirements
    if (customRequirements.backline && customRequirements.backline.length > 0) {
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('Custom Backline Requirements:', margin + 5, y);
        y += lineHeight;
        doc.setFont(PDF_BODY_FONT, 'normal');

        customRequirements.backline.forEach(req => {
            doc.text(`• ${req}`, margin + 10, y);
            y += lineHeight;
        });
    }

    y += sectionSpacing;

    // Lighting Requirements Section
    checkPageBreak(80); // Check if we need space for the section
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('4. LIGHTING REQUIREMENTS', margin, y);
    y += 8;

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const lightingRequirements = [
        '• Lighting Console: MA Lighting GrandMA3, Avolites Tiger Touch, or equivalent',
        '• LED Fixtures: 24x RGBW LED wash lights (minimum 300W each)',
        '• Moving Lights: 12x LED spot moving heads with gobo wheels',
        '• Front Lighting: 8x LED profiles for key lighting',
        '• Back Lighting: 6x LED wash for silhouette effects',
        '• Special Effects: Haze machine (oil-based), 4x LED strobes',
        '• DMX Control: Full DMX512 universe, wireless backup capability',
        '• Safety: All fixtures properly rigged with safety bonds'
    ];

    lightingRequirements.forEach(req => {
        checkPageBreak(lineHeight + 5); // Check before each line
        doc.text(req, margin + 5, y);
        y += lineHeight;
    });

    // Add custom lighting requirements
    if (customRequirements.lighting && customRequirements.lighting.length > 0) {
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('Custom Lighting Requirements:', margin + 5, y);
        y += lineHeight;
        doc.setFont(PDF_BODY_FONT, 'normal');

        customRequirements.lighting.forEach(req => {
            doc.text(`• ${req}`, margin + 10, y);
            y += lineHeight;
        });
    }

    y += sectionSpacing;

    // Personnel Requirements Section
    checkPageBreak(70); // Check if we need space for the section
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('5. PERSONNEL REQUIREMENTS', margin, y);
    y += 8;

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const personnelRequirements = [
        '• FOH Engineer: Experienced with international touring acts',
        '• Monitor Engineer: Dedicated monitor engineer for IEM systems',
        '• Lighting Technician: Qualified lighting operator',
        '• Stage Manager: Venue-appointed stage manager for coordination',
        '• Crew: 4x stage hands for load-in/out and show support',
        '• Security: Dedicated stage security and backstage access control',
        '• Rigger: Certified rigger for lighting installation (if required)'
    ];

    personnelRequirements.forEach(req => {
        checkPageBreak(lineHeight + 5); // Check before each line
        doc.text(req, margin + 5, y);
        y += lineHeight;
    });

    // Add custom personnel requirements
    if (customRequirements.personnel && customRequirements.personnel.length > 0) {
        doc.setFont(PDF_HEADING_FONT, 'bold');
        doc.text('Custom Personnel Requirements:', margin + 5, y);
        y += lineHeight;
        doc.setFont(PDF_BODY_FONT, 'normal');

        customRequirements.personnel.forEach(req => {
            doc.text(`• ${req}`, margin + 10, y);
            y += lineHeight;
        });
    }

    y += sectionSpacing;

    // Schedule Requirements Section
    checkPageBreak(60); // Check if we need space for the section
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('6. SCHEDULE REQUIREMENTS', margin, y);
    y += 8;

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const scheduleRequirements = [
        '• Load-in: 8 hours before doors open',
        '• Sound Check: 2 hours minimum (full band + individual instruments)',
        '• Line Check: 30 minutes before doors open',
        '• Show Duration: 90 minutes + 20 minute encore',
        '• Load-out: Immediate after show, 2 hours maximum',
        '• Curfew: All equipment removed by venue curfew time'
    ];

    scheduleRequirements.forEach(req => {
        checkPageBreak(lineHeight + 5); // Check before each line
        doc.text(req, margin + 5, y);
        y += lineHeight;
    });
    y += sectionSpacing;

    // Hospitality Requirements Section
    checkPageBreak(70); // Check if we need space for the section
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('7. HOSPITALITY REQUIREMENTS', margin, y);
    y += 8;

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const hospitalityRequirements = [
        '• Dressing Rooms: 2x private, lockable rooms with mirrors and seating',
        '• Green Room: Comfortable space for 10 people with WiFi access',
        '• Catering: Hot meals for 8 people (band + crew) 4 hours before show',
        '• Refreshments: Coffee, tea, water, fresh fruit, and light snacks',
        '• Parking: 2x parking spaces for tour vehicles near load-in',
        '• Towels: 6x clean towels for band use',
        '• Dietary Requirements: Vegetarian options available'
    ];

    hospitalityRequirements.forEach(req => {
        checkPageBreak(lineHeight + 5); // Check before each line
        doc.text(req, margin + 5, y);
        y += lineHeight;
    });
    y += sectionSpacing;

    // Safety & Compliance Section
    checkPageBreak(60); // Check if we need space for the section
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('8. SAFETY & COMPLIANCE', margin, y);
    y += 8;

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const safetyRequirements = [
        '• Electrical Safety: All equipment PAT tested and certified',
        '• Fire Safety: Clear emergency exits, fire extinguishers accessible',
        '• Insurance: Venue public liability insurance minimum €2M',
        '• Risk Assessment: Completed risk assessment for all technical elements',
        '• First Aid: Qualified first aider on site during event',
        '• Noise Limits: Compliance with local noise ordinances'
    ];

    safetyRequirements.forEach(req => {
        checkPageBreak(lineHeight + 5); // Check before each line
        doc.text(req, margin + 5, y);
        y += lineHeight;
    });
    y += sectionSpacing;

    // Contact Information Section
    checkPageBreak(60); // Check if we need space for the section
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('9. TECHNICAL CONTACTS', margin, y);
    y += 8;

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const contactInfo = [
        '• Production Manager: Available 48 hours before show',
        '• Tour Manager: On-site coordination and liaison',
        '• Technical Advance: Minimum 2 weeks prior to show date',
        '• Emergency Contact: 24/7 contact during tour period',
        '• Venue Confirmation: All requirements confirmed in writing'
    ];

    contactInfo.forEach(req => {
        checkPageBreak(lineHeight + 5); // Check before each line
        doc.text(req, margin + 5, y);
        y += lineHeight;
    });
    y += sectionSpacing;

    // Important Notes Section
    checkPageBreak(60); // Check if we need space for the section
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('IMPORTANT NOTES:', margin, y);
    y += 8;

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const importantNotes = [
        '• This rider forms part of the performance contract',
        '• Any deviations must be approved by production management',
        '• Venue must confirm all requirements 7 days before show',
        '• Failure to meet requirements may result in show cancellation',
        '• All equipment must be available for full soundcheck period',
        '• Professional attitude and cooperation expected from all venue staff'
    ];

    importantNotes.forEach(note => {
        checkPageBreak(lineHeight + 5); // Check before each line
        doc.text(note, margin + 5, y);
        y += lineHeight;
    });

    return y + 10; // Return the final Y position
}

// Generate comprehensive annexure content for entertainment industry
function generateAnnexureContent(doc, margin, startY, contentWidth, normalSize, sectionTitleSize, logoImg) {
    let y = margin - 14; // Move content up by 0.5cm (14 pixels) - fine-tuned positioning
    const lineHeight = 6;
    const sectionSpacing = 15;
    const pageHeight = doc.internal.pageSize.height;
    const bottomMargin = 35; // Increased bottom margin to avoid footer overlap
    const pageWidth = doc.internal.pageSize.width;

    // Function to check if we need a new page
    function checkPageBreak(requiredSpace = 25) {
        if (y + requiredSpace > pageHeight - bottomMargin - 10) { // Extra safety margin
            doc.addPage();
            y = margin; // Use standard margin on new pages
            return true;
        }
        return false;
    }

    // Add centered logo at the top (same as Artist Agreement) - ONLY ONE LOGO
    if (logoImg) {
        try {
            const logoWidth = 50; // Larger size to match Artist Agreement proportions
            const logoHeight = 50;
            const logoX = (pageWidth - logoWidth) / 2; // Center horizontally
            const logoY = y;

            doc.addImage(logoImg, 'PNG', logoX, logoY, logoWidth, logoHeight);
            y += logoHeight + 15; // More space after logo
            console.log('✅ Single centered logo added to Annexure');
        } catch (error) {
            console.warn('⚠️ Could not add logo to Annexure:', error);
            y += 15; // Space if logo fails
        }
    } else {
        y += 15; // Space if no logo
    }

    // Add centered "ANNEXURE" title
    setProfessionalHeadingFont(doc, 'bold', 28);
    doc.setTextColor(0, 0, 0);
    const annexureTitle = 'ANNEXURE';
    const titleWidth = doc.getTextWidth(annexureTitle);
    const titleX = (pageWidth - titleWidth) / 2;
    doc.text(annexureTitle, titleX, y);
    y += 20;

    // Add company information section (centered)
    const companyInfo = getStoredCompanyInfo();

    if (companyInfo) {
        setProfessionalBodyFont(doc, 'bold', 14);

        // Company name (centered and prominent)
        if (companyInfo.companyName) {
            const companyNameWidth = doc.getTextWidth(companyInfo.companyName);
            const companyNameX = (pageWidth - companyNameWidth) / 2;
            doc.text(companyInfo.companyName, companyNameX, y);
            y += 10;
        }

        setProfessionalBodyFont(doc, 'normal', 11);

        // Company details (centered) - separate lines to prevent text overlap
        const companyDetails = [];
        if (companyInfo.companyAddress) companyDetails.push(companyInfo.companyAddress);
        if (companyInfo.companyPhone) companyDetails.push(`Tel: ${companyInfo.companyPhone}`);
        if (companyInfo.companyEmail) companyDetails.push(`Email: ${companyInfo.companyEmail}`);
        if (companyInfo.registrationNumber) companyDetails.push(`Reg No: ${companyInfo.registrationNumber}`);
        if (companyInfo.vatNumber) companyDetails.push(`VAT No: ${companyInfo.vatNumber}`);

        companyDetails.forEach(detail => {
            const detailWidth = doc.getTextWidth(detail);
            const detailX = (pageWidth - detailWidth) / 2;
            doc.text(detail, detailX, y);
            y += 6;
        });

        y += 15; // Extra space after company info
    }

    // Add document reference information (single set, right-aligned)
    setProfessionalBodyFont(doc, 'normal', 10);

    const currentDate = new Date().toLocaleDateString('en-ZA'); // South African date format
    const annexureNumber = `ANX-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`;

    // Right-aligned document info (single set only)
    const rightMargin = pageWidth - margin;
    const docInfoY = y - 30; // Position it higher, near the top
    doc.text(`Annexure #: ${annexureNumber}`, rightMargin - doc.getTextWidth(`Annexure #: ${annexureNumber}`), docInfoY);
    doc.text(`Date: ${currentDate}`, rightMargin - doc.getTextWidth(`Date: ${currentDate}`), docInfoY + 5);
    doc.text(`Document ID: ${annexureNumber}`, rightMargin - doc.getTextWidth(`Document ID: ${annexureNumber}`), docInfoY + 10);

    // Add "DETAILS FOR" section (left side) - clean layout without pricing
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('DETAILS FOR', margin, y);
    y += 8;

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    // Get client/recipient information
    const billToCompany = document.getElementById('billToCompany')?.value || 'Client Company Name';
    const billToAddress = document.getElementById('billToAddress')?.value || '';
    const billToPhone = document.getElementById('billToPhone')?.value || '';
    const billToEmail = document.getElementById('billToEmail')?.value || '';

    if (billToCompany) doc.text(billToCompany, margin, y), y += 6;
    if (billToAddress) {
        const addressLines = billToAddress.split('\n');
        addressLines.forEach(line => {
            if (line.trim()) {
                doc.text(line.trim(), margin, y);
                y += 5;
            }
        });
    }
    if (billToPhone) doc.text(`Tel: ${billToPhone}`, margin, y), y += 5;
    if (billToEmail) doc.text(`Email: ${billToEmail}`, margin, y), y += 5;

    y += 15;

    // Add annexure purpose/description section
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('ANNEXURE PURPOSE:', margin, y);
    y += 8;

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');
    const annexurePurpose = [
        'This annexure forms an integral part of the main agreement and contains',
        'additional terms, conditions, and specifications that supplement the',
        'primary contract. All provisions herein are binding and enforceable.'
    ];

    annexurePurpose.forEach(line => {
        doc.text(line, margin, y);
        y += 6;
    });

    y += 15;

    // Marketing and Promotional Materials Section
    checkPageBreak(120); // Check if we need space for the section
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('1. MARKETING AND PROMOTIONAL MATERIALS', margin, y);
    y += 8;

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const marketingRequirements = [
        'Details the artist\'s promotional obligations and materials provided to the venue or promoter:',
        '',
        '• High-resolution photos, logos, and bios for advertising',
        '  - Minimum 300 DPI resolution for print materials',
        '  - RGB and CMYK versions available',
        '  - Artist biography (50, 100, and 200-word versions)',
        '  - Professional headshots and performance photos',
        '',
        '• Approved social media posts, hashtags, or branding guidelines',
        '  - Pre-approved social media content templates',
        '  - Official hashtags and tagging requirements',
        '  - Brand color codes and font specifications',
        '  - Logo usage guidelines and restrictions',
        '',
        '• Radio/TV interview schedules or promotional appearances',
        '  - Media availability windows and blackout periods',
        '  - Interview talking points and key messages',
        '  - Technical requirements for remote interviews',
        '',
        '• Deadlines for submitting materials to ensure timely marketing',
        '  - Initial materials: 4 weeks before event',
        '  - Final approvals: 2 weeks before event',
        '  - Last-minute changes: 48 hours before event',
        '',
        '• Restrictions on unapproved promotions by the venue',
        '  - All promotional materials require artist approval',
        '  - No unauthorized use of artist likeness or music',
        '  - Venue must credit artist as specified in guidelines'
    ];

    marketingRequirements.forEach(req => {
        if (req === '') {
            y += lineHeight / 2; // Half line spacing for empty lines
        } else {
            checkPageBreak(lineHeight + 5); // Check before each line
            doc.text(req, margin + 5, y);
            y += lineHeight;
        }
    });

    y += sectionSpacing;

    // Merchandise Agreement Details Section
    checkPageBreak(100); // Check if we need space for the section
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('2. MERCHANDISE AGREEMENT DETAILS', margin, y);
    y += 8;

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const merchandiseRequirements = [
        'Outlines terms for selling artist merchandise at the performance:',
        '',
        '• Types of merchandise (e.g., T-shirts, CDs, posters)',
        '  - Apparel: T-shirts, hoodies, hats, accessories',
        '  - Music: CDs, vinyl records, digital download cards',
        '  - Collectibles: Posters, stickers, signed items',
        '  - Special editions: Limited releases, tour exclusives',
        '',
        '• Percentage split between artist and venue (e.g., 80/20)',
        '  - Artist retains: 85% of gross merchandise sales',
        '  - Venue commission: 15% of gross merchandise sales',
        '  - Payment terms: Settlement within 24 hours of event',
        '  - Sales reporting: Detailed breakdown required',
        '',
        '• Booth or table setup requirements and location',
        '  - Minimum 8ft x 6ft space in high-traffic area',
        '  - Access to power outlets for card readers',
        '  - Adequate lighting for product display',
        '  - Security provisions for valuable items',
        '',
        '• Inventory management and sales reporting obligations',
        '  - Pre-event inventory count and documentation',
        '  - Real-time sales tracking and reporting',
        '  - End-of-event reconciliation and settlement',
        '  - Venue staff assistance if required',
        '',
        '• Handling of unsold items or damaged goods',
        '  - Artist retains all unsold merchandise',
        '  - Venue liable for damaged items due to negligence',
        '  - Insurance coverage for theft or damage',
        '  - Load-out procedures for remaining inventory'
    ];

    merchandiseRequirements.forEach(req => {
        if (req === '') {
            y += lineHeight / 2; // Half line spacing for empty lines
        } else {
            checkPageBreak(lineHeight + 5); // Check before each line
            doc.text(req, margin + 5, y);
            y += lineHeight;
        }
    });

    y += sectionSpacing;

    // Guest List or Travel Arrangements Section
    checkPageBreak(110); // Check if we need space for the section
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('3. GUEST LIST AND TRAVEL ARRANGEMENTS', margin, y);
    y += 8;

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const guestTravelRequirements = [
        'Specifies guest access and travel logistics for the artist and crew:',
        '',
        '• Number of complimentary tickets or backstage passes',
        '  - Artist guest list: 10 complimentary tickets',
        '  - Backstage passes: 6 all-access passes',
        '  - VIP meet & greet: 4 additional passes',
        '  - Industry/media passes: As mutually agreed',
        '',
        '• Guest list submission deadlines and verification process',
        '  - Initial guest list: 7 days before event',
        '  - Final guest list: 24 hours before doors',
        '  - Photo ID required for all guest list entries',
        '  - No additions permitted after final deadline',
        '',
        '• Travel itineraries, including flights, ground transport, or accommodations',
        '  - Flight arrangements: Business class for distances over 3 hours',
        '  - Ground transport: Private vehicle from airport to venue',
        '  - Hotel accommodations: 4-star minimum, separate rooms',
        '  - Local transport: Vehicle and driver for duration of stay',
        '',
        '• Per diems or meal allowances for the artist and entourage',
        '  - Artist per diem: $150 per day',
        '  - Crew per diem: $75 per day per person',
        '  - Meal buyouts: $50 per person if catering unavailable',
        '  - Currency: Local currency or USD equivalent',
        '',
        '• Special requests (e.g., accessibility needs or security)',
        '  - Accessibility requirements as specified',
        '  - Personal security arrangements if required',
        '  - Dietary restrictions and special meal requests',
        '  - Medical or health-related accommodations'
    ];

    guestTravelRequirements.forEach(req => {
        if (req === '') {
            y += lineHeight / 2; // Half line spacing for empty lines
        } else {
            checkPageBreak(lineHeight + 5); // Check before each line
            doc.text(req, margin + 5, y);
            y += lineHeight;
        }
    });

    y += sectionSpacing;

    // Force Majeure or Cancellation Clauses Section
    checkPageBreak(120); // Check if we need space for the section
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('4. FORCE MAJEURE AND CANCELLATION CLAUSES', margin, y);
    y += 8;

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const forceMajeureRequirements = [
        'Defines conditions for canceling the performance due to uncontrollable events:',
        '',
        '• Events covered (e.g., natural disasters, illness, government restrictions)',
        '  - Natural disasters: Earthquakes, floods, severe weather',
        '  - Health emergencies: Pandemic restrictions, artist illness',
        '  - Government actions: Travel bans, venue closures, curfews',
        '  - Infrastructure failures: Power outages, venue damage',
        '  - Security threats: Terrorism, civil unrest, safety concerns',
        '',
        '• Notification process and timelines for cancellation',
        '  - Immediate notification via phone and email',
        '  - Written confirmation within 24 hours',
        '  - Public announcement coordination between parties',
        '  - Media statement approval process',
        '',
        '• Refund or rescheduling obligations for artist, promoter, or venue',
        '  - Good faith effort to reschedule within 90 days',
        '  - Full refund if rescheduling not possible',
        '  - Ticket holder notification and refund process',
        '  - Alternative date must be mutually acceptable',
        '',
        '• Financial liabilities (e.g., deposits, travel costs)',
        '  - Non-refundable expenses: Actual costs incurred',
        '  - Deposit handling: Held in escrow until resolution',
        '  - Travel costs: Shared responsibility if cancellation unavoidable',
        '  - Insurance claims: Coordination between all parties',
        '',
        '• Dispute resolution for disagreements on cancellations',
        '  - Mediation: First attempt at resolution',
        '  - Arbitration: Binding arbitration if mediation fails',
        '  - Jurisdiction: Governed by venue location laws',
        '  - Legal costs: Each party bears own costs unless otherwise awarded'
    ];

    forceMajeureRequirements.forEach(req => {
        if (req === '') {
            y += lineHeight / 2; // Half line spacing for empty lines
        } else {
            checkPageBreak(lineHeight + 5); // Check before each line
            doc.text(req, margin + 5, y);
            y += lineHeight;
        }
    });

    y += sectionSpacing;

    // Important Notes Section
    checkPageBreak(60); // Check if we need space for the section
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('IMPORTANT NOTES:', margin, y);
    y += 8;

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const importantNotes = [
        '• This annexure forms an integral part of the main performance agreement',
        '• All terms herein are binding and enforceable under applicable law',
        '• Any modifications must be agreed upon in writing by all parties',
        '• This document supersedes any previous annexure agreements',
        '• Professional conduct and cooperation expected from all parties',
        '• Compliance with all local laws and regulations required'
    ];

    importantNotes.forEach(note => {
        checkPageBreak(lineHeight + 5); // Check before each line
        doc.text(note, margin + 5, y);
        y += lineHeight;
    });

    return y + 10; // Return the final Y position
}

// Document Sharing Functions
function showDocumentSharing() {
    if (!window.generatedPDF) {
        alert('Please generate a document first before sharing.');
        return;
    }

    // Show sharing modal
    document.getElementById('documentSharingModal').style.display = 'block';

    // Initialize sharing form
    initializeDocumentSharing();
}

function closeDocumentSharing() {
    document.getElementById('documentSharingModal').style.display = 'none';
}

function initializeDocumentSharing() {
    if (!window.generatedPDF) return;

    const documentName = window.generatedPDF.fileName || 'Document.pdf';
    const documentType = getDocumentTypeDisplayName();

    // Update document info display
    document.getElementById('sharingDocumentName').textContent = documentName;
    document.getElementById('sharingDocumentType').textContent = documentType;

    // Calculate and display file size
    if (window.generatedPDF.blob) {
        const sizeInBytes = window.generatedPDF.blob.size;
        const sizeInKB = (sizeInBytes / 1024).toFixed(1);
        document.getElementById('sharingDocumentSize').textContent = `${sizeInKB} KB`;
    }

    // Get company info for pre-filling
    const companyInfo = getStoredCompanyInfo();

    // Set default email subject
    const emailSubject = document.getElementById('emailSubject');
    if (emailSubject && companyInfo) {
        emailSubject.value = `${documentType} from ${companyInfo.name || 'DocuGen Pro'}`;
    }

    // Set default email message
    const emailMessage = document.getElementById('emailMessage');
    if (emailMessage && companyInfo) {
        emailMessage.value = `Hi,

Please find the attached ${documentType.toLowerCase()}: ${documentName}

Best regards,
${companyInfo.representative?.name || 'Team'}
${companyInfo.name}
${companyInfo.email}
${companyInfo.phone}`;
    }

    // Set default WhatsApp message
    const whatsappMessage = document.getElementById('whatsappMessage');
    if (whatsappMessage && companyInfo) {
        whatsappMessage.value = `Hi! Please find the attached ${documentType.toLowerCase()}: ${documentName}

From: ${companyInfo.name}`;
    }
}

function getDocumentTypeDisplayName() {
    switch (documentMode) {
        case 'invoice': return 'Invoice';
        case 'receipt': return 'Receipt';
        case 'quotation': return 'Quotation';
        case 'contract': return 'Contract';
        case 'rider': return 'Technical Rider';
        case 'annexure': return 'Annexure';
        case 'artist-agreement': return 'Artist Agreement';
        default: return 'Document';
    }
}

// This function was removed - using the correct version from line 3088 that reads from localStorage

// Email Sharing Function
function shareViaEmail() {
    const recipientEmail = document.getElementById('recipientEmail').value;
    const emailSubject = document.getElementById('emailSubject').value;
    const emailMessage = document.getElementById('emailMessage').value;
    const sendCopy = document.getElementById('sendCopy').checked;

    if (!recipientEmail) {
        alert('Please enter a recipient email address.');
        return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(recipientEmail)) {
        alert('Please enter a valid email address.');
        return;
    }

    console.log('Sharing via email:', recipientEmail);

    // Create mailto link with document info
    const companyInfo = getStoredCompanyInfo();
    const documentName = window.generatedPDF?.fileName || 'Document.pdf';

    const subject = encodeURIComponent(emailSubject || `Document from ${companyInfo?.name || 'DocuGen Pro'}`);
    const body = encodeURIComponent(`${emailMessage}

---
Document: ${documentName}
Generated with DocuGen Pro
${new Date().toLocaleString()}`);

    const mailtoLink = `mailto:${recipientEmail}?subject=${subject}&body=${body}`;

    // Open email client
    window.open(mailtoLink);

    // Show success message
    alert(`📧 Email client opened!\n\nTo: ${recipientEmail}\nSubject: ${emailSubject}\n\nPlease attach the downloaded PDF file and send the email.`);

    // Track sharing analytics
    trackDocumentSharing('email', recipientEmail);
}

// WhatsApp Sharing Functions
function shareViaWhatsApp() {
    const phoneNumber = document.getElementById('whatsappNumber').value;
    const message = document.getElementById('whatsappMessage').value;

    const documentName = window.generatedPDF?.fileName || 'Document.pdf';
    const fullMessage = `${message}

Document: ${documentName}
Generated with DocuGen Pro`;

    const encodedMessage = encodeURIComponent(fullMessage);

    if (phoneNumber) {
        // Clean phone number (remove spaces, dashes, etc.)
        const cleanNumber = phoneNumber.replace(/[^\d+]/g, '');
        const whatsappUrl = `https://wa.me/${cleanNumber}?text=${encodedMessage}`;
        window.open(whatsappUrl, '_blank');
    } else {
        const whatsappUrl = `https://wa.me/?text=${encodedMessage}`;
        window.open(whatsappUrl, '_blank');
    }

    alert('💬 WhatsApp opened! Please attach the downloaded PDF file and send the message.');
    trackDocumentSharing('whatsapp', phoneNumber || 'unknown');
}

function shareViaWhatsAppWeb() {
    const phoneNumber = document.getElementById('whatsappNumber').value;
    const message = document.getElementById('whatsappMessage').value;

    const documentName = window.generatedPDF?.fileName || 'Document.pdf';
    const fullMessage = `${message}

Document: ${documentName}
Generated with DocuGen Pro`;

    const encodedMessage = encodeURIComponent(fullMessage);

    if (phoneNumber) {
        const cleanNumber = phoneNumber.replace(/[^\d+]/g, '');
        const whatsappWebUrl = `https://web.whatsapp.com/send?phone=${cleanNumber}&text=${encodedMessage}`;
        window.open(whatsappWebUrl, '_blank');
    } else {
        const whatsappWebUrl = `https://web.whatsapp.com/send?text=${encodedMessage}`;
        window.open(whatsappWebUrl, '_blank');
    }

    alert('🌐 WhatsApp Web opened! Please attach the downloaded PDF file and send the message.');
    trackDocumentSharing('whatsapp-web', phoneNumber || 'unknown');
}

// Quick Share Functions
function copyDocumentLink() {
    if (!window.generatedPDF) {
        alert('Please generate a document first.');
        return;
    }

    const documentName = window.generatedPDF.fileName || 'Document.pdf';
    const companyInfo = getStoredCompanyInfo();
    const shareText = `Document: ${documentName}
Generated with DocuGen Pro
Company: ${companyInfo.name || 'N/A'}
Date: ${new Date().toLocaleString()}`;

    // Copy to clipboard
    navigator.clipboard.writeText(shareText).then(() => {
        alert('🔗 Document information copied to clipboard!\n\nNote: The actual PDF file needs to be shared separately as it\'s stored locally.');
    }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = shareText;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('🔗 Document information copied to clipboard!');
    });

    trackDocumentSharing('copy-link', 'clipboard');
}

function shareViaSMS() {
    const documentName = window.generatedPDF?.fileName || 'Document.pdf';
    const message = `Hi! I have a document to share with you: ${documentName}. Generated with DocuGen Pro.`;

    const smsUrl = `sms:?body=${encodeURIComponent(message)}`;
    window.open(smsUrl);

    alert('📱 SMS app opened! Please attach the downloaded PDF file and send the message.');
    trackDocumentSharing('sms', 'unknown');
}

function shareViaLinkedIn() {
    const documentName = window.generatedPDF?.fileName || 'Document.pdf';
    const companyInfo = getStoredCompanyInfo();

    const text = `Just generated a professional ${documentName} using DocuGen Pro! 📄✨`;
    const url = window.location.origin;

    const linkedInUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`;
    window.open(linkedInUrl, '_blank');

    alert('💼 LinkedIn opened! You can share information about your document generation.');
    trackDocumentSharing('linkedin', 'public');
}

function shareViaTelegram() {
    const documentName = window.generatedPDF?.fileName || 'Document.pdf';
    const message = `Hi! I have a document to share: ${documentName}. Generated with DocuGen Pro.`;

    const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(window.location.origin)}&text=${encodeURIComponent(message)}`;
    window.open(telegramUrl, '_blank');

    alert('✈️ Telegram opened! Please attach the downloaded PDF file and send the message.');
    trackDocumentSharing('telegram', 'unknown');
}

// Analytics and Tracking
function trackDocumentSharing(method, recipient) {
    const sharingData = {
        method: method,
        recipient: recipient,
        documentName: window.generatedPDF?.fileName || 'Document.pdf',
        documentType: getDocumentTypeDisplayName(),
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent
    };

    // Store in localStorage for analytics
    let sharingHistory = JSON.parse(localStorage.getItem('documentSharingHistory') || '[]');
    sharingHistory.push(sharingData);

    // Keep only last 100 entries
    if (sharingHistory.length > 100) {
        sharingHistory = sharingHistory.slice(-100);
    }

    localStorage.setItem('documentSharingHistory', JSON.stringify(sharingHistory));

    console.log('📊 Document sharing tracked:', sharingData);
}

// Initialize sharing modal event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Close sharing modal when clicking outside
    window.addEventListener('click', function(event) {
        const sharingModal = document.getElementById('documentSharingModal');
        if (event.target === sharingModal) {
            closeDocumentSharing();
        }
    });

    // Close sharing modal with Escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const sharingModal = document.getElementById('documentSharingModal');
            if (sharingModal && sharingModal.style.display === 'block') {
                closeDocumentSharing();
            }
        }
    });
});

// Quick Sharing Functions - Enhanced with AI's comprehensive solution
function showQuickSharingOptions() {
    console.log('🔍 showQuickSharingOptions called - AI Enhanced Version');
    const quickSharingSection = document.getElementById('quickSharingOptions');
    console.log('📄 quickSharingSection found:', !!quickSharingSection);

    if (quickSharingSection) {
        console.log('✅ Showing quick sharing section with AI-recommended approach');

        // AI SOLUTION 1: Clean element selection and display override
        quickSharingSection.style.display = 'block';
        quickSharingSection.style.visibility = 'visible';
        quickSharingSection.style.opacity = '1';

        // AI SOLUTION 2: Add CSS class for better control
        quickSharingSection.classList.add('visible');
        quickSharingSection.classList.remove('hidden');

        // AI SOLUTION 3: Remove any conflicting inline styles
        quickSharingSection.removeAttribute('hidden');

        console.log('📄 After AI fixes - Display:', quickSharingSection.style.display);
        console.log('📄 Computed style:', window.getComputedStyle(quickSharingSection).display);

        // AI RECOMMENDATION: Check parent elements for visibility issues
        let parent = quickSharingSection.parentElement;
        while (parent && parent !== document.body) {
            const parentStyle = window.getComputedStyle(parent);
            if (parentStyle.display === 'none' || parentStyle.visibility === 'hidden') {
                console.log('⚠️ Parent element is hidden:', parent.tagName, parent.className);
                parent.style.display = 'block';
                parent.style.visibility = 'visible';
            }
            parent = parent.parentElement;
        }

        // Pre-populate forms with document info if PDF is available
        if (window.generatedPDF) {
            initializeQuickSharing();
        } else {
            console.log('⚠️ PDF not ready yet, showing section anyway');
            // Set a basic message
            const dividerText = document.querySelector('.divider-text');
            if (dividerText) {
                dividerText.textContent = '📤 Share Document';
            }
        }

        // AI ENHANCEMENT: Smooth animation and scroll
        quickSharingSection.classList.add('fade-in');
        setTimeout(() => {
            quickSharingSection.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest'
            });
        }, 100);

        // AI TESTING: Verify the element is actually visible
        setTimeout(() => {
            const isVisible = quickSharingSection.offsetHeight > 0 && quickSharingSection.offsetWidth > 0;
            console.log('🧪 AI Test - Element actually visible:', isVisible);
            if (!isVisible) {
                console.log('❌ Element still not visible, applying emergency override');
                quickSharingSection.style.cssText = 'display: block !important; visibility: visible !important; opacity: 1 !important; height: auto !important;';
            }
        }, 200);

    } else {
        console.log('❌ quickSharingSection not found in DOM');
        // AI DEBUGGING: Enhanced element search
        const allElements = document.querySelectorAll('[id*="sharing"], [class*="sharing"]');
        console.log('🔍 Found sharing-related elements:', allElements.length);
        allElements.forEach((el, index) => {
            console.log(`   ${index}: ${el.tagName} id="${el.id}" class="${el.className}"`);
        });

        // AI FALLBACK: Try to create the element if it doesn't exist
        console.log('🔧 AI Fallback: Attempting to locate or recreate sharing section');
    }
}

function hideQuickSharingOptions() {
    const quickSharingSection = document.getElementById('quickSharingOptions');
    if (quickSharingSection) {
        quickSharingSection.style.display = 'none';
        hideQuickEmailShare();
        hideQuickWhatsAppShare();
    }
}

function initializeQuickSharing() {
    if (!window.generatedPDF) return;

    const documentName = window.generatedPDF.fileName || 'Document.pdf';
    const documentType = getDocumentTypeDisplayName();
    const companyInfo = getStoredCompanyInfo();

    // Update divider text based on document type
    const dividerText = document.querySelector('.divider-text');
    if (dividerText) {
        dividerText.textContent = `📤 Share ${documentType}`;
    }

    // Pre-fill email form
    const emailSubject = document.getElementById('quickEmailSubject');
    if (emailSubject) {
        emailSubject.value = `${documentType} from ${companyInfo.name || 'DocuGen Pro'}`;
    }

    const emailMessage = document.getElementById('quickEmailMessage');
    if (emailMessage) {
        emailMessage.value = `Hi,

Please find the attached ${documentType.toLowerCase()}: ${documentName}

Best regards,
${companyInfo.representative?.name || 'Team'}`;
    }

    // Pre-fill WhatsApp form
    const whatsappMessage = document.getElementById('quickWhatsAppMessage');
    if (whatsappMessage) {
        whatsappMessage.value = `Hi! Please find the attached ${documentType.toLowerCase()}: ${documentName}

From: ${companyInfo.name || 'DocuGen Pro'}`;
    }
}

// Quick Email Functions
function showQuickEmailShare() {
    hideQuickWhatsAppShare(); // Hide WhatsApp form if open
    const emailForm = document.getElementById('quickEmailForm');
    if (emailForm) {
        emailForm.style.display = 'block';
        // Focus on email input
        setTimeout(() => {
            const emailInput = document.getElementById('quickRecipientEmail');
            if (emailInput) emailInput.focus();
        }, 100);
    }
}

function hideQuickEmailShare() {
    const emailForm = document.getElementById('quickEmailForm');
    if (emailForm) {
        emailForm.style.display = 'none';
    }
}

function sendQuickEmail() {
    const recipientEmail = document.getElementById('quickRecipientEmail').value;
    const emailSubject = document.getElementById('quickEmailSubject').value;
    const emailMessage = document.getElementById('quickEmailMessage').value;

    if (!recipientEmail) {
        alert('Please enter a recipient email address.');
        return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(recipientEmail)) {
        alert('Please enter a valid email address.');
        return;
    }

    const documentName = window.generatedPDF?.fileName || 'Document.pdf';
    const subject = encodeURIComponent(emailSubject || `Document from DocuGen Pro`);
    const body = encodeURIComponent(`${emailMessage}

---
Document: ${documentName}
Generated with DocuGen Pro
${new Date().toLocaleString()}`);

    const mailtoLink = `mailto:${recipientEmail}?subject=${subject}&body=${body}`;
    window.open(mailtoLink);

    alert(`📧 Email client opened!\n\nTo: ${recipientEmail}\nSubject: ${emailSubject}\n\nPlease attach the downloaded PDF file and send the email.`);

    trackDocumentSharing('quick-email', recipientEmail);
    hideQuickEmailShare();
}

// Quick WhatsApp Functions
function showQuickWhatsAppShare() {
    hideQuickEmailShare(); // Hide email form if open
    const whatsappForm = document.getElementById('quickWhatsAppForm');
    if (whatsappForm) {
        whatsappForm.style.display = 'block';
        // Focus on message input
        setTimeout(() => {
            const messageInput = document.getElementById('quickWhatsAppMessage');
            if (messageInput) messageInput.focus();
        }, 100);
    }
}

function hideQuickWhatsAppShare() {
    const whatsappForm = document.getElementById('quickWhatsAppForm');
    if (whatsappForm) {
        whatsappForm.style.display = 'none';
    }
}

function sendQuickWhatsApp() {
    const phoneNumber = document.getElementById('quickWhatsAppNumber').value;
    const message = document.getElementById('quickWhatsAppMessage').value;

    const documentName = window.generatedPDF?.fileName || 'Document.pdf';
    const fullMessage = `${message}

Document: ${documentName}
Generated with DocuGen Pro`;

    const encodedMessage = encodeURIComponent(fullMessage);

    if (phoneNumber) {
        const cleanNumber = phoneNumber.replace(/[^\d+]/g, '');
        const whatsappUrl = `https://wa.me/${cleanNumber}?text=${encodedMessage}`;
        window.open(whatsappUrl, '_blank');
    } else {
        const whatsappUrl = `https://wa.me/?text=${encodedMessage}`;
        window.open(whatsappUrl, '_blank');
    }

    alert('💬 WhatsApp opened! Please attach the downloaded PDF file and send the message.');
    trackDocumentSharing('quick-whatsapp', phoneNumber || 'unknown');
    hideQuickWhatsAppShare();
}

function sendQuickWhatsAppWeb() {
    const phoneNumber = document.getElementById('quickWhatsAppNumber').value;
    const message = document.getElementById('quickWhatsAppMessage').value;

    const documentName = window.generatedPDF?.fileName || 'Document.pdf';
    const fullMessage = `${message}

Document: ${documentName}
Generated with DocuGen Pro`;

    const encodedMessage = encodeURIComponent(fullMessage);

    if (phoneNumber) {
        const cleanNumber = phoneNumber.replace(/[^\d+]/g, '');
        const whatsappWebUrl = `https://web.whatsapp.com/send?phone=${cleanNumber}&text=${encodedMessage}`;
        window.open(whatsappWebUrl, '_blank');
    } else {
        const whatsappWebUrl = `https://web.whatsapp.com/send?text=${encodedMessage}`;
        window.open(whatsappWebUrl, '_blank');
    }

    alert('🌐 WhatsApp Web opened! Please attach the downloaded PDF file and send the message.');
    trackDocumentSharing('quick-whatsapp-web', phoneNumber || 'unknown');
    hideQuickWhatsAppShare();
}

// Generate comprehensive artist agreement content
function generateArtistAgreementContent(doc, margin, startY, contentWidth, normalSize, sectionTitleSize, logoImg) {
    console.log('🔍 ARTIST AGREEMENT DEBUG - Starting generation...');

    // STEP 1: Validate ALL input parameters
    console.log('📋 Input Parameters Validation:', {
        'doc object': doc,
        'doc type': typeof doc,
        'doc.internal exists': !!doc.internal,
        'doc.text function': typeof doc.text,
        'margin': margin,
        'margin type': typeof margin,
        'margin valid': typeof margin === 'number' && !isNaN(margin) && margin >= 0,
        'startY': startY,
        'startY type': typeof startY,
        'startY valid': typeof startY === 'number' && !isNaN(startY) && startY > 0,
        'contentWidth': contentWidth,
        'contentWidth type': typeof contentWidth,
        'normalSize': normalSize,
        'normalSize type': typeof normalSize,
        'sectionTitleSize': sectionTitleSize,
        'sectionTitleSize type': typeof sectionTitleSize,
        'logoImg': logoImg
    });

    let y = margin - 14; // Move content up by 0.5cm (14 pixels) - fine-tuned positioning
    const lineHeight = 6;
    const sectionSpacing = 15;
    const pageHeight = doc.internal.pageSize.height;
    const bottomMargin = 35; // Increased bottom margin to avoid footer overlap

    // STEP 2: Validate calculated values
    console.log('📐 Calculated Values Validation:', {
        'y initial': y,
        'y type': typeof y,
        'y valid': typeof y === 'number' && !isNaN(y) && y > 0,
        'lineHeight': lineHeight,
        'sectionSpacing': sectionSpacing,
        'pageHeight': pageHeight,
        'pageHeight type': typeof pageHeight,
        'pageHeight valid': typeof pageHeight === 'number' && !isNaN(pageHeight) && pageHeight > 0,
        'bottomMargin': bottomMargin
    });

    // STEP 3: Validate fonts
    console.log('🔤 Font Validation:', {
        'PDF_HEADING_FONT': PDF_HEADING_FONT,
        'PDF_HEADING_FONT type': typeof PDF_HEADING_FONT,
        'PDF_BODY_FONT': PDF_BODY_FONT,
        'PDF_BODY_FONT type': typeof PDF_BODY_FONT
    });

    // Function to check if we need a new page
    function checkPageBreak(requiredSpace = 25) {
        const needsBreak = y + requiredSpace > pageHeight - bottomMargin - 10;
        console.log('📄 Page break check:', {
            'current y': y,
            'required space': requiredSpace,
            'page height': pageHeight,
            'bottom margin': bottomMargin,
            'calculation': y + requiredSpace,
            'threshold': pageHeight - bottomMargin - 10,
            'needs break': needsBreak
        });

        if (needsBreak) { // Extra safety margin
            console.log('🔄 Adding new page...');
            doc.addPage();
            y = margin; // Use standard margin on new pages
            console.log('✅ New page added, y reset to:', y);
            return true;
        }
        return false;
    }

    // Enhanced text wrapping function with better page break handling and safe text validation
    function addWrappedText(text, x, yPos, maxWidth, fontSize = 9, lineHeight = 5) {
        console.log('📝 addWrappedText called with:', {
            'text': text,
            'text type': typeof text,
            'x': x,
            'x type': typeof x,
            'yPos': yPos,
            'yPos type': typeof yPos,
            'maxWidth': maxWidth,
            'fontSize': fontSize,
            'lineHeight': lineHeight
        });

        // Validate text input
        const safeTextValue = safeText(text, '');
        console.log('🔒 Safe text result:', {
            'original text': text,
            'safe text': safeTextValue,
            'safe text type': typeof safeTextValue
        });

        if (!safeTextValue) {
            console.log('⚠️ No safe text value, returning original yPos:', yPos);
            return yPos;
        }

        try {
            doc.setFontSize(fontSize);
            const lines = doc.splitTextToSize(safeTextValue, maxWidth);
            console.log('📄 Text split into lines:', {
                'lines count': lines.length,
                'lines': lines
            });

            lines.forEach((line, index) => {
                console.log(`📝 Processing line ${index + 1}:`, {
                    'line': line,
                    'line type': typeof line,
                    'x': x,
                    'yPos': yPos
                });

                checkPageBreak(lineHeight + 2);

                // Additional safety check for line content
                if (line && typeof line === 'string') {
                    try {
                        console.log(`🎯 About to call doc.text for line ${index + 1}...`);
                        safeDocText(x, yPos, line);
                        console.log(`✅ Line ${index + 1} added successfully`);
                    } catch (lineError) {
                        console.error(`❌ Error adding line ${index + 1}:`, lineError);
                        console.log('🔍 Line error details:', {
                            'line': line,
                            'x': x,
                            'yPos': yPos,
                            'error': lineError.message
                        });
                        throw lineError;
                    }
                } else {
                    console.warn(`⚠️ Skipping invalid line ${index + 1}:`, line);
                }
                yPos += lineHeight;
            });

            console.log('✅ addWrappedText completed, final yPos:', yPos);
            return yPos;
        } catch (error) {
            console.error('❌ Error in addWrappedText:', error);
            throw error;
        }
    }

    // Safe doc.text wrapper to prevent jsPDF errors
    function safeDocText(text, x, y, options = {}) {
        try {
            const safeTextValue = safeText(text, '');
            if (safeTextValue && typeof x === 'number' && typeof y === 'number') {
                doc.text(safeTextValue, x, y, options);
            } else {
                console.warn('⚠️ Skipped invalid text:', { text, x, y });
            }
        } catch (error) {
            console.error('❌ Error in safeDocText:', error, { text, x, y });
        }
    }

    // Function to add page numbers
    function addPageNumber(pageNum) {
        const pageY = doc.internal.pageSize.height - 15;
        setProfessionalBodyFont(doc, 'normal', 10);
        doc.setTextColor(100, 100, 100);
        doc.text(`Page ${pageNum}`, doc.internal.pageSize.width / 2, pageY, { align: 'center' });
        doc.setTextColor(0, 0, 0); // Reset to black
    }

    // Get stored company information for dynamic data
    const storedCompanyInfo = getStoredCompanyInfo();

    // Helper function to safely get text values and prevent empty/null/undefined values
    function safeText(value, fallback = 'Not specified') {
        console.log('🔒 safeText called with:', { value, fallback, 'value type': typeof value });

        if (value === null || value === undefined || value === '') {
            console.log('⚠️ Using fallback value:', fallback);
            return fallback;
        }

        const result = String(value).trim() || fallback;
        console.log('✅ safeText result:', { original: value, result, 'result type': typeof result });
        return result;
    }

    // Enhanced safe doc.text function using WORKING PATTERN (x, y, text)
    function safeDocText(x, y, text, options = null) {
        console.log('🎯 safeDocText called with WORKING PATTERN (x, y, text):', {
            'x': x,
            'x type': typeof x,
            'y': y,
            'y type': typeof y,
            'text': text,
            'text type': typeof text,
            'options': options
        });

        // Validate coordinates - ensure they're valid numbers
        const safeX = typeof x === 'number' && !isNaN(x) ? x : 20;
        const safeY = typeof y === 'number' && !isNaN(y) && y > 0 ? y : 50;

        // Validate text - ensure it's a string using template literal pattern
        const safeTextValue = `${text || ''}`.trim();
        if (!safeTextValue) {
            console.warn('⚠️ Empty text provided, skipping doc.text call');
            return;
        }

        if (safeX !== x || safeY !== y) {
            console.warn('⚠️ Invalid coordinates corrected:', {
                'original x': x, 'safe x': safeX,
                'original y': y, 'safe y': safeY
            });
        }

        try {
            console.log('🎯 Calling doc.text with WORKING PATTERN (x, y, text):', {
                'x': safeX,
                'y': safeY,
                'text': safeTextValue,
                'options': options
            });

            // Use the WORKING PATTERN: doc.text(x, y, text)
            if (options) {
                doc.text(safeX, safeY, safeTextValue, options);
            } else {
                doc.text(safeX, safeY, safeTextValue);
            }

            console.log('✅ doc.text call successful with WORKING PATTERN');
        } catch (error) {
            console.error('❌ doc.text failed even with WORKING PATTERN:', error);
            console.log('🔍 Failed arguments:', {
                'x': safeX,
                'y': safeY,
                'text': safeTextValue,
                'options': options
            });
            throw error;
        }
    }

    // Get form values with fallbacks to stored company data - with safe text validation
    const contractCompanyName = safeText(
        document.getElementById('contractCompanyName')?.value ||
        storedCompanyInfo?.name ||
        storedCompanyInfo?.companyName,
        'BROTHER COLLECTIVE PTY (LTD)'
    );

    const contractMonth = safeText(document.getElementById('contractMonth')?.value, 'May 2025');
    const contractTitle = safeText(document.getElementById('contractTitleArtist')?.value, 'BOOKING CONTRACT');
    const agreementDay = safeText(document.getElementById('agreementDay')?.value, '___');
    const agreementMonth = safeText(document.getElementById('agreementMonth')?.value, '___');
    const agreementYear = safeText(document.getElementById('agreementYear')?.value, '2025');

    const purchaserAddress = safeText(
        document.getElementById('purchaserAddress')?.value ||
        storedCompanyInfo?.address ||
        storedCompanyInfo?.companyAddress,
        '181 Bryanston Drive, Bryanston, Johannesburg, 2191'
    );

    const purchaserRegNumber = safeText(
        document.getElementById('purchaserRegNumber')?.value ||
        storedCompanyInfo?.regNumber ||
        storedCompanyInfo?.registrationNumber,
        '2024/772455/07'
    );

    const purchaserRepresentative = safeText(
        document.getElementById('purchaserRepresentative')?.value ||
        storedCompanyInfo?.representative?.name ||
        storedCompanyInfo?.representativeName,
        'LIZIWE KWANINI'
    );

    const agentAddress = safeText(document.getElementById('agentAddress')?.value, '____________________');
    const artistName = safeText(document.getElementById('artistName')?.value, 'JAHSEED & ADMIRAL');
    const eventName = safeText(document.getElementById('eventName')?.value, 'FREEDOM DAY FESTIVAL');
    const performanceVenue = safeText(document.getElementById('performanceVenue')?.value, 'Constitutional Hill, 11 Kotze Street, Johannesburg\nGPS: -26.190217, 28.041916');
    const performanceDuration = safeText(document.getElementById('performanceDuration')?.value, '30 minutes');
    const performanceDate = safeText(document.getElementById('performanceDate')?.value, '2025-06-28');
    const performanceTime = safeText(document.getElementById('performanceTime')?.value, '18:40 - 19:10 (subject to change)');
    const performanceFee = safeText(document.getElementById('performanceFee')?.value, 'R25 000');

    // Build invoice address from stored company info with safe text validation
    const defaultInvoiceAddress = storedCompanyInfo ?
        `${safeText(storedCompanyInfo.name || storedCompanyInfo.companyName, 'Company Name')}\n${safeText(storedCompanyInfo.address || storedCompanyInfo.companyAddress, 'Company Address')}\n${safeText(storedCompanyInfo.regNumber || storedCompanyInfo.registrationNumber, 'Registration Number')}` :
        'Brother Collective PTY (LTD)\n181 Bryanston Drive, Sandton, 2191\n2024/772455/07';
    const invoiceAddress = safeText(document.getElementById('invoiceAddress')?.value, defaultInvoiceAddress);
    const contractPaymentTerms = safeText(document.getElementById('contractPaymentTerms')?.value, 'Payment terms to be specified');

    console.log('📋 Artist Agreement data validation completed:', {
        contractCompanyName,
        eventName,
        purchaserAddress,
        purchaserRegNumber,
        purchaserRepresentative,
        performanceVenue,
        performanceDate,
        performanceTime,
        performanceFee
    });

    // PAGE 1 - HEADER WITH LARGE CENTERED LOGO
    checkPageBreak(120);

    // Add large centered logo at the top
    if (logoImg && logoImg.src) {
        try {
            const logoSize = 80; // Increased logo size
            const logoX = (doc.internal.pageSize.width - logoSize) / 2;
            const logoY = y;

            // Add logo with higher quality
            doc.addImage(logoImg.src, 'PNG', logoX, logoY, logoSize, logoSize);
            y += logoSize + 15; // Space after logo
        } catch (e) {
            console.error('Error adding logo:', e);
            y += 10; // Fallback spacing
        }
    } else {
        y += 10; // Space if no logo
    }

    // Main title - "ARTIST PERFORMANCE AGREEMENT"
    setProfessionalHeadingFont(doc, 'bold', 16);
    safeDocText(doc.internal.pageSize.width / 2, y, 'ARTIST PERFORMANCE AGREEMENT', { align: 'center' });
    y += 15;

    // "between" text
    setProfessionalBodyFont(doc, 'normal', 12);
    safeDocText(doc.internal.pageSize.width / 2, y, 'between', { align: 'center' });
    y += 15;

    // Agreement details - ALL CENTERED
    setProfessionalBodyFont(doc, 'normal', normalSize);
    safeDocText(doc.internal.pageSize.width / 2, y, `Made this ${agreementDay} day of ${agreementMonth} ${agreementYear} between:`, { align: 'center' });
    y += 10;

    // Artist/Band details - ALL CENTERED
    setProfessionalHeadingFont(doc, 'bold', normalSize);
    safeDocText(doc.internal.pageSize.width / 2, y, `${contractCompanyName}`, { align: 'center' });
    y += 5;
    setProfessionalBodyFont(doc, 'normal', normalSize);
    safeDocText(doc.internal.pageSize.width / 2, y, '(Hereinafter referred to as the "Artist/Band")', { align: 'center' });
    y += 8;

    safeDocText(doc.internal.pageSize.width / 2, y, 'DOMICILED AT:', { align: 'center' });
    y += 5;
    const addressLines = purchaserAddress.split('\n');
    addressLines.forEach(line => {
        safeDocText(doc.internal.pageSize.width / 2, y, `${line}`, { align: 'center' });
        y += 5;
    });

    safeDocText(doc.internal.pageSize.width / 2, y, `COMPANY REGISTRATION NO: ${purchaserRegNumber}`, { align: 'center' });
    y += 8;

    safeDocText(doc.internal.pageSize.width / 2, y, 'REPRESENTED BY:', { align: 'center' });
    y += 5;
    safeDocText(doc.internal.pageSize.width / 2, y, `${purchaserRepresentative}`, { align: 'center' });
    y += 10;

    setProfessionalHeadingFont(doc, 'bold', normalSize);
    safeDocText(doc.internal.pageSize.width / 2, y, 'And', { align: 'center' });
    y += 8;

    // Client/Agent details - ALL CENTERED
    setProfessionalBodyFont(doc, 'normal', normalSize);
    safeDocText(doc.internal.pageSize.width / 2, y, '(Hereinafter referred to as the "CLIENT/AGENT")', { align: 'center' });
    y += 8;

    safeDocText(doc.internal.pageSize.width / 2, y, 'DOMICILED AT (ADDRESS):', { align: 'center' });
    y += 5;
    if (agentAddress && agentAddress !== '____________________') {
        const agentAddressLines = agentAddress.split('\n');
        agentAddressLines.forEach(line => {
            safeDocText(doc.internal.pageSize.width / 2, y, `${line}`, { align: 'center' });
            y += 5;
        });
    } else {
        safeDocText(doc.internal.pageSize.width / 2, y, '____________________', { align: 'center' });
        y += 5;
    }
    y += 5;

    safeDocText(doc.internal.pageSize.width / 2, y, 'To Perform at :', { align: 'center' });
    y += 5;
    setProfessionalHeadingFont(doc, 'bold', normalSize);
    safeDocText(doc.internal.pageSize.width / 2, y, `${eventName}`, { align: 'center' });
    y += 5;
    setProfessionalBodyFont(doc, 'normal', normalSize);
    safeDocText(doc.internal.pageSize.width / 2, y, '(Name of Event)', { align: 'center' });
    y += 15;

    // Signature lines for page 1 - positioned at opposite sides
    safeDocText(margin, y, 'FOR CLIENT: ____________________');
    safeDocText(doc.internal.pageSize.width - margin - 64, y, 'FOR ARTIST: ____________________'); // Moved 2cm (56px) more to the right

    // Add page number to Page 1
    addPageNumber(1);

    // PAGE 2 - TABLE OF CONTENTS
    doc.addPage();
    y = margin + 20; // Move content up by reducing from 28 to 20 to make space for additional entries

    setProfessionalHeadingFont(doc, 'bold', 18);
    safeDocText(doc.internal.pageSize.width / 2, y, 'TABLE OF CONTENTS', { align: 'center' });
    y += 18; // Reduced spacing from 20 to 18

    // Right align "Page No" header
    setProfessionalHeadingFont(doc, 'bold', 12);
    safeDocText(doc.internal.pageSize.width - margin - 20, y, 'Page No', { align: 'right' });
    y += 12; // Reduced spacing from 15 to 12

    // Table of contents items
    const tocItems = [
        { num: '1.', title: 'DEFINITIONS AND INTREPRETATION', page: '3', dots: 52 },
        { num: '2.', title: 'SCOPE OF SERVICES', page: '6', dots: 65 },
        { num: '3.', title: 'DURATION', page: '6', dots: 75 },
        { num: '4.', title: 'PAYMENT TERMS', page: '6', dots: 68 },
        { num: '5.', title: 'OBLIGATIONS OF THE CLIENT', page: '7', dots: 55 },
        { num: '6.', title: 'OBLIGATIONS OF ARTIST', page: '7', dots: 60 },
        { num: '7.', title: 'FILMING RIGHTS', page: '7', dots: 68 },
        { num: '8.', title: 'GOOD FAITH NEGOTIATIONS', page: '8', dots: 55 },
        { num: '9.', title: 'WARRANTIES', page: '8', dots: 70 },
        { num: '10.', title: 'CONFIDENTIAL INFORMATION', page: '9', dots: 55 },
        { num: '11.', title: 'TERMINATION', page: '11', dots: 68 },
        { num: '12.', title: 'INTELLECTUAL PROPERTY', page: '11', dots: 56 },
        { num: '13.', title: 'FORCE MAJEURE', page: '11', dots: 65 },
        { num: '14.', title: 'ADDRESSES AND NOTICES', page: '12', dots: 56 },
        { num: '15.', title: 'VARIATION, CANCELLATION AND WAIVER', page: '12', dots: 40 },
        { num: '16.', title: 'ENTIRE AGREEMENT', page: '13', dots: 62 },
        { num: '17.', title: 'SEVERABILITY', page: '13', dots: 68 },
        { num: '18.', title: 'DISPUTE RESOLUTION', page: '13', dots: 60 },
        { num: '19.', title: 'GOVERNING LAW', page: '14', dots: 65 },
        { num: '20.', title: 'FEES AND COSTS', page: '15', dots: 65 },
        { num: '21.', title: 'INDEMNITY', page: '15', dots: 72 },
        { num: '22.', title: 'CO-OPERATION', page: '15', dots: 68 },
        { num: '23.', title: 'CESSION AND DELEGATION', page: '16', dots: 55 },
        { num: '24.', title: 'NO STIPULATION FOR THE BENEFIT OF A THIRD PERSON', page: '16', dots: 17 },
        { num: '25.', title: 'COUNTERPARTS', page: '16', dots: 65 }
    ];

    setProfessionalBodyFont(doc, 'normal', normalSize);

    tocItems.forEach(item => {
        // Check if we need a new page
        checkPageBreak(7); // Reduced from 8 to 7

        // Item number and title
        safeDocText(margin, y, `${item.num}    ${item.title}`);

        // Create dots
        const dots = '.'.repeat(item.dots);
        const titleWidth = doc.getTextWidth(`${item.num}    ${item.title}`);
        safeDocText(margin + titleWidth, y, `${dots}`);

        // Page number (right aligned)
        safeDocText(doc.internal.pageSize.width - margin - 20, y, `${item.page}`, { align: 'right' });

        y += 6.5; // Reduced spacing from 7 to 6.5 to fit more entries
    });

    // Add page number to Page 2
    addPageNumber(2);

    // PAGE 3 - DEFINITIONS AND INTREPRETATION
    doc.addPage();
    y = margin - 5; // Move up by 20 points (0.7cm) from original margin + 15

    // Main heading with better styling
    setProfessionalHeadingFont(doc, 'bold', 17); // Increased for better readability
    doc.setTextColor(0, 0, 0);
    safeDocText(margin, y, '1.    DEFINITIONS AND INTREPRETATION');

    // Add underline for main heading
    const headingWidth = doc.getTextWidth('1.    DEFINITIONS AND INTREPRETATION');
    doc.setDrawColor(0, 0, 0);
    doc.setLineWidth(0.5);
    doc.line(margin, y + 2, margin + headingWidth, y + 2);
    y += 16; // Slightly increased spacing

    // Subsection 1.1
    setProfessionalHeadingFont(doc, 'bold', 12); // Increased for better readability
    safeDocText(margin, y, '1.1.');
    y += 7; // Slightly increased spacing

    // Definition paragraph (editable from form) with better formatting
    setProfessionalBodyFont(doc, 'normal', 9.5); // Increased for better readability
    const definitionText = document.getElementById('definitionsIntro')?.value || 'In this Agreement, the following words shall, unless otherwise stated or inconsistent with the context in which they appear bear the following meaning and other derived for the same origins as such words (that is, cognate words) shall bear corresponding meaning:';
    const definitionLines = doc.splitTextToSize(definitionText, contentWidth - 30);
    definitionLines.forEach(line => {
        safeDocText(margin + 15, y, `${line}`);
        y += 4.5; // Slightly increased line spacing
    });
    y += 11; // Slightly increased spacing

    // Get definitions from form fields (editable content)
    const definitions = getDefinitionsFromForm();

    // Add definitions with compact professional formatting
    definitions.forEach((def, index) => {
        checkPageBreak(20); // Reduced space check

        // Create a subtle background for each definition row
        doc.setFillColor(250, 250, 252);
        const rowHeight = 16; // Increased row height for better readability
        doc.rect(margin, y - 2, contentWidth, rowHeight, 'F');

        // Add a left border for visual appeal
        doc.setFillColor(74, 158, 255);
        doc.rect(margin, y - 2, 1.5, rowHeight, 'F');

        // Term (bold, left-aligned in a defined column)
        setProfessionalHeadingFont(doc, 'bold', 9.5); // Increased for better readability
        doc.setTextColor(0, 0, 0);

        // Split term if too long and position it properly
        const termColumnWidth = 78; // Slightly wider for larger font
        const termLines = doc.splitTextToSize(def.term, termColumnWidth);
        termLines.forEach((termLine, termLineIndex) => {
            safeDocText(margin + 6, y + 2 + (termLineIndex * 4.5), `${termLine}`);
        });

        // Definition (normal, in right column with proper wrapping)
        setProfessionalBodyFont(doc, 'normal', 9); // Increased for better readability

        if (def.highlight) {
            // Special handling for Artist definition with dynamic content and proper wrapping
            const artistBandName = document.getElementById('artistBandName')?.value || 'BONGO MAFFIN';
            const artistMembers = document.getElementById('artistMembers')?.value || 'Thandiswa Mazwai, Tshepo Seete, Harold MathlakU, Anesu Mupemhi';

            // Create the full definition text
            const fullDefinition = `means ${artistBandName} – Comprising of: ${artistMembers}.`;

            // Calculate available width for definition column
            const defColumnWidth = contentWidth - termColumnWidth - 18;
            const defStartX = margin + termColumnWidth + 10;

            // Split the definition into parts for highlighting
            const beforeHighlight = `means ${artistBandName} – `;
            const highlightText = `Comprising of: ${artistMembers}.`;

            // Check if the text fits on one line
            const totalWidth = doc.getTextWidth(fullDefinition);

            if (totalWidth <= defColumnWidth) {
                // Fits on one line - use inline highlighting
                doc.setTextColor(0, 0, 0);
                safeDocText(defStartX, y + 2, `${beforeHighlight}`);

                const beforeWidth = doc.getTextWidth(beforeHighlight);
                doc.setTextColor(200, 50, 50);
                safeDocText(defStartX + beforeWidth, y + 2, `${highlightText}`);
                doc.setTextColor(0, 0, 0);
            } else {
                // Needs wrapping - treat as multi-line
                const defLines = doc.splitTextToSize(fullDefinition, defColumnWidth);

                defLines.forEach((line, lineIndex) => {
                    if (lineIndex === 0) {
                        // Check if first line contains the highlighted part
                        if (line.includes('Comprising of:')) {
                            // Split the line for highlighting
                            const parts = line.split('Comprising of:');
                            doc.setTextColor(0, 0, 0);
                            safeDocText(defStartX, y + 2, `${parts[0]}Comprising of:`);

                            if (parts[1]) {
                                const beforeWidth = doc.getTextWidth(parts[0] + 'Comprising of:');
                                doc.setTextColor(200, 50, 50);
                                safeDocText(defStartX + beforeWidth, y + 2, `${parts[1]}`);
                                doc.setTextColor(0, 0, 0);
                            }
                        } else {
                            doc.setTextColor(0, 0, 0);
                            safeDocText(defStartX, y + 2, `${line}`);
                        }
                    } else {
                        // Subsequent lines
                        checkPageBreak(5);
                        y += 4; // Increased spacing for larger font

                        if (line.includes('Comprising of:') || line.includes(artistMembers)) {
                            doc.setTextColor(200, 50, 50);
                            safeDocText(defStartX, y + 2, `${line}`);
                            doc.setTextColor(0, 0, 0);
                        } else {
                            doc.setTextColor(0, 0, 0);
                            safeDocText(defStartX, y + 2, `${line}`);
                        }
                    }
                });
            }
        } else {
            // Regular definition formatting
            const defColumnWidth = contentWidth - termColumnWidth - 18;
            const defLines = doc.splitTextToSize(def.definition, defColumnWidth);
            defLines.forEach((line, lineIndex) => {
                if (lineIndex === 0) {
                    safeDocText(margin + termColumnWidth + 10, y + 2, `${line}`);
                } else {
                    // For multi-line definitions, continue on next lines
                    checkPageBreak(5);
                    y += 4; // Increased spacing for larger font
                    safeDocText(margin + termColumnWidth + 10, y + 2, `${line}`);
                }
            });
        }

        y += rowHeight + 2; // Slightly increased spacing between definitions
    });



    y += 11; // Slightly increased spacing

    // Add page number to Page 3
    addPageNumber(3);

    // PAGE 4 - ADDITIONAL DEFINITIONS
    doc.addPage();
    y = margin;

    // Page header
    setProfessionalHeadingFont(doc, 'bold', 18);
    doc.text('Artist Agreement', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 8;
    setProfessionalBodyFont(doc, 'normal', 12);
    doc.text('Page 4 - Additional Definitions', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 20;

    // Get Page 4 specific definitions
    const page4Definitions = [
        {
            term: '"Fees"-',
            definition: document.getElementById('feesDefinition')?.value || 'means the fees payable to the Artists in consideration for the Services provided in terms of this Agreement as per Annexure "A";'
        },
        {
            term: '"Good Industry Practice" -',
            definition: document.getElementById('goodIndustryPracticeDefinition')?.value || 'means providing the Service in a proper and professional manner, taking into account generally acceptable standards, practice, methods and procedures conforming to Applicable Laws and exercising that degree of skill, that would be reasonable and ordinarily be expected of a skilled and experienced person engaged in a similar type of undertaking under similar circumstance;'
        },
        {
            term: '"Intellectual Property" - Rights',
            definition: document.getElementById('intellectualPropertyDefinition')?.value || 'means all patents, copyrights, design rights, trademarks, service marks, trade secrets, know-how, database rights and other rights in the nature of intellectual property rights (whether registered or unregistered) and all applications for the same, in the Territory;'
        },
        {
            term: '"Parties and/or Party" -',
            definition: document.getElementById('partiesDefinition')?.value || 'Means individually or collectively, as the context may require the Artist and the Client;'
        },
        {
            term: '"Performance"-',
            definition: document.getElementById('performanceDefinition')?.value || 'means the musical performance to be undertaken by the Artist as part of the Services in terms of this Agreement;'
        },
        {
            term: '"Performance Date" –',
            definition: document.getElementById('performanceDateDefinition')?.value || 'means (insert performance date) being the date when the Artist will undertake the Performance as part of the Services for the Client in terms of this Agreement;'
        },
        {
            term: '"Rights of Copyright"-',
            definition: document.getElementById('rightsOfCopyrightDefinition')?.value || 'means the bundle of rights contemplated in the Copyright Act No. 98 of 1978 as amended from time to time, subsisting in the Performance and the music; and any additional or comparable rights of copyright, or sui generis right that may subsist in the future in South Africa in the Performance or that subsists in the Performanceor in the music in any other territory of jurisdiction of the world, including but not limited to do'
        }
    ];

    // Add custom definitions from Page 4
    const customDefinitionsPage4 = document.querySelectorAll('#customDefinitionsPage4Container .custom-definition');
    customDefinitionsPage4.forEach(customDef => {
        const termInput = customDef.querySelector('.custom-term');
        const defInput = customDef.querySelector('.custom-definition-text');
        if (termInput && defInput && termInput.value.trim() && defInput.value.trim()) {
            page4Definitions.push({
                term: `"${termInput.value.trim()}"`,
                definition: defInput.value.trim()
            });
        }
    });

    // Render Page 4 definitions using the same style as Page 3
    page4Definitions.forEach((def, index) => {
        checkPageBreak(20); // Ensure space for definition

        // Create a subtle background for each definition row
        doc.setFillColor(250, 250, 252);
        const rowHeight = 16; // Increased row height for better readability
        doc.rect(margin, y - 2, contentWidth, rowHeight, 'F');

        // Add a left border for visual appeal
        doc.setFillColor(74, 158, 255);
        doc.rect(margin, y - 2, 1.5, rowHeight, 'F');

        // Term (bold, left-aligned in a defined column)
        setProfessionalHeadingFont(doc, 'bold', 9.5); // Increased for better readability
        doc.setTextColor(0, 0, 0);

        // Split term if too long and position it properly
        const termColumnWidth = 78; // Slightly wider for larger font
        const termLines = doc.splitTextToSize(def.term, termColumnWidth);
        termLines.forEach((termLine, termLineIndex) => {
            doc.text(termLine, margin + 6, y + 2 + (termLineIndex * 4.5));
        });

        // Definition (normal, in right column with proper wrapping)
        setProfessionalBodyFont(doc, 'normal', 9); // Increased for better readability

        // Regular definition formatting
        const defColumnWidth = contentWidth - termColumnWidth - 18;
        const defLines = doc.splitTextToSize(def.definition, defColumnWidth);
        defLines.forEach((line, lineIndex) => {
            if (lineIndex === 0) {
                doc.text(line, margin + termColumnWidth + 10, y + 2);
            } else {
                // For multi-line definitions, continue on next lines
                checkPageBreak(5);
                y += 4; // Increased spacing for larger font
                doc.text(line, margin + termColumnWidth + 10, y + 2);
            }
        });

        y += rowHeight + 2; // Slightly increased spacing between definitions
    });

    // Add page number to Page 4
    addPageNumber(4);

    // PAGE 5 - ADDITIONAL DEFINITIONS & CLAUSES
    doc.addPage();
    y = margin;

    // Page header
    setProfessionalHeadingFont(doc, 'bold', 18);
    doc.text('Artist Agreement', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 8;
    setProfessionalBodyFont(doc, 'normal', 12);
    doc.text('Page 5 - Additional Definitions & Clauses', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 20;

    // Get Page 5 specific definitions and clauses
    const page5Content = [
        {
            term: '"Signature Date"-',
            definition: document.getElementById('signatureDateDefinition')?.value || 'means the date on which this agreement is signed by the party that signs it last in time;'
        },
        {
            term: '"Services" -',
            definition: document.getElementById('servicesDefinition')?.value || 'means all the work, duties and obligations related to the Performance by the Artist pursuant to this Agreement;'
        },
        {
            term: '1.2.',
            definition: document.getElementById('statutoryProvisionClause')?.value || 'Any reference to a statutory provision shall include any subordinate legislation made from time to time under that provision and shall include that provision as modified or re-enacted from time to time.'
        },
        {
            term: '1.3.',
            definition: document.getElementById('genderReferenceClause')?.value || 'In this Agreement, words importing the masculine gender shall include the feminine and neuter genders and vice versa.'
        },
        {
            term: '1.4.',
            definition: document.getElementById('definitionRightsClause')?.value || 'If a definition imposes substantive rights and obligations on a Party, such rights and obligations shall be given effect to and shall be enforceable, notwithstanding that they are contained in a definition.'
        },
        {
            term: '1.5.',
            definition: document.getElementById('daysCalculationClause')?.value || 'Where any number of days is prescribed in this Agreement, those days shall be reckoned exclusively of the first day and inclusively of the last day unless the last day falls on a day which is not a Business Day, in which event the last day shall be the next succeeding Business Day.'
        },
        {
            term: '1.6.',
            definition: document.getElementById('termDefinitionScopeClause')?.value || 'Where any term is defined within the context of any particular clause in this agreement, the term so defined, unless it is specifically stated in the clause in question that the term so defined has limited application to the relevant clause, shall bear the meaning ascribed to it for all purposes in terms of this agreement, notwithstanding that that term has not been defined in this clause.'
        },
        {
            term: '1.7.',
            definition: document.getElementById('agreementTerminationClause')?.value || 'The expiry or termination of this Agreement shall not affect such of the provisions of this agreement which are expressly provided to operate after any such expiry or termination, or which of necessity must continue to have effect after such expiry or termination, notwithstanding that the relevant clauses themselves do not provide for this.'
        },
        {
            term: '1.8.',
            definition: document.getElementById('contractConstructionClause')?.value || 'As the terms of this Agreement have been negotiated by the Parties and drafted for the benefit of the Parties, the rule of construction that the contract shall be interpreted against the Party responsible for its drafting or preparation, shall not apply.'
        },
        {
            term: '1.9.',
            definition: document.getElementById('ejusdemGenerisClause')?.value || 'In this Agreement, the ejusdem generis rule shall not apply and whenever the term "including" is used followed by specific examples, such examples shall not be construed so as to limit the meaning of that term.'
        }
    ];

    // Render Page 5 content using the same style as Page 3 and 4
    page5Content.forEach((def, index) => {
        checkPageBreak(20); // Ensure space for definition

        // Create a subtle background for each definition row
        doc.setFillColor(250, 250, 252);
        const rowHeight = 16; // Increased row height for better readability
        doc.rect(margin, y - 2, contentWidth, rowHeight, 'F');

        // Add a left border for visual appeal
        doc.setFillColor(74, 158, 255);
        doc.rect(margin, y - 2, 1.5, rowHeight, 'F');

        // Term (bold, left-aligned in a defined column)
        setProfessionalHeadingFont(doc, 'bold', 9.5); // Increased for better readability
        doc.setTextColor(0, 0, 0);

        // Split term if too long and position it properly
        const termColumnWidth = 78; // Slightly wider for larger font
        const termLines = doc.splitTextToSize(def.term, termColumnWidth);
        termLines.forEach((termLine, termLineIndex) => {
            doc.text(termLine, margin + 6, y + 2 + (termLineIndex * 4.5));
        });

        // Definition (normal, in right column with proper wrapping)
        setProfessionalBodyFont(doc, 'normal', 9); // Increased for better readability

        // Regular definition formatting
        const defColumnWidth = contentWidth - termColumnWidth - 18;
        const defLines = doc.splitTextToSize(def.definition, defColumnWidth);
        defLines.forEach((line, lineIndex) => {
            if (lineIndex === 0) {
                doc.text(line, margin + termColumnWidth + 10, y + 2);
            } else {
                // For multi-line definitions, continue on next lines
                checkPageBreak(5);
                y += 4; // Increased spacing for larger font
                doc.text(line, margin + termColumnWidth + 10, y + 2);
            }
        });

        y += rowHeight + 2; // Slightly increased spacing between definitions
    });

    // Add page number to Page 5
    addPageNumber(5);

    // PAGE 6 - SCOPE, DURATION & PAYMENT TERMS
    doc.addPage();
    y = margin;

    // Page header
    setProfessionalHeadingFont(doc, 'bold', 18);
    doc.text('Artist Agreement', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 8;
    setProfessionalBodyFont(doc, 'normal', 12);
    doc.text('Page 6 - Scope, Duration & Payment Terms', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 20;

    // 2. SCOPE OF SERVICES
    checkPageBreak(30);
    setProfessionalHeadingFont(doc, 'bold', sectionTitleSize);
    doc.setTextColor(0, 0, 0);
    doc.text('2.    SCOPE OF SERVICES', margin, y);
    y += 10;

    const scopeContent = document.getElementById('scopeOfServicesContent')?.value || 'It is the intention of the Parties that the Artist will engage in a Performance as part of the Services for the Client at the Performance Date and in accordance with the terms of this Agreement in Annexure and Annexure B';

    setProfessionalBodyFont(doc, 'normal', normalSize);
    doc.text('2.1.', margin, y);
    const scopeLines = doc.splitTextToSize(scopeContent, contentWidth - 20);
    scopeLines.forEach((line, index) => {
        if (index === 0) {
            doc.text(line, margin + 20, y);
        } else {
            checkPageBreak(lineHeight + 2);
            y += lineHeight;
            doc.text(line, margin + 20, y);
        }
    });
    y += 15;

    // 3. DURATION
    checkPageBreak(25);
    setProfessionalHeadingFont(doc, 'bold', sectionTitleSize);
    doc.text('3.    DURATION', margin, y);
    y += 10;

    const durationContent = document.getElementById('durationContent')?.value || 'This Agreement shall commence on the Signature Date and shall endure for a period agreed up by the Artist and the Client.';

    setProfessionalBodyFont(doc, 'normal', normalSize);
    doc.text('3.1.', margin, y);
    const durationLines = doc.splitTextToSize(durationContent, contentWidth - 20);
    durationLines.forEach((line, index) => {
        if (index === 0) {
            doc.text(line, margin + 20, y);
        } else {
            checkPageBreak(lineHeight + 2);
            y += lineHeight;
            doc.text(line, margin + 20, y);
        }
    });
    y += 15;

    // 4. PAYMENT TERMS
    checkPageBreak(40);
    setProfessionalHeadingFont(doc, 'bold', sectionTitleSize);
    doc.text('4.    PAYMENT TERMS', margin, y);
    y += 10;

    // 4.1 Fee Consideration
    const paymentIntro = document.getElementById('paymentTermsIntro')?.value || 'In consideration for the Services rendered by the Artist, Client agrees Artist the Fees as provided for in Annexure "A" as follows:';
    setProfessionalBodyFont(doc, 'normal', normalSize);
    doc.text('4.1.', margin, y);
    const introLines = doc.splitTextToSize(paymentIntro, contentWidth - 20);
    introLines.forEach((line, index) => {
        if (index === 0) {
            doc.text(line, margin + 20, y);
        } else {
            checkPageBreak(lineHeight + 2);
            y += lineHeight;
            doc.text(line, margin + 20, y);
        }
    });
    y += 10;

    // 4.2 Deposit Payment
    checkPageBreak(15);
    const depositPayment = document.getElementById('paymentTermsDeposit')?.value || 'Ps .The Client shall pay (50)% of the Fees into the bank account of the Artist on receiving invoice for the booking';
    doc.text('4.2.', margin, y);
    const depositLines = doc.splitTextToSize(depositPayment, contentWidth - 20);
    depositLines.forEach((line, index) => {
        if (index === 0) {
            doc.text(line, margin + 20, y);
        } else {
            checkPageBreak(lineHeight + 2);
            y += lineHeight;
            doc.text(line, margin + 20, y);
        }
    });
    y += 10;

    // 4.3 Balance Payment
    checkPageBreak(15);
    const balancePayment = document.getElementById('paymentTermsBalance')?.value || 'The Client shall deposit the remaining fifty (50)% of the Fees into the bank account of the Artist not less than a week before the show or day of departure.';
    doc.text('4.3.', margin, y);
    const balanceLines = doc.splitTextToSize(balancePayment, contentWidth - 20);
    balanceLines.forEach((line, index) => {
        if (index === 0) {
            doc.text(line, margin + 20, y);
        } else {
            checkPageBreak(lineHeight + 2);
            y += lineHeight;
            doc.text(line, margin + 20, y);
        }
    });
    y += 15;

    // Add custom sections from Page 6
    const customSectionsPage6 = document.querySelectorAll('#customSectionsPage6Container .custom-section');
    customSectionsPage6.forEach(customSection => {
        const titleInput = customSection.querySelector('.custom-section-title');
        const contentInput = customSection.querySelector('.custom-section-content');
        if (titleInput && contentInput && titleInput.value.trim() && contentInput.value.trim()) {
            checkPageBreak(25);

            // Section title
            doc.setFontSize(sectionTitleSize);
            doc.setFont(PDF_HEADING_FONT, 'bold');
            doc.text(titleInput.value.trim(), margin, y);
            y += 10;

            // Section content
            doc.setFontSize(normalSize);
            doc.setFont(PDF_BODY_FONT, 'normal');
            const customPage6Lines = doc.splitTextToSize(contentInput.value.trim(), contentWidth);
            customPage6Lines.forEach(line => {
                checkPageBreak(lineHeight + 2);
                doc.text(line, margin, y);
                y += lineHeight;
            });
            y += 10;
        }
    });

    // Add page number to Page 6
    addPageNumber(6);

    // PAGE 7 - OBLIGATIONS & FILMING RIGHTS
    doc.addPage();
    y = margin;

    // Page header
    doc.setFontSize(18);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('Artist Agreement', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 8;
    doc.setFontSize(12);
    doc.text('Page 7 - Obligations & Filming Rights', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 20;

    // 5. OBLIGATIONS OF THE CLIENT
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.setTextColor(0, 0, 0);
    doc.text('5.    OBLIGATIONS OF THE CLIENT', margin, y);
    y += 10;

    // 5.1 Client obligations intro
    const clientObligationsIntro = document.getElementById('clientObligationsIntro')?.value || 'The Client will be obliged to:';
    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');
    doc.text('5.1.', margin, y);
    const clientIntroLines = doc.splitTextToSize(clientObligationsIntro, contentWidth - 20);
    clientIntroLines.forEach((line, index) => {
        if (index === 0) {
            doc.text(line, margin + 20, y);
        } else {
            checkPageBreak(lineHeight + 2);
            y += lineHeight;
            doc.text(line, margin + 20, y);
        }
    });
    y += 10;

    // Client obligations subsections
    const clientObligations = [
        { num: '5.1.1.', content: document.getElementById('clientObligation511')?.value || 'remunerate the Artist for the Services in accordance with clause 4 above.' },
        { num: '5.1.2.', content: document.getElementById('clientObligation512')?.value || 'Pay all the costs of the accommodation, which shall hold no less than a four (4) star rating; breakfast and dinner, where applicable,' },
        { num: '', content: document.getElementById('travelCostsNote')?.value || 'Ps: travel costs are included in the fee quoted by the artist', isNote: true },
        { num: '5.1.3.', content: document.getElementById('clientObligation513')?.value || 'Details in Annexure A' },
        { num: '5.1.4.', content: document.getElementById('clientObligation514')?.value || 'provide all necessary support to the Artist so as to enable it to effectively render the Services including all sound and lighting requirements detailed in technical riders provided; and' },
        { num: '5.1.5.', content: document.getElementById('clientObligation515')?.value || 'use its best endeavours to ensure that the Artist has timely and adequate access to the location, all information and documentation available that will enable the Artist to render the Services.' }
    ];

    clientObligations.forEach(obligation => {
        checkPageBreak(15);

        if (obligation.isNote) {
            // Special formatting for travel costs note
            doc.setTextColor(255, 0, 0); // Red color
            const noteLines = doc.splitTextToSize(obligation.content, contentWidth - 40);
            noteLines.forEach((line, index) => {
                if (index === 0) {
                    doc.text(line, margin + 40, y);
                } else {
                    checkPageBreak(lineHeight + 2);
                    y += lineHeight;
                    doc.text(line, margin + 40, y);
                }
            });
            doc.setTextColor(0, 0, 0); // Reset to black
        } else {
            doc.text(obligation.num, margin + 20, y);
            const obligationLines = doc.splitTextToSize(obligation.content, contentWidth - 40);
            obligationLines.forEach((line, index) => {
                if (index === 0) {
                    doc.text(line, margin + 40, y);
                } else {
                    checkPageBreak(lineHeight + 2);
                    y += lineHeight;
                    doc.text(line, margin + 40, y);
                }
            });
        }
        y += 8;
    });

    y += 10;

    // 6. OBLIGATIONS OF ARTIST
    checkPageBreak(25);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('6.    OBLIGATIONS OF ARTIST', margin, y);
    y += 10;

    const artistObligations = document.getElementById('artistObligations')?.value || 'The Artist undertakes to conduct her performance with due skill, care and diligence at all times and on a standard commensurate with Good Industry Practice.';
    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');
    doc.text('6.1.', margin, y);
    const artistLines = doc.splitTextToSize(artistObligations, contentWidth - 20);
    artistLines.forEach((line, index) => {
        if (index === 0) {
            doc.text(line, margin + 20, y);
        } else {
            checkPageBreak(lineHeight + 2);
            y += lineHeight;
            doc.text(line, margin + 20, y);
        }
    });
    y += 15;

    // 7. FILMING RIGHTS
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('7.    FILMING RIGHTS', margin, y);
    y += 10;

    // Filming rights subsections
    const filmingRights = [
        { num: '7.1.', content: document.getElementById('filmingRights71')?.value || 'The Artist has not granted the Client the right to film the Performance unless its FOR ONLY ARCHIVING PURPOSES', hasHighlight: true, highlightText: 'FOR ONLY ARCHIVING PURPOSES' },
        { num: '7.2.', content: document.getElementById('filmingRights72')?.value || 'The film shall only be used for promotional use and not for any commercial benefit and /or use to the Client without written consent between the two parties;' },
        { num: '7.3.', content: document.getElementById('filmingRights73')?.value || 'The Client must ensure that ANY PROFESSIONAL people filming the Performance have received due authorisation from the Client and as such must at all times carry the media accreditation permit;', hasHighlight: true, highlightText: 'ANY PROFESSIONAL' },
        { num: '7.4.', content: document.getElementById('filmingRights74')?.value || 'The Client must deploy security services to stop unauthorised PROFESSIONAL people from filming the Performance. This means media or anyone filming from the media booth infront of the stage.', hasHighlight: true, highlightText: 'PROFESSIONAL' }
    ];

    filmingRights.forEach(right => {
        checkPageBreak(15);

        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.setTextColor(0, 0, 0);
        doc.text(right.num, margin, y);

        if (right.hasHighlight && right.highlightText) {
            // Split content and highlight specific text
            const filmingRightLines = doc.splitTextToSize(right.content, contentWidth - 20);
            filmingRightLines.forEach((line, index) => {
                if (index === 0) {
                    if (line.includes(right.highlightText)) {
                        // Split line by highlight text and render with different colors
                        const parts = line.split(right.highlightText);
                        let xPos = margin + 20;

                        // Render first part in black
                        if (parts[0]) {
                            doc.setTextColor(0, 0, 0);
                            doc.text(parts[0], xPos, y);
                            xPos += doc.getTextWidth(parts[0]);
                        }

                        // Render highlight text in red
                        doc.setTextColor(255, 0, 0);
                        doc.text(right.highlightText, xPos, y);
                        xPos += doc.getTextWidth(right.highlightText);

                        // Render remaining part in black
                        if (parts[1]) {
                            doc.setTextColor(0, 0, 0);
                            doc.text(parts[1], xPos, y);
                        }
                    } else {
                        doc.text(line, margin + 20, y);
                    }
                } else {
                    checkPageBreak(lineHeight + 2);
                    y += lineHeight;
                    doc.setTextColor(0, 0, 0);
                    doc.text(line, margin + 20, y);
                }
            });
        } else {
            const filmingRightLines2 = doc.splitTextToSize(right.content, contentWidth - 20);
            filmingRightLines2.forEach((line, index) => {
                if (index === 0) {
                    doc.text(line, margin + 20, y);
                } else {
                    checkPageBreak(lineHeight + 2);
                    y += lineHeight;
                    doc.text(line, margin + 20, y);
                }
            });
        }
        y += 8;
    });

    // Add custom sections from Page 7
    const customSectionsPage7 = document.querySelectorAll('#customSectionsPage7Container .custom-section');
    customSectionsPage7.forEach(customSection => {
        const titleInput = customSection.querySelector('.custom-section-title');
        const contentInput = customSection.querySelector('.custom-section-content');
        if (titleInput && contentInput && titleInput.value.trim() && contentInput.value.trim()) {
            checkPageBreak(25);

            // Section title
            doc.setFontSize(sectionTitleSize);
            doc.setFont(PDF_HEADING_FONT, 'bold');
            doc.setTextColor(0, 0, 0);
            doc.text(titleInput.value.trim(), margin, y);
            y += 10;

            // Section content
            doc.setFontSize(normalSize);
            doc.setFont(PDF_BODY_FONT, 'normal');
            const customPage7Lines = doc.splitTextToSize(contentInput.value.trim(), contentWidth);
            customPage7Lines.forEach(line => {
                checkPageBreak(lineHeight + 2);
                doc.text(line, margin, y);
                y += lineHeight;
            });
            y += 10;
        }
    });

    // Add page number to Page 7
    addPageNumber(7);

    // PAGE 8 - GOOD FAITH NEGOTIATIONS & WARRANTIES
    doc.addPage();
    y = margin;

    // Page header
    doc.setFontSize(18);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('Artist Agreement', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 8;
    doc.setFontSize(12);
    doc.text('Page 8 - Good Faith Negotiations & Warranties', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 20;

    // 8. GOOD FAITH NEGOTIATIONS
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.setTextColor(0, 0, 0);
    doc.text('8.    GOOD FAITH NEGOTIATIONS', margin, y);
    y += 10;

    // Good faith negotiations subsections
    const goodFaithSections = [
        { num: '8.1.', content: document.getElementById('goodFaith81')?.value || 'The Parties shall negotiate in good faith the terms and conditions of this Agreement.' },
        { num: '8.2.', content: document.getElementById('goodFaith82')?.value || 'In addition, the Parties agree to owe to each other a duty of good faith at all times, this means, inter alia, that the Parties shall:' },
        { num: '8.2.1.', content: document.getElementById('goodFaith821')?.value || 'at all times make full disclosure to each other on any matter which may affect the purpose of this Agreement; and', indent: 20 },
        { num: '8.2.2.', content: document.getElementById('goodFaith822')?.value || 'will at all times endeavour to keep all matters related to the activities, plans, strategies and any other material or documents of each other strictly confidential.', indent: 20 }
    ];

    goodFaithSections.forEach(section => {
        checkPageBreak(15);

        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.setTextColor(0, 0, 0);

        const indentLevel = section.indent || 0;
        doc.text(section.num, margin + indentLevel, y);

        const goodFaithLines = doc.splitTextToSize(section.content, contentWidth - 20 - indentLevel);
        goodFaithLines.forEach((line, index) => {
            if (index === 0) {
                doc.text(line, margin + 20 + indentLevel, y);
            } else {
                checkPageBreak(lineHeight + 2);
                y += lineHeight;
                doc.text(line, margin + 20 + indentLevel, y);
            }
        });
        y += 8;
    });

    y += 10;

    // 9. WARRANTIES
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('9.    WARRANTIES', margin, y);
    y += 10;

    // Warranties subsections
    const warrantiesSections = [
        { num: '9.1.', content: document.getElementById('warranties91')?.value || 'The Artist warrants that:' },
        { num: '9.1.1.', content: document.getElementById('warranties911')?.value || 'it has taken all necessary actions to authorize its execution of and to fulfil its obligations under this Agreement;', indent: 20 },
        { num: '9.1.2.', content: document.getElementById('warranties912')?.value || 'its obligations under this Agreement are legal, valid, binding and enforceable against it, in accordance with the terms of this Agreement;', indent: 20 },
        { num: '9.1.3.', content: document.getElementById('warranties913')?.value || 'all information disclosed by or on behalf of the Artist at any time up to Signature Date and up to the end of the term of this Agreement, and in particular, during the time preceding this Agreement to the Client is true, complete and accurate in all material respects and the Artist is not aware of any material facts or circumstances not disclosed to the Client which would, if disclosed, be likely to have an adverse effect on the Client\'s decision to enter into this Agreement with the Artist;', indent: 20 },
        { num: '9.1.4.', content: document.getElementById('warranties914')?.value || 'the execution and performance of this Agreement does not and will not contravene any provision of any order or other decision of any responsible authority or arbitrator that is binding on the Artist for the duration of this Agreement;', indent: 20 },
        { num: '9.1.5.', content: document.getElementById('warranties915')?.value || 'save as may be disclosed from time to time no litigation, arbitration, investigation or administrative proceedings is in progress or, to the Artist\'s best', indent: 20 }
    ];

    warrantiesSections.forEach(section => {
        checkPageBreak(15);

        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.setTextColor(0, 0, 0);

        const indentLevel = section.indent || 0;
        doc.text(section.num, margin + indentLevel, y);

        const warrantiesLines = doc.splitTextToSize(section.content, contentWidth - 20 - indentLevel);
        warrantiesLines.forEach((line, index) => {
            if (index === 0) {
                doc.text(line, margin + 20 + indentLevel, y);
            } else {
                checkPageBreak(lineHeight + 2);
                y += lineHeight;
                doc.text(line, margin + 20 + indentLevel, y);
            }
        });
        y += 8;
    });

    // Add custom sections from Page 8
    const customSectionsPage8 = document.querySelectorAll('#customSectionsPage8Container .custom-section');
    customSectionsPage8.forEach(customSection => {
        const titleInput = customSection.querySelector('.custom-section-title');
        const contentInput = customSection.querySelector('.custom-section-content');
        if (titleInput && contentInput && titleInput.value.trim() && contentInput.value.trim()) {
            checkPageBreak(25);

            // Section title
            doc.setFontSize(sectionTitleSize);
            doc.setFont(PDF_HEADING_FONT, 'bold');
            doc.setTextColor(0, 0, 0);
            doc.text(titleInput.value.trim(), margin, y);
            y += 10;

            // Section content
            doc.setFontSize(normalSize);
            doc.setFont(PDF_BODY_FONT, 'normal');
            const customPage8Lines = doc.splitTextToSize(contentInput.value.trim(), contentWidth);
            customPage8Lines.forEach(line => {
                checkPageBreak(lineHeight + 2);
                doc.text(line, margin, y);
                y += lineHeight;
            });
            y += 10;
        }
    });

    // Add page number to Page 8
    addPageNumber(8);

    // PAGE 9 - WARRANTIES CONTINUED & CONFIDENTIAL INFORMATION
    doc.addPage();
    y = margin;

    // Page header
    doc.setFontSize(18);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('Artist Agreement', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 8;
    doc.setFontSize(12);
    doc.text('Page 9 - Warranties Continued & Confidential Information', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 20;

    // 9. WARRANTIES (Continued)
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.setTextColor(0, 0, 0);
    doc.text('9.    WARRANTIES (Continued)', margin, y);
    y += 10;

    // Warranties continued subsections
    const warrantiesContinued = [
        { num: '9.1.5.', content: document.getElementById('warranties915Continued')?.value || 'knowledge of having made all reasonable enquiries, threatened against it which is likely to have a material adverse effect on the Artist\'s ability to provide the Services; and', indent: 20 },
        { num: '9.1.6.', content: document.getElementById('warranties916')?.value || 'the Artist will use reasonable care and skill in carrying out its obligations under this Agreement.', indent: 20 }
    ];

    warrantiesContinued.forEach(section => {
        checkPageBreak(15);

        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.setTextColor(0, 0, 0);

        const indentLevel = section.indent || 0;
        doc.text(section.num, margin + indentLevel, y);

        const warrantiesContinuedLines = doc.splitTextToSize(section.content, contentWidth - 20 - indentLevel);
        warrantiesContinuedLines.forEach((line, index) => {
            if (index === 0) {
                doc.text(line, margin + 20 + indentLevel, y);
            } else {
                checkPageBreak(lineHeight + 2);
                y += lineHeight;
                doc.text(line, margin + 20 + indentLevel, y);
            }
        });
        y += 8;
    });

    y += 10;

    // 10. CONFIDENTIAL INFORMATION
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('10.   CONFIDENTIAL INFORMATION', margin, y);
    y += 10;

    // Confidential information subsections
    const confidentialSections = [
        { num: '10.1.', content: document.getElementById('confidential101')?.value || 'Each Party ("the Receiving Party") must treat and hold as confidential all information, which they may receive from the other party ("the Disclosing Party") or which becomes known to them concerning the Disclosing Party during the subsistence of this Agreement and any extension thereof.' },
        { num: '10.2.', content: document.getElementById('confidential102')?.value || 'The confidential information of the Disclosing Party shall, without limitation, include:-' },
        { num: '10.2.1.', content: document.getElementById('confidential1021')?.value || 'All associated material and documentation, including information contained therein;', indent: 20 },
        { num: '10.2.2.', content: document.getElementById('confidential1022')?.value || 'All information relating to:-', indent: 20 },
        { num: '10.2.2.1.', content: document.getElementById('confidential10221')?.value || 'The Disclosing Party\'s business activities, products,services, customers and clients, as well as its technical knowledge and trade secrets;', indent: 40 },
        { num: '10.2.2.2.', content: document.getElementById('confidential10222')?.value || 'The terms and conditions of this Agreement.', indent: 40 },
        { num: '10.3.', content: document.getElementById('confidential103')?.value || 'The Receiving Party agrees that in order to protect the proprietary interests of the Disclosing Party in its confidential information:' },
        { num: '10.3.1.', content: document.getElementById('confidential1031')?.value || 'It will only make the confidential information available to those of its personnel who are actively involved in the delivery of agreed Services;', indent: 20 },
        { num: '10.3.2.', content: document.getElementById('confidential1032')?.value || 'It will initiate internal security procedures reasonably acceptable to the Disclosing Party to prevent unauthorised disclosure and will take all practical steps to impress upon those personnel who need to be given access to confidential information, the confidential nature thereof;', indent: 20 },
        { num: '10.3.3.', content: document.getElementById('confidential1033')?.value || 'Subject to the right to make the confidential information available to their personnel under clause 10.3.1 above, they will not at any time, whether during', indent: 20 }
    ];

    confidentialSections.forEach(section => {
        checkPageBreak(15);

        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.setTextColor(0, 0, 0);

        const indentLevel = section.indent || 0;
        doc.text(section.num, margin + indentLevel, y);

        const confidentialLines = doc.splitTextToSize(section.content, contentWidth - 20 - indentLevel);
        confidentialLines.forEach((line, index) => {
            if (index === 0) {
                doc.text(line, margin + 20 + indentLevel, y);
            } else {
                checkPageBreak(lineHeight + 2);
                y += lineHeight;
                doc.text(line, margin + 20 + indentLevel, y);
            }
        });
        y += 8;
    });

    // Add custom sections from Page 9
    const customSectionsPage9 = document.querySelectorAll('#customSectionsPage9Container .custom-section');
    customSectionsPage9.forEach(customSection => {
        const titleInput = customSection.querySelector('.custom-section-title');
        const contentInput = customSection.querySelector('.custom-section-content');
        if (titleInput && contentInput && titleInput.value.trim() && contentInput.value.trim()) {
            checkPageBreak(25);

            // Section title
            doc.setFontSize(sectionTitleSize);
            doc.setFont(PDF_HEADING_FONT, 'bold');
            doc.setTextColor(0, 0, 0);
            doc.text(titleInput.value.trim(), margin, y);
            y += 10;

            // Section content
            doc.setFontSize(normalSize);
            doc.setFont(PDF_BODY_FONT, 'normal');
            const customPage9Lines = doc.splitTextToSize(contentInput.value.trim(), contentWidth);
            customPage9Lines.forEach(line => {
                checkPageBreak(lineHeight + 2);
                doc.text(line, margin, y);
                y += lineHeight;
            });
            y += 10;
        }
    });

    // Add page number to Page 9
    addPageNumber(9);

    // PAGE 10 - CONFIDENTIAL INFORMATION CONTINUED
    doc.addPage();
    y = margin;

    // Page header
    doc.setFontSize(18);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('Artist Agreement', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 8;
    doc.setFontSize(12);
    doc.text('Page 10 - Confidential Information Continued', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 20;

    // 10. CONFIDENTIAL INFORMATION (Continued)
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.setTextColor(0, 0, 0);
    doc.text('10.   CONFIDENTIAL INFORMATION (Continued)', margin, y);
    y += 10;

    // Confidential information continued subsections
    const confidentialContinuedSections = [
        { num: '10.3.3.', content: document.getElementById('confidential1033Continued')?.value || 'this Agreement or thereafter, either use any Confidential Information of the Disclosing Party or directly or indirectly disclose any Confidential Information of the Disclosing Party to third parties; and', indent: 20 },
        { num: '10.3.4.', content: document.getElementById('confidential1034')?.value || 'all written instructions, drawings, notes, memoranda and records of whatever nature relating to the confidential information of the Disclosing Party which have or will come into the possession of the Receiving Party and its Personnel, will be, and will at all times remain, the sole and absolute property of such Party and shall be promptly handed over to such Party when no longer required for the purposes of this Agreement.', indent: 20 },
        { num: '10.4.', content: document.getElementById('confidential104')?.value || 'Upon termination or expiry of this Agreement, the Receiving Party will deliver to the Disclosing Party, or at the Disclosing Party\'s option, destroy all originals and copies of the Disclosing Party\'s confidential information in its possession.' },
        { num: '10.5.', content: document.getElementById('confidential105')?.value || 'The foregoing obligations shall not apply to any information which:' },
        { num: '10.5.1.', content: document.getElementById('confidential1051')?.value || 'Is lawfully in the public domain at the time of disclosure;', indent: 20 },
        { num: '10.5.2.', content: document.getElementById('confidential1052')?.value || 'Subsequently and lawfully becomes part of the public domain by publication or otherwise;', indent: 20 },
        { num: '10.5.3.', content: document.getElementById('confidential1053')?.value || 'Subsequently becomes available to the Receiving Party from a source other than the Disclosing Party, which source is lawfully entitled without any restriction on disclosure to disclose such Confidential Information; or', indent: 20 },
        { num: '10.5.4.', content: document.getElementById('confidential1054')?.value || 'is disclosed pursuant to a requirement or request by operation of law, regulation or court order; or', indent: 20 },
        { num: '10.5.5.', content: document.getElementById('confidential1055')?.value || 'is disclosed with the express written permission of the Parties.', indent: 20 },
        { num: '10.6.', content: document.getElementById('confidential106')?.value || 'Copyright of all materials developed shall remain the exclusive intellectual property of the Party that developed the material.' },
        { num: '10.7.', content: document.getElementById('confidential107')?.value || 'Nothing in this clause shall preclude the Parties from disclosing the confidential information to their professional advisors or financiers in the bona fide course of seeking finance, business and professional advice.' },
        { num: '10.8.', content: document.getElementById('confidential108')?.value || 'the Client hereby indemnifies the Artist against any loss or damage, which the Client may suffer as a result of a breach of this clause by the Client or its personnel.' }
    ];

    confidentialContinuedSections.forEach(section => {
        checkPageBreak(15);

        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.setTextColor(0, 0, 0);

        const indentLevel = section.indent || 0;
        doc.text(section.num, margin + indentLevel, y);

        const confidentialContinuedLines = doc.splitTextToSize(section.content, contentWidth - 20 - indentLevel);
        confidentialContinuedLines.forEach((line, index) => {
            if (index === 0) {
                doc.text(line, margin + 20 + indentLevel, y);
            } else {
                checkPageBreak(lineHeight + 2);
                y += lineHeight;
                doc.text(line, margin + 20 + indentLevel, y);
            }
        });
        y += 8;
    });

    // Add custom sections from Page 10
    const customSectionsPage10 = document.querySelectorAll('#customSectionsPage10Container .custom-section');
    customSectionsPage10.forEach(customSection => {
        const titleInput = customSection.querySelector('.custom-section-title');
        const contentInput = customSection.querySelector('.custom-section-content');
        if (titleInput && contentInput && titleInput.value.trim() && contentInput.value.trim()) {
            checkPageBreak(25);

            // Section title
            doc.setFontSize(sectionTitleSize);
            doc.setFont(PDF_HEADING_FONT, 'bold');
            doc.setTextColor(0, 0, 0);
            doc.text(titleInput.value.trim(), margin, y);
            y += 10;

            // Section content
            doc.setFontSize(normalSize);
            doc.setFont(PDF_BODY_FONT, 'normal');
            const customPage10Lines = doc.splitTextToSize(contentInput.value.trim(), contentWidth);
            customPage10Lines.forEach(line => {
                checkPageBreak(lineHeight + 2);
                doc.text(line, margin, y);
                y += lineHeight;
            });
            y += 10;
        }
    });

    // Add page number to Page 10
    addPageNumber(10);

    // PAGE 11 - TERMINATION, INTELLECTUAL PROPERTY & FORCE MAJEURE
    doc.addPage();
    y = margin;

    // Page header
    doc.setFontSize(18);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('Artist Agreement', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 8;
    doc.setFontSize(12);
    doc.text('Page 11 - Termination, Intellectual Property & Force Majeure', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 20;

    // 11. TERMINATION
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.setTextColor(0, 0, 0);
    doc.text('11.   TERMINATION', margin, y);
    y += 10;

    // Termination subsections
    const terminationSections = [
        { num: '11.1.', content: document.getElementById('termination111')?.value || 'The Parties shall be entitled to terminate this Agreement upon fourteen (14) calendar day\'s written notice in the event that an event of default occurs.' },
        { num: '11.2.', content: document.getElementById('termination112')?.value || 'Where the Client terminates the Agreement within fourteen (14) days from the date for the Performance Date, the Client\'s deposited amount shall be forfeited.' },
        { num: '11.3.', content: document.getElementById('termination113')?.value || 'Where the Client terminates the Agreement for a period exceeding fourteen (14) days but less than 30 days from the Performance Date, 25% of the Client\'s deposited amount shall be forfeited;' },
        { num: '11.4.', content: document.getElementById('termination114')?.value || 'Where the Client terminates the Agreement more than 30 days from the Performance Date, 10% of the Client\'s deposited amount shall be forfeited.' }
    ];

    terminationSections.forEach(section => {
        checkPageBreak(15);

        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.setTextColor(0, 0, 0);

        doc.text(section.num, margin, y);

        const terminationLines = doc.splitTextToSize(section.content, contentWidth - 20);
        terminationLines.forEach((line, index) => {
            if (index === 0) {
                doc.text(line, margin + 20, y);
            } else {
                checkPageBreak(lineHeight + 2);
                y += lineHeight;
                doc.text(line, margin + 20, y);
            }
        });
        y += 8;
    });

    y += 10;

    // 12. INTELLECTUAL PROPERTY
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('12.   INTELLECTUAL PROPERTY', margin, y);
    y += 10;

    // Intellectual Property subsections
    const intellectualPropertySections = [
        { num: '12.1.', content: document.getElementById('intellectualProperty121')?.value || 'Each Party\'s own Intellectual Property Rights, including the Rights of Copyright shall remain the property of that Party and no other Party shall be entitled to use those Intellectual Property Rightsincluding the Rights of Copyright without first having obtained the requisite licence/s from the Party concerned.' },
        { num: '12.2.', content: document.getElementById('intellectualProperty122')?.value || 'Any Intellectual Property Rights including the Rights of Copyrightcreated in any joint developments or deliverables pursuant to this Agreement shall be owned by the Artist. To the extent that such Intellectual Property Rightsincluding the Rights of Copyright are registerable, such rights shall be registered in the name of the Artist.' }
    ];

    intellectualPropertySections.forEach(section => {
        checkPageBreak(15);

        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.setTextColor(0, 0, 0);

        doc.text(section.num, margin, y);

        const intellectualPropertyLines = doc.splitTextToSize(section.content, contentWidth - 20);
        intellectualPropertyLines.forEach((line, index) => {
            if (index === 0) {
                doc.text(line, margin + 20, y);
            } else {
                checkPageBreak(lineHeight + 2);
                y += lineHeight;
                doc.text(line, margin + 20, y);
            }
        });
        y += 8;
    });

    y += 10;

    // 13. FORCE MAJEURE
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('13.   FORCE MAJEURE', margin, y);
    y += 10;

    // Force Majeure subsection
    const forceMajeureContent = document.getElementById('forceMajeure131')?.value || 'Neither Party shall have any claim against the other Party arising from any failure or delay in the performance of any obligation of either Party under this Agreement caused by an act of force majeure such as acts of God, fire, flood, war, strike, lockout, industrial dispute, government action, laws or regulations, riots, terrorism or civil disturbance, defaults, delays or discontinuance on the part of independent contractors, suppliers, or other circumstances or factors beyond the reasonable control of either Party, and to the extent that the performance of obligations of either Party hereunder is delayed by virtue of the aforegoing, any period stipulated for any such performance shall be reasonably extended.';

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');
    doc.setTextColor(0, 0, 0);
    doc.text('13.1.', margin, y);

    const forceMajeureLines = doc.splitTextToSize(forceMajeureContent, contentWidth - 20);
    forceMajeureLines.forEach((line, index) => {
        if (index === 0) {
            doc.text(line, margin + 20, y);
        } else {
            checkPageBreak(lineHeight + 2);
            y += lineHeight;
            doc.text(line, margin + 20, y);
        }
    });
    y += 15;

    // Add custom sections from Page 11
    const customSectionsPage11 = document.querySelectorAll('#customSectionsPage11Container .custom-section');
    customSectionsPage11.forEach(customSection => {
        const titleInput = customSection.querySelector('.custom-section-title');
        const contentInput = customSection.querySelector('.custom-section-content');
        if (titleInput && contentInput && titleInput.value.trim() && contentInput.value.trim()) {
            checkPageBreak(25);

            // Section title
            doc.setFontSize(sectionTitleSize);
            doc.setFont(PDF_HEADING_FONT, 'bold');
            doc.setTextColor(0, 0, 0);
            doc.text(titleInput.value.trim(), margin, y);
            y += 10;

            // Section content
            doc.setFontSize(normalSize);
            doc.setFont(PDF_BODY_FONT, 'normal');
            const customPage11Lines = doc.splitTextToSize(contentInput.value.trim(), contentWidth);
            customPage11Lines.forEach(line => {
                checkPageBreak(lineHeight + 2);
                doc.text(line, margin, y);
                y += lineHeight;
            });
            y += 10;
        }
    });

    // Add page number to Page 11
    addPageNumber(11);

    // PAGE 12 - FORCE MAJEURE CONTINUED, ADDRESSES & NOTICES, VARIATION
    doc.addPage();
    y = margin;

    // Page header
    doc.setFontSize(18);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('Artist Agreement', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 8;
    doc.setFontSize(12);
    doc.text('Page 12 - Force Majeure Continued, Addresses & Notices, Variation', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 20;

    // 13. FORCE MAJEURE (Continued)
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.setTextColor(0, 0, 0);
    doc.text('13.   FORCE MAJEURE (Continued)', margin, y);
    y += 10;

    // Force Majeure Continued subsection
    const forceMajeure132Content = document.getElementById('forceMajeure132')?.value || 'Each Party will take all reasonable steps by whatever lawful means that are available, to resume full performance as soon as practical and will seek agreement to modification of the relevant provisions of this Agreement in order to accommodate the new circumstances caused by the act of force majeure. If a Party fails to agree to such modifications proposed by the other Party within 10 days of the act of force majeure first occurring, either Party may thereafter terminate this Agreement with immediate notice.';

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');
    doc.setTextColor(0, 0, 0);
    doc.text('13.2.', margin, y);

    const forceMajeure132Lines = doc.splitTextToSize(forceMajeure132Content, contentWidth - 20);
    forceMajeure132Lines.forEach((line, index) => {
        if (index === 0) {
            doc.text(line, margin + 20, y);
        } else {
            checkPageBreak(lineHeight + 2);
            y += lineHeight;
            doc.text(line, margin + 20, y);
        }
    });
    y += 15;

    // 14. ADDRESSES AND NOTICES
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('14.   ADDRESSES AND NOTICES', margin, y);
    y += 10;

    // Addresses and Notices subsections
    const addresses141Content = document.getElementById('addresses141')?.value || 'For the purposes of this Agreement, including the giving of notices and the serving of legal process (as applicable), the Parties choose domicilium citandi executandi ("domicilium") at:';

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');
    doc.text('14.1.', margin, y);

    const addresses141Lines = doc.splitTextToSize(addresses141Content, contentWidth - 20);
    addresses141Lines.forEach((line, index) => {
        if (index === 0) {
            doc.text(line, margin + 20, y);
        } else {
            checkPageBreak(lineHeight + 2);
            y += lineHeight;
            doc.text(line, margin + 20, y);
        }
    });
    y += 10;

    // Artist Details
    const artistDetails = document.getElementById('artistAddress1411')?.value || 'Artist: BONGO MAFFIN    Company: Bongo Music (PTY) Ltd';
    const artistPhysicalAddress = document.getElementById('artistPhysicalAddress')?.value || '16, 21ST Street, Parkhurst.';
    const artistPhone = document.getElementById('artistPhone')?.value || '0732205971';
    const artistEmail = document.getElementById('artistEmail')?.value || '<EMAIL>';

    checkPageBreak(25);
    doc.text('14.1.1.', margin + 20, y);
    doc.text(artistDetails, margin + 40, y);
    y += 8;

    doc.text('Physical Address: ' + artistPhysicalAddress, margin + 40, y);
    y += 8;

    doc.text('Johannesburg Telephone: ' + artistPhone, margin + 40, y);
    y += 8;

    doc.text('e-mail address: ' + artistEmail, margin + 40, y);
    y += 15;

    // Client Details
    const clientDetails = document.getElementById('clientDetails1412')?.value || 'Client: Morgan Ross    Company: UMG LIVE';
    const clientPhysicalAddress = document.getElementById('clientPhysicalAddress')?.value || '2nd Floor Design District\nKeyes Avenue\nRosebank\n2196';
    const clientEmail = document.getElementById('clientEmail')?.value || '<EMAIL>';

    checkPageBreak(25);
    doc.text('14.1.2.', margin + 20, y);
    doc.text(clientDetails, margin + 40, y);
    y += 8;

    doc.text('Physical Address:', margin + 40, y);
    y += 6;
    const clientAddressLines = clientPhysicalAddress.split('\n');
    clientAddressLines.forEach(line => {
        checkPageBreak(lineHeight + 2);
        doc.text(line, margin + 60, y);
        y += lineHeight;
    });
    y += 5;

    doc.text('e-mail address: ' + clientEmail, margin + 40, y);
    y += 15;

    // 15. VARIATION, CANCELLATION AND WAIVER
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('15.   VARIATION, CANCELLATION AND WAIVER', margin, y);
    y += 10;

    const variation15Content = document.getElementById('variation15')?.value || 'No contract varying, adding to, deleting from or cancelling this Agreement, and no waiver of any right under this Agreement, shall be effective unless reduced to writing and signed by or on behalf of the Parties.';

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const variation15Lines = doc.splitTextToSize(variation15Content, contentWidth);
    variation15Lines.forEach(line => {
        checkPageBreak(lineHeight + 2);
        doc.text(line, margin, y);
        y += lineHeight;
    });
    y += 15;

    // Add custom sections from Page 12
    const customSectionsPage12 = document.querySelectorAll('#customSectionsPage12Container .custom-section');
    customSectionsPage12.forEach(customSection => {
        const titleInput = customSection.querySelector('.custom-section-title');
        const contentInput = customSection.querySelector('.custom-section-content');
        if (titleInput && contentInput && titleInput.value.trim() && contentInput.value.trim()) {
            checkPageBreak(25);

            // Section title
            doc.setFontSize(sectionTitleSize);
            doc.setFont(PDF_HEADING_FONT, 'bold');
            doc.setTextColor(0, 0, 0);
            doc.text(titleInput.value.trim(), margin, y);
            y += 10;

            // Section content
            doc.setFontSize(normalSize);
            doc.setFont(PDF_BODY_FONT, 'normal');
            const customPage12Lines = doc.splitTextToSize(contentInput.value.trim(), contentWidth);
            customPage12Lines.forEach(line => {
                checkPageBreak(lineHeight + 2);
                doc.text(line, margin, y);
                y += lineHeight;
            });
            y += 10;
        }
    });

    // Add page number to Page 12
    addPageNumber(12);

    // PAGE 13 - ENTIRE AGREEMENT, SEVERABILITY & DISPUTE RESOLUTION
    doc.addPage();
    y = margin;

    // Page header
    doc.setFontSize(18);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('Artist Agreement', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 8;
    doc.setFontSize(12);
    doc.text('Page 13 - Entire Agreement, Severability & Dispute Resolution', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 20;

    // 16. ENTIRE AGREEMENT
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.setTextColor(0, 0, 0);
    doc.text('16.   ENTIRE AGREEMENT', margin, y);
    y += 10;

    // Entire Agreement subsections
    const entireAgreementSections = [
        { num: '16.1.', content: document.getElementById('entireAgreement161')?.value || 'Except where expressly provided otherwise in this Agreement, this Agreement constitutes the entire Agreement between the Parties in connection with its subject matter and supersedes all prior representations, communications, negotiations and understandings concerning the subject matter of this Agreement.' },
        { num: '16.2.', content: document.getElementById('entireAgreement162')?.value || 'Each of the Parties acknowledges that:' },
        { num: '16.2.1.', content: document.getElementById('entireAgreement1621')?.value || 'it does not enter into this Agreement on the basis of and does not rely, and has not relied, upon any statement, representation (whether negligent or innocent) or warranty or other provision (in any case whether oral, written, express or implied) made or agreed to by any person (whether a Party to this Agreement or not) except those expressly contained in or referred to in this Agreement, and the only remedy available in respect of any misrepresentation or untrue statement made to it shall be a remedy available under this Agreement; and', indent: 20 },
        { num: '16.2.2.', content: document.getElementById('entireAgreement1622')?.value || 'this clause shall not apply to any statement, representation or warranty made fraudulently, or to any provision of this Agreement which was induced by fraud, for which the remedies available shall be all those available under any Law governing this Agreement.', indent: 20 }
    ];

    entireAgreementSections.forEach(section => {
        checkPageBreak(15);

        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.setTextColor(0, 0, 0);

        const indentLevel = section.indent || 0;
        doc.text(section.num, margin + indentLevel, y);

        const entireAgreementLines = doc.splitTextToSize(section.content, contentWidth - 20 - indentLevel);
        entireAgreementLines.forEach((line, index) => {
            if (index === 0) {
                doc.text(line, margin + 20 + indentLevel, y);
            } else {
                checkPageBreak(lineHeight + 2);
                y += lineHeight;
                doc.text(line, margin + 20 + indentLevel, y);
            }
        });
        y += 8;
    });

    y += 10;

    // 17. SEVERABILITY
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('17.   SEVERABILITY', margin, y);
    y += 10;

    const severability17Content = document.getElementById('severability17')?.value || 'Whenever possible, each provision of this Agreement shall be interpreted in a manner which makes it effective and valid under any Law, but if any provision of this Agreement is held to be illegal, invalid or unenforceable under any Law, that illegality, invalidity or unenforceability shall not affect the other provisions of this Agreement, all of which shall remain in full force.';

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const severability17Lines = doc.splitTextToSize(severability17Content, contentWidth);
    severability17Lines.forEach(line => {
        checkPageBreak(lineHeight + 2);
        doc.text(line, margin, y);
        y += lineHeight;
    });
    y += 15;

    // 18. DISPUTE RESOLUTION
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('18.   DISPUTE RESOLUTION', margin, y);
    y += 10;

    const disputeResolution181Content = document.getElementById('disputeResolution181')?.value || 'Should a dispute arise pursuant to this Agreement, then the Party declaring the dispute shall notify the other relevant Party in writing and give such other party ten (10) days within which to reply to the allegations set out in such notice. All efforts will be made to resolve the dispute amicably. If the dispute cannot be resolved';

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');
    doc.text('18.1.', margin, y);

    const disputeResolution181Lines = doc.splitTextToSize(disputeResolution181Content, contentWidth - 20);
    disputeResolution181Lines.forEach((line, index) => {
        if (index === 0) {
            doc.text(line, margin + 20, y);
        } else {
            checkPageBreak(lineHeight + 2);
            y += lineHeight;
            doc.text(line, margin + 20, y);
        }
    });
    y += 15;

    // Add custom sections from Page 13
    const customSectionsPage13 = document.querySelectorAll('#customSectionsPage13Container .custom-section');
    customSectionsPage13.forEach(customSection => {
        const titleInput = customSection.querySelector('.custom-section-title');
        const contentInput = customSection.querySelector('.custom-section-content');
        if (titleInput && contentInput && titleInput.value.trim() && contentInput.value.trim()) {
            checkPageBreak(25);

            // Section title
            doc.setFontSize(sectionTitleSize);
            doc.setFont(PDF_HEADING_FONT, 'bold');
            doc.setTextColor(0, 0, 0);
            doc.text(titleInput.value.trim(), margin, y);
            y += 10;

            // Section content
            doc.setFontSize(normalSize);
            doc.setFont(PDF_BODY_FONT, 'normal');
            const customPage13Lines = doc.splitTextToSize(contentInput.value.trim(), contentWidth);
            customPage13Lines.forEach(line => {
                checkPageBreak(lineHeight + 2);
                doc.text(line, margin, y);
                y += lineHeight;
            });
            y += 10;
        }
    });

    // Add page number to Page 13
    addPageNumber(13);

    // PAGE 14 - DISPUTE RESOLUTION CONTINUED & GOVERNING LAW
    doc.addPage();
    y = margin;

    // Page header
    doc.setFontSize(18);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('Artist Agreement', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 8;
    doc.setFontSize(12);
    doc.text('Page 14 - Dispute Resolution Continued & Governing Law', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 20;

    // 18. DISPUTE RESOLUTION (Continued)
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.setTextColor(0, 0, 0);
    doc.text('18.   DISPUTE RESOLUTION (Continued)', margin, y);
    y += 10;

    // Dispute Resolution Continued subsections
    const disputeResolutionSections = [
        { num: '18.1.', content: document.getElementById('disputeResolution181Continued')?.value || 'within twenty (20) days of notification of the existence of the dispute, then any Party to such dispute may require that such dispute shall be referred to arbitration.' },
        { num: '18.2.', content: document.getElementById('disputeResolution182')?.value || 'The arbitrator shall be appointed by the Parties, and failing agreement, shall be nominated by the President of the Law Society of the Northern Province at the time. The arbitration shall be held in Gauteng.' },
        { num: '18.3.', content: document.getElementById('disputeResolution183')?.value || 'The arbitration shall be held in accordance with the formalities and procedures settled by the arbitrator, which shall be in an informal and summary manner, that is, it shall not be necessary to observe or carry out either the usual formalities or procedure or the strict rules of evidence, and the arbitration shall be otherwise subject to the provisions of the Arbitration Act 1965, as amended.' },
        { num: '18.4.', content: document.getElementById('disputeResolution184')?.value || 'The arbitrator shall be entitled to investigate or cause to be investigated any matter, fact or thing which he/she considers necessary or desirable in connection with any matter referred to him/her for decision, to decide the matters submitted to him/her according to what he/she considers just and equitable in all the circumstances, having regard to the purpose of this Agreement and make such award, as he/she in his discretion may deem fit and appropriate.' },
        { num: '18.5.', content: document.getElementById('disputeResolution185')?.value || 'The arbitration shall be held as expeditiously as possible after such arbitration is demanded, with a view to such arbitration being completed within 20 (twenty) business days after such arbitration has been so demanded.' },
        { num: '18.6.', content: document.getElementById('disputeResolution186')?.value || 'This clause is severable from the rest of this Agreement and shall therefore remain in effect even if this Agreement is terminated.' },
        { num: '18.7.', content: document.getElementById('disputeResolution187')?.value || 'This clause shall not preclude any Party from obtaining interim relief on an urgent basis from a court of competent jurisdiction pending the decision of the arbitrator or enforcing any award made by the arbitrator under this clause.' },
        { num: '18.8.', content: document.getElementById('disputeResolution188')?.value || 'The Parties hereby confirm that they shall abide by the terms of any arbitral award, the terms of which shall be final and binding on the Parties hereto.' }
    ];

    disputeResolutionSections.forEach(section => {
        checkPageBreak(15);

        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.setTextColor(0, 0, 0);
        doc.text(section.num, margin, y);

        const disputeResolutionLines = doc.splitTextToSize(section.content, contentWidth - 20);
        disputeResolutionLines.forEach((line, index) => {
            if (index === 0) {
                doc.text(line, margin + 20, y);
            } else {
                checkPageBreak(lineHeight + 2);
                y += lineHeight;
                doc.text(line, margin + 20, y);
            }
        });
        y += 8;
    });

    y += 10;

    // 19. GOVERNING LAW
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('19.   GOVERNING LAW', margin, y);
    y += 10;

    // Governing Law subsections
    const governingLawSections = [
        { num: '19.1.', content: document.getElementById('governingLaw191')?.value || 'This Agreement is exclusively governed by and construed in accordance with the laws of the Republic of South Africa and is subject to the jurisdiction of the courts of the Republic of South Africa.' },
        { num: '19.2.', content: document.getElementById('governingLaw192')?.value || 'Change of Law: In this Agreement, unless the context otherwise requires, references to a statutory provision include references to that statutory provision as from time to time amended, extended or re-enacted and any regulations made under it, provided that in the event that the amendment, extension or re-enactment of any statutory provision or introduction of any new statutory provision has a material impact on the obligations of either Party, the Parties will negotiate in good faith to agree such amendments to this Agreement as may be appropriate in the circumstances. If, within a reasonable period of time, the Parties cannot reach agreement on the nature of the changes required or on modification of fees, deliverables, warranties, or other terms and conditions, either Party may seek to have the matter determined in accordance with clause 18 above.' }
    ];

    governingLawSections.forEach(section => {
        checkPageBreak(15);

        doc.setFontSize(normalSize);
        doc.setFont(PDF_BODY_FONT, 'normal');
        doc.setTextColor(0, 0, 0);
        doc.text(section.num, margin, y);

        const governingLawLines = doc.splitTextToSize(section.content, contentWidth - 20);
        governingLawLines.forEach((line, index) => {
            if (index === 0) {
                doc.text(line, margin + 20, y);
            } else {
                checkPageBreak(lineHeight + 2);
                y += lineHeight;
                doc.text(line, margin + 20, y);
            }
        });
        y += 8;
    });

    y += 15;

    // Add custom sections from Page 14
    const customSectionsPage14 = document.querySelectorAll('#customSectionsPage14Container .custom-section');
    customSectionsPage14.forEach(customSection => {
        const titleInput = customSection.querySelector('.custom-section-title');
        const contentInput = customSection.querySelector('.custom-section-content');
        if (titleInput && contentInput && titleInput.value.trim() && contentInput.value.trim()) {
            checkPageBreak(25);

            // Section title
            doc.setFontSize(sectionTitleSize);
            doc.setFont(PDF_HEADING_FONT, 'bold');
            doc.setTextColor(0, 0, 0);
            doc.text(titleInput.value.trim(), margin, y);
            y += 10;

            // Section content
            doc.setFontSize(normalSize);
            doc.setFont(PDF_BODY_FONT, 'normal');
            const customPage14Lines = doc.splitTextToSize(contentInput.value.trim(), contentWidth);
            customPage14Lines.forEach(line => {
                checkPageBreak(lineHeight + 2);
                doc.text(line, margin, y);
                y += lineHeight;
            });
            y += 10;
        }
    });

    // PAGE 15 - FEES & COSTS, INDEMNITY & CO-OPERATION
    doc.addPage();
    y = margin;

    // Page header
    doc.setFontSize(18);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('Artist Agreement', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 8;
    doc.setFontSize(12);
    doc.text('Page 15 - Fees & Costs, Indemnity & Co-operation', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 20;

    // 20. FEES AND COSTS
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.setTextColor(0, 0, 0);
    doc.text('20.   FEES AND COSTS', margin, y);
    y += 10;

    const feesAndCosts20Content = document.getElementById('feesAndCosts20')?.value || 'Each Party shall bear its own legal costs and disbursements of and incidental to the negotiation, preparation, settling, signing and implementation of this Agreement.';

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const feesAndCosts20Lines = doc.splitTextToSize(feesAndCosts20Content, contentWidth);
    feesAndCosts20Lines.forEach(line => {
        checkPageBreak(lineHeight + 2);
        doc.text(line, margin, y);
        y += lineHeight;
    });
    y += 15;

    // 21. INDEMNITY
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('21.   INDEMNITY', margin, y);
    y += 10;

    const indemnity21Content = document.getElementById('indemnity21')?.value || 'The Client indemnifies, holds harmless and defends the Artist, its officers, employees, agents and representatives, from and against any claim, liability, loss or expense arising from any injury, loss or damage (including without limitation injury, loss or damage incurred the Artist, its officers, employees, agents or representatives) arising directly through the acts or omissions of the Artist in relation to the Services to be provided in terms of this Agreement.';

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const indemnity21Lines = doc.splitTextToSize(indemnity21Content, contentWidth);
    indemnity21Lines.forEach(line => {
        checkPageBreak(lineHeight + 2);
        doc.text(line, margin, y);
        y += lineHeight;
    });
    y += 15;

    // 22. CO-OPERATION
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('22.   CO-OPERATION', margin, y);
    y += 10;

    const cooperation22Content = document.getElementById('cooperation22')?.value || 'Each of the Parties undertakes at all times to do all such things, perform all such acts and take all such steps, and to procure the doing of all such things, within its power and control, as may be open to it and necessary for and incidental to the putting into effect or maintenance of the terms and conditions of this Agreement.';

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const cooperation22Lines = doc.splitTextToSize(cooperation22Content, contentWidth);
    cooperation22Lines.forEach(line => {
        checkPageBreak(lineHeight + 2);
        doc.text(line, margin, y);
        y += lineHeight;
    });
    y += 15;

    // Add custom sections from Page 15
    const customSectionsPage15 = document.querySelectorAll('#customSectionsPage15Container .custom-section');
    customSectionsPage15.forEach(customSection => {
        const titleInput = customSection.querySelector('.custom-section-title');
        const contentInput = customSection.querySelector('.custom-section-content');
        if (titleInput && contentInput && titleInput.value.trim() && contentInput.value.trim()) {
            checkPageBreak(25);

            // Section title
            doc.setFontSize(sectionTitleSize);
            doc.setFont(PDF_HEADING_FONT, 'bold');
            doc.setTextColor(0, 0, 0);
            doc.text(titleInput.value.trim(), margin, y);
            y += 10;

            // Section content
            doc.setFontSize(normalSize);
            doc.setFont(PDF_BODY_FONT, 'normal');
            const customPage15Lines = doc.splitTextToSize(contentInput.value.trim(), contentWidth);
            customPage15Lines.forEach(line => {
                checkPageBreak(lineHeight + 2);
                doc.text(line, margin, y);
                y += lineHeight;
            });
            y += 10;
        }
    });

    // PAGE 16 - CESSION & DELEGATION, THIRD PERSON STIPULATION & COUNTERPARTS
    doc.addPage();
    y = margin;

    // Page header
    doc.setFontSize(18);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('Artist Agreement', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 8;
    doc.setFontSize(12);
    doc.text('Page 16 - Cession & Delegation, Third Person Stipulation & Counterparts', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 20;

    // 23. CESSION AND DELEGATION
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.setTextColor(0, 0, 0);
    doc.text('23.   CESSION AND DELEGATION', margin, y);
    y += 10;

    const cessionDelegation23Content = document.getElementById('cessionDelegation23')?.value || 'A Party may not cede any or all of that Party\'s rights or delegate any or all of that Party\'s obligations under this Agreement without the prior written consent of the other Party.';

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const cessionDelegation23Lines = doc.splitTextToSize(cessionDelegation23Content, contentWidth);
    cessionDelegation23Lines.forEach(line => {
        checkPageBreak(lineHeight + 2);
        doc.text(line, margin, y);
        y += lineHeight;
    });
    y += 15;

    // 24. NO STIPULATION FOR THE BENEFIT OF A THIRD PERSON
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('24.   NO STIPULATION FOR THE BENEFIT OF A THIRD PERSON', margin, y);
    y += 10;

    const noStipulation24Content = document.getElementById('noStipulation24')?.value || 'Save as is expressly provided for in this Agreement, no provision of this Agreement constitutes a stipulation for the benefit of a third person (i.e. a stipulation alteri) which, if accepted by the person, would bind any Party in favour of that person.';

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const noStipulation24Lines = doc.splitTextToSize(noStipulation24Content, contentWidth);
    noStipulation24Lines.forEach(line => {
        checkPageBreak(lineHeight + 2);
        doc.text(line, margin, y);
        y += lineHeight;
    });
    y += 15;

    // 25. COUNTERPARTS
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('25.   COUNTERPARTS', margin, y);
    y += 10;

    const counterparts25Content = document.getElementById('counterparts25')?.value || 'This Agreement may be executed in any number of identical counterparts, all of which when taken together shall constitute one Agreement. Any single counterpart or a set of counterparts taken together, which, in either case, are executed by the Parties, shall constitute a full original of this Agreement for all purposes.';

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');

    const counterparts25Lines = doc.splitTextToSize(counterparts25Content, contentWidth);
    counterparts25Lines.forEach(line => {
        checkPageBreak(lineHeight + 2);
        doc.text(line, margin, y);
        y += lineHeight;
    });
    y += 15;

    // Add custom sections from Page 16
    const customSectionsPage16 = document.querySelectorAll('#customSectionsPage16Container .custom-section');
    customSectionsPage16.forEach(customSection => {
        const titleInput = customSection.querySelector('.custom-section-title');
        const contentInput = customSection.querySelector('.custom-section-content');
        if (titleInput && contentInput && titleInput.value.trim() && contentInput.value.trim()) {
            checkPageBreak(25);

            // Section title
            doc.setFontSize(sectionTitleSize);
            doc.setFont(PDF_HEADING_FONT, 'bold');
            doc.setTextColor(0, 0, 0);
            doc.text(titleInput.value.trim(), margin, y);
            y += 10;

            // Section content
            doc.setFontSize(normalSize);
            doc.setFont(PDF_BODY_FONT, 'normal');
            const customPage16Lines = doc.splitTextToSize(contentInput.value.trim(), contentWidth);
            customPage16Lines.forEach(line => {
                checkPageBreak(lineHeight + 2);
                doc.text(line, margin, y);
                y += lineHeight;
            });
            y += 10;
        }
    });

    // PAGE 17 - SIGNATURES
    doc.addPage();
    y = margin;

    // Page header
    doc.setFontSize(18);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.text('Artist Agreement', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 8;
    doc.setFontSize(12);
    doc.text('Page 17 - Signatures', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 20;

    // SIGNATURES
    checkPageBreak(30);
    doc.setFontSize(sectionTitleSize);
    doc.setFont(PDF_HEADING_FONT, 'bold');
    doc.setTextColor(0, 0, 0);
    doc.text('SIGNATURES', doc.internal.pageSize.width / 2, y, { align: 'center' });
    y += 15;

    // Execution Statement
    const executionStatementContent = document.getElementById('executionStatement')?.value || 'IN WITNESS WHEREOF the Parties have executed this Agreement on the date first written above.';

    doc.setFontSize(normalSize);
    doc.setFont(PDF_BODY_FONT, 'normal');
    doc.setTextColor(0, 0, 0);

    checkPageBreak(30); // Check space before adding execution statement
    const executionStatementLines = doc.splitTextToSize(executionStatementContent, contentWidth);
    executionStatementLines.forEach(line => {
        checkPageBreak(lineHeight + 2);
        doc.text(line, margin, y);
        y += lineHeight;
    });
    y += 20;

    // Artist Signature Block
    const artistSignatureBlockContent = document.getElementById('artistSignatureBlock')?.value || 'SIGNED at _________________________ on this _____ day of _____________, 20___\n\nARTIST/BAND:\n\n_________________________________\nSignature\n\n_________________________________\nName (Print)\n\n_________________________________\nCapacity\n\n_________________________________\nDate';

    checkPageBreak(70); // Reduced space for signature block
    const artistSignatureLines = artistSignatureBlockContent.split('\n');

    // Get selected signatures
    const selectedSignatures = getSelectedSignatures();

    artistSignatureLines.forEach((line, index) => {
        checkPageBreak(lineHeight + 2); // Reduced space check

        // Check if this is the signature line and we have a digital signature
        if (line.includes('_________________________________') && line.includes('Signature') &&
            selectedSignatures.artist && index < 6) { // First signature line in artist block

            // Add digital signature image
            try {
                const img = new Image();
                img.onload = function() {
                    const signatureWidth = 120;
                    const signatureHeight = 40;
                    doc.addImage(selectedSignatures.artist.data, 'PNG', margin, y - 5, signatureWidth, signatureHeight);
                };
                img.src = selectedSignatures.artist.data;

                // Add a note instead of the line
                const wrappedText = doc.splitTextToSize('Digital Signature Applied', contentWidth);
                wrappedText.forEach(textLine => {
                    checkPageBreak(lineHeight + 2);
                    doc.text(textLine, margin, y);
                    y += lineHeight;
                });
                y -= lineHeight; // Adjust for the loop increment
            } catch (error) {
                console.error('Error adding artist signature:', error);
                const wrappedLine = doc.splitTextToSize(line, contentWidth);
                wrappedLine.forEach(textLine => {
                    checkPageBreak(lineHeight + 2);
                    doc.text(textLine, margin, y);
                    y += lineHeight;
                });
                y -= lineHeight; // Adjust for the loop increment
            }
        } else {
            // Wrap long lines properly
            const wrappedLine = doc.splitTextToSize(line, contentWidth);
            wrappedLine.forEach(textLine => {
                checkPageBreak(lineHeight + 2);
                doc.text(textLine, margin, y);
                y += lineHeight;
            });
            y -= lineHeight; // Adjust for the loop increment
        }
        y += lineHeight + 1; // Reduced spacing
    });
    y += 10; // Reduced spacing

    // Client Signature Block
    const clientSignatureBlockContent = document.getElementById('clientSignatureBlock')?.value || 'SIGNED at _________________________ on this _____ day of _____________, 20___\n\nCLIENT/AGENT:\n\n_________________________________\nSignature\n\n_________________________________\nName (Print)\n\n_________________________________\nCapacity\n\n_________________________________\nDate';

    checkPageBreak(70); // Reduced space for signature block
    const clientSignatureLines = clientSignatureBlockContent.split('\n');

    clientSignatureLines.forEach((line, index) => {
        checkPageBreak(lineHeight + 2); // Reduced space check

        // Check if this is the signature line and we have a digital signature
        if (line.includes('_________________________________') && line.includes('Signature') &&
            selectedSignatures.client && index < 6) { // First signature line in client block

            // Add digital signature image
            try {
                const img = new Image();
                img.onload = function() {
                    const signatureWidth = 120;
                    const signatureHeight = 40;
                    doc.addImage(selectedSignatures.client.data, 'PNG', margin, y - 5, signatureWidth, signatureHeight);
                };
                img.src = selectedSignatures.client.data;

                // Add a note instead of the line
                const wrappedText = doc.splitTextToSize('Digital Signature Applied', contentWidth);
                wrappedText.forEach(textLine => {
                    checkPageBreak(lineHeight + 2);
                    doc.text(textLine, margin, y);
                    y += lineHeight;
                });
                y -= lineHeight; // Adjust for the loop increment
            } catch (error) {
                console.error('Error adding client signature:', error);
                const wrappedLine = doc.splitTextToSize(line, contentWidth);
                wrappedLine.forEach(textLine => {
                    checkPageBreak(lineHeight + 2);
                    doc.text(textLine, margin, y);
                    y += lineHeight;
                });
                y -= lineHeight; // Adjust for the loop increment
            }
        } else {
            // Wrap long lines properly
            const wrappedLine = doc.splitTextToSize(line, contentWidth);
            wrappedLine.forEach(textLine => {
                checkPageBreak(lineHeight + 2);
                doc.text(textLine, margin, y);
                y += lineHeight;
            });
            y -= lineHeight; // Adjust for the loop increment
        }
        y += lineHeight + 1; // Reduced spacing
    });
    y += 10; // Reduced spacing

    // Witness Section
    const witnessSectionContent = document.getElementById('witnessSection')?.value || 'WITNESSES:\n\n1. _________________________________\n   Signature\n\n   _________________________________\n   Name (Print)\n\n2. _________________________________\n   Signature\n\n   _________________________________\n   Name (Print)';

    checkPageBreak(60); // Reduced space check for witness section
    const witnessLines = witnessSectionContent.split('\n');
    witnessLines.forEach(line => {
        checkPageBreak(lineHeight + 2); // Reduced space check

        // Wrap long lines properly
        const wrappedLine = doc.splitTextToSize(line, contentWidth);
        wrappedLine.forEach(textLine => {
            checkPageBreak(lineHeight + 2);
            doc.text(textLine, margin, y);
            y += lineHeight;
        });
        y += 1; // Reduced additional spacing
    });
    y += 10; // Reduced final spacing

    // Add custom sections from Page 17
    const customSectionsPage17 = document.querySelectorAll('#customSectionsPage17Container .custom-section');
    customSectionsPage17.forEach(customSection => {
        const titleInput = customSection.querySelector('.custom-section-title');
        const contentInput = customSection.querySelector('.custom-section-content');
        if (titleInput && contentInput && titleInput.value.trim() && contentInput.value.trim()) {
            checkPageBreak(20); // Reduced space check

            // Section title
            doc.setFontSize(sectionTitleSize);
            doc.setFont(PDF_HEADING_FONT, 'bold');
            doc.setTextColor(0, 0, 0);
            doc.text(titleInput.value.trim(), margin, y);
            y += 8; // Reduced spacing

            // Section content
            doc.setFontSize(normalSize);
            doc.setFont(PDF_BODY_FONT, 'normal');
            const customPage17Lines = doc.splitTextToSize(contentInput.value.trim(), contentWidth);
            customPage17Lines.forEach(line => {
                checkPageBreak(lineHeight + 2);
                doc.text(line, margin, y);
                y += lineHeight;
            });
            y += 6; // Reduced spacing
        }
    });

    // End of Artist Agreement PDF generation
    // Note: Save operation is handled by the calling function
}

// Function to get definitions from form fields
function getDefinitionsFromForm() {
    const definitions = [];

    // Standard definitions from form fields
    const agreementDef = document.getElementById('agreementDefinition')?.value || 'means this Artist Performance Agreement between the Parties';
    definitions.push({
        term: '"Agreement"',
        definition: agreementDef
    });

    const applicableLawsDef = document.getElementById('applicableLawsDefinition')?.value || 'means all South African laws, ordinances, writs, orders, regulations, judgments and orders of any competent South African court or governmental agency or authority in South Africa;';
    definitions.push({
        term: '"Applicable laws"',
        definition: applicableLawsDef
    });

    // Artist definition (special handling for editable names)
    const artistBandName = document.getElementById('artistBandName')?.value || 'BONGO MAFFIN';
    const artistMembers = document.getElementById('artistMembers')?.value || 'Thandiswa Mazwai, Tshepo Seete, Harold MathlakU, Anesu Mupemhi';
    const artistDefinition = `means ${artistBandName} – Comprising of: ${artistMembers}.`;
    definitions.push({
        term: '"Artist"—',
        definition: artistDefinition,
        highlight: true
    });

    const businessDayDef = document.getElementById('businessDayDefinition')?.value || 'means any day other than a Saturday, Sunday or official public holiday in South Africa;';
    definitions.push({
        term: '"Business Day"',
        definition: businessDayDef
    });

    const clientDef = document.getElementById('clientDefinition')?.value || 'means any Party receiving the Services of the Artist;';
    definitions.push({
        term: '"Client"',
        definition: clientDef
    });

    const confidentialInfoDef = document.getElementById('confidentialInfoDefinition')?.value || 'means all information confidential to a party, including, to the extent that it is not freely and publicly available, commercial, financial, legal, technical, scientific and research information, trade secrets, passwords, or other secret codes, information disclosed with the permission of third parties in which the third parties have confidentiality rights, information legally protected from disclosure, any information the unauthorised disclosure of which could reasonably be expected to cause harm or risk to the disclosing party and any other information designated by the disclosing party as confidential or which is manifestly confidential;';
    definitions.push({
        term: '"Confidential Information"',
        definition: confidentialInfoDef
    });

    // Page 4 definitions
    const feesDef = document.getElementById('feesDefinition')?.value || 'means the fees payable to the Artists in consideration for the Services provided in terms of this Agreement as per Annexure "A";';
    definitions.push({
        term: '"Fees"-',
        definition: feesDef
    });

    const goodIndustryPracticeDef = document.getElementById('goodIndustryPracticeDefinition')?.value || 'means providing the Service in a proper and professional manner, taking into account generally acceptable standards, practice, methods and procedures conforming to Applicable Laws and exercising that degree of skill, that would be reasonable and ordinarily be expected of a skilled and experienced person engaged in a similar type of undertaking under similar circumstance;';
    definitions.push({
        term: '"Good Industry Practice" -',
        definition: goodIndustryPracticeDef
    });

    const intellectualPropertyDef = document.getElementById('intellectualPropertyDefinition')?.value || 'means all patents, copyrights, design rights, trademarks, service marks, trade secrets, know-how, database rights and other rights in the nature of intellectual property rights (whether registered or unregistered) and all applications for the same, in the Territory;';
    definitions.push({
        term: '"Intellectual Property" - Rights',
        definition: intellectualPropertyDef
    });

    const partiesDef = document.getElementById('partiesDefinition')?.value || 'Means individually or collectively, as the context may require the Artist and the Client;';
    definitions.push({
        term: '"Parties and/or Party" -',
        definition: partiesDef
    });

    const performanceDef = document.getElementById('performanceDefinition')?.value || 'means the musical performance to be undertaken by the Artist as part of the Services in terms of this Agreement;';
    definitions.push({
        term: '"Performance"-',
        definition: performanceDef
    });

    const performanceDateDef = document.getElementById('performanceDateDefinition')?.value || 'means (insert performance date) being the date when the Artist will undertake the Performance as part of the Services for the Client in terms of this Agreement;';
    definitions.push({
        term: '"Performance Date" –',
        definition: performanceDateDef
    });

    const rightsOfCopyrightDef = document.getElementById('rightsOfCopyrightDefinition')?.value || 'means the bundle of rights contemplated in the Copyright Act No. 98 of 1978 as amended from time to time, subsisting in the Performance and the music; and any additional or comparable rights of copyright, or sui generis right that may subsist in the future in South Africa in the Performance or that subsists in the Performanceor in the music in any other territory of jurisdiction of the world, including but not limited to do';
    definitions.push({
        term: '"Rights of Copyright"-',
        definition: rightsOfCopyrightDef
    });

    // Add custom definitions
    const customDefinitions = document.querySelectorAll('.custom-definition');
    customDefinitions.forEach(customDef => {
        const termInput = customDef.querySelector('.custom-term');
        const defInput = customDef.querySelector('.custom-definition-text');
        if (termInput && defInput && termInput.value.trim() && defInput.value.trim()) {
            definitions.push({
                term: `"${termInput.value.trim()}"`,
                definition: defInput.value.trim()
            });
        }
    });

    return definitions;
}

// Function to update artist preview
function updateArtistPreview() {
    const bandName = document.getElementById('artistBandName')?.value || 'BONGO MAFFIN';
    const members = document.getElementById('artistMembers')?.value || 'Thandiswa Mazwai, Tshepo Seete, Harold MathlakU, Anesu Mupemhi';
    const preview = document.getElementById('artistPreview');
    if (preview) {
        preview.textContent = `${bandName} – Comprising of: ${members}.`;
    }
}

// Function to add custom definition
function addCustomDefinition() {
    const container = document.getElementById('customDefinitionsContainer');
    const definitionId = 'customDef_' + Date.now();

    const customDefHTML = `
        <div class="definition-group custom-definition" id="${definitionId}">
            <button type="button" class="remove-definition" onclick="removeCustomDefinition('${definitionId}')" title="Remove Definition">×</button>
            <h4 class="definition-term">Custom Definition</h4>
            <div class="form-group">
                <label for="${definitionId}_term">Term (without quotes):</label>
                <input type="text" id="${definitionId}_term" class="custom-term" placeholder="e.g., Performance Venue">
            </div>
            <div class="form-group">
                <label for="${definitionId}_definition">Definition:</label>
                <textarea id="${definitionId}_definition" class="custom-definition-text" rows="3" placeholder="means..."></textarea>
            </div>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', customDefHTML);
}

// Function to remove custom definition
function removeCustomDefinition(definitionId) {
    const element = document.getElementById(definitionId);
    if (element) {
        element.remove();
    }
}

// Function to add custom definition for Page 4
function addCustomDefinitionPage4() {
    const container = document.getElementById('customDefinitionsPage4Container');
    const definitionId = 'customDefPage4_' + Date.now();

    const customDefHTML = `
        <div class="definition-group custom-definition" id="${definitionId}">
            <button type="button" class="remove-definition" onclick="removeCustomDefinition('${definitionId}')" title="Remove Definition">×</button>
            <h4 class="definition-term">Custom Definition</h4>
            <div class="form-group">
                <label for="${definitionId}_term">Term (without quotes):</label>
                <input type="text" id="${definitionId}_term" class="custom-term" placeholder="e.g., Territory">
            </div>
            <div class="form-group">
                <label for="${definitionId}_definition">Definition:</label>
                <textarea id="${definitionId}_definition" class="custom-definition-text" rows="3" placeholder="means..."></textarea>
            </div>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', customDefHTML);
}

// Initialize artist preview updates
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners for artist preview updates
    const artistBandNameField = document.getElementById('artistBandName');
    const artistMembersField = document.getElementById('artistMembers');

    if (artistBandNameField) {
        artistBandNameField.addEventListener('input', updateArtistPreview);
    }

    if (artistMembersField) {
        artistMembersField.addEventListener('input', updateArtistPreview);
    }

    // Initialize the preview
    updateArtistPreview();
});

// Function to add custom sections for Page 6
function addCustomSectionPage6() {
    const container = document.getElementById('customSectionsPage6Container');
    const sectionId = 'customSectionPage6_' + Date.now();

    const customSectionHTML = `
        <div class="definition-group custom-section" id="${sectionId}">
            <div class="form-group">
                <label>Section Title:</label>
                <input type="text" class="custom-section-title" placeholder="Enter section title (e.g., '5. ADDITIONAL TERMS')">
            </div>
            <div class="form-group">
                <label>Section Content:</label>
                <textarea class="custom-section-content" rows="3" placeholder="Enter section content"></textarea>
            </div>
            <button type="button" class="btn-remove" onclick="removeCustomSectionPage6('${sectionId}')">Remove Section</button>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', customSectionHTML);
}

// Function to remove custom sections for Page 6
function removeCustomSectionPage6(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        element.remove();
    }
}

// Function to add custom sections for Page 7
function addCustomSectionPage7() {
    const container = document.getElementById('customSectionsPage7Container');
    const sectionId = 'customSectionPage7_' + Date.now();

    const customSectionHTML = `
        <div class="definition-group custom-section" id="${sectionId}">
            <div class="form-group">
                <label>Section Title:</label>
                <input type="text" class="custom-section-title" placeholder="Enter section title (e.g., '8. ADDITIONAL OBLIGATIONS')">
            </div>
            <div class="form-group">
                <label>Section Content:</label>
                <textarea class="custom-section-content" rows="3" placeholder="Enter section content"></textarea>
            </div>
            <button type="button" class="btn-remove" onclick="removeCustomSectionPage7('${sectionId}')">Remove Section</button>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', customSectionHTML);
}

// Function to remove custom sections for Page 7
function removeCustomSectionPage7(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        element.remove();
    }
}

// Function to add custom sections for Page 8
function addCustomSectionPage8() {
    const container = document.getElementById('customSectionsPage8Container');
    const sectionId = 'customSectionPage8_' + Date.now();

    const customSectionHTML = `
        <div class="definition-group custom-section" id="${sectionId}">
            <div class="form-group">
                <label>Section Title:</label>
                <input type="text" class="custom-section-title" placeholder="Enter section title (e.g., '10. ADDITIONAL WARRANTIES')">
            </div>
            <div class="form-group">
                <label>Section Content:</label>
                <textarea class="custom-section-content" rows="3" placeholder="Enter section content"></textarea>
            </div>
            <button type="button" class="btn-remove" onclick="removeCustomSectionPage8('${sectionId}')">Remove Section</button>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', customSectionHTML);
}

// Function to remove custom sections for Page 8
function removeCustomSectionPage8(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        element.remove();
    }
}

// Function to add custom sections for Page 9
function addCustomSectionPage9() {
    const container = document.getElementById('customSectionsPage9Container');
    const sectionId = 'customSectionPage9_' + Date.now();

    const customSectionHTML = `
        <div class="definition-group custom-section" id="${sectionId}">
            <div class="form-group">
                <label>Section Title:</label>
                <input type="text" class="custom-section-title" placeholder="Enter section title (e.g., '11. ADDITIONAL TERMS')">
            </div>
            <div class="form-group">
                <label>Section Content:</label>
                <textarea class="custom-section-content" rows="3" placeholder="Enter section content"></textarea>
            </div>
            <button type="button" class="btn-remove" onclick="removeCustomSectionPage9('${sectionId}')">Remove Section</button>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', customSectionHTML);
}

// Function to remove custom sections for Page 9
function removeCustomSectionPage9(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        element.remove();
    }
}

// Function to add custom sections for Page 10
function addCustomSectionPage10() {
    const container = document.getElementById('customSectionsPage10Container');
    const sectionId = 'customSectionPage10_' + Date.now();

    const customSectionHTML = `
        <div class="definition-group custom-section" id="${sectionId}">
            <div class="form-group">
                <label>Section Title:</label>
                <input type="text" class="custom-section-title" placeholder="Enter section title (e.g., '11. ADDITIONAL CONFIDENTIALITY')">
            </div>
            <div class="form-group">
                <label>Section Content:</label>
                <textarea class="custom-section-content" rows="3" placeholder="Enter section content"></textarea>
            </div>
            <button type="button" class="btn-remove" onclick="removeCustomSectionPage10('${sectionId}')">Remove Section</button>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', customSectionHTML);
}

// Function to remove custom sections for Page 10
function removeCustomSectionPage10(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        element.remove();
    }
}

// Function to add custom sections for Page 11
function addCustomSectionPage11() {
    const container = document.getElementById('customSectionsPage11Container');
    const sectionId = 'customSectionPage11_' + Date.now();

    const customSectionHTML = `
        <div class="definition-group custom-section" id="${sectionId}">
            <div class="form-group">
                <label>Section Title:</label>
                <input type="text" class="custom-section-title" placeholder="Enter section title (e.g., '14. ADDITIONAL TERMS')">
            </div>
            <div class="form-group">
                <label>Section Content:</label>
                <textarea class="custom-section-content" rows="3" placeholder="Enter section content"></textarea>
            </div>
            <button type="button" class="btn-remove" onclick="removeCustomSectionPage11('${sectionId}')">Remove Section</button>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', customSectionHTML);
}

// Function to remove custom sections for Page 11
function removeCustomSectionPage11(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        element.remove();
    }
}

// Function to add custom sections for Page 12
function addCustomSectionPage12() {
    const container = document.getElementById('customSectionsPage12Container');
    const sectionId = 'customSectionPage12_' + Date.now();

    const customSectionHTML = `
        <div class="definition-group custom-section" id="${sectionId}">
            <div class="form-group">
                <label>Section Title:</label>
                <input type="text" class="custom-section-title" placeholder="Enter section title (e.g., '19. ADDITIONAL PROVISIONS')">
            </div>
            <div class="form-group">
                <label>Section Content:</label>
                <textarea class="custom-section-content" rows="3" placeholder="Enter section content"></textarea>
            </div>
            <button type="button" class="btn-remove" onclick="removeCustomSectionPage12('${sectionId}')">Remove Section</button>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', customSectionHTML);
}

// Function to remove custom sections for Page 12
function removeCustomSectionPage12(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        element.remove();
    }
}

// Function to add custom sections for Page 13
function addCustomSectionPage13() {
    const container = document.getElementById('customSectionsPage13Container');
    const sectionId = 'customSectionPage13_' + Date.now();

    const customSectionHTML = `
        <div class="definition-group custom-section" id="${sectionId}">
            <div class="form-group">
                <label>Section Title:</label>
                <input type="text" class="custom-section-title" placeholder="Enter section title (e.g., '19. GOVERNING LAW')">
            </div>
            <div class="form-group">
                <label>Section Content:</label>
                <textarea class="custom-section-content" rows="3" placeholder="Enter section content"></textarea>
            </div>
            <button type="button" class="btn-remove" onclick="removeCustomSectionPage13('${sectionId}')">Remove Section</button>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', customSectionHTML);
}

// Function to remove custom sections for Page 13
function removeCustomSectionPage13(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        element.remove();
    }
}

// Function to add custom sections for Page 14
function addCustomSectionPage14() {
    const container = document.getElementById('customSectionsPage14Container');
    const sectionId = 'customSectionPage14_' + Date.now();

    const customSectionHTML = `
        <div class="definition-group custom-section" id="${sectionId}">
            <div class="form-group">
                <label>Section Title:</label>
                <input type="text" class="custom-section-title" placeholder="Enter section title (e.g., '23. ADDITIONAL PROVISIONS')">
            </div>
            <div class="form-group">
                <label>Section Content:</label>
                <textarea class="custom-section-content" rows="3" placeholder="Enter section content"></textarea>
            </div>
            <button type="button" class="btn-remove" onclick="removeCustomSectionPage14('${sectionId}')">Remove Section</button>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', customSectionHTML);
}

// Function to remove custom sections for Page 14
function removeCustomSectionPage14(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        element.remove();
    }
}

// Function to add custom sections for Page 15
function addCustomSectionPage15() {
    const container = document.getElementById('customSectionsPage15Container');
    const sectionId = 'customSectionPage15_' + Date.now();

    const customSectionHTML = `
        <div class="definition-group custom-section" id="${sectionId}">
            <div class="form-group">
                <label>Section Title:</label>
                <input type="text" class="custom-section-title" placeholder="Enter section title (e.g., '23. SIGNATURES')">
            </div>
            <div class="form-group">
                <label>Section Content:</label>
                <textarea class="custom-section-content" rows="3" placeholder="Enter section content"></textarea>
            </div>
            <button type="button" class="btn-remove" onclick="removeCustomSectionPage15('${sectionId}')">Remove Section</button>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', customSectionHTML);
}

// Function to remove custom sections for Page 15
function removeCustomSectionPage15(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        element.remove();
    }
}

// Function to add custom sections for Page 16
function addCustomSectionPage16() {
    const container = document.getElementById('customSectionsPage16Container');
    const sectionId = 'customSectionPage16_' + Date.now();

    const customSectionHTML = `
        <div class="definition-group custom-section" id="${sectionId}">
            <div class="form-group">
                <label>Section Title:</label>
                <input type="text" class="custom-section-title" placeholder="Enter section title (e.g., '26. ADDITIONAL PROVISIONS')">
            </div>
            <div class="form-group">
                <label>Section Content:</label>
                <textarea class="custom-section-content" rows="3" placeholder="Enter section content"></textarea>
            </div>
            <button type="button" class="btn-remove" onclick="removeCustomSectionPage16('${sectionId}')">Remove Section</button>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', customSectionHTML);
}

// Function to remove custom sections for Page 16
function removeCustomSectionPage16(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        element.remove();
    }
}

// Function to add custom sections for Page 17
function addCustomSectionPage17() {
    const container = document.getElementById('customSectionsPage17Container');
    const sectionId = 'customSectionPage17_' + Date.now();

    const customSectionHTML = `
        <div class="definition-group custom-section" id="${sectionId}">
            <div class="form-group">
                <label>Section Title:</label>
                <input type="text" class="custom-section-title" placeholder="Enter section title (e.g., 'ADDITIONAL SIGNATURES')">
            </div>
            <div class="form-group">
                <label>Section Content:</label>
                <textarea class="custom-section-content" rows="3" placeholder="Enter section content"></textarea>
            </div>
            <button type="button" class="btn-remove" onclick="removeCustomSectionPage17('${sectionId}')">Remove Section</button>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', customSectionHTML);
}

// Function to remove custom sections for Page 17
function removeCustomSectionPage17(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        element.remove();
    }
}

// Digital Signature Integration Functions
function loadSavedSignaturesForAgreement() {
    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
    const artistSelect = document.getElementById('artistSignatureSelect');
    const clientSelect = document.getElementById('clientSignatureSelect');

    if (artistSelect && clientSelect) {
        // Clear existing options except the first one
        artistSelect.innerHTML = '<option value="">Select a saved signature...</option>';
        clientSelect.innerHTML = '<option value="">Select a saved signature...</option>';

        // Add saved signatures to both dropdowns
        signatures.forEach(signature => {
            const artistOption = document.createElement('option');
            artistOption.value = signature.id;
            artistOption.textContent = signature.name;
            artistSelect.appendChild(artistOption);

            const clientOption = document.createElement('option');
            clientOption.value = signature.id;
            clientOption.textContent = signature.name;
            clientSelect.appendChild(clientOption);
        });

        console.log(`Loaded ${signatures.length} signatures for Artist Agreement`);
    }
}

function previewSelectedSignature(type) {
    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');
    const selectElement = document.getElementById(`${type}SignatureSelect`);
    const previewContainer = document.getElementById(`${type}SignaturePreview`);
    const previewImg = document.getElementById(`${type}SignatureImg`);

    if (!selectElement || !previewContainer || !previewImg) return;

    const selectedId = selectElement.value;

    if (selectedId) {
        const signature = signatures.find(s => s.id == selectedId);
        if (signature) {
            previewImg.src = signature.data;
            previewImg.alt = signature.name;
            previewContainer.style.display = 'block';
            console.log(`Preview loaded for ${type} signature: ${signature.name}`);
        }
    } else {
        previewContainer.style.display = 'none';
    }
}

function openSignatureTools() {
    // Check if we're in the dashboard context
    if (typeof showSignatureTools === 'function') {
        showSignatureTools();
    } else {
        // Show signature tools inline since dashboard.html was removed
        showSignatureTools();
    }
}

function toggleSignatureInclusion() {
    const checkbox = document.getElementById('includeDigitalSignatures');
    const isEnabled = checkbox.checked;

    console.log(`Digital signature inclusion ${isEnabled ? 'enabled' : 'disabled'}`);

    // You can add visual feedback here if needed
    const signatureGroups = document.querySelectorAll('.signature-selection-group');
    signatureGroups.forEach(group => {
        group.style.opacity = isEnabled ? '1' : '0.6';
    });
}

function getSelectedSignatures() {
    const artistSelect = document.getElementById('artistSignatureSelect');
    const clientSelect = document.getElementById('clientSignatureSelect');
    const includeSignatures = document.getElementById('includeDigitalSignatures');

    if (!includeSignatures || !includeSignatures.checked) {
        return { artist: null, client: null };
    }

    const signatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');

    const artistSignature = artistSelect && artistSelect.value ?
        signatures.find(s => s.id == artistSelect.value) : null;
    const clientSignature = clientSelect && clientSelect.value ?
        signatures.find(s => s.id == clientSelect.value) : null;

    return {
        artist: artistSignature,
        client: clientSignature
    };
}

// Initialize signature integration when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Load signatures when the page loads
    setTimeout(() => {
        loadSavedSignaturesForAgreement();
    }, 500);

    // Refresh signatures when returning from signature tools
    window.addEventListener('focus', function() {
        loadSavedSignaturesForAgreement();
    });

    // Initialize file upload handler for document import
    const fileInput = document.getElementById('documentUpload');
    if (fileInput) {
        fileInput.addEventListener('change', handleFileUpload);
    }
});

// Document Import Functions
function showDocumentImport() {
    console.log('📥 Opening Document Import Modal...');
    const modal = document.getElementById('documentImportModal');
    if (modal) {
        modal.style.display = 'block';
        // Reset the modal state
        resetDocumentImport();
        // Load saved signatures
        loadSignatureLibrary();
    } else {
        console.error('Document Import Modal not found');
    }
}

function closeDocumentImport() {
    console.log('📥 Closing Document Import Modal...');
    const modal = document.getElementById('documentImportModal');
    if (modal) {
        modal.style.display = 'none';
        // Clean up any uploaded files
        resetDocumentImport();
    }
}

function resetDocumentImport() {
    // Reset file upload
    const fileInput = document.getElementById('documentUpload');
    const uploadedFileInfo = document.getElementById('uploadedFileInfo');
    const documentPreviewContainer = document.getElementById('documentPreviewContainer');
    const processBtn = document.getElementById('processDocumentBtn');
    const shareBtn = document.getElementById('shareDocumentBtn');

    if (fileInput) fileInput.value = '';
    if (uploadedFileInfo) uploadedFileInfo.style.display = 'none';
    if (processBtn) processBtn.disabled = true;
    if (shareBtn) shareBtn.disabled = true;

    // Reset preview container
    if (documentPreviewContainer) {
        documentPreviewContainer.innerHTML = `
            <div class="preview-placeholder">
                <div class="preview-icon">📄</div>
                <h4>Upload a document to see preview</h4>
                <p>The document will appear here for editing</p>
            </div>
        `;
    }

    // Reset tool selection
    const toolCards = document.querySelectorAll('.tool-card');
    toolCards.forEach(card => card.classList.remove('active'));

    // Hide signature library
    const signatureLibrarySection = document.getElementById('signatureLibrarySection');
    if (signatureLibrarySection) {
        signatureLibrarySection.style.display = 'none';
    }

    console.log('✅ Document Import reset completed');
}

function handleFileUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    console.log('📄 File selected:', file.name, 'Size:', file.size, 'Type:', file.type);

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
        alert('File size exceeds 10MB limit. Please choose a smaller file.');
        event.target.value = '';
        return;
    }

    // Validate file type
    const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/bmp'
    ];

    if (!allowedTypes.includes(file.type)) {
        alert('Unsupported file type. Please upload PDF, DOC, DOCX, TXT, or image files.');
        event.target.value = '';
        return;
    }

    // Show file info
    displayUploadedFileInfo(file);

    // Preview the document
    previewDocument(file);

    // Enable process button
    const processBtn = document.getElementById('processDocumentBtn');
    if (processBtn) processBtn.disabled = false;
}

function displayUploadedFileInfo(file) {
    const uploadedFileInfo = document.getElementById('uploadedFileInfo');
    const fileName = uploadedFileInfo.querySelector('.file-name');
    const fileSize = uploadedFileInfo.querySelector('.file-size');

    if (fileName) fileName.textContent = file.name;
    if (fileSize) fileSize.textContent = formatFileSize(file.size);

    uploadedFileInfo.style.display = 'block';

    console.log('✅ File info displayed');
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function removeUploadedFile() {
    const fileInput = document.getElementById('documentUpload');
    const uploadedFileInfo = document.getElementById('uploadedFileInfo');
    const processBtn = document.getElementById('processDocumentBtn');
    const shareBtn = document.getElementById('shareDocumentBtn');

    if (fileInput) fileInput.value = '';
    if (uploadedFileInfo) uploadedFileInfo.style.display = 'none';
    if (processBtn) processBtn.disabled = true;
    if (shareBtn) shareBtn.disabled = true;

    // Reset preview
    const documentPreviewContainer = document.getElementById('documentPreviewContainer');
    if (documentPreviewContainer) {
        documentPreviewContainer.innerHTML = `
            <div class="preview-placeholder">
                <div class="preview-icon">📄</div>
                <h4>Upload a document to see preview</h4>
                <p>The document will appear here for editing</p>
            </div>
        `;
    }

    console.log('🗑️ File removed');
}

function previewDocument(file) {
    const documentPreviewContainer = document.getElementById('documentPreviewContainer');

    if (file.type.startsWith('image/')) {
        // Handle image files
        const reader = new FileReader();
        reader.onload = function(e) {
            documentPreviewContainer.innerHTML = `
                <div class="document-canvas">
                    <div class="document-page" style="width: 100%; max-width: 600px;">
                        <img src="${e.target.result}" style="width: 100%; height: auto; display: block;">
                    </div>
                </div>
            `;
        };
        reader.readAsDataURL(file);
    } else if (file.type === 'application/pdf') {
        // Handle PDF files
        documentPreviewContainer.innerHTML = `
            <div class="document-canvas">
                <div class="document-page" style="width: 100%; max-width: 600px; height: 400px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; border: 1px solid #dee2e6;">
                    <div style="text-align: center; color: #6c757d;">
                        <div style="font-size: 48px; margin-bottom: 10px;">📄</div>
                        <h4>PDF Document</h4>
                        <p>${file.name}</p>
                        <p style="font-size: 12px;">PDF preview not available in this view</p>
                    </div>
                </div>
            </div>
        `;
    } else {
        // Handle other file types
        documentPreviewContainer.innerHTML = `
            <div class="document-canvas">
                <div class="document-page" style="width: 100%; max-width: 600px; height: 400px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; border: 1px solid #dee2e6;">
                    <div style="text-align: center; color: #6c757d;">
                        <div style="font-size: 48px; margin-bottom: 10px;">📄</div>
                        <h4>Document Preview</h4>
                        <p>${file.name}</p>
                        <p style="font-size: 12px;">Preview will be available after processing</p>
                    </div>
                </div>
            </div>
        `;
    }

    console.log('✅ Document preview generated');
}

// Tool mode functions
function enableSignatureMode() {
    // Reset all tool cards
    document.querySelectorAll('.tool-card').forEach(card => card.classList.remove('active'));

    // Activate signature tool
    event.target.closest('.tool-card').classList.add('active');

    // Show signature library
    const signatureLibrarySection = document.getElementById('signatureLibrarySection');
    if (signatureLibrarySection) {
        signatureLibrarySection.style.display = 'block';
        loadSignatureLibrary();
    }

    console.log('✍️ Signature mode enabled');
}

function enableTextMode() {
    // Reset all tool cards
    document.querySelectorAll('.tool-card').forEach(card => card.classList.remove('active'));

    // Activate text tool
    event.target.closest('.tool-card').classList.add('active');

    // Hide signature library
    const signatureLibrarySection = document.getElementById('signatureLibrarySection');
    if (signatureLibrarySection) {
        signatureLibrarySection.style.display = 'none';
    }

    console.log('📝 Text mode enabled');
}

function enableStampMode() {
    // Reset all tool cards
    document.querySelectorAll('.tool-card').forEach(card => card.classList.remove('active'));

    // Activate stamp tool
    event.target.closest('.tool-card').classList.add('active');

    // Hide signature library
    const signatureLibrarySection = document.getElementById('signatureLibrarySection');
    if (signatureLibrarySection) {
        signatureLibrarySection.style.display = 'none';
    }

    console.log('🔖 Stamp mode enabled');
}

function enableDateMode() {
    // Reset all tool cards
    document.querySelectorAll('.tool-card').forEach(card => card.classList.remove('active'));

    // Activate date tool
    event.target.closest('.tool-card').classList.add('active');

    // Hide signature library
    const signatureLibrarySection = document.getElementById('signatureLibrarySection');
    if (signatureLibrarySection) {
        signatureLibrarySection.style.display = 'none';
    }

    console.log('📅 Date mode enabled');
}

function loadSignatureLibrary() {
    const signatureLibrary = document.getElementById('signatureLibrary');
    if (!signatureLibrary) return;

    // Get saved signatures from localStorage
    const savedSignatures = JSON.parse(localStorage.getItem('savedSignatures') || '[]');

    if (savedSignatures.length === 0) {
        signatureLibrary.innerHTML = '<p class="no-signatures">No saved signatures. Create some in Signature Tools first.</p>';
        return;
    }

    // Display saved signatures
    signatureLibrary.innerHTML = '';
    savedSignatures.forEach((signature, index) => {
        const signatureItem = document.createElement('div');
        signatureItem.className = 'signature-item';
        signatureItem.innerHTML = `
            <div class="signature-preview">
                <img src="${signature.dataUrl}" alt="${signature.name}" style="width: 100%; height: 100%; object-fit: contain;">
            </div>
            <div class="signature-name">${signature.name}</div>
        `;

        signatureItem.addEventListener('click', () => {
            // Add signature to document (placeholder functionality)
            console.log('📝 Adding signature to document:', signature.name);
            alert(`Signature "${signature.name}" would be added to the document at cursor position.`);
        });

        signatureLibrary.appendChild(signatureItem);
    });

    console.log(`✅ Loaded ${savedSignatures.length} signatures`);
}

function createTestDocument() {
    // Create a simple test document
    const canvas = document.createElement('canvas');
    canvas.width = 600;
    canvas.height = 800;
    const ctx = canvas.getContext('2d');

    // White background
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Add some sample content
    ctx.fillStyle = '#000000';
    ctx.font = 'bold 24px Arial';
    ctx.fillText('TEST DOCUMENT', 50, 50);

    ctx.font = '16px Arial';
    ctx.fillText('This is a test document for demonstration purposes.', 50, 100);
    ctx.fillText('You can add signatures, text, stamps, and dates to this document.', 50, 130);

    // Add signature line
    ctx.strokeStyle = '#cccccc';
    ctx.beginPath();
    ctx.moveTo(50, 700);
    ctx.lineTo(300, 700);
    ctx.stroke();
    ctx.fillText('Signature: ___________________', 50, 720);

    // Add date line
    ctx.beginPath();
    ctx.moveTo(350, 700);
    ctx.lineTo(550, 700);
    ctx.stroke();
    ctx.fillText('Date: ___________________', 350, 720);

    // Convert to blob and simulate file upload
    canvas.toBlob(function(blob) {
        const file = new File([blob], 'test-document.png', { type: 'image/png' });

        // Simulate file selection
        const fileInput = document.getElementById('documentUpload');
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);
        fileInput.files = dataTransfer.files;

        // Trigger file upload handling
        handleFileUpload({ target: { files: [file] } });

        console.log('🧪 Test document created and loaded');
    }, 'image/png');
}

function processDocument() {
    const fileInput = document.getElementById('documentUpload');
    const file = fileInput.files[0];

    if (!file) {
        alert('Please upload a document first.');
        return;
    }

    console.log('⚙️ Processing document:', file.name);

    // Simulate document processing
    const processBtn = document.getElementById('processDocumentBtn');
    const shareBtn = document.getElementById('shareDocumentBtn');

    if (processBtn) {
        processBtn.disabled = true;
        processBtn.textContent = 'Processing...';
    }

    // Simulate processing delay
    setTimeout(() => {
        if (processBtn) {
            processBtn.disabled = false;
            processBtn.textContent = 'Process Document';
        }

        if (shareBtn) {
            shareBtn.disabled = false;
        }

        alert('Document processed successfully! You can now share it.');
        console.log('✅ Document processing completed');
    }, 2000);
}

function shareDocument() {
    const fileInput = document.getElementById('documentUpload');
    const file = fileInput.files[0];

    if (!file) {
        alert('Please upload and process a document first.');
        return;
    }

    console.log('📤 Opening document sharing options for:', file.name);

    // Close import modal and open sharing modal
    closeDocumentImport();

    // Set up sharing modal with document info
    const sharingModal = document.getElementById('documentSharingModal');
    const sharingDocumentName = document.getElementById('sharingDocumentName');
    const sharingDocumentType = document.getElementById('sharingDocumentType');
    const sharingDocumentSize = document.getElementById('sharingDocumentSize');

    if (sharingDocumentName) sharingDocumentName.textContent = file.name;
    if (sharingDocumentType) sharingDocumentType.textContent = file.type;
    if (sharingDocumentSize) sharingDocumentSize.textContent = formatFileSize(file.size);

    if (sharingModal) {
        sharingModal.style.display = 'block';
    }
}
