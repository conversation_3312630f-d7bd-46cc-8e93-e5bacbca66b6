/**
 * 🤖 AI Assistant Integration for DocuGen Pro
 * Supports multiple AI APIs including DeepSeek, OpenAI, Claude, etc.
 */

class AIAssistant {
    constructor(config = {}) {
        this.apiKey = config.apiKey || '';
        this.provider = config.provider || 'deepseek'; // deepseek, openai, claude, groq
        this.baseURL = this.getBaseURL();
        this.model = this.getModel();
        this.maxTokens = config.maxTokens || 2000;
        this.temperature = config.temperature || 0.7;
    }

    getBaseURL() {
        const urls = {
            deepseek: 'https://api.deepseek.com/v1',
            openai: 'https://api.openai.com/v1',
            claude: 'https://api.anthropic.com/v1',
            groq: 'https://api.groq.com/openai/v1'
        };
        return urls[this.provider] || urls.deepseek;
    }

    getModel() {
        const models = {
            deepseek: 'deepseek-chat',
            openai: 'gpt-4',
            claude: 'claude-3-sonnet-20240229',
            groq: 'mixtral-8x7b-32768'
        };
        return models[this.provider] || models.deepseek;
    }

    async debugCode(code, problem, context = '') {
        const prompt = `🔧 DEBUG REQUEST:

PROBLEM: ${problem}

CODE TO DEBUG:
\`\`\`javascript
${code}
\`\`\`

CONTEXT: ${context}

Please analyze this code and provide:
1. 🎯 Root cause of the problem
2. 🔧 Specific fix with code
3. 🚀 Improved version if possible
4. 📋 Testing steps to verify the fix

Focus on practical, working solutions.`;

        return await this.sendRequest(prompt);
    }

    async optimizeInvoiceCode(codeSection, feature) {
        const prompt = `🚀 OPTIMIZATION REQUEST:

FEATURE: ${feature}

CODE TO OPTIMIZE:
\`\`\`javascript
${codeSection}
\`\`\`

Please provide:
1. 📈 Performance improvements
2. 🎨 Code quality enhancements  
3. 🔧 Bug fixes and edge cases
4. 💡 Additional features suggestions
5. 📱 Mobile responsiveness improvements

Return optimized code with explanations.`;

        return await this.sendRequest(prompt);
    }

    async generateInvoiceFeature(description, requirements = []) {
        const prompt = `🎯 FEATURE GENERATION REQUEST:

DESCRIPTION: ${description}

REQUIREMENTS:
${requirements.map(req => `• ${req}`).join('\n')}

Please generate:
1. 📄 Complete HTML structure
2. 🎨 CSS styling (responsive)
3. ⚡ JavaScript functionality
4. 🧪 Testing code
5. 📋 Integration instructions

Focus on DocuGen Pro invoice system integration.`;

        return await this.sendRequest(prompt);
    }

    async fixSharingIssue(htmlStructure, cssRules, jsCode) {
        const prompt = `🔧 SHARING ISSUE FIX:

PROBLEM: Quick sharing section not appearing after PDF generation

HTML STRUCTURE:
\`\`\`html
${htmlStructure}
\`\`\`

CSS RULES:
\`\`\`css
${cssRules}
\`\`\`

JAVASCRIPT CODE:
\`\`\`javascript
${jsCode}
\`\`\`

Please provide:
1. 🎯 Exact cause of visibility issue
2. 🔧 Working fix with code
3. 🚀 Improved implementation
4. 🧪 Testing steps

Return complete working solution.`;

        return await this.sendRequest(prompt);
    }

    async sendRequest(prompt) {
        try {
            console.log('🤖 Sending request to AI assistant...');
            
            const requestBody = this.buildRequestBody(prompt);
            
            const response = await fetch(`${this.baseURL}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`,
                    ...(this.provider === 'claude' && { 'anthropic-version': '2023-06-01' })
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`API request failed: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            const result = this.extractResponse(data);
            
            console.log('✅ AI assistant response received');
            return {
                success: true,
                response: result,
                usage: data.usage || null
            };

        } catch (error) {
            console.error('❌ AI assistant error:', error);
            return {
                success: false,
                error: error.message,
                response: null
            };
        }
    }

    buildRequestBody(prompt) {
        const baseBody = {
            model: this.model,
            messages: [
                {
                    role: 'system',
                    content: 'You are an expert JavaScript developer specializing in invoice generation systems, PDF creation, and web application debugging. Provide practical, working solutions with complete code examples.'
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            max_tokens: this.maxTokens,
            temperature: this.temperature
        };

        // Provider-specific adjustments
        if (this.provider === 'claude') {
            return {
                model: this.model,
                max_tokens: this.maxTokens,
                messages: baseBody.messages
            };
        }

        return baseBody;
    }

    extractResponse(data) {
        if (this.provider === 'claude') {
            return data.content?.[0]?.text || 'No response';
        }
        return data.choices?.[0]?.message?.content || 'No response';
    }

    // Quick setup methods
    static async setupDeepSeek(apiKey) {
        const assistant = new AIAssistant({
            provider: 'deepseek',
            apiKey: apiKey
        });
        
        // Test connection
        const test = await assistant.sendRequest('Hello! Can you help with JavaScript debugging?');
        if (test.success) {
            console.log('✅ DeepSeek API connected successfully');
            return assistant;
        } else {
            console.error('❌ DeepSeek API connection failed:', test.error);
            return null;
        }
    }

    static async setupOpenAI(apiKey) {
        const assistant = new AIAssistant({
            provider: 'openai',
            apiKey: apiKey
        });
        
        const test = await assistant.sendRequest('Hello! Can you help with JavaScript debugging?');
        if (test.success) {
            console.log('✅ OpenAI API connected successfully');
            return assistant;
        } else {
            console.error('❌ OpenAI API connection failed:', test.error);
            return null;
        }
    }

    static async setupGroq(apiKey) {
        const assistant = new AIAssistant({
            provider: 'groq',
            apiKey: apiKey
        });
        
        const test = await assistant.sendRequest('Hello! Can you help with JavaScript debugging?');
        if (test.success) {
            console.log('✅ Groq API connected successfully');
            return assistant;
        } else {
            console.error('❌ Groq API connection failed:', test.error);
            return null;
        }
    }
}

// Global AI assistant instance
window.aiAssistant = null;

// Easy setup functions
window.setupAI = {
    deepseek: async (apiKey) => {
        window.aiAssistant = await AIAssistant.setupDeepSeek(apiKey);
        return window.aiAssistant;
    },
    
    openai: async (apiKey) => {
        window.aiAssistant = await AIAssistant.setupOpenAI(apiKey);
        return window.aiAssistant;
    },
    
    groq: async (apiKey) => {
        window.aiAssistant = await AIAssistant.setupGroq(apiKey);
        return window.aiAssistant;
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AIAssistant;
}
