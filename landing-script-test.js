// Minimal test JavaScript file
console.log('🚀 Test script loading...');
alert('Test JavaScript is working!');

function showLogin() {
    alert('showLogin function called!');
    document.getElementById('authModal').style.display = 'block';
    document.getElementById('signinForm').style.display = 'block';
    document.getElementById('signupForm').style.display = 'none';
}

function showSignup() {
    alert('showSignup function called!');
    document.getElementById('authModal').style.display = 'block';
    document.getElementById('signinForm').style.display = 'none';
    document.getElementById('signupForm').style.display = 'block';
}

function showDemo() {
    alert('showDemo function called!');
}

function previewDocument(type) {
    alert('previewDocument function called with type: ' + type);
}

function openSimpleSigningTool() {
    alert('openSimpleSigningTool function called!');
    const modal = document.getElementById('simpleSigningModal');
    if (modal) {
        modal.style.display = 'block';
    } else {
        alert('Modal not found!');
    }
}

function closeAuth() {
    document.getElementById('authModal').style.display = 'none';
}

function closeSimpleSigningTool() {
    document.getElementById('simpleSigningModal').style.display = 'none';
}

console.log('✅ Test script loaded successfully');
